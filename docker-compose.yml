# Shared environment variables for all services
x-shared-env: &shared-env
  TLS_DISABLED: true
  COGNITO_USER_POOL_ID: us-west-2_88f247NGw
  AWS_REGION: us-west-2
  DATABASE_URL: **********************************************/mydatabase?sslmode=disable
  PERMS_SERVICE_URL: http://perms-service:8080
  SKIP_PERMISSIONS_CHECK: ${SKIP_PERMISSIONS_CHECK:-false}
  REPO_TYPE: postgres
  ENVIRONMENT: ${ENVIRONMENT:-development}
  SENTRY_DSN: https://<EMAIL>/4509550656618496
  DISABLE_SENTRY: ${DISABLE_SENTRY:-true}
services:
  postgres:
    image: postgres:17
    container_name: postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: mypassword
      POSTGRES_DB: mydatabase
      ENVIRONMENT: ${ENVIRONMENT:-development}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./infra/scripts/init-db.sh:/docker-entrypoint-initdb.d/01-init-db.sh
      - ./infra/scripts:/scripts
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres -d mydatabase" ]
      interval: 5s
      timeout: 5s
      retries: 5

  fga-postgres:
    image: postgres:17
    container_name: fga-postgres
    ports:
      - "5433:5432"
    environment:
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypassword
      POSTGRES_DB: fga
      ENVIRONMENT: ${ENVIRONMENT:-development}
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U myuser -d fga" ]
      interval: 5s
      timeout: 5s
      retries: 5

  openfga-migrate:
    depends_on:
      postgres:
        condition: service_healthy
    image: openfga/openfga:latest
    container_name: migrate
    command: migrate
    environment:
      OPENFGA_DATASTORE_ENGINE: postgres
      OPENFGA_DATASTORE_URI: **********************************************/fga?sslmode=disable
      ENVIRONMENT: ${ENVIRONMENT:-development}

  openfga:
    image: openfga/openfga:latest
    container_name: openfga
    ports:
      # Needed for the http server
      - "8080:8080"
      # Needed for the grpc server (if used)
      - "8081:8081"
      # Needed for the playground (Do not enable in prod!)
      # - "3000:3000"
    command: run
    depends_on:
      openfga-migrate:
        condition: service_completed_successfully
    environment:
      - OPENFGA_DATASTORE_ENGINE=postgres
      - OPENFGA_DATASTORE_URI=**********************************************/fga?sslmode=disable
      - OPENFGA_LOG_FORMAT=json
      - OPENFGA_LOG_LEVEL=error
      - OPENFGA_CACHE_CONTROLLER_ENABLED=true
      - OPENFGA_CHECK_QUERY_CACHE_ENABLED=true
      - OPENFGA_CHECK_QUERY_CACHE_TTL=300s
      - OPENFGA_CHECK_ITERATOR_CACHE_ENABLED=true
      - OPENFGA_CHECK_ITERATOR_CACHE_TTL=300s
      - OPENFGA_LIST_OBJECTS_ITERATOR_CACHE_ENABLED=true
      - OPENFGA_LIST_OBJECTS_ITERATOR_CACHE_TTL=300s

  perms-service:
    build:
      context: .
      dockerfile: ./services/perms/Dockerfile
    container_name: perms-service
    ports:
      - "9088:8080"
    environment:
      <<: *shared-env
      FGA_API_URL: http://openfga:8080
      WORKFLOW_SERVICE_URL: http://workflow-service:8080
      PERMS_SERVICE_URL: http://perms-service:8080
    depends_on:
      - postgres
      - openfga

  workflow-service:
    build:
      context: .
      dockerfile: ./services/workflow/Dockerfile
    container_name: workflow-service
    depends_on:
      - postgres
    volumes:
      - type: bind
        source: ${HOME}/.aws
        target: /root/.aws
    environment:
      <<: *shared-env
      AWS_PROFILE: ${AWS_PROFILE}
      KMS_KEY_ARN: arn:aws:kms:us-west-2:************:key/7beb303b-506b-421e-b325-5839307bf9d7
      COMMAND_SERVICE_URL: http://command-service:8080
      WORKFLOW_SERVICE_URL: http://workflow-service:8080
      GO_ENV: ${GO_ENV:-local} # Set to 'local' by default, set to 'production' for production
    ports:
      - "9086:8080"

  filerepository-service:
    build:
      context: .
      dockerfile: ./services/filerepository/Dockerfile
    container_name: filerepository-service
    depends_on:
      - postgres
    volumes:
      - type: bind
        source: ${HOME}/.aws
        target: /root/.aws
    environment:
      <<: *shared-env
      AWS_PROFILE: ${AWS_PROFILE}
      AWS_S3_BUCKET_NAME: ${AWS_S3_BUCKET_NAME:-demo-1-hero-file-repository-dev}
    ports:
      - "9089:8080"

  featureflags-service:
    build:
      context: .
      dockerfile: ./services/featureflags/Dockerfile
    container_name: featureflags-service
    depends_on:
      - postgres
    environment:
      <<: *shared-env
    ports:
      - "9090:8080"

  # command-service:
  #   build:
  #     context: .
  #     dockerfile: ./services/command/Dockerfile
  #   container_name: command-service
  #   depends_on:
  #     - postgres
  #   environment:
  #     DATABASE_URL: ******************************************/mydatabase?sslmode=disable
  #     COMMAND_SERVICE_URL: http://command-service:8080
  #     WORKFLOW_SERVICE_URL: http://workflow-service:8080
  #     SKIP_PERMISSIONS_CHECK: ${SKIP_PERMISSIONS_CHECK:-false}
  #   ports:
  #     - "9083:8080"

  communications-service:
    build:
      context: .
      dockerfile: ./services/communications/Dockerfile.dev
    container_name: communications-service
    env_file:
      - services/.env
    volumes:
      # Mount source code for hot reloading
      - ./services/communications:/app/services/communications
      - ./lib:/app/lib
      - type: bind
        source: ${HOME}/.aws
        target: /root/.aws
    environment:
      <<: *shared-env
      ENV: ${ENV:-local} # Set to 'local' by default, set to 'production' for production
      ORGS_SERVICE_URL: http://orgs-service:8080
      WORKFLOW_SERVICE_URL: http://workflow-service:8080
      GO_ENV: ${GO_ENV:-local} # Set to 'local' by default, set to 'production' for production
      COMMS_SERVER_PUBLIC_DOMAIN: ${COMMS_SERVER_PUBLIC_DOMAIN:-communications.basic.api.gethero.com}
    ports:
      - "9084:8080"

  orgs-service:
    build:
      context: .
      dockerfile: ./services/orgs/Dockerfile
    container_name: orgs-service
    depends_on:
      - postgres
    env_file:
      - services/.env
    volumes:
      - type: bind
        source: ${HOME}/.aws
        target: /root/.aws
      - type: bind
        source: ./services/orgs/internal/config
        target: /app/config
    environment:
      <<: *shared-env
      AWS_PROFILE: ${AWS_PROFILE}
      KMS_KEY_ARN: arn:aws:kms:us-west-2:************:key/01920be1-7591-433d-9ac2-449bed40e840
      COMMS_SERVER_PUBLIC_DOMAIN: ${COMMS_SERVER_PUBLIC_DOMAIN:-communications.basic.api.gethero.com}
    ports:
      - "9087:8080"
 
  sensors-service:
    build:
      context: .
      dockerfile: ./services/sensors/Dockerfile
      args:
        AWS_ACCOUNT_ID: ************
    container_name: sensors-service
    depends_on:
      - postgres
      - workflow-service
    volumes:
      - type: bind
        source: ${HOME}/.aws
        target: /root/.aws
    environment:
      <<: *shared-env
      AWS_PROFILE: ${AWS_PROFILE}
      KMS_KEY_ARN: arn:aws:kms:us-west-2:************:key/0ac225aa-3b86-425d-8c6e-7c38911f7079
      WORKFLOW_SERVICE_URL: http://workflow-service:8080
      PORT: 8080
    ports:
      - "9085:8080"
      
volumes:
  postgres-data:

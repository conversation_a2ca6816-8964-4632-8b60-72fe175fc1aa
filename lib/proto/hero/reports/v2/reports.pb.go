// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/reports/v2/reports.proto

package reports

import (
	_ "proto/hero/assets/v2"
	v1 "proto/hero/entity/v1"
	_ "proto/hero/property/v1"
	v2 "proto/hero/situations/v2"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// -----------------------------------------------------------------------------
// Report Status Lifecycle
// -----------------------------------------------------------------------------
// Defines the high-level lifecycle states of a report.
type ReportStatus int32

const (
	ReportStatus_REPORT_STATUS_UNSPECIFIED          ReportStatus = 0 // Default unset state
	ReportStatus_REPORT_STATUS_ASSIGNED             ReportStatus = 1 // Task has been assigned to an author
	ReportStatus_REPORT_STATUS_IN_PROGRESS          ReportStatus = 2 // Author is actively writing the report
	ReportStatus_REPORT_STATUS_SUBMITTED_FOR_REVIEW ReportStatus = 3 // Report submitted and awaiting review
	ReportStatus_REPORT_STATUS_UNDER_REVIEW         ReportStatus = 4 // Reviewer is actively reviewing
	ReportStatus_REPORT_STATUS_CHANGES_REQUESTED    ReportStatus = 5 // Reviewer requested changes
	ReportStatus_REPORT_STATUS_IN_REWORK            ReportStatus = 6 // Author is reworking requested changes
	ReportStatus_REPORT_STATUS_APPROVED             ReportStatus = 7 // Report approved and finalized
	ReportStatus_REPORT_STATUS_REJECTED             ReportStatus = 8 // Report permanently rejected
	ReportStatus_REPORT_STATUS_CANCELLED            ReportStatus = 9 // Report process cancelled
)

// Enum value maps for ReportStatus.
var (
	ReportStatus_name = map[int32]string{
		0: "REPORT_STATUS_UNSPECIFIED",
		1: "REPORT_STATUS_ASSIGNED",
		2: "REPORT_STATUS_IN_PROGRESS",
		3: "REPORT_STATUS_SUBMITTED_FOR_REVIEW",
		4: "REPORT_STATUS_UNDER_REVIEW",
		5: "REPORT_STATUS_CHANGES_REQUESTED",
		6: "REPORT_STATUS_IN_REWORK",
		7: "REPORT_STATUS_APPROVED",
		8: "REPORT_STATUS_REJECTED",
		9: "REPORT_STATUS_CANCELLED",
	}
	ReportStatus_value = map[string]int32{
		"REPORT_STATUS_UNSPECIFIED":          0,
		"REPORT_STATUS_ASSIGNED":             1,
		"REPORT_STATUS_IN_PROGRESS":          2,
		"REPORT_STATUS_SUBMITTED_FOR_REVIEW": 3,
		"REPORT_STATUS_UNDER_REVIEW":         4,
		"REPORT_STATUS_CHANGES_REQUESTED":    5,
		"REPORT_STATUS_IN_REWORK":            6,
		"REPORT_STATUS_APPROVED":             7,
		"REPORT_STATUS_REJECTED":             8,
		"REPORT_STATUS_CANCELLED":            9,
	}
)

func (x ReportStatus) Enum() *ReportStatus {
	p := new(ReportStatus)
	*p = x
	return p
}

func (x ReportStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_reports_v2_reports_proto_enumTypes[0].Descriptor()
}

func (ReportStatus) Type() protoreflect.EnumType {
	return &file_hero_reports_v2_reports_proto_enumTypes[0]
}

func (x ReportStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportStatus.Descriptor instead.
func (ReportStatus) EnumDescriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{0}
}

// -----------------------------------------------------------------------------
// Report Type
// -----------------------------------------------------------------------------
// Defines the different types of reports that can be created.
type ReportType int32

const (
	ReportType_REPORT_TYPE_UNSPECIFIED                         ReportType = 0 // Default unset type
	ReportType_REPORT_TYPE_INCIDENT_PRIMARY                    ReportType = 1 // Primary incident report for an incident
	ReportType_REPORT_TYPE_INCIDENT_SUPPLEMENTAL               ReportType = 2 // Supplemental incident report for an incident
	ReportType_REPORT_TYPE_INCIDENT_SUPPLEMENTAL_INVESTIGATIVE ReportType = 3 // Supplemental investigative report for an incident
)

// Enum value maps for ReportType.
var (
	ReportType_name = map[int32]string{
		0: "REPORT_TYPE_UNSPECIFIED",
		1: "REPORT_TYPE_INCIDENT_PRIMARY",
		2: "REPORT_TYPE_INCIDENT_SUPPLEMENTAL",
		3: "REPORT_TYPE_INCIDENT_SUPPLEMENTAL_INVESTIGATIVE",
	}
	ReportType_value = map[string]int32{
		"REPORT_TYPE_UNSPECIFIED":                         0,
		"REPORT_TYPE_INCIDENT_PRIMARY":                    1,
		"REPORT_TYPE_INCIDENT_SUPPLEMENTAL":               2,
		"REPORT_TYPE_INCIDENT_SUPPLEMENTAL_INVESTIGATIVE": 3,
	}
)

func (x ReportType) Enum() *ReportType {
	p := new(ReportType)
	*p = x
	return p
}

func (x ReportType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_reports_v2_reports_proto_enumTypes[1].Descriptor()
}

func (ReportType) Type() protoreflect.EnumType {
	return &file_hero_reports_v2_reports_proto_enumTypes[1]
}

func (x ReportType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportType.Descriptor instead.
func (ReportType) EnumDescriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{1}
}

// -----------------------------------------------------------------------------
// Section Types
// -----------------------------------------------------------------------------
// Enumerates the different types of sections within a report.
type SectionType int32

const (
	SectionType_SECTION_TYPE_UNSPECIFIED               SectionType = 0  // Default unset type
	SectionType_SECTION_TYPE_NARRATIVE                 SectionType = 1  // Free-form rich text (e.g., HTML/Markdown)
	SectionType_SECTION_TYPE_INCIDENT_DETAILS          SectionType = 3  // Structured incident summary
	SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE        SectionType = 4  // List of references to people entities
	SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE       SectionType = 5  // List of references to vehicle entities
	SectionType_SECTION_TYPE_ENTITY_LIST_PROPERTIES    SectionType = 6  // List of references to property entities
	SectionType_SECTION_TYPE_OFFENSE                   SectionType = 7  // Free-form offense information
	SectionType_SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS SectionType = 8  // List of references to organization entities
	SectionType_SECTION_TYPE_ARREST                    SectionType = 9  // Structured arrest details
	SectionType_SECTION_TYPE_MEDIA                     SectionType = 10 // Media attachments and files
	SectionType_SECTION_TYPE_PROPERTY                  SectionType = 11 // New property service section
)

// Enum value maps for SectionType.
var (
	SectionType_name = map[int32]string{
		0:  "SECTION_TYPE_UNSPECIFIED",
		1:  "SECTION_TYPE_NARRATIVE",
		3:  "SECTION_TYPE_INCIDENT_DETAILS",
		4:  "SECTION_TYPE_ENTITY_LIST_PEOPLE",
		5:  "SECTION_TYPE_ENTITY_LIST_VEHICLE",
		6:  "SECTION_TYPE_ENTITY_LIST_PROPERTIES",
		7:  "SECTION_TYPE_OFFENSE",
		8:  "SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS",
		9:  "SECTION_TYPE_ARREST",
		10: "SECTION_TYPE_MEDIA",
		11: "SECTION_TYPE_PROPERTY",
	}
	SectionType_value = map[string]int32{
		"SECTION_TYPE_UNSPECIFIED":               0,
		"SECTION_TYPE_NARRATIVE":                 1,
		"SECTION_TYPE_INCIDENT_DETAILS":          3,
		"SECTION_TYPE_ENTITY_LIST_PEOPLE":        4,
		"SECTION_TYPE_ENTITY_LIST_VEHICLE":       5,
		"SECTION_TYPE_ENTITY_LIST_PROPERTIES":    6,
		"SECTION_TYPE_OFFENSE":                   7,
		"SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS": 8,
		"SECTION_TYPE_ARREST":                    9,
		"SECTION_TYPE_MEDIA":                     10,
		"SECTION_TYPE_PROPERTY":                  11,
	}
)

func (x SectionType) Enum() *SectionType {
	p := new(SectionType)
	*p = x
	return p
}

func (x SectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_reports_v2_reports_proto_enumTypes[2].Descriptor()
}

func (SectionType) Type() protoreflect.EnumType {
	return &file_hero_reports_v2_reports_proto_enumTypes[2]
}

func (x SectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SectionType.Descriptor instead.
func (SectionType) EnumDescriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{2}
}

// -----------------------------------------------------------------------------
// Review Status
// -----------------------------------------------------------------------------
// State machine for individual review rounds.
type ReviewStatus int32

const (
	ReviewStatus_REVIEW_STATUS_UNSPECIFIED       ReviewStatus = 0
	ReviewStatus_REVIEW_STATUS_AWAITING_ACTION   ReviewStatus = 1 // Waiting on reviewer or author
	ReviewStatus_REVIEW_STATUS_APPROVED          ReviewStatus = 2 // Round approved
	ReviewStatus_REVIEW_STATUS_CHANGES_REQUESTED ReviewStatus = 3 // Reviewer asked for changes
)

// Enum value maps for ReviewStatus.
var (
	ReviewStatus_name = map[int32]string{
		0: "REVIEW_STATUS_UNSPECIFIED",
		1: "REVIEW_STATUS_AWAITING_ACTION",
		2: "REVIEW_STATUS_APPROVED",
		3: "REVIEW_STATUS_CHANGES_REQUESTED",
	}
	ReviewStatus_value = map[string]int32{
		"REVIEW_STATUS_UNSPECIFIED":       0,
		"REVIEW_STATUS_AWAITING_ACTION":   1,
		"REVIEW_STATUS_APPROVED":          2,
		"REVIEW_STATUS_CHANGES_REQUESTED": 3,
	}
)

func (x ReviewStatus) Enum() *ReviewStatus {
	p := new(ReviewStatus)
	*p = x
	return p
}

func (x ReviewStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_reports_v2_reports_proto_enumTypes[3].Descriptor()
}

func (ReviewStatus) Type() protoreflect.EnumType {
	return &file_hero_reports_v2_reports_proto_enumTypes[3]
}

func (x ReviewStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewStatus.Descriptor instead.
func (ReviewStatus) EnumDescriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{3}
}

// / How to sort the search results
type SearchOrderBy int32

const (
	SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED SearchOrderBy = 0
	SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE   SearchOrderBy = 1
	SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT  SearchOrderBy = 2
	SearchOrderBy_SEARCH_ORDER_BY_UPDATED_AT  SearchOrderBy = 3
	SearchOrderBy_SEARCH_ORDER_BY_STATUS      SearchOrderBy = 4
)

// Enum value maps for SearchOrderBy.
var (
	SearchOrderBy_name = map[int32]string{
		0: "SEARCH_ORDER_BY_UNSPECIFIED",
		1: "SEARCH_ORDER_BY_RELEVANCE",
		2: "SEARCH_ORDER_BY_CREATED_AT",
		3: "SEARCH_ORDER_BY_UPDATED_AT",
		4: "SEARCH_ORDER_BY_STATUS",
	}
	SearchOrderBy_value = map[string]int32{
		"SEARCH_ORDER_BY_UNSPECIFIED": 0,
		"SEARCH_ORDER_BY_RELEVANCE":   1,
		"SEARCH_ORDER_BY_CREATED_AT":  2,
		"SEARCH_ORDER_BY_UPDATED_AT":  3,
		"SEARCH_ORDER_BY_STATUS":      4,
	}
)

func (x SearchOrderBy) Enum() *SearchOrderBy {
	p := new(SearchOrderBy)
	*p = x
	return p
}

func (x SearchOrderBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchOrderBy) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_reports_v2_reports_proto_enumTypes[4].Descriptor()
}

func (SearchOrderBy) Type() protoreflect.EnumType {
	return &file_hero_reports_v2_reports_proto_enumTypes[4]
}

func (x SearchOrderBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchOrderBy.Descriptor instead.
func (SearchOrderBy) EnumDescriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{4}
}

// -----------------------------------------------------------------------------
// Object Reference (for Relations)
// -----------------------------------------------------------------------------
// Flexible reference to any object within a report that can participate in relations.
// Uses string-based types for maximum flexibility without requiring protobuf changes.
//
// VALIDATION: At least one of report_scoped_id, global_id, or external_id MUST be set.
//
// ID Types:
//   - report_scoped_id: IDs unique within this report (e.g., responder.id, reporting_person.id, section content IDs)
//   - global_id: System-wide unique IDs (e.g., asset_id, entity_id, case_id, situation_id)
//   - external_id: IDs from external systems or custom references
//
// Common object_type values (extensible):
//   - "entity"           - Reference to an entity (person, vehicle, property) - use global_id
//   - "section"          - Reference to a report section - use global_id (section.id)
//   - "offense"          - Reference to an offense within a section - use report_scoped_id (offense.id)
//   - "incident_details" - Reference to incident details within a section - use report_scoped_id (incident_details.id)
//   - "narrative"        - Reference to narrative content within a section - use report_scoped_id (narrative.id)
//   - "comment"          - Reference to a comment - use global_id (comment.id)
//   - "responder"        - Reference to a specific responder in incident details - use report_scoped_id (responder.id)
//   - "reporting_person" - Reference to the reporting person in incident details - use report_scoped_id (reporting_person.id)
//   - "asset"            - Reference to an asset - use global_id (asset_id)
//   - "case"             - Reference to a case - use global_id (case_id)
//   - "situation"        - Reference to a situation - use global_id (situation_id)
//   - "file_reference"   - Reference to a file reference within a media section - use report_scoped_id (file_reference.id)
//   - "media_content"    - Reference to media content within a section - use report_scoped_id (media_content.id)
//   - "custom_*"         - Any custom object type
type ObjectReference struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ObjectType     string                 `protobuf:"bytes,1,opt,name=object_type,json=objectType,proto3" json:"object_type,omitempty"`               // Type of the referenced object (flexible string) - REQUIRED
	ReportScopedId string                 `protobuf:"bytes,2,opt,name=report_scoped_id,json=reportScopedId,proto3" json:"report_scoped_id,omitempty"` // ID unique within this report (e.g., responder.id, content.id)
	GlobalId       string                 `protobuf:"bytes,3,opt,name=global_id,json=globalId,proto3" json:"global_id,omitempty"`                     // System-wide unique ID (e.g., asset_id, entity_id, section.id)
	ExternalId     string                 `protobuf:"bytes,4,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`               // External system ID or custom reference
	SectionId      string                 `protobuf:"bytes,5,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`                  // If object is within a section, the section ID for context
	DisplayName    string                 `protobuf:"bytes,6,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`            // Human-readable name for the referenced object (recommended)
	Metadata       *structpb.Struct       `protobuf:"bytes,7,opt,name=metadata,proto3" json:"metadata,omitempty"`                                     // Additional metadata about the reference
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ObjectReference) Reset() {
	*x = ObjectReference{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObjectReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectReference) ProtoMessage() {}

func (x *ObjectReference) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectReference.ProtoReflect.Descriptor instead.
func (*ObjectReference) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{0}
}

func (x *ObjectReference) GetObjectType() string {
	if x != nil {
		return x.ObjectType
	}
	return ""
}

func (x *ObjectReference) GetReportScopedId() string {
	if x != nil {
		return x.ReportScopedId
	}
	return ""
}

func (x *ObjectReference) GetGlobalId() string {
	if x != nil {
		return x.GlobalId
	}
	return ""
}

func (x *ObjectReference) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *ObjectReference) GetSectionId() string {
	if x != nil {
		return x.SectionId
	}
	return ""
}

func (x *ObjectReference) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *ObjectReference) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// -----------------------------------------------------------------------------
// Relation
// -----------------------------------------------------------------------------
// Establishes a semantic connection between any two objects within a report.
// Uses string-based relation types for maximum flexibility without requiring protobuf changes.
// Relationships are bidirectional - both objects have equal weight in the relationship.
//
// VALIDATION:
//   - object_a and object_b MUST be valid ObjectReference instances
//   - relation_type MUST be non-empty
//   - object_a and object_b SHOULD NOT reference the same object (no self-relations)
//
// Common relation_type values (extensible - contextualized for college campus incidents):
//   - "associated_with"    - General association between objects
//   - "connected_to"       - Direct connection between objects
//   - "related_to"         - General relation between objects
//   - "involved_in"        - Both objects involved in same incident
//   - "occurred_with"      - Objects occurred together in incident
//   - "located_at"         - Object/person located at facility/room/building
//   - "part_of"            - Object is part of another (e.g., room part of building)
//   - "witnessed_by"       - Incident/event witnessed by person
//   - "reported_by"        - Incident reported by person/student/staff
//   - "responded_to_by"    - Incident responded to by officer/staff/security
//   - "victim_of"          - Person was victim of incident/violation
//   - "perpetrator_of"     - Person was perpetrator of incident/violation
//   - "suspect_in"         - Person is suspect in incident
//   - "complainant_in"     - Person filed complaint in incident
//   - "violates_policy"    - Incident violates specific campus policy
//   - "assigned_to"        - Case/incident assigned to officer/staff
//   - "escalated_to"       - Incident escalated to higher authority
//   - "follows_up_on"      - Report follows up on previous incident
//   - "references"         - Object references another object
//   - "occurred_in"        - Incident occurred in specific location/timeframe
//   - "involves_substance" - Incident involves drugs/alcohol
//   - "involves_property"  - Incident involves campus property/equipment
//   - "custom_*"           - Any custom relation type
type Relation struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                          // Unique relation identifier - auto-generated
	ReportId         string                 `protobuf:"bytes,2,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`                              // The report this relation belongs to - REQUIRED
	ObjectA          *ObjectReference       `protobuf:"bytes,3,opt,name=object_a,json=objectA,proto3" json:"object_a,omitempty"`                                 // First object in the bidirectional relationship - REQUIRED
	ObjectB          *ObjectReference       `protobuf:"bytes,4,opt,name=object_b,json=objectB,proto3" json:"object_b,omitempty"`                                 // Second object in the bidirectional relationship - REQUIRED
	RelationType     string                 `protobuf:"bytes,5,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"`                  // Semantic meaning of the relationship (flexible string) - REQUIRED
	Description      string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                                        // Optional human-readable description of the relationship
	Metadata         *structpb.Struct       `protobuf:"bytes,7,opt,name=metadata,proto3" json:"metadata,omitempty"`                                              // Flexible JSON for additional relationship data
	CreatedAt        string                 `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                           // ISO8601 timestamp when relation was created - auto-generated
	UpdatedAt        string                 `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                           // ISO8601 timestamp for last relation update - auto-managed
	CreatedByAssetId string                 `protobuf:"bytes,10,opt,name=created_by_asset_id,json=createdByAssetId,proto3" json:"created_by_asset_id,omitempty"` // Asset ID who created this relation - REQUIRED
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Relation) Reset() {
	*x = Relation{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Relation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Relation) ProtoMessage() {}

func (x *Relation) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Relation.ProtoReflect.Descriptor instead.
func (*Relation) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{1}
}

func (x *Relation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Relation) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *Relation) GetObjectA() *ObjectReference {
	if x != nil {
		return x.ObjectA
	}
	return nil
}

func (x *Relation) GetObjectB() *ObjectReference {
	if x != nil {
		return x.ObjectB
	}
	return nil
}

func (x *Relation) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

func (x *Relation) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Relation) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Relation) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Relation) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *Relation) GetCreatedByAssetId() string {
	if x != nil {
		return x.CreatedByAssetId
	}
	return ""
}

// -----------------------------------------------------------------------------
// Comment
// -----------------------------------------------------------------------------
// Represents either a global report comment or one tied to a specific section.
type Comment struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                             // Unique comment identifier
	ReportId          string                 `protobuf:"bytes,2,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`                                 // The report this comment belongs to
	SectionId         string                 `protobuf:"bytes,3,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`                              // If set, ties comment to a particular section
	ReplyToCommentId  string                 `protobuf:"bytes,4,opt,name=reply_to_comment_id,json=replyToCommentId,proto3" json:"reply_to_comment_id,omitempty"`     // If set, this is a threaded reply
	AuthorAssetId     string                 `protobuf:"bytes,5,opt,name=author_asset_id,json=authorAssetId,proto3" json:"author_asset_id,omitempty"`                // Asset ID of the comment author
	Text              string                 `protobuf:"bytes,6,opt,name=text,proto3" json:"text,omitempty"`                                                         // Comment body text
	CreatedAt         string                 `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                              // ISO8601 timestamp when created
	Resolved          bool                   `protobuf:"varint,8,opt,name=resolved,proto3" json:"resolved,omitempty"`                                                // True if comment has been marked resolved
	ResolvedAt        string                 `protobuf:"bytes,9,opt,name=resolved_at,json=resolvedAt,proto3" json:"resolved_at,omitempty"`                           // ISO8601 timestamp when resolved
	ResolvedByAssetId string                 `protobuf:"bytes,10,opt,name=resolved_by_asset_id,json=resolvedByAssetId,proto3" json:"resolved_by_asset_id,omitempty"` // Asset ID of user who resolved it
	UpdatedAt         string                 `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                             // ISO8601 timestamp for last edit
	ResourceType      string                 `protobuf:"bytes,12,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`                    // Constant string "COMMENT"
	DisplayName       string                 `protobuf:"bytes,13,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`                       // Display name of the comment author
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Comment) Reset() {
	*x = Comment{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Comment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Comment) ProtoMessage() {}

func (x *Comment) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Comment.ProtoReflect.Descriptor instead.
func (*Comment) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{2}
}

func (x *Comment) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Comment) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *Comment) GetSectionId() string {
	if x != nil {
		return x.SectionId
	}
	return ""
}

func (x *Comment) GetReplyToCommentId() string {
	if x != nil {
		return x.ReplyToCommentId
	}
	return ""
}

func (x *Comment) GetAuthorAssetId() string {
	if x != nil {
		return x.AuthorAssetId
	}
	return ""
}

func (x *Comment) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Comment) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Comment) GetResolved() bool {
	if x != nil {
		return x.Resolved
	}
	return false
}

func (x *Comment) GetResolvedAt() string {
	if x != nil {
		return x.ResolvedAt
	}
	return ""
}

func (x *Comment) GetResolvedByAssetId() string {
	if x != nil {
		return x.ResolvedByAssetId
	}
	return ""
}

func (x *Comment) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *Comment) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Comment) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

// -----------------------------------------------------------------------------
// Narrative Content
// -----------------------------------------------------------------------------
// Free-text rich content section (HTML/Markdown).
type NarrativeContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                             // Unique identifier for this narrative content
	RichText      string                 `protobuf:"bytes,2,opt,name=rich_text,json=richText,proto3" json:"rich_text,omitempty"` // Raw rich-text blob
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NarrativeContent) Reset() {
	*x = NarrativeContent{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NarrativeContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NarrativeContent) ProtoMessage() {}

func (x *NarrativeContent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NarrativeContent.ProtoReflect.Descriptor instead.
func (*NarrativeContent) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{3}
}

func (x *NarrativeContent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *NarrativeContent) GetRichText() string {
	if x != nil {
		return x.RichText
	}
	return ""
}

// -----------------------------------------------------------------------------
// Offense
// -----------------------------------------------------------------------------
// Individual offense information within an offense content section.
type Offense struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                      // Unique identifier for this individual offense
	OffenseType   string                 `protobuf:"bytes,2,opt,name=offense_type,json=offenseType,proto3" json:"offense_type,omitempty"` // Type or classification of offense
	Data          *structpb.Struct       `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                                  // Flexible JSON structure for additional data
	Schema        *structpb.Struct       `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`                              // Schema for this specific offense
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Offense) Reset() {
	*x = Offense{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Offense) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Offense) ProtoMessage() {}

func (x *Offense) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Offense.ProtoReflect.Descriptor instead.
func (*Offense) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{4}
}

func (x *Offense) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Offense) GetOffenseType() string {
	if x != nil {
		return x.OffenseType
	}
	return ""
}

func (x *Offense) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Offense) GetSchema() *structpb.Struct {
	if x != nil {
		return x.Schema
	}
	return nil
}

// -----------------------------------------------------------------------------
// Offense Content
// -----------------------------------------------------------------------------
// Section containing multiple offenses.
type OffenseContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`             // Unique identifier for this offense content section
	Offenses      []*Offense             `protobuf:"bytes,2,rep,name=offenses,proto3" json:"offenses,omitempty"` // List of individual offenses
	Metadata      *structpb.Struct       `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"` // Section-level metadata
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OffenseContent) Reset() {
	*x = OffenseContent{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OffenseContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OffenseContent) ProtoMessage() {}

func (x *OffenseContent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OffenseContent.ProtoReflect.Descriptor instead.
func (*OffenseContent) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{5}
}

func (x *OffenseContent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OffenseContent) GetOffenses() []*Offense {
	if x != nil {
		return x.Offenses
	}
	return nil
}

func (x *OffenseContent) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// -----------------------------------------------------------------------------
// Entity List Content
// -----------------------------------------------------------------------------
// Section containing a title and references to existing entities.
type EntityListContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                   // Unique identifier for this entity list content
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`                             // Human-readable section header
	EntityRefs    []*v1.Reference        `protobuf:"bytes,3,rep,name=entity_refs,json=entityRefs,proto3" json:"entity_refs,omitempty"` // Links to other entities
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EntityListContent) Reset() {
	*x = EntityListContent{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityListContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityListContent) ProtoMessage() {}

func (x *EntityListContent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityListContent.ProtoReflect.Descriptor instead.
func (*EntityListContent) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{6}
}

func (x *EntityListContent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EntityListContent) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *EntityListContent) GetEntityRefs() []*v1.Reference {
	if x != nil {
		return x.EntityRefs
	}
	return nil
}

// -----------------------------------------------------------------------------
// Incident Details Content
// -----------------------------------------------------------------------------
// Editable snapshot of the situation that triggered the report.
type IncidentResponder struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                      // Report-scoped unique identifier for this responder
	AssetId       string                 `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`             // Asset ID of the responder
	DisplayName   string                 `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"` // Responder's name
	Role          string                 `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`                                  // Responder's role or title
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IncidentResponder) Reset() {
	*x = IncidentResponder{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IncidentResponder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncidentResponder) ProtoMessage() {}

func (x *IncidentResponder) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncidentResponder.ProtoReflect.Descriptor instead.
func (*IncidentResponder) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{7}
}

func (x *IncidentResponder) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IncidentResponder) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *IncidentResponder) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *IncidentResponder) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type ReportingPerson struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                         // Report-scoped unique identifier for this reporting person
	AssetId       string                 `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                // Asset ID of the reporting individual
	FirstName     string                 `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`          // First name
	MiddleName    string                 `protobuf:"bytes,4,opt,name=middle_name,json=middleName,proto3" json:"middle_name,omitempty"`       // Middle name (optional)
	LastName      string                 `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`             // Last name
	PhoneNumber   string                 `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`    // Contact number
	ReporterRole  string                 `protobuf:"bytes,7,opt,name=reporter_role,json=reporterRole,proto3" json:"reporter_role,omitempty"` // Role of the reporter (e.g., witness, victim)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportingPerson) Reset() {
	*x = ReportingPerson{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportingPerson) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportingPerson) ProtoMessage() {}

func (x *ReportingPerson) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportingPerson.ProtoReflect.Descriptor instead.
func (*ReportingPerson) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{8}
}

func (x *ReportingPerson) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReportingPerson) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *ReportingPerson) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ReportingPerson) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *ReportingPerson) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *ReportingPerson) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *ReportingPerson) GetReporterRole() string {
	if x != nil {
		return x.ReporterRole
	}
	return ""
}

type InvolvedAgency struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	Id                      string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                            // unique identier for the content
	AgencyName              string                 `protobuf:"bytes,2,opt,name=agency_name,json=agencyName,proto3" json:"agency_name,omitempty"`                                          // Name of the agency
	IncidentReferenceNumber string                 `protobuf:"bytes,3,opt,name=incident_reference_number,json=incidentReferenceNumber,proto3" json:"incident_reference_number,omitempty"` // Role of the agency
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *InvolvedAgency) Reset() {
	*x = InvolvedAgency{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InvolvedAgency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvolvedAgency) ProtoMessage() {}

func (x *InvolvedAgency) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvolvedAgency.ProtoReflect.Descriptor instead.
func (*InvolvedAgency) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{9}
}

func (x *InvolvedAgency) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InvolvedAgency) GetAgencyName() string {
	if x != nil {
		return x.AgencyName
	}
	return ""
}

func (x *InvolvedAgency) GetIncidentReferenceNumber() string {
	if x != nil {
		return x.IncidentReferenceNumber
	}
	return ""
}

type IncidentDetailsContent struct {
	state                         protoimpl.MessageState `protogen:"open.v1"`
	Id                            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                // Unique identifier for this incident details content
	InitialType                   v2.SituationType       `protobuf:"varint,2,opt,name=initial_type,json=initialType,proto3,enum=hero.situations.v2.SituationType" json:"initial_type,omitempty"`                    // Original situation type enum
	IncidentStartTime             string                 `protobuf:"bytes,3,opt,name=incident_start_time,json=incidentStartTime,proto3" json:"incident_start_time,omitempty"`                                       // ISO8601 start time
	IncidentEndTime               string                 `protobuf:"bytes,4,opt,name=incident_end_time,json=incidentEndTime,proto3" json:"incident_end_time,omitempty"`                                             // ISO8601 end time
	ReportedTime                  string                 `protobuf:"bytes,5,opt,name=reported_time,json=reportedTime,proto3" json:"reported_time,omitempty"`                                                        // ISO8601 reported time
	IncidentLocationCleryType     string                 `protobuf:"bytes,6,opt,name=incident_location_clery_type,json=incidentLocationCleryType,proto3" json:"incident_location_clery_type,omitempty"`             // CLERY location type (Optional)
	IncidentLocationStreetAddress string                 `protobuf:"bytes,7,opt,name=incident_location_street_address,json=incidentLocationStreetAddress,proto3" json:"incident_location_street_address,omitempty"` // Street address
	IncidentLocationUnitInfo      string                 `protobuf:"bytes,8,opt,name=incident_location_unit_info,json=incidentLocationUnitInfo,proto3" json:"incident_location_unit_info,omitempty"`                // Apt/Suite/Unit # (Optional)
	IncidentLocationType          string                 `protobuf:"bytes,9,opt,name=incident_location_type,json=incidentLocationType,proto3" json:"incident_location_type,omitempty"`                              // Location type (e.g., Dorm, Building, etc.)
	IncidentLocationCommonName    string                 `protobuf:"bytes,10,opt,name=incident_location_common_name,json=incidentLocationCommonName,proto3" json:"incident_location_common_name,omitempty"`         // Common name for the location (Optional)
	IncidentLocationCity          string                 `protobuf:"bytes,11,opt,name=incident_location_city,json=incidentLocationCity,proto3" json:"incident_location_city,omitempty"`                             // City
	IncidentLocationState         string                 `protobuf:"bytes,12,opt,name=incident_location_state,json=incidentLocationState,proto3" json:"incident_location_state,omitempty"`                          // State
	IncidentLocationZipCode       string                 `protobuf:"bytes,13,opt,name=incident_location_zip_code,json=incidentLocationZipCode,proto3" json:"incident_location_zip_code,omitempty"`                  // Zip code
	IncidentLocationCountry       string                 `protobuf:"bytes,14,opt,name=incident_location_country,json=incidentLocationCountry,proto3" json:"incident_location_country,omitempty"`                    // Country
	IncidentLocationLatitude      float64                `protobuf:"fixed64,15,opt,name=incident_location_latitude,json=incidentLocationLatitude,proto3" json:"incident_location_latitude,omitempty"`               // Latitude coordinate
	IncidentLocationLongitude     float64                `protobuf:"fixed64,16,opt,name=incident_location_longitude,json=incidentLocationLongitude,proto3" json:"incident_location_longitude,omitempty"`            // Longitude coordinate
	Responders                    []*IncidentResponder   `protobuf:"bytes,17,rep,name=responders,proto3" json:"responders,omitempty"`                                                                               // List of responders
	ReportingPerson               *ReportingPerson       `protobuf:"bytes,18,opt,name=reporting_person,json=reportingPerson,proto3" json:"reporting_person,omitempty"`                                              // Person who reported the incident
	FinalType                     v2.SituationType       `protobuf:"varint,19,opt,name=final_type,json=finalType,proto3,enum=hero.situations.v2.SituationType" json:"final_type,omitempty"`                         // Final situation type enum
	InvolvedAgencies              []*InvolvedAgency      `protobuf:"bytes,20,rep,name=involved_agencies,json=involvedAgencies,proto3" json:"involved_agencies,omitempty"`                                           // List of involved agencies
	Description                   string                 `protobuf:"bytes,21,opt,name=description,proto3" json:"description,omitempty"`                                                                             // Description of the incident
	unknownFields                 protoimpl.UnknownFields
	sizeCache                     protoimpl.SizeCache
}

func (x *IncidentDetailsContent) Reset() {
	*x = IncidentDetailsContent{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IncidentDetailsContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncidentDetailsContent) ProtoMessage() {}

func (x *IncidentDetailsContent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncidentDetailsContent.ProtoReflect.Descriptor instead.
func (*IncidentDetailsContent) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{10}
}

func (x *IncidentDetailsContent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IncidentDetailsContent) GetInitialType() v2.SituationType {
	if x != nil {
		return x.InitialType
	}
	return v2.SituationType(0)
}

func (x *IncidentDetailsContent) GetIncidentStartTime() string {
	if x != nil {
		return x.IncidentStartTime
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentEndTime() string {
	if x != nil {
		return x.IncidentEndTime
	}
	return ""
}

func (x *IncidentDetailsContent) GetReportedTime() string {
	if x != nil {
		return x.ReportedTime
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationCleryType() string {
	if x != nil {
		return x.IncidentLocationCleryType
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationStreetAddress() string {
	if x != nil {
		return x.IncidentLocationStreetAddress
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationUnitInfo() string {
	if x != nil {
		return x.IncidentLocationUnitInfo
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationType() string {
	if x != nil {
		return x.IncidentLocationType
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationCommonName() string {
	if x != nil {
		return x.IncidentLocationCommonName
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationCity() string {
	if x != nil {
		return x.IncidentLocationCity
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationState() string {
	if x != nil {
		return x.IncidentLocationState
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationZipCode() string {
	if x != nil {
		return x.IncidentLocationZipCode
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationCountry() string {
	if x != nil {
		return x.IncidentLocationCountry
	}
	return ""
}

func (x *IncidentDetailsContent) GetIncidentLocationLatitude() float64 {
	if x != nil {
		return x.IncidentLocationLatitude
	}
	return 0
}

func (x *IncidentDetailsContent) GetIncidentLocationLongitude() float64 {
	if x != nil {
		return x.IncidentLocationLongitude
	}
	return 0
}

func (x *IncidentDetailsContent) GetResponders() []*IncidentResponder {
	if x != nil {
		return x.Responders
	}
	return nil
}

func (x *IncidentDetailsContent) GetReportingPerson() *ReportingPerson {
	if x != nil {
		return x.ReportingPerson
	}
	return nil
}

func (x *IncidentDetailsContent) GetFinalType() v2.SituationType {
	if x != nil {
		return x.FinalType
	}
	return v2.SituationType(0)
}

func (x *IncidentDetailsContent) GetInvolvedAgencies() []*InvolvedAgency {
	if x != nil {
		return x.InvolvedAgencies
	}
	return nil
}

func (x *IncidentDetailsContent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type Arrest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                   // Unique identifier for this individual arrest
	ArrestType    string                 `protobuf:"bytes,2,opt,name=arrest_type,json=arrestType,proto3" json:"arrest_type,omitempty"` // Type or classification of arrest
	Data          *structpb.Struct       `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                               // Flexible JSON structure for additional data
	Schema        *structpb.Struct       `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`                           // Schema for this specific arrest
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Arrest) Reset() {
	*x = Arrest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Arrest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Arrest) ProtoMessage() {}

func (x *Arrest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Arrest.ProtoReflect.Descriptor instead.
func (*Arrest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{11}
}

func (x *Arrest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Arrest) GetArrestType() string {
	if x != nil {
		return x.ArrestType
	}
	return ""
}

func (x *Arrest) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Arrest) GetSchema() *structpb.Struct {
	if x != nil {
		return x.Schema
	}
	return nil
}

// -----------------------------------------------------------------------------
// Arrest Content
// -----------------------------------------------------------------------------
// Section containing multiple arrests.
type ArrestContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`             // Unique identifier for this arrest content section
	Arrests       []*Arrest              `protobuf:"bytes,2,rep,name=arrests,proto3" json:"arrests,omitempty"`   // List of individual arrests
	Metadata      *structpb.Struct       `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"` // Section-level metadata
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArrestContent) Reset() {
	*x = ArrestContent{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArrestContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrestContent) ProtoMessage() {}

func (x *ArrestContent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrestContent.ProtoReflect.Descriptor instead.
func (*ArrestContent) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{12}
}

func (x *ArrestContent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ArrestContent) GetArrests() []*Arrest {
	if x != nil {
		return x.Arrests
	}
	return nil
}

func (x *ArrestContent) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// -----------------------------------------------------------------------------
// File Reference
// -----------------------------------------------------------------------------
// Individual file reference for media sections, linking to filerepository service.
type FileReference struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                          // Unique identifier for this file reference within the section
	FileId        string                 `protobuf:"bytes,2,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`                    // FileMetadata.id from filerepository service - REQUIRED
	Caption       string                 `protobuf:"bytes,3,opt,name=caption,proto3" json:"caption,omitempty"`                                // Optional caption/description for the file
	DisplayName   string                 `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`     // Optional display name (fallback to original filename)
	DisplayOrder  int32                  `protobuf:"varint,5,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"` // Order for displaying files in UI (0-based)
	FileCategory  string                 `protobuf:"bytes,6,opt,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"`  // Category of the file (e.g., "incident_photo", "incident_video", "incident_audio", "incident_document", "incident_other")
	Metadata      *structpb.Struct       `protobuf:"bytes,7,opt,name=metadata,proto3" json:"metadata,omitempty"`                              // Additional metadata about the file reference
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileReference) Reset() {
	*x = FileReference{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileReference) ProtoMessage() {}

func (x *FileReference) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileReference.ProtoReflect.Descriptor instead.
func (*FileReference) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{13}
}

func (x *FileReference) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FileReference) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *FileReference) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *FileReference) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *FileReference) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *FileReference) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

func (x *FileReference) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// -----------------------------------------------------------------------------
// Media Content
// -----------------------------------------------------------------------------
// Section containing media attachments and file references.
type MediaContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                             // Unique identifier for this media content section
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`                       // Human-readable section header
	FileRefs      []*FileReference       `protobuf:"bytes,3,rep,name=file_refs,json=fileRefs,proto3" json:"file_refs,omitempty"` // References to files in filerepository
	Metadata      *structpb.Struct       `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`                 // Section-level metadata
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaContent) Reset() {
	*x = MediaContent{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaContent) ProtoMessage() {}

func (x *MediaContent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaContent.ProtoReflect.Descriptor instead.
func (*MediaContent) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{14}
}

func (x *MediaContent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MediaContent) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MediaContent) GetFileRefs() []*FileReference {
	if x != nil {
		return x.FileRefs
	}
	return nil
}

func (x *MediaContent) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// -----------------------------------------------------------------------------
// Property Reference
// -----------------------------------------------------------------------------
// Reference to a property from the property service.
type PropertyReference struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                         // Unique identifier for the referenced property
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`                                     // Type of the referenced property (e.g., "property")
	Version       int32                  `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`                              // Version of the referenced property
	DisplayName   string                 `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`    // Optional display name for the reference
	RelationType  string                 `protobuf:"bytes,5,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"` // Type of relation between the referencing and referenced objects
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PropertyReference) Reset() {
	*x = PropertyReference{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PropertyReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyReference) ProtoMessage() {}

func (x *PropertyReference) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyReference.ProtoReflect.Descriptor instead.
func (*PropertyReference) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{15}
}

func (x *PropertyReference) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PropertyReference) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PropertyReference) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *PropertyReference) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *PropertyReference) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

// -----------------------------------------------------------------------------
// Property List Content
// -----------------------------------------------------------------------------
// Section containing a title and references to existing properties from the property service.
type PropertyListContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                         // Unique identifier for this property list content
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`                                   // Human-readable section header
	PropertyRefs  []*PropertyReference   `protobuf:"bytes,3,rep,name=property_refs,json=propertyRefs,proto3" json:"property_refs,omitempty"` // Links to properties from the property service
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PropertyListContent) Reset() {
	*x = PropertyListContent{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PropertyListContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyListContent) ProtoMessage() {}

func (x *PropertyListContent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyListContent.ProtoReflect.Descriptor instead.
func (*PropertyListContent) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{16}
}

func (x *PropertyListContent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PropertyListContent) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *PropertyListContent) GetPropertyRefs() []*PropertyReference {
	if x != nil {
		return x.PropertyRefs
	}
	return nil
}

// -----------------------------------------------------------------------------
// Report Section
// -----------------------------------------------------------------------------
// A single section within a report; content is polymorphic via `oneof`.
type ReportSection struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                       // Unique section ID
	Type  SectionType            `protobuf:"varint,2,opt,name=type,proto3,enum=hero.reports.v2.SectionType" json:"type,omitempty"` // Determines which `oneof` field is set
	// Types that are valid to be assigned to Content:
	//
	//	*ReportSection_Narrative
	//	*ReportSection_EntityList
	//	*ReportSection_IncidentDetails
	//	*ReportSection_OffenseList
	//	*ReportSection_ArrestList
	//	*ReportSection_MediaList
	//	*ReportSection_PropertyList
	Content       isReportSection_Content `protobuf_oneof:"content"`
	Comments      []*Comment              `protobuf:"bytes,7,rep,name=comments,proto3" json:"comments,omitempty"`                    // Comments attached to this section
	CreatedAt     string                  `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // ISO8601 timestamp when section was created
	UpdatedAt     string                  `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // ISO8601 timestamp for last section update
	ReportId      string                  `protobuf:"bytes,10,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`   // Parent report ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportSection) Reset() {
	*x = ReportSection{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSection) ProtoMessage() {}

func (x *ReportSection) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSection.ProtoReflect.Descriptor instead.
func (*ReportSection) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{17}
}

func (x *ReportSection) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReportSection) GetType() SectionType {
	if x != nil {
		return x.Type
	}
	return SectionType_SECTION_TYPE_UNSPECIFIED
}

func (x *ReportSection) GetContent() isReportSection_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *ReportSection) GetNarrative() *NarrativeContent {
	if x != nil {
		if x, ok := x.Content.(*ReportSection_Narrative); ok {
			return x.Narrative
		}
	}
	return nil
}

func (x *ReportSection) GetEntityList() *EntityListContent {
	if x != nil {
		if x, ok := x.Content.(*ReportSection_EntityList); ok {
			return x.EntityList
		}
	}
	return nil
}

func (x *ReportSection) GetIncidentDetails() *IncidentDetailsContent {
	if x != nil {
		if x, ok := x.Content.(*ReportSection_IncidentDetails); ok {
			return x.IncidentDetails
		}
	}
	return nil
}

func (x *ReportSection) GetOffenseList() *OffenseContent {
	if x != nil {
		if x, ok := x.Content.(*ReportSection_OffenseList); ok {
			return x.OffenseList
		}
	}
	return nil
}

func (x *ReportSection) GetArrestList() *ArrestContent {
	if x != nil {
		if x, ok := x.Content.(*ReportSection_ArrestList); ok {
			return x.ArrestList
		}
	}
	return nil
}

func (x *ReportSection) GetMediaList() *MediaContent {
	if x != nil {
		if x, ok := x.Content.(*ReportSection_MediaList); ok {
			return x.MediaList
		}
	}
	return nil
}

func (x *ReportSection) GetPropertyList() *PropertyListContent {
	if x != nil {
		if x, ok := x.Content.(*ReportSection_PropertyList); ok {
			return x.PropertyList
		}
	}
	return nil
}

func (x *ReportSection) GetComments() []*Comment {
	if x != nil {
		return x.Comments
	}
	return nil
}

func (x *ReportSection) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *ReportSection) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *ReportSection) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

type isReportSection_Content interface {
	isReportSection_Content()
}

type ReportSection_Narrative struct {
	Narrative *NarrativeContent `protobuf:"bytes,3,opt,name=narrative,proto3,oneof"`
}

type ReportSection_EntityList struct {
	EntityList *EntityListContent `protobuf:"bytes,4,opt,name=entity_list,json=entityList,proto3,oneof"`
}

type ReportSection_IncidentDetails struct {
	IncidentDetails *IncidentDetailsContent `protobuf:"bytes,5,opt,name=incident_details,json=incidentDetails,proto3,oneof"`
}

type ReportSection_OffenseList struct {
	OffenseList *OffenseContent `protobuf:"bytes,6,opt,name=offense_list,json=offenseList,proto3,oneof"`
}

type ReportSection_ArrestList struct {
	ArrestList *ArrestContent `protobuf:"bytes,11,opt,name=arrest_list,json=arrestList,proto3,oneof"`
}

type ReportSection_MediaList struct {
	MediaList *MediaContent `protobuf:"bytes,12,opt,name=media_list,json=mediaList,proto3,oneof"`
}

type ReportSection_PropertyList struct {
	PropertyList *PropertyListContent `protobuf:"bytes,13,opt,name=property_list,json=propertyList,proto3,oneof"`
}

func (*ReportSection_Narrative) isReportSection_Content() {}

func (*ReportSection_EntityList) isReportSection_Content() {}

func (*ReportSection_IncidentDetails) isReportSection_Content() {}

func (*ReportSection_OffenseList) isReportSection_Content() {}

func (*ReportSection_ArrestList) isReportSection_Content() {}

func (*ReportSection_MediaList) isReportSection_Content() {}

func (*ReportSection_PropertyList) isReportSection_Content() {}

// -----------------------------------------------------------------------------
// Review Round
// -----------------------------------------------------------------------------
// Tracks each iteration in a multi-level review workflow.
type ReviewRound struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                       // Unique review round ID
	ReportId        string                 `protobuf:"bytes,2,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`                           // Parent report identifier
	ReviewerAssetId string                 `protobuf:"bytes,3,opt,name=reviewer_asset_id,json=reviewerAssetId,proto3" json:"reviewer_asset_id,omitempty"`    // Who is reviewing
	Level           int32                  `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`                                                // Hierarchy level (e.g., 1 = peer, 2 = manager)
	Status          ReviewStatus           `protobuf:"varint,5,opt,name=status,proto3,enum=hero.reports.v2.ReviewStatus" json:"status,omitempty"`            // Current review state
	SentToLevel     int32                  `protobuf:"varint,6,opt,name=sent_to_level,json=sentToLevel,proto3" json:"sent_to_level,omitempty"`               // Next level to route (0 returns to author)
	SentToAssetId   string                 `protobuf:"bytes,7,opt,name=sent_to_asset_id,json=sentToAssetId,proto3" json:"sent_to_asset_id,omitempty"`        // Specific asset for next routing
	RequestedAt     string                 `protobuf:"bytes,8,opt,name=requested_at,json=requestedAt,proto3" json:"requested_at,omitempty"`                  // ISO8601 timestamp when round was created
	ResolvedAt      string                 `protobuf:"bytes,9,opt,name=resolved_at,json=resolvedAt,proto3" json:"resolved_at,omitempty"`                     // ISO8601 timestamp when round was finalized
	RoundNote       string                 `protobuf:"bytes,10,opt,name=round_note,json=roundNote,proto3" json:"round_note,omitempty"`                       // Reviewer's comment for this round
	SnapshotVersion int32                  `protobuf:"varint,11,opt,name=snapshot_version,json=snapshotVersion,proto3" json:"snapshot_version,omitempty"`    // Report.version at start of round
	DueAt           string                 `protobuf:"bytes,12,opt,name=due_at,json=dueAt,proto3" json:"due_at,omitempty"`                                   // ISO8601 timestamp for review deadline
	CreateByAssetId string                 `protobuf:"bytes,13,opt,name=create_by_asset_id,json=createByAssetId,proto3" json:"create_by_asset_id,omitempty"` // Asset ID who initiated this round
	NoteForReviewer string                 `protobuf:"bytes,14,opt,name=note_for_reviewer,json=noteForReviewer,proto3" json:"note_for_reviewer,omitempty"`   // Optional instructions/note
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ReviewRound) Reset() {
	*x = ReviewRound{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewRound) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRound) ProtoMessage() {}

func (x *ReviewRound) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRound.ProtoReflect.Descriptor instead.
func (*ReviewRound) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{18}
}

func (x *ReviewRound) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReviewRound) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *ReviewRound) GetReviewerAssetId() string {
	if x != nil {
		return x.ReviewerAssetId
	}
	return ""
}

func (x *ReviewRound) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *ReviewRound) GetStatus() ReviewStatus {
	if x != nil {
		return x.Status
	}
	return ReviewStatus_REVIEW_STATUS_UNSPECIFIED
}

func (x *ReviewRound) GetSentToLevel() int32 {
	if x != nil {
		return x.SentToLevel
	}
	return 0
}

func (x *ReviewRound) GetSentToAssetId() string {
	if x != nil {
		return x.SentToAssetId
	}
	return ""
}

func (x *ReviewRound) GetRequestedAt() string {
	if x != nil {
		return x.RequestedAt
	}
	return ""
}

func (x *ReviewRound) GetResolvedAt() string {
	if x != nil {
		return x.ResolvedAt
	}
	return ""
}

func (x *ReviewRound) GetRoundNote() string {
	if x != nil {
		return x.RoundNote
	}
	return ""
}

func (x *ReviewRound) GetSnapshotVersion() int32 {
	if x != nil {
		return x.SnapshotVersion
	}
	return 0
}

func (x *ReviewRound) GetDueAt() string {
	if x != nil {
		return x.DueAt
	}
	return ""
}

func (x *ReviewRound) GetCreateByAssetId() string {
	if x != nil {
		return x.CreateByAssetId
	}
	return ""
}

func (x *ReviewRound) GetNoteForReviewer() string {
	if x != nil {
		return x.NoteForReviewer
	}
	return ""
}

// -----------------------------------------------------------------------------
// Report
// -----------------------------------------------------------------------------
// Aggregates sections, comments, review rounds, and metadata.
type Report struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                     // Primary key
	OrgId              int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`                                                 // Tenant/organization ID
	AuthorAssetId      string                 `protobuf:"bytes,3,opt,name=author_asset_id,json=authorAssetId,proto3" json:"author_asset_id,omitempty"`                        // Who authored it
	Title              string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`                                                               // User-defined title
	Sections           []*ReportSection       `protobuf:"bytes,5,rep,name=sections,proto3" json:"sections,omitempty"`                                                         // All sections in order
	Status             ReportStatus           `protobuf:"varint,6,opt,name=status,proto3,enum=hero.reports.v2.ReportStatus" json:"status,omitempty"`                          // Overall report state
	ReviewRounds       []*ReviewRound         `protobuf:"bytes,7,rep,name=review_rounds,json=reviewRounds,proto3" json:"review_rounds,omitempty"`                             // Historical review data
	Relations          []*Relation            `protobuf:"bytes,8,rep,name=relations,proto3" json:"relations,omitempty"`                                                       // Object relationships within the report
	Comments           []*Comment             `protobuf:"bytes,9,rep,name=comments,proto3" json:"comments,omitempty"`                                                         // Global, report-level comments
	AssignedAt         string                 `protobuf:"bytes,10,opt,name=assigned_at,json=assignedAt,proto3" json:"assigned_at,omitempty"`                                  // ISO8601 timestamp when first assigned
	UpdatedAt          string                 `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                     // ISO8601 timestamp for last update
	CompletedAt        string                 `protobuf:"bytes,12,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`                               // ISO8601 timestamp when terminal
	ResourceType       string                 `protobuf:"bytes,13,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`                            // Constant string "REPORT"
	AdditionalInfoJson *structpb.Struct       `protobuf:"bytes,14,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`        // Arbitrary JSON key/value pairs
	Version            int32                  `protobuf:"varint,15,opt,name=version,proto3" json:"version,omitempty"`                                                         // Monotonic version number
	SituationId        string                 `protobuf:"bytes,16,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`                               // Link back to a Situation entity
	CaseId             string                 `protobuf:"bytes,17,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                                              // Link back to an Incident Case
	WatcherAssetIds    []string               `protobuf:"bytes,18,rep,name=watcher_asset_ids,json=watcherAssetIds,proto3" json:"watcher_asset_ids,omitempty"`                 // Users subscribed for updates
	CreatedAt          string                 `protobuf:"bytes,19,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                     // ISO8601 timestamp when report was created
	CreatedByAssetId   string                 `protobuf:"bytes,20,opt,name=created_by_asset_id,json=createdByAssetId,proto3" json:"created_by_asset_id,omitempty"`            // Asset ID of the report author
	ReportType         ReportType             `protobuf:"varint,21,opt,name=report_type,json=reportType,proto3,enum=hero.reports.v2.ReportType" json:"report_type,omitempty"` // Type of the report
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *Report) Reset() {
	*x = Report{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Report) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report) ProtoMessage() {}

func (x *Report) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report.ProtoReflect.Descriptor instead.
func (*Report) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{19}
}

func (x *Report) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Report) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *Report) GetAuthorAssetId() string {
	if x != nil {
		return x.AuthorAssetId
	}
	return ""
}

func (x *Report) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Report) GetSections() []*ReportSection {
	if x != nil {
		return x.Sections
	}
	return nil
}

func (x *Report) GetStatus() ReportStatus {
	if x != nil {
		return x.Status
	}
	return ReportStatus_REPORT_STATUS_UNSPECIFIED
}

func (x *Report) GetReviewRounds() []*ReviewRound {
	if x != nil {
		return x.ReviewRounds
	}
	return nil
}

func (x *Report) GetRelations() []*Relation {
	if x != nil {
		return x.Relations
	}
	return nil
}

func (x *Report) GetComments() []*Comment {
	if x != nil {
		return x.Comments
	}
	return nil
}

func (x *Report) GetAssignedAt() string {
	if x != nil {
		return x.AssignedAt
	}
	return ""
}

func (x *Report) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *Report) GetCompletedAt() string {
	if x != nil {
		return x.CompletedAt
	}
	return ""
}

func (x *Report) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Report) GetAdditionalInfoJson() *structpb.Struct {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return nil
}

func (x *Report) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Report) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

func (x *Report) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *Report) GetWatcherAssetIds() []string {
	if x != nil {
		return x.WatcherAssetIds
	}
	return nil
}

func (x *Report) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Report) GetCreatedByAssetId() string {
	if x != nil {
		return x.CreatedByAssetId
	}
	return ""
}

func (x *Report) GetReportType() ReportType {
	if x != nil {
		return x.ReportType
	}
	return ReportType_REPORT_TYPE_UNSPECIFIED
}

// -----------------------------------------------------------------------------
// Report Snapshot
// -----------------------------------------------------------------------------
// Immutable capture of a report at a specific version.
type ReportSnapshot struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`    // Parent report ID
	Version       int32                  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`                     // Version number
	Report        *Report                `protobuf:"bytes,3,opt,name=report,proto3" json:"report,omitempty"`                        // Full report state
	CreatedAt     string                 `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // ISO8601 timestamp when snapshot was taken
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportSnapshot) Reset() {
	*x = ReportSnapshot{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSnapshot) ProtoMessage() {}

func (x *ReportSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSnapshot.ProtoReflect.Descriptor instead.
func (*ReportSnapshot) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{20}
}

func (x *ReportSnapshot) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *ReportSnapshot) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *ReportSnapshot) GetReport() *Report {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *ReportSnapshot) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

// -----------------------------------------------------------------------------
// ResolveCommentRequest
// -----------------------------------------------------------------------------
// Marks a comment as resolved (idempotent).
type ResolveCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommentId     string                 `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"` // Target comment
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResolveCommentRequest) Reset() {
	*x = ResolveCommentRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResolveCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveCommentRequest) ProtoMessage() {}

func (x *ResolveCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveCommentRequest.ProtoReflect.Descriptor instead.
func (*ResolveCommentRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{21}
}

func (x *ResolveCommentRequest) GetCommentId() string {
	if x != nil {
		return x.CommentId
	}
	return ""
}

// -----------------------------------------------------------------------------
// Report RPC Payloads
// -----------------------------------------------------------------------------
type CreateReportRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Report        *Report                `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateReportRequest) Reset() {
	*x = CreateReportRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateReportRequest) ProtoMessage() {}

func (x *CreateReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateReportRequest.ProtoReflect.Descriptor instead.
func (*CreateReportRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{22}
}

func (x *CreateReportRequest) GetReport() *Report {
	if x != nil {
		return x.Report
	}
	return nil
}

type CreateReportResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Report        *Report                `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateReportResponse) Reset() {
	*x = CreateReportResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateReportResponse) ProtoMessage() {}

func (x *CreateReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateReportResponse.ProtoReflect.Descriptor instead.
func (*CreateReportResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{23}
}

func (x *CreateReportResponse) GetReport() *Report {
	if x != nil {
		return x.Report
	}
	return nil
}

type GetReportRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReportRequest) Reset() {
	*x = GetReportRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReportRequest) ProtoMessage() {}

func (x *GetReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReportRequest.ProtoReflect.Descriptor instead.
func (*GetReportRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{24}
}

func (x *GetReportRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateReportRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Report        *Report                `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateReportRequest) Reset() {
	*x = UpdateReportRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReportRequest) ProtoMessage() {}

func (x *UpdateReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReportRequest.ProtoReflect.Descriptor instead.
func (*UpdateReportRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateReportRequest) GetReport() *Report {
	if x != nil {
		return x.Report
	}
	return nil
}

type ListReportsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageSize      int32                  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`               // Max items per page
	PageToken     string                 `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`             // Cursor for pagination
	Status        ReportStatus           `protobuf:"varint,3,opt,name=status,proto3,enum=hero.reports.v2.ReportStatus" json:"status,omitempty"` // Filter by status (optional)
	OrgId         int32                  `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`                        // Filter by organization (optional)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReportsRequest) Reset() {
	*x = ListReportsRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReportsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportsRequest) ProtoMessage() {}

func (x *ListReportsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportsRequest.ProtoReflect.Descriptor instead.
func (*ListReportsRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{26}
}

func (x *ListReportsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListReportsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListReportsRequest) GetStatus() ReportStatus {
	if x != nil {
		return x.Status
	}
	return ReportStatus_REPORT_STATUS_UNSPECIFIED
}

func (x *ListReportsRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

type ListReportsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Reports       []*Report              `protobuf:"bytes,1,rep,name=reports,proto3" json:"reports,omitempty"`                                    // Page of reports
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"` // Cursor for next page
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReportsResponse) Reset() {
	*x = ListReportsResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReportsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportsResponse) ProtoMessage() {}

func (x *ListReportsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportsResponse.ProtoReflect.Descriptor instead.
func (*ListReportsResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{27}
}

func (x *ListReportsResponse) GetReports() []*Report {
	if x != nil {
		return x.Reports
	}
	return nil
}

func (x *ListReportsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type DeleteReportRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteReportRequest) Reset() {
	*x = DeleteReportRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteReportRequest) ProtoMessage() {}

func (x *DeleteReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteReportRequest.ProtoReflect.Descriptor instead.
func (*DeleteReportRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{28}
}

func (x *DeleteReportRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateReportStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                            // ID of the report to update
	Status        ReportStatus           `protobuf:"varint,2,opt,name=status,proto3,enum=hero.reports.v2.ReportStatus" json:"status,omitempty"` // The new status to set
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateReportStatusRequest) Reset() {
	*x = UpdateReportStatusRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateReportStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReportStatusRequest) ProtoMessage() {}

func (x *UpdateReportStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReportStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateReportStatusRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateReportStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateReportStatusRequest) GetStatus() ReportStatus {
	if x != nil {
		return x.Status
	}
	return ReportStatus_REPORT_STATUS_UNSPECIFIED
}

type UpdateReportStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Report        *Report                `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"` // The updated report
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateReportStatusResponse) Reset() {
	*x = UpdateReportStatusResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateReportStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReportStatusResponse) ProtoMessage() {}

func (x *UpdateReportStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReportStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateReportStatusResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateReportStatusResponse) GetReport() *Report {
	if x != nil {
		return x.Report
	}
	return nil
}

// Comment operations
type AddCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Comment       *Comment               `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCommentRequest) Reset() {
	*x = AddCommentRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCommentRequest) ProtoMessage() {}

func (x *AddCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCommentRequest.ProtoReflect.Descriptor instead.
func (*AddCommentRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{31}
}

func (x *AddCommentRequest) GetComment() *Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

type GetCommentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	SectionId     string                 `protobuf:"bytes,2,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCommentsRequest) Reset() {
	*x = GetCommentsRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentsRequest) ProtoMessage() {}

func (x *GetCommentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentsRequest.ProtoReflect.Descriptor instead.
func (*GetCommentsRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{32}
}

func (x *GetCommentsRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *GetCommentsRequest) GetSectionId() string {
	if x != nil {
		return x.SectionId
	}
	return ""
}

func (x *GetCommentsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetCommentsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type GetCommentsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Comments      []*Comment             `protobuf:"bytes,1,rep,name=comments,proto3" json:"comments,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCommentsResponse) Reset() {
	*x = GetCommentsResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCommentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentsResponse) ProtoMessage() {}

func (x *GetCommentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentsResponse.ProtoReflect.Descriptor instead.
func (*GetCommentsResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{33}
}

func (x *GetCommentsResponse) GetComments() []*Comment {
	if x != nil {
		return x.Comments
	}
	return nil
}

func (x *GetCommentsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type UpdateCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Comment       *Comment               `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCommentRequest) Reset() {
	*x = UpdateCommentRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCommentRequest) ProtoMessage() {}

func (x *UpdateCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCommentRequest.ProtoReflect.Descriptor instead.
func (*UpdateCommentRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{34}
}

func (x *UpdateCommentRequest) GetComment() *Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

type DeleteCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommentId     string                 `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCommentRequest) Reset() {
	*x = DeleteCommentRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCommentRequest) ProtoMessage() {}

func (x *DeleteCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCommentRequest.ProtoReflect.Descriptor instead.
func (*DeleteCommentRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{35}
}

func (x *DeleteCommentRequest) GetCommentId() string {
	if x != nil {
		return x.CommentId
	}
	return ""
}

// -----------------------------------------------------------------------------
// Review Workflow Payloads
// -----------------------------------------------------------------------------
type SubmitForReviewRequest struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	ReportId                 string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	Note                     string                 `protobuf:"bytes,2,opt,name=note,proto3" json:"note,omitempty"`
	PreferredReviewerAssetId string                 `protobuf:"bytes,3,opt,name=preferred_reviewer_asset_id,json=preferredReviewerAssetId,proto3" json:"preferred_reviewer_asset_id,omitempty"` // Optional: specific reviewer to assign
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *SubmitForReviewRequest) Reset() {
	*x = SubmitForReviewRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitForReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitForReviewRequest) ProtoMessage() {}

func (x *SubmitForReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitForReviewRequest.ProtoReflect.Descriptor instead.
func (*SubmitForReviewRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{36}
}

func (x *SubmitForReviewRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *SubmitForReviewRequest) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *SubmitForReviewRequest) GetPreferredReviewerAssetId() string {
	if x != nil {
		return x.PreferredReviewerAssetId
	}
	return ""
}

type AddReviewRoundRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReviewRound   *ReviewRound           `protobuf:"bytes,1,opt,name=review_round,json=reviewRound,proto3" json:"review_round,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddReviewRoundRequest) Reset() {
	*x = AddReviewRoundRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddReviewRoundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddReviewRoundRequest) ProtoMessage() {}

func (x *AddReviewRoundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddReviewRoundRequest.ProtoReflect.Descriptor instead.
func (*AddReviewRoundRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{37}
}

func (x *AddReviewRoundRequest) GetReviewRound() *ReviewRound {
	if x != nil {
		return x.ReviewRound
	}
	return nil
}

type GetReviewRoundRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReviewRoundId string                 `protobuf:"bytes,1,opt,name=review_round_id,json=reviewRoundId,proto3" json:"review_round_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReviewRoundRequest) Reset() {
	*x = GetReviewRoundRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReviewRoundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewRoundRequest) ProtoMessage() {}

func (x *GetReviewRoundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewRoundRequest.ProtoReflect.Descriptor instead.
func (*GetReviewRoundRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{38}
}

func (x *GetReviewRoundRequest) GetReviewRoundId() string {
	if x != nil {
		return x.ReviewRoundId
	}
	return ""
}

type UpdateReviewRoundRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReviewRound   *ReviewRound           `protobuf:"bytes,1,opt,name=review_round,json=reviewRound,proto3" json:"review_round,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateReviewRoundRequest) Reset() {
	*x = UpdateReviewRoundRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateReviewRoundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReviewRoundRequest) ProtoMessage() {}

func (x *UpdateReviewRoundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReviewRoundRequest.ProtoReflect.Descriptor instead.
func (*UpdateReviewRoundRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{39}
}

func (x *UpdateReviewRoundRequest) GetReviewRound() *ReviewRound {
	if x != nil {
		return x.ReviewRound
	}
	return nil
}

type DeleteReviewRoundRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReviewRoundId string                 `protobuf:"bytes,1,opt,name=review_round_id,json=reviewRoundId,proto3" json:"review_round_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteReviewRoundRequest) Reset() {
	*x = DeleteReviewRoundRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteReviewRoundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteReviewRoundRequest) ProtoMessage() {}

func (x *DeleteReviewRoundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteReviewRoundRequest.ProtoReflect.Descriptor instead.
func (*DeleteReviewRoundRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{40}
}

func (x *DeleteReviewRoundRequest) GetReviewRoundId() string {
	if x != nil {
		return x.ReviewRoundId
	}
	return ""
}

type ApproveReviewRoundRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReviewRoundId string                 `protobuf:"bytes,1,opt,name=review_round_id,json=reviewRoundId,proto3" json:"review_round_id,omitempty"`
	Note          string                 `protobuf:"bytes,2,opt,name=note,proto3" json:"note,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApproveReviewRoundRequest) Reset() {
	*x = ApproveReviewRoundRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveReviewRoundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveReviewRoundRequest) ProtoMessage() {}

func (x *ApproveReviewRoundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveReviewRoundRequest.ProtoReflect.Descriptor instead.
func (*ApproveReviewRoundRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{41}
}

func (x *ApproveReviewRoundRequest) GetReviewRoundId() string {
	if x != nil {
		return x.ReviewRoundId
	}
	return ""
}

func (x *ApproveReviewRoundRequest) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

type RequestChangesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReviewRoundId string                 `protobuf:"bytes,1,opt,name=review_round_id,json=reviewRoundId,proto3" json:"review_round_id,omitempty"`
	Note          string                 `protobuf:"bytes,2,opt,name=note,proto3" json:"note,omitempty"`
	SendToLevel   int32                  `protobuf:"varint,3,opt,name=send_to_level,json=sendToLevel,proto3" json:"send_to_level,omitempty"`
	SendToAssetId string                 `protobuf:"bytes,4,opt,name=send_to_asset_id,json=sendToAssetId,proto3" json:"send_to_asset_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestChangesRequest) Reset() {
	*x = RequestChangesRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestChangesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestChangesRequest) ProtoMessage() {}

func (x *RequestChangesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestChangesRequest.ProtoReflect.Descriptor instead.
func (*RequestChangesRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{42}
}

func (x *RequestChangesRequest) GetReviewRoundId() string {
	if x != nil {
		return x.ReviewRoundId
	}
	return ""
}

func (x *RequestChangesRequest) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *RequestChangesRequest) GetSendToLevel() int32 {
	if x != nil {
		return x.SendToLevel
	}
	return 0
}

func (x *RequestChangesRequest) GetSendToAssetId() string {
	if x != nil {
		return x.SendToAssetId
	}
	return ""
}

type ListReviewRoundsForReportRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`    // Target report ID
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`   // Max items per page
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"` // Cursor for pagination
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReviewRoundsForReportRequest) Reset() {
	*x = ListReviewRoundsForReportRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReviewRoundsForReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReviewRoundsForReportRequest) ProtoMessage() {}

func (x *ListReviewRoundsForReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReviewRoundsForReportRequest.ProtoReflect.Descriptor instead.
func (*ListReviewRoundsForReportRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{43}
}

func (x *ListReviewRoundsForReportRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *ListReviewRoundsForReportRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListReviewRoundsForReportRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListReviewRoundsForReportResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReviewRounds  []*ReviewRound         `protobuf:"bytes,1,rep,name=review_rounds,json=reviewRounds,proto3" json:"review_rounds,omitempty"`      // Page of review rounds
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"` // Cursor for next page
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReviewRoundsForReportResponse) Reset() {
	*x = ListReviewRoundsForReportResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReviewRoundsForReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReviewRoundsForReportResponse) ProtoMessage() {}

func (x *ListReviewRoundsForReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReviewRoundsForReportResponse.ProtoReflect.Descriptor instead.
func (*ListReviewRoundsForReportResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{44}
}

func (x *ListReviewRoundsForReportResponse) GetReviewRounds() []*ReviewRound {
	if x != nil {
		return x.ReviewRounds
	}
	return nil
}

func (x *ListReviewRoundsForReportResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// -----------------------------------------------------------------------------
// Eligible Reviewers Payloads
// -----------------------------------------------------------------------------
// Individual eligible reviewer information
type EligibleReviewer struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	AssetId           string                 `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                                  // Asset ID of the reviewer
	Name              string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                       // Display name of the reviewer
	AssetType         string                 `protobuf:"bytes,3,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`                            // Type of asset (e.g., SUPERVISOR)
	ActiveReviewCount int32                  `protobuf:"varint,4,opt,name=active_review_count,json=activeReviewCount,proto3" json:"active_review_count,omitempty"` // Number of active reviews assigned
	Email             string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`                                                     // Email address (optional)
	Role              string                 `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`                                                       // Role/title (optional)
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *EligibleReviewer) Reset() {
	*x = EligibleReviewer{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EligibleReviewer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EligibleReviewer) ProtoMessage() {}

func (x *EligibleReviewer) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EligibleReviewer.ProtoReflect.Descriptor instead.
func (*EligibleReviewer) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{45}
}

func (x *EligibleReviewer) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *EligibleReviewer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EligibleReviewer) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

func (x *EligibleReviewer) GetActiveReviewCount() int32 {
	if x != nil {
		return x.ActiveReviewCount
	}
	return 0
}

func (x *EligibleReviewer) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *EligibleReviewer) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

// Request to get eligible reviewers for a report
type GetEligibleReviewersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"` // Target report ID
	Level         int32                  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`                      // Optional: specific level (defaults to next level)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEligibleReviewersRequest) Reset() {
	*x = GetEligibleReviewersRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEligibleReviewersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEligibleReviewersRequest) ProtoMessage() {}

func (x *GetEligibleReviewersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEligibleReviewersRequest.ProtoReflect.Descriptor instead.
func (*GetEligibleReviewersRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{46}
}

func (x *GetEligibleReviewersRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *GetEligibleReviewersRequest) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// Response with list of eligible reviewers
type GetEligibleReviewersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Reviewers     []*EligibleReviewer    `protobuf:"bytes,1,rep,name=reviewers,proto3" json:"reviewers,omitempty"`                   // List of eligible reviewers
	NextLevel     int32                  `protobuf:"varint,2,opt,name=next_level,json=nextLevel,proto3" json:"next_level,omitempty"` // The level these reviewers are for
	LevelName     string                 `protobuf:"bytes,3,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`  // Human-readable level name
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEligibleReviewersResponse) Reset() {
	*x = GetEligibleReviewersResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEligibleReviewersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEligibleReviewersResponse) ProtoMessage() {}

func (x *GetEligibleReviewersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEligibleReviewersResponse.ProtoReflect.Descriptor instead.
func (*GetEligibleReviewersResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{47}
}

func (x *GetEligibleReviewersResponse) GetReviewers() []*EligibleReviewer {
	if x != nil {
		return x.Reviewers
	}
	return nil
}

func (x *GetEligibleReviewersResponse) GetNextLevel() int32 {
	if x != nil {
		return x.NextLevel
	}
	return 0
}

func (x *GetEligibleReviewersResponse) GetLevelName() string {
	if x != nil {
		return x.LevelName
	}
	return ""
}

// -----------------------------------------------------------------------------
// Relation Payloads
// -----------------------------------------------------------------------------
type CreateRelationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"` // Target report ID
	Relation      *Relation              `protobuf:"bytes,2,opt,name=relation,proto3" json:"relation,omitempty"`                 // Relation object to create
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRelationRequest) Reset() {
	*x = CreateRelationRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRelationRequest) ProtoMessage() {}

func (x *CreateRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRelationRequest.ProtoReflect.Descriptor instead.
func (*CreateRelationRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{48}
}

func (x *CreateRelationRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *CreateRelationRequest) GetRelation() *Relation {
	if x != nil {
		return x.Relation
	}
	return nil
}

type GetRelationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`       // Target report ID
	RelationId    string                 `protobuf:"bytes,2,opt,name=relation_id,json=relationId,proto3" json:"relation_id,omitempty"` // Target relation ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRelationRequest) Reset() {
	*x = GetRelationRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelationRequest) ProtoMessage() {}

func (x *GetRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelationRequest.ProtoReflect.Descriptor instead.
func (*GetRelationRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{49}
}

func (x *GetRelationRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *GetRelationRequest) GetRelationId() string {
	if x != nil {
		return x.RelationId
	}
	return ""
}

type UpdateRelationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"` // Target report ID
	Relation      *Relation              `protobuf:"bytes,2,opt,name=relation,proto3" json:"relation,omitempty"`                 // Relation object with updates
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRelationRequest) Reset() {
	*x = UpdateRelationRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRelationRequest) ProtoMessage() {}

func (x *UpdateRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRelationRequest.ProtoReflect.Descriptor instead.
func (*UpdateRelationRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{50}
}

func (x *UpdateRelationRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *UpdateRelationRequest) GetRelation() *Relation {
	if x != nil {
		return x.Relation
	}
	return nil
}

type DeleteRelationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`       // Target report ID
	RelationId    string                 `protobuf:"bytes,2,opt,name=relation_id,json=relationId,proto3" json:"relation_id,omitempty"` // Target relation ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRelationRequest) Reset() {
	*x = DeleteRelationRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRelationRequest) ProtoMessage() {}

func (x *DeleteRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRelationRequest.ProtoReflect.Descriptor instead.
func (*DeleteRelationRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{51}
}

func (x *DeleteRelationRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *DeleteRelationRequest) GetRelationId() string {
	if x != nil {
		return x.RelationId
	}
	return ""
}

type ListRelationsRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	ReportId  string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`    // Target report ID
	PageSize  int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`   // Max items per page
	PageToken string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"` // Cursor for pagination
	// Optional filters
	RelationType       string `protobuf:"bytes,4,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"`                     // Filter by relation type
	InvolvesObjectType string `protobuf:"bytes,5,opt,name=involves_object_type,json=involvesObjectType,proto3" json:"involves_object_type,omitempty"` // Filter by any object (object_a OR object_b) having this type
	InvolvesObjectId   string `protobuf:"bytes,6,opt,name=involves_object_id,json=involvesObjectId,proto3" json:"involves_object_id,omitempty"`       // Filter by any object (object_a OR object_b) having this ID
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListRelationsRequest) Reset() {
	*x = ListRelationsRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRelationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRelationsRequest) ProtoMessage() {}

func (x *ListRelationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRelationsRequest.ProtoReflect.Descriptor instead.
func (*ListRelationsRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{52}
}

func (x *ListRelationsRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *ListRelationsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListRelationsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListRelationsRequest) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

func (x *ListRelationsRequest) GetInvolvesObjectType() string {
	if x != nil {
		return x.InvolvesObjectType
	}
	return ""
}

func (x *ListRelationsRequest) GetInvolvesObjectId() string {
	if x != nil {
		return x.InvolvesObjectId
	}
	return ""
}

type ListRelationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Relations     []*Relation            `protobuf:"bytes,1,rep,name=relations,proto3" json:"relations,omitempty"`                                // Page of relations
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"` // Cursor for next page
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRelationsResponse) Reset() {
	*x = ListRelationsResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRelationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRelationsResponse) ProtoMessage() {}

func (x *ListRelationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRelationsResponse.ProtoReflect.Descriptor instead.
func (*ListRelationsResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{53}
}

func (x *ListRelationsResponse) GetRelations() []*Relation {
	if x != nil {
		return x.Relations
	}
	return nil
}

func (x *ListRelationsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// -----------------------------------------------------------------------------
// JSON Metadata Payloads
// -----------------------------------------------------------------------------
type UpdateAdditionalInfoJsonRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ReportId           string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	AdditionalInfoJson string                 `protobuf:"bytes,2,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UpdateAdditionalInfoJsonRequest) Reset() {
	*x = UpdateAdditionalInfoJsonRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAdditionalInfoJsonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAdditionalInfoJsonRequest) ProtoMessage() {}

func (x *UpdateAdditionalInfoJsonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAdditionalInfoJsonRequest.ProtoReflect.Descriptor instead.
func (*UpdateAdditionalInfoJsonRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{54}
}

func (x *UpdateAdditionalInfoJsonRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *UpdateAdditionalInfoJsonRequest) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

type UpdateAdditionalInfoJsonResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ReportId           string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	AdditionalInfoJson string                 `protobuf:"bytes,2,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UpdateAdditionalInfoJsonResponse) Reset() {
	*x = UpdateAdditionalInfoJsonResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAdditionalInfoJsonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAdditionalInfoJsonResponse) ProtoMessage() {}

func (x *UpdateAdditionalInfoJsonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAdditionalInfoJsonResponse.ProtoReflect.Descriptor instead.
func (*UpdateAdditionalInfoJsonResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{55}
}

func (x *UpdateAdditionalInfoJsonResponse) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *UpdateAdditionalInfoJsonResponse) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

type GetAdditionalInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAdditionalInfoRequest) Reset() {
	*x = GetAdditionalInfoRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAdditionalInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdditionalInfoRequest) ProtoMessage() {}

func (x *GetAdditionalInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdditionalInfoRequest.ProtoReflect.Descriptor instead.
func (*GetAdditionalInfoRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{56}
}

func (x *GetAdditionalInfoRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

type GetAdditionalInfoResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ReportId           string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	AdditionalInfoJson string                 `protobuf:"bytes,2,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetAdditionalInfoResponse) Reset() {
	*x = GetAdditionalInfoResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAdditionalInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdditionalInfoResponse) ProtoMessage() {}

func (x *GetAdditionalInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdditionalInfoResponse.ProtoReflect.Descriptor instead.
func (*GetAdditionalInfoResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{57}
}

func (x *GetAdditionalInfoResponse) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *GetAdditionalInfoResponse) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

// -----------------------------------------------------------------------------
// Versioning Payloads
// -----------------------------------------------------------------------------
type GetReportVersionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	Version       int32                  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReportVersionRequest) Reset() {
	*x = GetReportVersionRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReportVersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReportVersionRequest) ProtoMessage() {}

func (x *GetReportVersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReportVersionRequest.ProtoReflect.Descriptor instead.
func (*GetReportVersionRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{58}
}

func (x *GetReportVersionRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *GetReportVersionRequest) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type ListReportVersionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReportVersionsRequest) Reset() {
	*x = ListReportVersionsRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReportVersionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportVersionsRequest) ProtoMessage() {}

func (x *ListReportVersionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportVersionsRequest.ProtoReflect.Descriptor instead.
func (*ListReportVersionsRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{59}
}

func (x *ListReportVersionsRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

type ListReportVersionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Versions      []int32                `protobuf:"varint,1,rep,packed,name=versions,proto3" json:"versions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReportVersionsResponse) Reset() {
	*x = ListReportVersionsResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReportVersionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportVersionsResponse) ProtoMessage() {}

func (x *ListReportVersionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportVersionsResponse.ProtoReflect.Descriptor instead.
func (*ListReportVersionsResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{60}
}

func (x *ListReportVersionsResponse) GetVersions() []int32 {
	if x != nil {
		return x.Versions
	}
	return nil
}

type BatchGetReportsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportIds     []string               `protobuf:"bytes,1,rep,name=report_ids,json=reportIds,proto3" json:"report_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetReportsRequest) Reset() {
	*x = BatchGetReportsRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetReportsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetReportsRequest) ProtoMessage() {}

func (x *BatchGetReportsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetReportsRequest.ProtoReflect.Descriptor instead.
func (*BatchGetReportsRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{61}
}

func (x *BatchGetReportsRequest) GetReportIds() []string {
	if x != nil {
		return x.ReportIds
	}
	return nil
}

type BatchGetReportsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Reports       []*Report              `protobuf:"bytes,1,rep,name=reports,proto3" json:"reports,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetReportsResponse) Reset() {
	*x = BatchGetReportsResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetReportsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetReportsResponse) ProtoMessage() {}

func (x *BatchGetReportsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetReportsResponse.ProtoReflect.Descriptor instead.
func (*BatchGetReportsResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{62}
}

func (x *BatchGetReportsResponse) GetReports() []*Report {
	if x != nil {
		return x.Reports
	}
	return nil
}

// -----------------------------------------------------------------------------
// List by Situation / Case
// -----------------------------------------------------------------------------
type ListReportsBySituationIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SituationId   string                 `protobuf:"bytes,1,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"` // Target Situation ID
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`         // Max items per page
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`       // Cursor for pagination
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReportsBySituationIdRequest) Reset() {
	*x = ListReportsBySituationIdRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReportsBySituationIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportsBySituationIdRequest) ProtoMessage() {}

func (x *ListReportsBySituationIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportsBySituationIdRequest.ProtoReflect.Descriptor instead.
func (*ListReportsBySituationIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{63}
}

func (x *ListReportsBySituationIdRequest) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

func (x *ListReportsBySituationIdRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListReportsBySituationIdRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListReportsByCaseIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`          // Target Case ID
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`   // Max items per page
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"` // Cursor for pagination
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReportsByCaseIdRequest) Reset() {
	*x = ListReportsByCaseIdRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReportsByCaseIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportsByCaseIdRequest) ProtoMessage() {}

func (x *ListReportsByCaseIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportsByCaseIdRequest.ProtoReflect.Descriptor instead.
func (*ListReportsByCaseIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{64}
}

func (x *ListReportsByCaseIdRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ListReportsByCaseIdRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListReportsByCaseIdRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// -----------------------------------------------------------------------------
// Section RPC Payloads
// -----------------------------------------------------------------------------
type CreateReportSectionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	Section       *ReportSection         `protobuf:"bytes,2,opt,name=section,proto3" json:"section,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateReportSectionRequest) Reset() {
	*x = CreateReportSectionRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateReportSectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateReportSectionRequest) ProtoMessage() {}

func (x *CreateReportSectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateReportSectionRequest.ProtoReflect.Descriptor instead.
func (*CreateReportSectionRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{65}
}

func (x *CreateReportSectionRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *CreateReportSectionRequest) GetSection() *ReportSection {
	if x != nil {
		return x.Section
	}
	return nil
}

type GetReportSectionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	SectionId     string                 `protobuf:"bytes,2,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReportSectionRequest) Reset() {
	*x = GetReportSectionRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReportSectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReportSectionRequest) ProtoMessage() {}

func (x *GetReportSectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReportSectionRequest.ProtoReflect.Descriptor instead.
func (*GetReportSectionRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{66}
}

func (x *GetReportSectionRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *GetReportSectionRequest) GetSectionId() string {
	if x != nil {
		return x.SectionId
	}
	return ""
}

type UpdateReportSectionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	Section       *ReportSection         `protobuf:"bytes,2,opt,name=section,proto3" json:"section,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateReportSectionRequest) Reset() {
	*x = UpdateReportSectionRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateReportSectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReportSectionRequest) ProtoMessage() {}

func (x *UpdateReportSectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReportSectionRequest.ProtoReflect.Descriptor instead.
func (*UpdateReportSectionRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{67}
}

func (x *UpdateReportSectionRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *UpdateReportSectionRequest) GetSection() *ReportSection {
	if x != nil {
		return x.Section
	}
	return nil
}

type DeleteReportSectionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	SectionId     string                 `protobuf:"bytes,2,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteReportSectionRequest) Reset() {
	*x = DeleteReportSectionRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteReportSectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteReportSectionRequest) ProtoMessage() {}

func (x *DeleteReportSectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteReportSectionRequest.ProtoReflect.Descriptor instead.
func (*DeleteReportSectionRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{68}
}

func (x *DeleteReportSectionRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *DeleteReportSectionRequest) GetSectionId() string {
	if x != nil {
		return x.SectionId
	}
	return ""
}

type ListReportSectionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReportSectionsRequest) Reset() {
	*x = ListReportSectionsRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReportSectionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportSectionsRequest) ProtoMessage() {}

func (x *ListReportSectionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportSectionsRequest.ProtoReflect.Descriptor instead.
func (*ListReportSectionsRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{69}
}

func (x *ListReportSectionsRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

type ListReportSectionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sections      []*ReportSection       `protobuf:"bytes,1,rep,name=sections,proto3" json:"sections,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReportSectionsResponse) Reset() {
	*x = ListReportSectionsResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReportSectionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportSectionsResponse) ProtoMessage() {}

func (x *ListReportSectionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportSectionsResponse.ProtoReflect.Descriptor instead.
func (*ListReportSectionsResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{70}
}

func (x *ListReportSectionsResponse) GetSections() []*ReportSection {
	if x != nil {
		return x.Sections
	}
	return nil
}

// / A half-open timestamp range (inclusive).
type DateRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	From          string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"` // RFC3339 timestamp, inclusive
	To            string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`     // RFC3339 timestamp, inclusive
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DateRange) Reset() {
	*x = DateRange{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DateRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateRange) ProtoMessage() {}

func (x *DateRange) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateRange.ProtoReflect.Descriptor instead.
func (*DateRange) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{71}
}

func (x *DateRange) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *DateRange) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

// / Field-specific query (limits a search term to one field)
type FieldQuery struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"` // e.g. "title", "entity_list_title", "reporting_person_first_name"
	Query         string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"` // the term to match in that field
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FieldQuery) Reset() {
	*x = FieldQuery{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FieldQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldQuery) ProtoMessage() {}

func (x *FieldQuery) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldQuery.ProtoReflect.Descriptor instead.
func (*FieldQuery) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{72}
}

func (x *FieldQuery) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *FieldQuery) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

// / Highlighted fragments for a given field in each report
type HighlightResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`         // the field name where matches occurred
	Fragments     []string               `protobuf:"bytes,2,rep,name=fragments,proto3" json:"fragments,omitempty"` // snippets with matches, e.g. ["…urgent…", "…critical…"]
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HighlightResult) Reset() {
	*x = HighlightResult{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HighlightResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HighlightResult) ProtoMessage() {}

func (x *HighlightResult) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HighlightResult.ProtoReflect.Descriptor instead.
func (*HighlightResult) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{73}
}

func (x *HighlightResult) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *HighlightResult) GetFragments() []string {
	if x != nil {
		return x.Fragments
	}
	return nil
}

// / Request for searching reports, flat and non-nested.
// /
// / All partial/fuzzy matching (ILIKE, substring, full-text) must be done via the `query`, `search_fields`, or `field_queries` parameters.
// /
// / PERFORMANCE NOTE: For optimal performance, combine exact filters (status, date ranges) with full-text search.
// / Avoid using only full-text search on large datasets without additional filters.
// /
// / Supported field names for `search_fields` and `field_queries.field`:
// /   - "title"                  (report title)
// /   - "id"                     (report id)
// /   - "narrative"              (narrative section rich text)
// /   - "entity_list_title"      (entity list section title)
// /   - "incident_location"      (incident details location)
// /   - "reporting_person_name"  (reporting person first/middle/last name)
// /   - "reporting_person_phone_number" (reporting person phone number)
// /   - "reporting_person_role"        (reporting person role)
// /   - "reference_display_name" (entity reference display name)
// /   - "responder_display_name" (incident responder display name)
// /   - "responder_role"         (incident responder role)
// /   - "offense_type"           (offense type/classification - searches across all offenses in offense sections)
// /   - "relation_description"   (relation description text)
// /   - "relation_object_name"   (relation object display names - searches both object_a and object_b)
// /   - "agency_name"            (involved agency name)
// /   - "agency_reference"       (involved agency incident reference number)
// /
// / For exact-value and range filters, use the dedicated fields below. These include DateRange, enums, and array fields for exact/range matching.
type SearchReportsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ────── Free-text & scoped field queries ──────
	Query        string        `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`                                   // full-text / fuzzy across all indexed fields
	SearchFields []string      `protobuf:"bytes,2,rep,name=search_fields,json=searchFields,proto3" json:"search_fields,omitempty"` // limit `query` to these fields; empty = all
	FieldQueries []*FieldQuery `protobuf:"bytes,3,rep,name=field_queries,json=fieldQueries,proto3" json:"field_queries,omitempty"` // individual term→field queries
	// ────── Top-level report filters ──────
	Status            []ReportStatus `protobuf:"varint,4,rep,packed,name=status,proto3,enum=hero.reports.v2.ReportStatus" json:"status,omitempty"`
	SituationIds      []string       `protobuf:"bytes,5,rep,name=situation_ids,json=situationIds,proto3" json:"situation_ids,omitempty"`
	CaseIds           []string       `protobuf:"bytes,6,rep,name=case_ids,json=caseIds,proto3" json:"case_ids,omitempty"`
	CreatedAt         *DateRange     `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                                // reports.created_at BETWEEN …
	UpdatedAt         *DateRange     `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                                // reports.updated_at BETWEEN …
	AssignedAt        *DateRange     `protobuf:"bytes,9,opt,name=assigned_at,json=assignedAt,proto3" json:"assigned_at,omitempty"`                                             // reports.assigned_at BETWEEN …
	CompletedAt       *DateRange     `protobuf:"bytes,10,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`                                         // reports.completed_at BETWEEN …
	CreatedByAssetIds []string       `protobuf:"bytes,11,rep,name=created_by_asset_ids,json=createdByAssetIds,proto3" json:"created_by_asset_ids,omitempty"`                   // reports.created_by_asset_id IN (…)
	ReportTypes       []ReportType   `protobuf:"varint,12,rep,packed,name=report_types,json=reportTypes,proto3,enum=hero.reports.v2.ReportType" json:"report_types,omitempty"` // reports.report_type IN (…)
	// ────── Entity-list sections (exact/range) ──────
	EntityListRefIds []string `protobuf:"bytes,13,rep,name=entity_list_ref_ids,json=entityListRefIds,proto3" json:"entity_list_ref_ids,omitempty"` // generated ref_ids @> ARRAY[…]
	ReferenceType    string   `protobuf:"bytes,14,opt,name=reference_type,json=referenceType,proto3" json:"reference_type,omitempty"`              // generated entity_refs[].type = …
	// ────── Incident-details sections (exact/range) ──────
	IncidentStartTime *DateRange         `protobuf:"bytes,15,opt,name=incident_start_time,json=incidentStartTime,proto3" json:"incident_start_time,omitempty"`                              // generated incident_start_t BETWEEN …
	IncidentEndTime   *DateRange         `protobuf:"bytes,16,opt,name=incident_end_time,json=incidentEndTime,proto3" json:"incident_end_time,omitempty"`                                    // generated incident_end_t BETWEEN …
	InitialTypes      []v2.SituationType `protobuf:"varint,17,rep,packed,name=initial_types,json=initialTypes,proto3,enum=hero.situations.v2.SituationType" json:"initial_types,omitempty"` // generated initial_type IN (…)
	FinalTypes        []v2.SituationType `protobuf:"varint,18,rep,packed,name=final_types,json=finalTypes,proto3,enum=hero.situations.v2.SituationType" json:"final_types,omitempty"`       // generated final_type   IN (…)
	ResponderAssetIds []string           `protobuf:"bytes,19,rep,name=responder_asset_ids,json=responderAssetIds,proto3" json:"responder_asset_ids,omitempty"`                              // generated responders[].asset_id IN (…)
	ResponderRoles    []string           `protobuf:"bytes,20,rep,name=responder_roles,json=responderRoles,proto3" json:"responder_roles,omitempty"`                                         // generated responders[].role IN (…)
	// ────── Offense sections (exact/range) ──────
	OffenseTypes []string `protobuf:"bytes,21,rep,name=offense_types,json=offenseTypes,proto3" json:"offense_types,omitempty"` // offenses[].offense_type IN (…)
	OffenseIds   []string `protobuf:"bytes,22,rep,name=offense_ids,json=offenseIds,proto3" json:"offense_ids,omitempty"`       // offenses[].id IN (…)
	// ────── Relation filters (exact/range) ──────
	RelationTypes                   []string `protobuf:"bytes,23,rep,name=relation_types,json=relationTypes,proto3" json:"relation_types,omitempty"`                                                             // relations[].relation_type IN (…)
	RelationCreatedByAssetIds       []string `protobuf:"bytes,24,rep,name=relation_created_by_asset_ids,json=relationCreatedByAssetIds,proto3" json:"relation_created_by_asset_ids,omitempty"`                   // relations[].created_by_asset_id IN (…)
	RelationInvolvesObjectTypes     []string `protobuf:"bytes,25,rep,name=relation_involves_object_types,json=relationInvolvesObjectTypes,proto3" json:"relation_involves_object_types,omitempty"`               // relations where either object_a OR object_b has type IN (…)
	RelationInvolvesReportScopedIds []string `protobuf:"bytes,26,rep,name=relation_involves_report_scoped_ids,json=relationInvolvesReportScopedIds,proto3" json:"relation_involves_report_scoped_ids,omitempty"` // relations where either object_a OR object_b has report_scoped_id IN (…)
	RelationInvolvesGlobalIds       []string `protobuf:"bytes,27,rep,name=relation_involves_global_ids,json=relationInvolvesGlobalIds,proto3" json:"relation_involves_global_ids,omitempty"`                     // relations where either object_a OR object_b has global_id IN (…)
	RelationInvolvesExternalIds     []string `protobuf:"bytes,28,rep,name=relation_involves_external_ids,json=relationInvolvesExternalIds,proto3" json:"relation_involves_external_ids,omitempty"`               // relations where either object_a OR object_b has external_id IN (…)
	// ────── Pagination & sorting ──────
	PageSize      int32         `protobuf:"varint,29,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string        `protobuf:"bytes,30,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`                               // cursor
	OrderBy       SearchOrderBy `protobuf:"varint,31,opt,name=order_by,json=orderBy,proto3,enum=hero.reports.v2.SearchOrderBy" json:"order_by,omitempty"` // default = RELEVANCE
	Ascending     bool          `protobuf:"varint,32,opt,name=ascending,proto3" json:"ascending,omitempty"`                                               // default = false (DESC)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchReportsRequest) Reset() {
	*x = SearchReportsRequest{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchReportsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchReportsRequest) ProtoMessage() {}

func (x *SearchReportsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchReportsRequest.ProtoReflect.Descriptor instead.
func (*SearchReportsRequest) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{74}
}

func (x *SearchReportsRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchReportsRequest) GetSearchFields() []string {
	if x != nil {
		return x.SearchFields
	}
	return nil
}

func (x *SearchReportsRequest) GetFieldQueries() []*FieldQuery {
	if x != nil {
		return x.FieldQueries
	}
	return nil
}

func (x *SearchReportsRequest) GetStatus() []ReportStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SearchReportsRequest) GetSituationIds() []string {
	if x != nil {
		return x.SituationIds
	}
	return nil
}

func (x *SearchReportsRequest) GetCaseIds() []string {
	if x != nil {
		return x.CaseIds
	}
	return nil
}

func (x *SearchReportsRequest) GetCreatedAt() *DateRange {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SearchReportsRequest) GetUpdatedAt() *DateRange {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SearchReportsRequest) GetAssignedAt() *DateRange {
	if x != nil {
		return x.AssignedAt
	}
	return nil
}

func (x *SearchReportsRequest) GetCompletedAt() *DateRange {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *SearchReportsRequest) GetCreatedByAssetIds() []string {
	if x != nil {
		return x.CreatedByAssetIds
	}
	return nil
}

func (x *SearchReportsRequest) GetReportTypes() []ReportType {
	if x != nil {
		return x.ReportTypes
	}
	return nil
}

func (x *SearchReportsRequest) GetEntityListRefIds() []string {
	if x != nil {
		return x.EntityListRefIds
	}
	return nil
}

func (x *SearchReportsRequest) GetReferenceType() string {
	if x != nil {
		return x.ReferenceType
	}
	return ""
}

func (x *SearchReportsRequest) GetIncidentStartTime() *DateRange {
	if x != nil {
		return x.IncidentStartTime
	}
	return nil
}

func (x *SearchReportsRequest) GetIncidentEndTime() *DateRange {
	if x != nil {
		return x.IncidentEndTime
	}
	return nil
}

func (x *SearchReportsRequest) GetInitialTypes() []v2.SituationType {
	if x != nil {
		return x.InitialTypes
	}
	return nil
}

func (x *SearchReportsRequest) GetFinalTypes() []v2.SituationType {
	if x != nil {
		return x.FinalTypes
	}
	return nil
}

func (x *SearchReportsRequest) GetResponderAssetIds() []string {
	if x != nil {
		return x.ResponderAssetIds
	}
	return nil
}

func (x *SearchReportsRequest) GetResponderRoles() []string {
	if x != nil {
		return x.ResponderRoles
	}
	return nil
}

func (x *SearchReportsRequest) GetOffenseTypes() []string {
	if x != nil {
		return x.OffenseTypes
	}
	return nil
}

func (x *SearchReportsRequest) GetOffenseIds() []string {
	if x != nil {
		return x.OffenseIds
	}
	return nil
}

func (x *SearchReportsRequest) GetRelationTypes() []string {
	if x != nil {
		return x.RelationTypes
	}
	return nil
}

func (x *SearchReportsRequest) GetRelationCreatedByAssetIds() []string {
	if x != nil {
		return x.RelationCreatedByAssetIds
	}
	return nil
}

func (x *SearchReportsRequest) GetRelationInvolvesObjectTypes() []string {
	if x != nil {
		return x.RelationInvolvesObjectTypes
	}
	return nil
}

func (x *SearchReportsRequest) GetRelationInvolvesReportScopedIds() []string {
	if x != nil {
		return x.RelationInvolvesReportScopedIds
	}
	return nil
}

func (x *SearchReportsRequest) GetRelationInvolvesGlobalIds() []string {
	if x != nil {
		return x.RelationInvolvesGlobalIds
	}
	return nil
}

func (x *SearchReportsRequest) GetRelationInvolvesExternalIds() []string {
	if x != nil {
		return x.RelationInvolvesExternalIds
	}
	return nil
}

func (x *SearchReportsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchReportsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *SearchReportsRequest) GetOrderBy() SearchOrderBy {
	if x != nil {
		return x.OrderBy
	}
	return SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED
}

func (x *SearchReportsRequest) GetAscending() bool {
	if x != nil {
		return x.Ascending
	}
	return false
}

type SearchReportsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The page of reports that matched the query (already ordered & trimmed).
	Reports []*Report `protobuf:"bytes,1,rep,name=reports,proto3" json:"reports,omitempty"`
	// Cursor for fetching the next page; empty when you're on the last page.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// Per-report highlight information keyed by report ID.
	// Each HighlightResult lists the field name and one-or-more matched fragments
	// (e.g.  "…suspect vehicle…", "…dark-blue sedan…").
	Highlights map[string]*HighlightResult `protobuf:"bytes,3,rep,name=highlights,proto3" json:"highlights,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Total number of hits *before* pagination—useful for UI counters.
	TotalResults  int32 `protobuf:"varint,4,opt,name=total_results,json=totalResults,proto3" json:"total_results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchReportsResponse) Reset() {
	*x = SearchReportsResponse{}
	mi := &file_hero_reports_v2_reports_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchReportsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchReportsResponse) ProtoMessage() {}

func (x *SearchReportsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_reports_v2_reports_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchReportsResponse.ProtoReflect.Descriptor instead.
func (*SearchReportsResponse) Descriptor() ([]byte, []int) {
	return file_hero_reports_v2_reports_proto_rawDescGZIP(), []int{75}
}

func (x *SearchReportsResponse) GetReports() []*Report {
	if x != nil {
		return x.Reports
	}
	return nil
}

func (x *SearchReportsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *SearchReportsResponse) GetHighlights() map[string]*HighlightResult {
	if x != nil {
		return x.Highlights
	}
	return nil
}

func (x *SearchReportsResponse) GetTotalResults() int32 {
	if x != nil {
		return x.TotalResults
	}
	return 0
}

var File_hero_reports_v2_reports_proto protoreflect.FileDescriptor

var file_hero_reports_v2_reports_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x68,
	0x65, 0x72, 0x6f, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x68, 0x65, 0x72, 0x6f,
	0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x73, 0x69,
	0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x73, 0x69, 0x74, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x68, 0x65,
	0x72, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x02, 0x0a, 0x0f,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x9a, 0x03, 0x0a, 0x08, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x08, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x07, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x12, 0x3b, 0x0a, 0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x07, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x42, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2d, 0x0a,
	0x13, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0xb4, 0x03, 0x0a,
	0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x54, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2f, 0x0a, 0x14, 0x72,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x6c,
	0x76, 0x65, 0x64, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x3f, 0x0a, 0x10, 0x4e, 0x61, 0x72, 0x72, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x69, 0x63, 0x68, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x69, 0x63, 0x68,
	0x54, 0x65, 0x78, 0x74, 0x22, 0x9a, 0x01, 0x0a, 0x07, 0x4f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x2f, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x22, 0x8b, 0x01, 0x0a, 0x0e, 0x4f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65,
	0x52, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x75, 0x0a, 0x11, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x66, 0x73, 0x22, 0x75, 0x0a, 0x11, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0xe1, 0x01,
	0x0a, 0x0f, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d,
	0x69, 0x64, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x6f, 0x6c,
	0x65, 0x22, 0x7d, 0x0a, 0x0e, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x41, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x19, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0xd9, 0x09, 0x0a, 0x16, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x44, 0x0a, 0x0c, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e,
	0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x1c, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x65, 0x72, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x20, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x69,
	0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x72, 0x65, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3d, 0x0a, 0x1b,
	0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x18, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x16, 0x69,
	0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x63,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x41, 0x0a, 0x1d, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x69, 0x74, 0x79, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6e,
	0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6e, 0x63,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x7a, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x3a, 0x0a, 0x19, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x17, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x3c, 0x0a, 0x1a, 0x69,
	0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x18, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x3e, 0x0a, 0x1b, 0x69, 0x6e, 0x63,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52, 0x19,
	0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x64, 0x65, 0x72, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x72, 0x73, 0x12, 0x4b, 0x0a,
	0x10, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0a, 0x66, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a, 0x11,
	0x69, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x69, 0x65,
	0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76,
	0x65, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x10, 0x69, 0x6e, 0x76, 0x6f, 0x6c, 0x76,
	0x65, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x97, 0x01, 0x0a,
	0x06, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x72, 0x72, 0x65, 0x73,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72,
	0x72, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x22, 0x87, 0x01, 0x0a, 0x0d, 0x41, 0x72, 0x72, 0x65, 0x73,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x07, 0x61, 0x72, 0x72, 0x65,
	0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x72, 0x72, 0x65,
	0x73, 0x74, 0x52, 0x07, 0x61, 0x72, 0x72, 0x65, 0x73, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xf4, 0x01, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x61, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a,
	0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa6, 0x01, 0x0a, 0x0c, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3b,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x73, 0x12, 0x33, 0x0a, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x99, 0x01, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x84, 0x01, 0x0a,
	0x13, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x47, 0x0a, 0x0d, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52,
	0x65, 0x66, 0x73, 0x22, 0xe3, 0x05, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x6e, 0x61, 0x72, 0x72, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x72,
	0x72, 0x61, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52,
	0x09, 0x6e, 0x61, 0x72, 0x72, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x54, 0x0a, 0x10, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e,
	0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x44, 0x0a, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x6e,
	0x73, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x4f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00,
	0x52, 0x0b, 0x6f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x41, 0x0a,
	0x0b, 0x61, 0x72, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x72, 0x72, 0x65, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x3e, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x4b, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52,
	0x0c, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x42, 0x09,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xfe, 0x03, 0x0a, 0x0b, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x65, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x22, 0x0a, 0x0d, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x27, 0x0a, 0x10, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73,
	0x65, 0x6e, 0x74, 0x54, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x4e, 0x6f, 0x74, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x75,
	0x65, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x75, 0x65, 0x41,
	0x74, 0x12, 0x2b, 0x0a, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6e, 0x6f, 0x74, 0x65, 0x46,
	0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x22, 0xf3, 0x06, 0x0a, 0x06, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a,
	0x0d, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x52, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x73,
	0x12, 0x37, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x34, 0x0a, 0x08, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x12,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x77, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x12, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0f, 0x77, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x2d, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x97, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x06, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x36, 0x0a, 0x15, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0x46, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x06, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x47, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x06, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x22, 0x22, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x46, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f,
	0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22,
	0x9e, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64,
	0x22, 0x70, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x22, 0x25, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x62, 0x0a, 0x19, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4d, 0x0a,
	0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x47, 0x0a, 0x11,
	0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x32, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x73, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x4a, 0x0a, 0x14, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x32, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x35, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x88, 0x01, 0x0a,
	0x16, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x1b, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3f, 0x0a, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e,
	0x64, 0x22, 0x3f, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f,
	0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64,
	0x49, 0x64, 0x22, 0x5b, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f,
	0x0a, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x22,
	0x42, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x19, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0xa0, 0x01, 0x0a,
	0x15, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f,
	0x74, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x54,
	0x6f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x27, 0x0a, 0x10, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x6f, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22,
	0x7b, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x8e, 0x01, 0x0a,
	0x21, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64,
	0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xba, 0x01,
	0x0a, 0x10, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x50, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x9d, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a,
	0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x65, 0x72, 0x52, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x6e, 0x65, 0x78, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x6b, 0x0a, 0x15,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x49, 0x64, 0x12, 0x35, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x52, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x6b, 0x0a,
	0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x55, 0x0a, 0x15, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0xf4, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x76, 0x6f,
	0x6c, 0x76, 0x65, 0x73, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x73,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6e,
	0x76, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x73,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x78, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x37, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x22, 0x70, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x71, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x37, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64,
	0x22, 0x6a, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x50, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x38,
	0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0x37, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x73, 0x22, 0x4c, 0x0a, 0x17, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x1f, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x42, 0x79, 0x53, 0x69, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x71, 0x0a, 0x1a,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x42, 0x79, 0x43, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22,
	0x73, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x07, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x55, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x73, 0x0a, 0x1a, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x58, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x19, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x2f,
	0x0a, 0x09, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12,
	0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x22,
	0x38, 0x0a, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x45, 0x0a, 0x0f, 0x48, 0x69, 0x67,
	0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0x99, 0x0d, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x71, 0x75,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x0c, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x51,
	0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x2f, 0x0a, 0x14, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x3e, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x0c, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x2d, 0x0a, 0x13, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x13, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x11, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x46, 0x0a, 0x11, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0f, 0x69, 0x6e, 0x63, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x0d, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73,
	0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x69, 0x74,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x61,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x64, 0x65, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x13, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x64, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x65, 0x6e, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x6e,
	0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x1d,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x18, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x19, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x43,
	0x0a, 0x1e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x6c,
	0x76, 0x65, 0x73, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x19, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1b, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x23, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x1f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76,
	0x65, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x49, 0x64,
	0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x76, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x5f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x19, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x49,
	0x64, 0x73, 0x12, 0x43, 0x0a, 0x1e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1b, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x73, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x39, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18,
	0x1f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0xd0, 0x02, 0x0a,
	0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x56, 0x0a, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x48, 0x69,
	0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x68,
	0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x5f,
	0x0a, 0x0f, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a,
	0xc7, 0x02, 0x0a, 0x0c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1a, 0x0a, 0x16, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f,
	0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x42, 0x4d,
	0x49, 0x54, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x50, 0x4f, 0x52,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x57, 0x4f,
	0x52, 0x4b, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x07,
	0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x08, 0x12, 0x1b, 0x0a, 0x17,
	0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41,
	0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x09, 0x2a, 0xa7, 0x01, 0x0a, 0x0a, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52,
	0x49, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x50, 0x4f, 0x52,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f,
	0x53, 0x55, 0x50, 0x50, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x33,
	0x0a, 0x2f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e,
	0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x49, 0x47, 0x41, 0x54, 0x49, 0x56,
	0x45, 0x10, 0x03, 0x2a, 0xf0, 0x02, 0x0a, 0x0b, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4e, 0x41, 0x52, 0x52, 0x41, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x21, 0x0a,
	0x1d, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e,
	0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x03,
	0x12, 0x23, 0x0a, 0x1f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x50, 0x45, 0x4f,
	0x50, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53,
	0x54, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x10, 0x05, 0x12, 0x27, 0x0a, 0x23, 0x53,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49,
	0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x49,
	0x45, 0x53, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x4e, 0x53, 0x45, 0x10, 0x07, 0x12, 0x2a,
	0x0a, 0x26, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45,
	0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x4f, 0x52, 0x47, 0x41, 0x4e,
	0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x08, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x52, 0x52, 0x45, 0x53,
	0x54, 0x10, 0x09, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x10, 0x0a, 0x12, 0x19, 0x0a, 0x15, 0x53,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x50,
	0x45, 0x52, 0x54, 0x59, 0x10, 0x0b, 0x2a, 0x91, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x56,
	0x49, 0x45, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x44, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x53, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x45, 0x44, 0x10, 0x03, 0x2a, 0xab, 0x01, 0x0a, 0x0d, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x1b,
	0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a,
	0x19, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59,
	0x5f, 0x52, 0x45, 0x4c, 0x45, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a,
	0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a,
	0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16,
	0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x32, 0x9f, 0x1c, 0x0a, 0x0d, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x62, 0x0a, 0x13, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5c,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x13,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x5a, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6d, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x2a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x0c, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x24, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x4d, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x6d, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x58, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x23,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0f, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x27, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4c, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x72, 0x0a,
	0x18, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x42, 0x79, 0x53, 0x69,
	0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x30, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x42, 0x79, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x68, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x42, 0x79, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x42, 0x79, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x41,
	0x64, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x58, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x50, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x52, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x53, 0x0a, 0x0f, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x56, 0x0a, 0x0e,
	0x41, 0x64, 0x64, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x26,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x12, 0x56, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x5c, 0x0a, 0x11,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e,
	0x64, 0x12, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x56, 0x0a, 0x11, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12,
	0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f,
	0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x5e, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x2a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x12, 0x56, 0x0a, 0x0e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x82, 0x01, 0x0a, 0x19, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x46,
	0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x31, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x46, 0x6f,
	0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x73, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73, 0x12, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x6c, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e,
	0x12, 0x30, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x31, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x29, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x12, 0x6d, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5e, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x53, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5e, 0x0a, 0x0d, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x1f, 0x5a, 0x1d, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_hero_reports_v2_reports_proto_rawDescOnce sync.Once
	file_hero_reports_v2_reports_proto_rawDescData = file_hero_reports_v2_reports_proto_rawDesc
)

func file_hero_reports_v2_reports_proto_rawDescGZIP() []byte {
	file_hero_reports_v2_reports_proto_rawDescOnce.Do(func() {
		file_hero_reports_v2_reports_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_reports_v2_reports_proto_rawDescData)
	})
	return file_hero_reports_v2_reports_proto_rawDescData
}

var file_hero_reports_v2_reports_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_hero_reports_v2_reports_proto_msgTypes = make([]protoimpl.MessageInfo, 77)
var file_hero_reports_v2_reports_proto_goTypes = []any{
	(ReportStatus)(0),                         // 0: hero.reports.v2.ReportStatus
	(ReportType)(0),                           // 1: hero.reports.v2.ReportType
	(SectionType)(0),                          // 2: hero.reports.v2.SectionType
	(ReviewStatus)(0),                         // 3: hero.reports.v2.ReviewStatus
	(SearchOrderBy)(0),                        // 4: hero.reports.v2.SearchOrderBy
	(*ObjectReference)(nil),                   // 5: hero.reports.v2.ObjectReference
	(*Relation)(nil),                          // 6: hero.reports.v2.Relation
	(*Comment)(nil),                           // 7: hero.reports.v2.Comment
	(*NarrativeContent)(nil),                  // 8: hero.reports.v2.NarrativeContent
	(*Offense)(nil),                           // 9: hero.reports.v2.Offense
	(*OffenseContent)(nil),                    // 10: hero.reports.v2.OffenseContent
	(*EntityListContent)(nil),                 // 11: hero.reports.v2.EntityListContent
	(*IncidentResponder)(nil),                 // 12: hero.reports.v2.IncidentResponder
	(*ReportingPerson)(nil),                   // 13: hero.reports.v2.ReportingPerson
	(*InvolvedAgency)(nil),                    // 14: hero.reports.v2.InvolvedAgency
	(*IncidentDetailsContent)(nil),            // 15: hero.reports.v2.IncidentDetailsContent
	(*Arrest)(nil),                            // 16: hero.reports.v2.Arrest
	(*ArrestContent)(nil),                     // 17: hero.reports.v2.ArrestContent
	(*FileReference)(nil),                     // 18: hero.reports.v2.FileReference
	(*MediaContent)(nil),                      // 19: hero.reports.v2.MediaContent
	(*PropertyReference)(nil),                 // 20: hero.reports.v2.PropertyReference
	(*PropertyListContent)(nil),               // 21: hero.reports.v2.PropertyListContent
	(*ReportSection)(nil),                     // 22: hero.reports.v2.ReportSection
	(*ReviewRound)(nil),                       // 23: hero.reports.v2.ReviewRound
	(*Report)(nil),                            // 24: hero.reports.v2.Report
	(*ReportSnapshot)(nil),                    // 25: hero.reports.v2.ReportSnapshot
	(*ResolveCommentRequest)(nil),             // 26: hero.reports.v2.ResolveCommentRequest
	(*CreateReportRequest)(nil),               // 27: hero.reports.v2.CreateReportRequest
	(*CreateReportResponse)(nil),              // 28: hero.reports.v2.CreateReportResponse
	(*GetReportRequest)(nil),                  // 29: hero.reports.v2.GetReportRequest
	(*UpdateReportRequest)(nil),               // 30: hero.reports.v2.UpdateReportRequest
	(*ListReportsRequest)(nil),                // 31: hero.reports.v2.ListReportsRequest
	(*ListReportsResponse)(nil),               // 32: hero.reports.v2.ListReportsResponse
	(*DeleteReportRequest)(nil),               // 33: hero.reports.v2.DeleteReportRequest
	(*UpdateReportStatusRequest)(nil),         // 34: hero.reports.v2.UpdateReportStatusRequest
	(*UpdateReportStatusResponse)(nil),        // 35: hero.reports.v2.UpdateReportStatusResponse
	(*AddCommentRequest)(nil),                 // 36: hero.reports.v2.AddCommentRequest
	(*GetCommentsRequest)(nil),                // 37: hero.reports.v2.GetCommentsRequest
	(*GetCommentsResponse)(nil),               // 38: hero.reports.v2.GetCommentsResponse
	(*UpdateCommentRequest)(nil),              // 39: hero.reports.v2.UpdateCommentRequest
	(*DeleteCommentRequest)(nil),              // 40: hero.reports.v2.DeleteCommentRequest
	(*SubmitForReviewRequest)(nil),            // 41: hero.reports.v2.SubmitForReviewRequest
	(*AddReviewRoundRequest)(nil),             // 42: hero.reports.v2.AddReviewRoundRequest
	(*GetReviewRoundRequest)(nil),             // 43: hero.reports.v2.GetReviewRoundRequest
	(*UpdateReviewRoundRequest)(nil),          // 44: hero.reports.v2.UpdateReviewRoundRequest
	(*DeleteReviewRoundRequest)(nil),          // 45: hero.reports.v2.DeleteReviewRoundRequest
	(*ApproveReviewRoundRequest)(nil),         // 46: hero.reports.v2.ApproveReviewRoundRequest
	(*RequestChangesRequest)(nil),             // 47: hero.reports.v2.RequestChangesRequest
	(*ListReviewRoundsForReportRequest)(nil),  // 48: hero.reports.v2.ListReviewRoundsForReportRequest
	(*ListReviewRoundsForReportResponse)(nil), // 49: hero.reports.v2.ListReviewRoundsForReportResponse
	(*EligibleReviewer)(nil),                  // 50: hero.reports.v2.EligibleReviewer
	(*GetEligibleReviewersRequest)(nil),       // 51: hero.reports.v2.GetEligibleReviewersRequest
	(*GetEligibleReviewersResponse)(nil),      // 52: hero.reports.v2.GetEligibleReviewersResponse
	(*CreateRelationRequest)(nil),             // 53: hero.reports.v2.CreateRelationRequest
	(*GetRelationRequest)(nil),                // 54: hero.reports.v2.GetRelationRequest
	(*UpdateRelationRequest)(nil),             // 55: hero.reports.v2.UpdateRelationRequest
	(*DeleteRelationRequest)(nil),             // 56: hero.reports.v2.DeleteRelationRequest
	(*ListRelationsRequest)(nil),              // 57: hero.reports.v2.ListRelationsRequest
	(*ListRelationsResponse)(nil),             // 58: hero.reports.v2.ListRelationsResponse
	(*UpdateAdditionalInfoJsonRequest)(nil),   // 59: hero.reports.v2.UpdateAdditionalInfoJsonRequest
	(*UpdateAdditionalInfoJsonResponse)(nil),  // 60: hero.reports.v2.UpdateAdditionalInfoJsonResponse
	(*GetAdditionalInfoRequest)(nil),          // 61: hero.reports.v2.GetAdditionalInfoRequest
	(*GetAdditionalInfoResponse)(nil),         // 62: hero.reports.v2.GetAdditionalInfoResponse
	(*GetReportVersionRequest)(nil),           // 63: hero.reports.v2.GetReportVersionRequest
	(*ListReportVersionsRequest)(nil),         // 64: hero.reports.v2.ListReportVersionsRequest
	(*ListReportVersionsResponse)(nil),        // 65: hero.reports.v2.ListReportVersionsResponse
	(*BatchGetReportsRequest)(nil),            // 66: hero.reports.v2.BatchGetReportsRequest
	(*BatchGetReportsResponse)(nil),           // 67: hero.reports.v2.BatchGetReportsResponse
	(*ListReportsBySituationIdRequest)(nil),   // 68: hero.reports.v2.ListReportsBySituationIdRequest
	(*ListReportsByCaseIdRequest)(nil),        // 69: hero.reports.v2.ListReportsByCaseIdRequest
	(*CreateReportSectionRequest)(nil),        // 70: hero.reports.v2.CreateReportSectionRequest
	(*GetReportSectionRequest)(nil),           // 71: hero.reports.v2.GetReportSectionRequest
	(*UpdateReportSectionRequest)(nil),        // 72: hero.reports.v2.UpdateReportSectionRequest
	(*DeleteReportSectionRequest)(nil),        // 73: hero.reports.v2.DeleteReportSectionRequest
	(*ListReportSectionsRequest)(nil),         // 74: hero.reports.v2.ListReportSectionsRequest
	(*ListReportSectionsResponse)(nil),        // 75: hero.reports.v2.ListReportSectionsResponse
	(*DateRange)(nil),                         // 76: hero.reports.v2.DateRange
	(*FieldQuery)(nil),                        // 77: hero.reports.v2.FieldQuery
	(*HighlightResult)(nil),                   // 78: hero.reports.v2.HighlightResult
	(*SearchReportsRequest)(nil),              // 79: hero.reports.v2.SearchReportsRequest
	(*SearchReportsResponse)(nil),             // 80: hero.reports.v2.SearchReportsResponse
	nil,                                       // 81: hero.reports.v2.SearchReportsResponse.HighlightsEntry
	(*structpb.Struct)(nil),                   // 82: google.protobuf.Struct
	(*v1.Reference)(nil),                      // 83: hero.entity.v1.Reference
	(v2.SituationType)(0),                     // 84: hero.situations.v2.SituationType
	(*emptypb.Empty)(nil),                     // 85: google.protobuf.Empty
}
var file_hero_reports_v2_reports_proto_depIdxs = []int32{
	82,  // 0: hero.reports.v2.ObjectReference.metadata:type_name -> google.protobuf.Struct
	5,   // 1: hero.reports.v2.Relation.object_a:type_name -> hero.reports.v2.ObjectReference
	5,   // 2: hero.reports.v2.Relation.object_b:type_name -> hero.reports.v2.ObjectReference
	82,  // 3: hero.reports.v2.Relation.metadata:type_name -> google.protobuf.Struct
	82,  // 4: hero.reports.v2.Offense.data:type_name -> google.protobuf.Struct
	82,  // 5: hero.reports.v2.Offense.schema:type_name -> google.protobuf.Struct
	9,   // 6: hero.reports.v2.OffenseContent.offenses:type_name -> hero.reports.v2.Offense
	82,  // 7: hero.reports.v2.OffenseContent.metadata:type_name -> google.protobuf.Struct
	83,  // 8: hero.reports.v2.EntityListContent.entity_refs:type_name -> hero.entity.v1.Reference
	84,  // 9: hero.reports.v2.IncidentDetailsContent.initial_type:type_name -> hero.situations.v2.SituationType
	12,  // 10: hero.reports.v2.IncidentDetailsContent.responders:type_name -> hero.reports.v2.IncidentResponder
	13,  // 11: hero.reports.v2.IncidentDetailsContent.reporting_person:type_name -> hero.reports.v2.ReportingPerson
	84,  // 12: hero.reports.v2.IncidentDetailsContent.final_type:type_name -> hero.situations.v2.SituationType
	14,  // 13: hero.reports.v2.IncidentDetailsContent.involved_agencies:type_name -> hero.reports.v2.InvolvedAgency
	82,  // 14: hero.reports.v2.Arrest.data:type_name -> google.protobuf.Struct
	82,  // 15: hero.reports.v2.Arrest.schema:type_name -> google.protobuf.Struct
	16,  // 16: hero.reports.v2.ArrestContent.arrests:type_name -> hero.reports.v2.Arrest
	82,  // 17: hero.reports.v2.ArrestContent.metadata:type_name -> google.protobuf.Struct
	82,  // 18: hero.reports.v2.FileReference.metadata:type_name -> google.protobuf.Struct
	18,  // 19: hero.reports.v2.MediaContent.file_refs:type_name -> hero.reports.v2.FileReference
	82,  // 20: hero.reports.v2.MediaContent.metadata:type_name -> google.protobuf.Struct
	20,  // 21: hero.reports.v2.PropertyListContent.property_refs:type_name -> hero.reports.v2.PropertyReference
	2,   // 22: hero.reports.v2.ReportSection.type:type_name -> hero.reports.v2.SectionType
	8,   // 23: hero.reports.v2.ReportSection.narrative:type_name -> hero.reports.v2.NarrativeContent
	11,  // 24: hero.reports.v2.ReportSection.entity_list:type_name -> hero.reports.v2.EntityListContent
	15,  // 25: hero.reports.v2.ReportSection.incident_details:type_name -> hero.reports.v2.IncidentDetailsContent
	10,  // 26: hero.reports.v2.ReportSection.offense_list:type_name -> hero.reports.v2.OffenseContent
	17,  // 27: hero.reports.v2.ReportSection.arrest_list:type_name -> hero.reports.v2.ArrestContent
	19,  // 28: hero.reports.v2.ReportSection.media_list:type_name -> hero.reports.v2.MediaContent
	21,  // 29: hero.reports.v2.ReportSection.property_list:type_name -> hero.reports.v2.PropertyListContent
	7,   // 30: hero.reports.v2.ReportSection.comments:type_name -> hero.reports.v2.Comment
	3,   // 31: hero.reports.v2.ReviewRound.status:type_name -> hero.reports.v2.ReviewStatus
	22,  // 32: hero.reports.v2.Report.sections:type_name -> hero.reports.v2.ReportSection
	0,   // 33: hero.reports.v2.Report.status:type_name -> hero.reports.v2.ReportStatus
	23,  // 34: hero.reports.v2.Report.review_rounds:type_name -> hero.reports.v2.ReviewRound
	6,   // 35: hero.reports.v2.Report.relations:type_name -> hero.reports.v2.Relation
	7,   // 36: hero.reports.v2.Report.comments:type_name -> hero.reports.v2.Comment
	82,  // 37: hero.reports.v2.Report.additional_info_json:type_name -> google.protobuf.Struct
	1,   // 38: hero.reports.v2.Report.report_type:type_name -> hero.reports.v2.ReportType
	24,  // 39: hero.reports.v2.ReportSnapshot.report:type_name -> hero.reports.v2.Report
	24,  // 40: hero.reports.v2.CreateReportRequest.report:type_name -> hero.reports.v2.Report
	24,  // 41: hero.reports.v2.CreateReportResponse.report:type_name -> hero.reports.v2.Report
	24,  // 42: hero.reports.v2.UpdateReportRequest.report:type_name -> hero.reports.v2.Report
	0,   // 43: hero.reports.v2.ListReportsRequest.status:type_name -> hero.reports.v2.ReportStatus
	24,  // 44: hero.reports.v2.ListReportsResponse.reports:type_name -> hero.reports.v2.Report
	0,   // 45: hero.reports.v2.UpdateReportStatusRequest.status:type_name -> hero.reports.v2.ReportStatus
	24,  // 46: hero.reports.v2.UpdateReportStatusResponse.report:type_name -> hero.reports.v2.Report
	7,   // 47: hero.reports.v2.AddCommentRequest.comment:type_name -> hero.reports.v2.Comment
	7,   // 48: hero.reports.v2.GetCommentsResponse.comments:type_name -> hero.reports.v2.Comment
	7,   // 49: hero.reports.v2.UpdateCommentRequest.comment:type_name -> hero.reports.v2.Comment
	23,  // 50: hero.reports.v2.AddReviewRoundRequest.review_round:type_name -> hero.reports.v2.ReviewRound
	23,  // 51: hero.reports.v2.UpdateReviewRoundRequest.review_round:type_name -> hero.reports.v2.ReviewRound
	23,  // 52: hero.reports.v2.ListReviewRoundsForReportResponse.review_rounds:type_name -> hero.reports.v2.ReviewRound
	50,  // 53: hero.reports.v2.GetEligibleReviewersResponse.reviewers:type_name -> hero.reports.v2.EligibleReviewer
	6,   // 54: hero.reports.v2.CreateRelationRequest.relation:type_name -> hero.reports.v2.Relation
	6,   // 55: hero.reports.v2.UpdateRelationRequest.relation:type_name -> hero.reports.v2.Relation
	6,   // 56: hero.reports.v2.ListRelationsResponse.relations:type_name -> hero.reports.v2.Relation
	24,  // 57: hero.reports.v2.BatchGetReportsResponse.reports:type_name -> hero.reports.v2.Report
	22,  // 58: hero.reports.v2.CreateReportSectionRequest.section:type_name -> hero.reports.v2.ReportSection
	22,  // 59: hero.reports.v2.UpdateReportSectionRequest.section:type_name -> hero.reports.v2.ReportSection
	22,  // 60: hero.reports.v2.ListReportSectionsResponse.sections:type_name -> hero.reports.v2.ReportSection
	77,  // 61: hero.reports.v2.SearchReportsRequest.field_queries:type_name -> hero.reports.v2.FieldQuery
	0,   // 62: hero.reports.v2.SearchReportsRequest.status:type_name -> hero.reports.v2.ReportStatus
	76,  // 63: hero.reports.v2.SearchReportsRequest.created_at:type_name -> hero.reports.v2.DateRange
	76,  // 64: hero.reports.v2.SearchReportsRequest.updated_at:type_name -> hero.reports.v2.DateRange
	76,  // 65: hero.reports.v2.SearchReportsRequest.assigned_at:type_name -> hero.reports.v2.DateRange
	76,  // 66: hero.reports.v2.SearchReportsRequest.completed_at:type_name -> hero.reports.v2.DateRange
	1,   // 67: hero.reports.v2.SearchReportsRequest.report_types:type_name -> hero.reports.v2.ReportType
	76,  // 68: hero.reports.v2.SearchReportsRequest.incident_start_time:type_name -> hero.reports.v2.DateRange
	76,  // 69: hero.reports.v2.SearchReportsRequest.incident_end_time:type_name -> hero.reports.v2.DateRange
	84,  // 70: hero.reports.v2.SearchReportsRequest.initial_types:type_name -> hero.situations.v2.SituationType
	84,  // 71: hero.reports.v2.SearchReportsRequest.final_types:type_name -> hero.situations.v2.SituationType
	4,   // 72: hero.reports.v2.SearchReportsRequest.order_by:type_name -> hero.reports.v2.SearchOrderBy
	24,  // 73: hero.reports.v2.SearchReportsResponse.reports:type_name -> hero.reports.v2.Report
	81,  // 74: hero.reports.v2.SearchReportsResponse.highlights:type_name -> hero.reports.v2.SearchReportsResponse.HighlightsEntry
	78,  // 75: hero.reports.v2.SearchReportsResponse.HighlightsEntry.value:type_name -> hero.reports.v2.HighlightResult
	70,  // 76: hero.reports.v2.ReportService.CreateReportSection:input_type -> hero.reports.v2.CreateReportSectionRequest
	71,  // 77: hero.reports.v2.ReportService.GetReportSection:input_type -> hero.reports.v2.GetReportSectionRequest
	72,  // 78: hero.reports.v2.ReportService.UpdateReportSection:input_type -> hero.reports.v2.UpdateReportSectionRequest
	73,  // 79: hero.reports.v2.ReportService.DeleteReportSection:input_type -> hero.reports.v2.DeleteReportSectionRequest
	74,  // 80: hero.reports.v2.ReportService.ListReportSections:input_type -> hero.reports.v2.ListReportSectionsRequest
	27,  // 81: hero.reports.v2.ReportService.CreateReport:input_type -> hero.reports.v2.CreateReportRequest
	29,  // 82: hero.reports.v2.ReportService.GetReport:input_type -> hero.reports.v2.GetReportRequest
	30,  // 83: hero.reports.v2.ReportService.UpdateReport:input_type -> hero.reports.v2.UpdateReportRequest
	34,  // 84: hero.reports.v2.ReportService.UpdateReportStatus:input_type -> hero.reports.v2.UpdateReportStatusRequest
	31,  // 85: hero.reports.v2.ReportService.ListReports:input_type -> hero.reports.v2.ListReportsRequest
	66,  // 86: hero.reports.v2.ReportService.BatchGetReports:input_type -> hero.reports.v2.BatchGetReportsRequest
	33,  // 87: hero.reports.v2.ReportService.DeleteReport:input_type -> hero.reports.v2.DeleteReportRequest
	68,  // 88: hero.reports.v2.ReportService.ListReportsBySituationId:input_type -> hero.reports.v2.ListReportsBySituationIdRequest
	69,  // 89: hero.reports.v2.ReportService.ListReportsByCaseId:input_type -> hero.reports.v2.ListReportsByCaseIdRequest
	36,  // 90: hero.reports.v2.ReportService.AddComment:input_type -> hero.reports.v2.AddCommentRequest
	37,  // 91: hero.reports.v2.ReportService.GetComments:input_type -> hero.reports.v2.GetCommentsRequest
	39,  // 92: hero.reports.v2.ReportService.UpdateComment:input_type -> hero.reports.v2.UpdateCommentRequest
	40,  // 93: hero.reports.v2.ReportService.DeleteComment:input_type -> hero.reports.v2.DeleteCommentRequest
	26,  // 94: hero.reports.v2.ReportService.ResolveComment:input_type -> hero.reports.v2.ResolveCommentRequest
	41,  // 95: hero.reports.v2.ReportService.SubmitForReview:input_type -> hero.reports.v2.SubmitForReviewRequest
	42,  // 96: hero.reports.v2.ReportService.AddReviewRound:input_type -> hero.reports.v2.AddReviewRoundRequest
	43,  // 97: hero.reports.v2.ReportService.GetReviewRound:input_type -> hero.reports.v2.GetReviewRoundRequest
	44,  // 98: hero.reports.v2.ReportService.UpdateReviewRound:input_type -> hero.reports.v2.UpdateReviewRoundRequest
	45,  // 99: hero.reports.v2.ReportService.DeleteReviewRound:input_type -> hero.reports.v2.DeleteReviewRoundRequest
	46,  // 100: hero.reports.v2.ReportService.ApproveReviewRound:input_type -> hero.reports.v2.ApproveReviewRoundRequest
	47,  // 101: hero.reports.v2.ReportService.RequestChanges:input_type -> hero.reports.v2.RequestChangesRequest
	48,  // 102: hero.reports.v2.ReportService.ListReviewRoundsForReport:input_type -> hero.reports.v2.ListReviewRoundsForReportRequest
	51,  // 103: hero.reports.v2.ReportService.GetEligibleReviewers:input_type -> hero.reports.v2.GetEligibleReviewersRequest
	59,  // 104: hero.reports.v2.ReportService.UpdateAdditionalInfoJson:input_type -> hero.reports.v2.UpdateAdditionalInfoJsonRequest
	61,  // 105: hero.reports.v2.ReportService.GetAdditionalInfo:input_type -> hero.reports.v2.GetAdditionalInfoRequest
	63,  // 106: hero.reports.v2.ReportService.GetReportVersion:input_type -> hero.reports.v2.GetReportVersionRequest
	64,  // 107: hero.reports.v2.ReportService.ListReportVersions:input_type -> hero.reports.v2.ListReportVersionsRequest
	79,  // 108: hero.reports.v2.ReportService.SearchReports:input_type -> hero.reports.v2.SearchReportsRequest
	53,  // 109: hero.reports.v2.ReportService.CreateRelation:input_type -> hero.reports.v2.CreateRelationRequest
	54,  // 110: hero.reports.v2.ReportService.GetRelation:input_type -> hero.reports.v2.GetRelationRequest
	55,  // 111: hero.reports.v2.ReportService.UpdateRelation:input_type -> hero.reports.v2.UpdateRelationRequest
	56,  // 112: hero.reports.v2.ReportService.DeleteRelation:input_type -> hero.reports.v2.DeleteRelationRequest
	57,  // 113: hero.reports.v2.ReportService.ListRelations:input_type -> hero.reports.v2.ListRelationsRequest
	22,  // 114: hero.reports.v2.ReportService.CreateReportSection:output_type -> hero.reports.v2.ReportSection
	22,  // 115: hero.reports.v2.ReportService.GetReportSection:output_type -> hero.reports.v2.ReportSection
	22,  // 116: hero.reports.v2.ReportService.UpdateReportSection:output_type -> hero.reports.v2.ReportSection
	85,  // 117: hero.reports.v2.ReportService.DeleteReportSection:output_type -> google.protobuf.Empty
	75,  // 118: hero.reports.v2.ReportService.ListReportSections:output_type -> hero.reports.v2.ListReportSectionsResponse
	28,  // 119: hero.reports.v2.ReportService.CreateReport:output_type -> hero.reports.v2.CreateReportResponse
	24,  // 120: hero.reports.v2.ReportService.GetReport:output_type -> hero.reports.v2.Report
	24,  // 121: hero.reports.v2.ReportService.UpdateReport:output_type -> hero.reports.v2.Report
	35,  // 122: hero.reports.v2.ReportService.UpdateReportStatus:output_type -> hero.reports.v2.UpdateReportStatusResponse
	32,  // 123: hero.reports.v2.ReportService.ListReports:output_type -> hero.reports.v2.ListReportsResponse
	67,  // 124: hero.reports.v2.ReportService.BatchGetReports:output_type -> hero.reports.v2.BatchGetReportsResponse
	85,  // 125: hero.reports.v2.ReportService.DeleteReport:output_type -> google.protobuf.Empty
	32,  // 126: hero.reports.v2.ReportService.ListReportsBySituationId:output_type -> hero.reports.v2.ListReportsResponse
	32,  // 127: hero.reports.v2.ReportService.ListReportsByCaseId:output_type -> hero.reports.v2.ListReportsResponse
	7,   // 128: hero.reports.v2.ReportService.AddComment:output_type -> hero.reports.v2.Comment
	38,  // 129: hero.reports.v2.ReportService.GetComments:output_type -> hero.reports.v2.GetCommentsResponse
	7,   // 130: hero.reports.v2.ReportService.UpdateComment:output_type -> hero.reports.v2.Comment
	85,  // 131: hero.reports.v2.ReportService.DeleteComment:output_type -> google.protobuf.Empty
	7,   // 132: hero.reports.v2.ReportService.ResolveComment:output_type -> hero.reports.v2.Comment
	24,  // 133: hero.reports.v2.ReportService.SubmitForReview:output_type -> hero.reports.v2.Report
	23,  // 134: hero.reports.v2.ReportService.AddReviewRound:output_type -> hero.reports.v2.ReviewRound
	23,  // 135: hero.reports.v2.ReportService.GetReviewRound:output_type -> hero.reports.v2.ReviewRound
	23,  // 136: hero.reports.v2.ReportService.UpdateReviewRound:output_type -> hero.reports.v2.ReviewRound
	85,  // 137: hero.reports.v2.ReportService.DeleteReviewRound:output_type -> google.protobuf.Empty
	23,  // 138: hero.reports.v2.ReportService.ApproveReviewRound:output_type -> hero.reports.v2.ReviewRound
	23,  // 139: hero.reports.v2.ReportService.RequestChanges:output_type -> hero.reports.v2.ReviewRound
	49,  // 140: hero.reports.v2.ReportService.ListReviewRoundsForReport:output_type -> hero.reports.v2.ListReviewRoundsForReportResponse
	52,  // 141: hero.reports.v2.ReportService.GetEligibleReviewers:output_type -> hero.reports.v2.GetEligibleReviewersResponse
	60,  // 142: hero.reports.v2.ReportService.UpdateAdditionalInfoJson:output_type -> hero.reports.v2.UpdateAdditionalInfoJsonResponse
	62,  // 143: hero.reports.v2.ReportService.GetAdditionalInfo:output_type -> hero.reports.v2.GetAdditionalInfoResponse
	25,  // 144: hero.reports.v2.ReportService.GetReportVersion:output_type -> hero.reports.v2.ReportSnapshot
	65,  // 145: hero.reports.v2.ReportService.ListReportVersions:output_type -> hero.reports.v2.ListReportVersionsResponse
	80,  // 146: hero.reports.v2.ReportService.SearchReports:output_type -> hero.reports.v2.SearchReportsResponse
	6,   // 147: hero.reports.v2.ReportService.CreateRelation:output_type -> hero.reports.v2.Relation
	6,   // 148: hero.reports.v2.ReportService.GetRelation:output_type -> hero.reports.v2.Relation
	6,   // 149: hero.reports.v2.ReportService.UpdateRelation:output_type -> hero.reports.v2.Relation
	85,  // 150: hero.reports.v2.ReportService.DeleteRelation:output_type -> google.protobuf.Empty
	58,  // 151: hero.reports.v2.ReportService.ListRelations:output_type -> hero.reports.v2.ListRelationsResponse
	114, // [114:152] is the sub-list for method output_type
	76,  // [76:114] is the sub-list for method input_type
	76,  // [76:76] is the sub-list for extension type_name
	76,  // [76:76] is the sub-list for extension extendee
	0,   // [0:76] is the sub-list for field type_name
}

func init() { file_hero_reports_v2_reports_proto_init() }
func file_hero_reports_v2_reports_proto_init() {
	if File_hero_reports_v2_reports_proto != nil {
		return
	}
	file_hero_reports_v2_reports_proto_msgTypes[17].OneofWrappers = []any{
		(*ReportSection_Narrative)(nil),
		(*ReportSection_EntityList)(nil),
		(*ReportSection_IncidentDetails)(nil),
		(*ReportSection_OffenseList)(nil),
		(*ReportSection_ArrestList)(nil),
		(*ReportSection_MediaList)(nil),
		(*ReportSection_PropertyList)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_reports_v2_reports_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   77,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hero_reports_v2_reports_proto_goTypes,
		DependencyIndexes: file_hero_reports_v2_reports_proto_depIdxs,
		EnumInfos:         file_hero_reports_v2_reports_proto_enumTypes,
		MessageInfos:      file_hero_reports_v2_reports_proto_msgTypes,
	}.Build()
	File_hero_reports_v2_reports_proto = out.File
	file_hero_reports_v2_reports_proto_rawDesc = nil
	file_hero_reports_v2_reports_proto_goTypes = nil
	file_hero_reports_v2_reports_proto_depIdxs = nil
}

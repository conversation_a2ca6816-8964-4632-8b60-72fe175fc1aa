syntax = "proto3";

package hero.reports.v2;

// -----------------------------------------------------------------------------
// Imports
// -----------------------------------------------------------------------------
// Core Google-protobuf types and project-specific message definitions.
import "google/protobuf/empty.proto";
import "hero/assets/v2/assets.proto";
import "hero/entity/v1/entity.proto";
import "hero/situations/v2/situations.proto";
import "hero/property/v1/property.proto";
import "google/protobuf/struct.proto";

// -----------------------------------------------------------------------------
// Go Package Option
// -----------------------------------------------------------------------------
// Specifies the Go import path for the generated code.
option go_package = "proto/hero/reports/v2;reports";

// -----------------------------------------------------------------------------
// Report Status Lifecycle
// -----------------------------------------------------------------------------
// Defines the high-level lifecycle states of a report.
enum ReportStatus {
  REPORT_STATUS_UNSPECIFIED           = 0; // Default unset state
  REPORT_STATUS_ASSIGNED              = 1; // Task has been assigned to an author
  REPORT_STATUS_IN_PROGRESS           = 2; // Author is actively writing the report
  REPORT_STATUS_SUBMITTED_FOR_REVIEW  = 3; // Report submitted and awaiting review
  REPORT_STATUS_UNDER_REVIEW          = 4; // Reviewer is actively reviewing
  REPORT_STATUS_CHANGES_REQUESTED     = 5; // Reviewer requested changes
  REPORT_STATUS_IN_REWORK             = 6; // Author is reworking requested changes
  REPORT_STATUS_APPROVED              = 7; // Report approved and finalized
  REPORT_STATUS_REJECTED              = 8; // Report permanently rejected
  REPORT_STATUS_CANCELLED             = 9; // Report process cancelled
}

// -----------------------------------------------------------------------------
// Report Type
// -----------------------------------------------------------------------------
// Defines the different types of reports that can be created.
enum ReportType {
  REPORT_TYPE_UNSPECIFIED                = 0; // Default unset type
  REPORT_TYPE_INCIDENT_PRIMARY           = 1; // Primary incident report for an incident 
  REPORT_TYPE_INCIDENT_SUPPLEMENTAL      = 2; // Supplemental incident report for an incident
  REPORT_TYPE_INCIDENT_SUPPLEMENTAL_INVESTIGATIVE = 3; // Supplemental investigative report for an incident
}

// -----------------------------------------------------------------------------
// Section Types
// -----------------------------------------------------------------------------
// Enumerates the different types of sections within a report.
enum SectionType {
  SECTION_TYPE_UNSPECIFIED         = 0; // Default unset type
  SECTION_TYPE_NARRATIVE           = 1; // Free-form rich text (e.g., HTML/Markdown)
  SECTION_TYPE_INCIDENT_DETAILS    = 3; // Structured incident summary
  SECTION_TYPE_ENTITY_LIST_PEOPLE  = 4; // List of references to people entities
  SECTION_TYPE_ENTITY_LIST_VEHICLE = 5; // List of references to vehicle entities
  SECTION_TYPE_ENTITY_LIST_PROPERTIES = 6; // List of references to property entities
  SECTION_TYPE_OFFENSE             = 7; // Free-form offense information
  SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS = 8; // List of references to organization entities
  SECTION_TYPE_ARREST              = 9; // Structured arrest details
  SECTION_TYPE_MEDIA               = 10; // Media attachments and files
  SECTION_TYPE_PROPERTY            = 11; // New property service section
}

// -----------------------------------------------------------------------------
// Object Reference (for Relations)
// -----------------------------------------------------------------------------
// Flexible reference to any object within a report that can participate in relations.
// Uses string-based types for maximum flexibility without requiring protobuf changes.
//
// VALIDATION: At least one of report_scoped_id, global_id, or external_id MUST be set.
//
// ID Types:
//   - report_scoped_id: IDs unique within this report (e.g., responder.id, reporting_person.id, section content IDs)
//   - global_id: System-wide unique IDs (e.g., asset_id, entity_id, case_id, situation_id)
//   - external_id: IDs from external systems or custom references
//
// Common object_type values (extensible):
//   - "entity"           - Reference to an entity (person, vehicle, property) - use global_id
//   - "section"          - Reference to a report section - use global_id (section.id)
//   - "offense"          - Reference to an offense within a section - use report_scoped_id (offense.id)
//   - "incident_details" - Reference to incident details within a section - use report_scoped_id (incident_details.id)
//   - "narrative"        - Reference to narrative content within a section - use report_scoped_id (narrative.id)
//   - "comment"          - Reference to a comment - use global_id (comment.id)
//   - "responder"        - Reference to a specific responder in incident details - use report_scoped_id (responder.id)
//   - "reporting_person" - Reference to the reporting person in incident details - use report_scoped_id (reporting_person.id)
//   - "asset"            - Reference to an asset - use global_id (asset_id)
//   - "case"             - Reference to a case - use global_id (case_id)
//   - "situation"        - Reference to a situation - use global_id (situation_id)
//   - "file_reference"   - Reference to a file reference within a media section - use report_scoped_id (file_reference.id)
//   - "media_content"    - Reference to media content within a section - use report_scoped_id (media_content.id)
//   - "custom_*"         - Any custom object type
message ObjectReference {
  string object_type     = 1; // Type of the referenced object (flexible string) - REQUIRED
  string report_scoped_id = 2; // ID unique within this report (e.g., responder.id, content.id)
  string global_id       = 3; // System-wide unique ID (e.g., asset_id, entity_id, section.id)
  string external_id     = 4; // External system ID or custom reference
  string section_id      = 5; // If object is within a section, the section ID for context
  string display_name    = 6; // Human-readable name for the referenced object (recommended)
  google.protobuf.Struct metadata = 7; // Additional metadata about the reference
}

// -----------------------------------------------------------------------------
// Relation
// -----------------------------------------------------------------------------
// Establishes a semantic connection between any two objects within a report.
// Uses string-based relation types for maximum flexibility without requiring protobuf changes.
// Relationships are bidirectional - both objects have equal weight in the relationship.
//
// VALIDATION: 
//   - object_a and object_b MUST be valid ObjectReference instances
//   - relation_type MUST be non-empty
//   - object_a and object_b SHOULD NOT reference the same object (no self-relations)
//
// Common relation_type values (extensible - contextualized for college campus incidents):
//   - "associated_with"    - General association between objects
//   - "connected_to"       - Direct connection between objects  
//   - "related_to"         - General relation between objects
//   - "involved_in"        - Both objects involved in same incident
//   - "occurred_with"      - Objects occurred together in incident
//   - "located_at"         - Object/person located at facility/room/building
//   - "part_of"            - Object is part of another (e.g., room part of building)
//   - "witnessed_by"       - Incident/event witnessed by person
//   - "reported_by"        - Incident reported by person/student/staff
//   - "responded_to_by"    - Incident responded to by officer/staff/security
//   - "victim_of"          - Person was victim of incident/violation
//   - "perpetrator_of"     - Person was perpetrator of incident/violation
//   - "suspect_in"         - Person is suspect in incident
//   - "complainant_in"     - Person filed complaint in incident
//   - "violates_policy"    - Incident violates specific campus policy
//   - "assigned_to"        - Case/incident assigned to officer/staff
//   - "escalated_to"       - Incident escalated to higher authority
//   - "follows_up_on"      - Report follows up on previous incident
//   - "references"         - Object references another object
//   - "occurred_in"        - Incident occurred in specific location/timeframe
//   - "involves_substance" - Incident involves drugs/alcohol
//   - "involves_property"  - Incident involves campus property/equipment
//   - "custom_*"           - Any custom relation type
message Relation {
  string          id            = 1;  // Unique relation identifier - auto-generated
  string          report_id     = 2;  // The report this relation belongs to - REQUIRED
  ObjectReference object_a      = 3;  // First object in the bidirectional relationship - REQUIRED
  ObjectReference object_b      = 4;  // Second object in the bidirectional relationship - REQUIRED
  string          relation_type = 5;  // Semantic meaning of the relationship (flexible string) - REQUIRED
  string          description   = 6;  // Optional human-readable description of the relationship
  google.protobuf.Struct metadata = 7; // Flexible JSON for additional relationship data
  string          created_at    = 8;  // ISO8601 timestamp when relation was created - auto-generated
  string          updated_at    = 9;  // ISO8601 timestamp for last relation update - auto-managed
  string          created_by_asset_id = 10; // Asset ID who created this relation - REQUIRED
}

// -----------------------------------------------------------------------------
// Comment
// -----------------------------------------------------------------------------
// Represents either a global report comment or one tied to a specific section.
message Comment {
  string id                   = 1;  // Unique comment identifier
  string report_id            = 2;  // The report this comment belongs to
  string section_id           = 3;  // If set, ties comment to a particular section
  string reply_to_comment_id  = 4;  // If set, this is a threaded reply
  string author_asset_id      = 5;  // Asset ID of the comment author
  string text                 = 6;  // Comment body text
  string created_at           = 7;  // ISO8601 timestamp when created
  bool   resolved             = 8;  // True if comment has been marked resolved
  string resolved_at          = 9;  // ISO8601 timestamp when resolved
  string resolved_by_asset_id = 10; // Asset ID of user who resolved it
  string updated_at           = 11; // ISO8601 timestamp for last edit
  string resource_type        = 12; // Constant string "COMMENT"
  string display_name         = 13; // Display name of the comment author
}

// -----------------------------------------------------------------------------
// Narrative Content
// -----------------------------------------------------------------------------
// Free-text rich content section (HTML/Markdown).
message NarrativeContent {
  string id        = 1; // Unique identifier for this narrative content
  string rich_text = 2; // Raw rich-text blob
}

// -----------------------------------------------------------------------------
// Offense
// -----------------------------------------------------------------------------
// Individual offense information within an offense content section.
message Offense {
  string id                         = 1; // Unique identifier for this individual offense
  string offense_type               = 2; // Type or classification of offense 
  google.protobuf.Struct data       = 3; // Flexible JSON structure for additional data
  google.protobuf.Struct schema     = 4; // Schema for this specific offense
}

// -----------------------------------------------------------------------------
// Offense Content
// -----------------------------------------------------------------------------
// Section containing multiple offenses.
message OffenseContent {
  string id                         = 1; // Unique identifier for this offense content section
  repeated Offense offenses         = 2; // List of individual offenses
  google.protobuf.Struct metadata   = 3; // Section-level metadata
}

// -----------------------------------------------------------------------------
// Entity List Content
// -----------------------------------------------------------------------------
// Section containing a title and references to existing entities.
message EntityListContent {
  string                          id          = 1; // Unique identifier for this entity list content
  string                          title       = 2; // Human-readable section header
  repeated hero.entity.v1.Reference entity_refs = 3; // Links to other entities
}

// -----------------------------------------------------------------------------
// Incident Details Content
// -----------------------------------------------------------------------------
// Editable snapshot of the situation that triggered the report.
message IncidentResponder {
  string id           = 1; // Report-scoped unique identifier for this responder
  string asset_id     = 2; // Asset ID of the responder
  string display_name = 3; // Responder's name
  string role         = 4; // Responder's role or title
}

message ReportingPerson {
  string id           = 1; // Report-scoped unique identifier for this reporting person
  string asset_id     = 2; // Asset ID of the reporting individual
  string first_name   = 3; // First name
  string middle_name  = 4; // Middle name (optional)
  string last_name    = 5; // Last name
  string phone_number = 6; // Contact number
  string reporter_role = 7; // Role of the reporter (e.g., witness, victim)
}

message InvolvedAgency {
  string id     = 1; // unique identier for the content
  string agency_name   = 2; // Name of the agency
  string incident_reference_number   = 3; // Role of the agency
}

message IncidentDetailsContent {
  string                            id                          = 1; // Unique identifier for this incident details content
  hero.situations.v2.SituationType  initial_type                = 2; // Original situation type enum
  string                            incident_start_time        = 3; // ISO8601 start time
  string                            incident_end_time          = 4; // ISO8601 end time
  string                            reported_time              = 5; // ISO8601 reported time
  string                            incident_location_clery_type = 6; // CLERY location type (Optional)
  string                            incident_location_street_address = 7; // Street address
  string                            incident_location_unit_info = 8; // Apt/Suite/Unit # (Optional)
  string                            incident_location_type     = 9; // Location type (e.g., Dorm, Building, etc.)
  string                            incident_location_common_name = 10; // Common name for the location (Optional)
  string                            incident_location_city     = 11; // City
  string                            incident_location_state    = 12; // State
  string                            incident_location_zip_code = 13; // Zip code
  string                            incident_location_country  = 14; // Country
  double                            incident_location_latitude = 15; // Latitude coordinate
  double                            incident_location_longitude = 16; // Longitude coordinate
  repeated IncidentResponder        responders                 = 17; // List of responders
  ReportingPerson                   reporting_person           = 18; // Person who reported the incident
  hero.situations.v2.SituationType  final_type                 = 19; // Final situation type enum
  repeated InvolvedAgency           involved_agencies                   = 20; // List of involved agencies
  string                            description                = 21; // Description of the incident
}

// -----------------------------------------------------------------------------
// Arrest
// -----------------------------------------------------------------------------
// Individual arrest information within an arrest content section.
// -----------------------------------------------------------------------------

message Arrest {
  string id                         = 1; // Unique identifier for this individual arrest
  string arrest_type               = 2; // Type or classification of arrest
  google.protobuf.Struct data       = 3; // Flexible JSON structure for additional data
  google.protobuf.Struct schema     = 4; // Schema for this specific arrest
}

// -----------------------------------------------------------------------------
// Arrest Content
// -----------------------------------------------------------------------------
// Section containing multiple arrests.
message ArrestContent {
  string id                         = 1; // Unique identifier for this arrest content section
  repeated Arrest arrests           = 2; // List of individual arrests
  google.protobuf.Struct metadata   = 3; // Section-level metadata
}

// -----------------------------------------------------------------------------
// File Reference
// -----------------------------------------------------------------------------
// Individual file reference for media sections, linking to filerepository service.
message FileReference {
  string id                         = 1; // Unique identifier for this file reference within the section
  string file_id                    = 2; // FileMetadata.id from filerepository service - REQUIRED
  string caption                    = 3; // Optional caption/description for the file
  string display_name               = 4; // Optional display name (fallback to original filename)
  int32  display_order              = 5; // Order for displaying files in UI (0-based)
  string file_category              = 6; // Category of the file (e.g., "incident_photo", "incident_video", "incident_audio", "incident_document", "incident_other")
  google.protobuf.Struct metadata   = 7; // Additional metadata about the file reference
}

// -----------------------------------------------------------------------------
// Media Content
// -----------------------------------------------------------------------------
// Section containing media attachments and file references.
message MediaContent {
  string id                         = 1; // Unique identifier for this media content section
  string title                      = 2; // Human-readable section header
  repeated FileReference file_refs  = 3; // References to files in filerepository
  google.protobuf.Struct metadata   = 4; // Section-level metadata
}

// -----------------------------------------------------------------------------
// Property Reference
// -----------------------------------------------------------------------------
// Reference to a property from the property service.
message PropertyReference {
  string id = 1;   // Unique identifier for the referenced property
  string type = 2; // Type of the referenced property (e.g., "property")
  int32 version = 3; // Version of the referenced property
  string display_name = 4; // Optional display name for the reference
  string relation_type = 5; // Type of relation between the referencing and referenced objects
}

// -----------------------------------------------------------------------------
// Property List Content
// -----------------------------------------------------------------------------
// Section containing a title and references to existing properties from the property service.
message PropertyListContent {
  string                          id          = 1; // Unique identifier for this property list content
  string                          title       = 2; // Human-readable section header
  repeated PropertyReference      property_refs = 3; // Links to properties from the property service
}

// -----------------------------------------------------------------------------
// Report Section
// -----------------------------------------------------------------------------
// A single section within a report; content is polymorphic via `oneof`.
message ReportSection {
  string                id         = 1; // Unique section ID
  SectionType           type       = 2; // Determines which `oneof` field is set
  oneof content {                      // Exactly one content type per section
    NarrativeContent       narrative        = 3;
    EntityListContent      entity_list      = 4;
    IncidentDetailsContent incident_details = 5;
    OffenseContent         offense_list     = 6;
    ArrestContent          arrest_list      = 11;
    MediaContent           media_list    = 12;
    PropertyListContent    property_list    = 13;
  }
  repeated Comment comments     = 7; // Comments attached to this section
  string         created_at   = 8; // ISO8601 timestamp when section was created
  string         updated_at   = 9; // ISO8601 timestamp for last section update
  string          report_id   = 10; // Parent report ID
}

// -----------------------------------------------------------------------------
// Review Status
// -----------------------------------------------------------------------------
// State machine for individual review rounds.
enum ReviewStatus {
  REVIEW_STATUS_UNSPECIFIED       = 0;
  REVIEW_STATUS_AWAITING_ACTION   = 1; // Waiting on reviewer or author
  REVIEW_STATUS_APPROVED          = 2; // Round approved
  REVIEW_STATUS_CHANGES_REQUESTED = 3; // Reviewer asked for changes
}

// -----------------------------------------------------------------------------
// Review Round
// -----------------------------------------------------------------------------
// Tracks each iteration in a multi-level review workflow.
message ReviewRound {
  string        id                 = 1;  // Unique review round ID
  string        report_id          = 2;  // Parent report identifier
  string        reviewer_asset_id  = 3;  // Who is reviewing
  int32         level              = 4;  // Hierarchy level (e.g., 1 = peer, 2 = manager)
  ReviewStatus  status             = 5;  // Current review state
  int32         sent_to_level      = 6;  // Next level to route (0 returns to author)
  string        sent_to_asset_id   = 7;  // Specific asset for next routing
  string        requested_at       = 8;  // ISO8601 timestamp when round was created
  string        resolved_at        = 9;  // ISO8601 timestamp when round was finalized
  string        round_note         = 10; // Reviewer's comment for this round
  int32         snapshot_version   = 11; // Report.version at start of round
  string        due_at             = 12; // ISO8601 timestamp for review deadline
  string        create_by_asset_id = 13; // Asset ID who initiated this round
  string        note_for_reviewer  = 14; // Optional instructions/note
}

// -----------------------------------------------------------------------------
// Report
// -----------------------------------------------------------------------------
// Aggregates sections, comments, review rounds, and metadata.
message Report {
  string                   id                   = 1;  // Primary key
  int32                    org_id               = 2;  // Tenant/organization ID
  string                   author_asset_id      = 3;  // Who authored it
  string                   title                = 4;  // User-defined title
  repeated ReportSection   sections             = 5;  // All sections in order
  ReportStatus             status               = 6;  // Overall report state
  repeated ReviewRound     review_rounds        = 7;  // Historical review data
  repeated Relation        relations            = 8;  // Object relationships within the report
  repeated Comment         comments             = 9;  // Global, report-level comments
  string                   assigned_at          = 10; // ISO8601 timestamp when first assigned
  string                   updated_at           = 11; // ISO8601 timestamp for last update
  string                   completed_at         = 12; // ISO8601 timestamp when terminal
  string                   resource_type        = 13; // Constant string "REPORT"
  google.protobuf.Struct   additional_info_json = 14; // Arbitrary JSON key/value pairs
  int32                    version              = 15; // Monotonic version number
  string                   situation_id         = 16; // Link back to a Situation entity
  string                   case_id              = 17; // Link back to an Incident Case
  repeated string          watcher_asset_ids    = 18; // Users subscribed for updates
  string                   created_at           = 19; // ISO8601 timestamp when report was created
  string                   created_by_asset_id  = 20; // Asset ID of the report author
  ReportType               report_type          = 21; // Type of the report
}

// -----------------------------------------------------------------------------
// Report Snapshot
// -----------------------------------------------------------------------------
// Immutable capture of a report at a specific version.
message ReportSnapshot {
  string report_id  = 1; // Parent report ID
  int32  version    = 2; // Version number
  Report report     = 3; // Full report state
  string created_at = 4; // ISO8601 timestamp when snapshot was taken
}

// -----------------------------------------------------------------------------
// ResolveCommentRequest
// -----------------------------------------------------------------------------
// Marks a comment as resolved (idempotent).
message ResolveCommentRequest {
  string comment_id        = 1; // Target comment
}

// -----------------------------------------------------------------------------
// Report RPC Payloads
// -----------------------------------------------------------------------------
message CreateReportRequest    { Report report = 1; }
message CreateReportResponse   { Report report = 1; }
message GetReportRequest       { string id    = 1; }
message UpdateReportRequest    { Report report = 1; }
message ListReportsRequest {
  int32        page_size  = 1; // Max items per page
  string       page_token = 2; // Cursor for pagination
  ReportStatus status     = 3; // Filter by status (optional)
  int32        org_id     = 4; // Filter by organization (optional)
}
message ListReportsResponse {
  repeated Report reports         = 1; // Page of reports
  string           next_page_token = 2; // Cursor for next page
}
message DeleteReportRequest     { string id    = 1; }

message UpdateReportStatusRequest {
  string       id     = 1; // ID of the report to update
  ReportStatus status = 2; // The new status to set
}
message UpdateReportStatusResponse {
  Report report = 1; // The updated report
}

// Comment operations
message AddCommentRequest       { Comment comment = 1; }
message GetCommentsRequest      { string report_id = 1; string section_id = 2; int32 page_size = 3; string page_token = 4; }
message GetCommentsResponse     { repeated Comment comments = 1; string next_page_token = 2; }
message UpdateCommentRequest    { Comment comment = 1; }
message DeleteCommentRequest    { string comment_id = 1; }

// -----------------------------------------------------------------------------
// Review Workflow Payloads
// -----------------------------------------------------------------------------
message SubmitForReviewRequest    { 
  string report_id = 1; 
  string note = 2; 
  string preferred_reviewer_asset_id = 3; // Optional: specific reviewer to assign
}
message AddReviewRoundRequest     { ReviewRound review_round = 1; }
message GetReviewRoundRequest     { string review_round_id = 1; } 
message UpdateReviewRoundRequest  { ReviewRound review_round = 1; } 
message DeleteReviewRoundRequest  { string review_round_id = 1; } 
message ApproveReviewRoundRequest { string review_round_id = 1; string note = 2; }
message RequestChangesRequest     { string review_round_id = 1; string note = 2; int32 send_to_level = 3; string send_to_asset_id = 4;  }
message ListReviewRoundsForReportRequest {
  string report_id  = 1; // Target report ID
  int32  page_size  = 2; // Max items per page
  string page_token = 3; // Cursor for pagination
}
message ListReviewRoundsForReportResponse {
  repeated ReviewRound review_rounds   = 1; // Page of review rounds
  string               next_page_token = 2; // Cursor for next page
}

// -----------------------------------------------------------------------------
// Eligible Reviewers Payloads
// -----------------------------------------------------------------------------
// Individual eligible reviewer information
message EligibleReviewer {
  string asset_id           = 1; // Asset ID of the reviewer
  string name               = 2; // Display name of the reviewer
  string asset_type         = 3; // Type of asset (e.g., SUPERVISOR)
  int32  active_review_count = 4; // Number of active reviews assigned
  string email              = 5; // Email address (optional)
  string role               = 6; // Role/title (optional)
}

// Request to get eligible reviewers for a report
message GetEligibleReviewersRequest {
  string report_id = 1; // Target report ID
  int32  level     = 2; // Optional: specific level (defaults to next level)
}

// Response with list of eligible reviewers
message GetEligibleReviewersResponse {
  repeated EligibleReviewer reviewers  = 1; // List of eligible reviewers
  int32                     next_level = 2; // The level these reviewers are for
  string                    level_name = 3; // Human-readable level name
}

// -----------------------------------------------------------------------------
// Relation Payloads
// -----------------------------------------------------------------------------
message CreateRelationRequest { 
  string   report_id = 1; // Target report ID
  Relation relation  = 2; // Relation object to create
}
message GetRelationRequest { 
  string report_id   = 1; // Target report ID
  string relation_id = 2; // Target relation ID
}
message UpdateRelationRequest { 
  string   report_id = 1; // Target report ID
  Relation relation  = 2; // Relation object with updates
}
message DeleteRelationRequest { 
  string report_id   = 1; // Target report ID
  string relation_id = 2; // Target relation ID
}
message ListRelationsRequest {
  string report_id  = 1; // Target report ID
  int32  page_size  = 2; // Max items per page
  string page_token = 3; // Cursor for pagination
  // Optional filters
  string relation_type  = 4; // Filter by relation type
  string involves_object_type = 5; // Filter by any object (object_a OR object_b) having this type
  string involves_object_id   = 6; // Filter by any object (object_a OR object_b) having this ID
}
message ListRelationsResponse {
  repeated Relation relations       = 1; // Page of relations
  string            next_page_token = 2; // Cursor for next page
}

// -----------------------------------------------------------------------------
// JSON Metadata Payloads
// -----------------------------------------------------------------------------
message UpdateAdditionalInfoJsonRequest  { string report_id = 1; string additional_info_json = 2; }
message UpdateAdditionalInfoJsonResponse { string report_id = 1; string additional_info_json = 2; }
message GetAdditionalInfoRequest         { string report_id = 1; }
message GetAdditionalInfoResponse        { string report_id = 1; string additional_info_json = 2; }

// -----------------------------------------------------------------------------
// Versioning Payloads
// -----------------------------------------------------------------------------
message GetReportVersionRequest    { string report_id = 1; int32 version = 2; }
message ListReportVersionsRequest  { string report_id = 1; }
message ListReportVersionsResponse { repeated int32 versions = 1; }
message BatchGetReportsRequest     { repeated string report_ids = 1; }
message BatchGetReportsResponse    { repeated Report reports = 1; }

// -----------------------------------------------------------------------------
// List by Situation / Case
// -----------------------------------------------------------------------------
message ListReportsBySituationIdRequest {
  string situation_id = 1;  // Target Situation ID
  int32  page_size    = 2;  // Max items per page
  string page_token   = 3;  // Cursor for pagination
}

message ListReportsByCaseIdRequest {
  string case_id    = 1;  // Target Case ID
  int32  page_size  = 2;  // Max items per page
  string page_token = 3;  // Cursor for pagination
}

// -----------------------------------------------------------------------------
// Section RPC Payloads
// -----------------------------------------------------------------------------
message CreateReportSectionRequest { string report_id = 1; ReportSection section = 2; }
message GetReportSectionRequest    { string report_id = 1; string section_id = 2; }
message UpdateReportSectionRequest { string report_id = 1; ReportSection section = 2; }
message DeleteReportSectionRequest { string report_id = 1; string section_id = 2; }
message ListReportSectionsRequest  { string report_id = 1; }
message ListReportSectionsResponse { repeated ReportSection sections = 1; }

// ──────────────────────────────────────────────────────
// Search-related enums & messages
// ──────────────────────────────────────────────────────

/// How to sort the search results
enum SearchOrderBy {
  SEARCH_ORDER_BY_UNSPECIFIED    = 0;
  SEARCH_ORDER_BY_RELEVANCE      = 1;
  SEARCH_ORDER_BY_CREATED_AT     = 2;
  SEARCH_ORDER_BY_UPDATED_AT     = 3;
  SEARCH_ORDER_BY_STATUS         = 4;
}

/// A half-open timestamp range (inclusive).
message DateRange {
  string from = 1;  // RFC3339 timestamp, inclusive
  string to   = 2;  // RFC3339 timestamp, inclusive
}

/// Field-specific query (limits a search term to one field)
message FieldQuery {
  string field = 1;   // e.g. "title", "entity_list_title", "reporting_person_first_name"
  string query = 2;   // the term to match in that field
}

/// Highlighted fragments for a given field in each report
message HighlightResult {
  string field             = 1;  // the field name where matches occurred
  repeated string fragments = 2; // snippets with matches, e.g. ["…urgent…", "…critical…"]
}

/// Request for searching reports, flat and non-nested.
///
/// All partial/fuzzy matching (ILIKE, substring, full-text) must be done via the `query`, `search_fields`, or `field_queries` parameters.
///
/// PERFORMANCE NOTE: For optimal performance, combine exact filters (status, date ranges) with full-text search.
/// Avoid using only full-text search on large datasets without additional filters.
///
/// Supported field names for `search_fields` and `field_queries.field`:
///   - "title"                  (report title)
///   - "id"                     (report id)
///   - "narrative"              (narrative section rich text)
///   - "entity_list_title"      (entity list section title)
///   - "incident_location"      (incident details location)
///   - "reporting_person_name"  (reporting person first/middle/last name)
///   - "reporting_person_phone_number" (reporting person phone number)
///   - "reporting_person_role"        (reporting person role)
///   - "reference_display_name" (entity reference display name)
///   - "responder_display_name" (incident responder display name)
///   - "responder_role"         (incident responder role)
///   - "offense_type"           (offense type/classification - searches across all offenses in offense sections)
///   - "relation_description"   (relation description text)
///   - "relation_object_name"   (relation object display names - searches both object_a and object_b)
///   - "agency_name"            (involved agency name)
///   - "agency_reference"       (involved agency incident reference number)
///
/// For exact-value and range filters, use the dedicated fields below. These include DateRange, enums, and array fields for exact/range matching.
message SearchReportsRequest {
  // ────── Free-text & scoped field queries ──────
  string               query           = 1;  // full-text / fuzzy across all indexed fields
  repeated string      search_fields   = 2;  // limit `query` to these fields; empty = all
  repeated FieldQuery  field_queries   = 3;  // individual term→field queries

  // ────── Top-level report filters ──────
  repeated ReportStatus       status                     = 4;
  repeated string             situation_ids              = 5;
  repeated string             case_ids                   = 6;
  DateRange                   created_at                 = 7;  // reports.created_at BETWEEN …
  DateRange                   updated_at                 = 8;  // reports.updated_at BETWEEN …
  DateRange                   assigned_at                = 9;  // reports.assigned_at BETWEEN …
  DateRange                   completed_at               = 10; // reports.completed_at BETWEEN …
  repeated string             created_by_asset_ids       = 11; // reports.created_by_asset_id IN (…)
  repeated ReportType         report_types               = 12; // reports.report_type IN (…)

  // ────── Entity-list sections (exact/range) ──────
  repeated string             entity_list_ref_ids        = 13;  // generated ref_ids @> ARRAY[…]
  string                      reference_type             = 14;  // generated entity_refs[].type = …

  // ────── Incident-details sections (exact/range) ──────
  DateRange                   incident_start_time        = 15;  // generated incident_start_t BETWEEN …
  DateRange                   incident_end_time          = 16;  // generated incident_end_t BETWEEN …
  repeated hero.situations.v2.SituationType initial_types   = 17;  // generated initial_type IN (…)
  repeated hero.situations.v2.SituationType final_types     = 18;  // generated final_type   IN (…)
  repeated string             responder_asset_ids        = 19;  // generated responders[].asset_id IN (…)
  repeated string             responder_roles            = 20;  // generated responders[].role IN (…)

  // ────── Offense sections (exact/range) ──────
  repeated string             offense_types              = 21;  // offenses[].offense_type IN (…)
  repeated string             offense_ids                = 22;  // offenses[].id IN (…)

  // ────── Relation filters (exact/range) ──────
  repeated string             relation_types             = 23;  // relations[].relation_type IN (…)
  repeated string             relation_created_by_asset_ids = 24; // relations[].created_by_asset_id IN (…)
  repeated string             relation_involves_object_types = 25; // relations where either object_a OR object_b has type IN (…)
  repeated string             relation_involves_report_scoped_ids = 26; // relations where either object_a OR object_b has report_scoped_id IN (…)
  repeated string             relation_involves_global_ids = 27; // relations where either object_a OR object_b has global_id IN (…)
  repeated string             relation_involves_external_ids = 28; // relations where either object_a OR object_b has external_id IN (…)

  // ────── Pagination & sorting ──────
  int32                       page_size                  = 29; 
  string                      page_token                 = 30; // cursor
  SearchOrderBy               order_by                   = 31; // default = RELEVANCE
  bool                        ascending                  = 32; // default = false (DESC)
}

message SearchReportsResponse {
  // The page of reports that matched the query (already ordered & trimmed).
  repeated Report reports = 1;

  // Cursor for fetching the next page; empty when you're on the last page.
  string next_page_token = 2;

  // Per-report highlight information keyed by report ID.
  // Each HighlightResult lists the field name and one-or-more matched fragments
  // (e.g.  "…suspect vehicle…", "…dark-blue sedan…").
  map<string, HighlightResult> highlights = 3;

  // Total number of hits *before* pagination—useful for UI counters.
  int32 total_results = 4;
}

// -----------------------------------------------------------------------------
// ReportService
// -----------------------------------------------------------------------------
service ReportService {
  // Section CRUD
  rpc CreateReportSection       (CreateReportSectionRequest)  returns (ReportSection);
  rpc GetReportSection          (GetReportSectionRequest)     returns (ReportSection);
  rpc UpdateReportSection       (UpdateReportSectionRequest)  returns (ReportSection);
  rpc DeleteReportSection       (DeleteReportSectionRequest)  returns (google.protobuf.Empty);
  rpc ListReportSections        (ListReportSectionsRequest)   returns (ListReportSectionsResponse);

  // Report CRUD & metadata
  rpc CreateReport              (CreateReportRequest)         returns (CreateReportResponse);
  rpc GetReport                 (GetReportRequest)            returns (Report);
  rpc UpdateReport              (UpdateReportRequest)         returns (Report);
  rpc UpdateReportStatus        (UpdateReportStatusRequest)   returns (UpdateReportStatusResponse); 
  rpc ListReports               (ListReportsRequest)          returns (ListReportsResponse);
  rpc BatchGetReports           (BatchGetReportsRequest)      returns (BatchGetReportsResponse);
  rpc DeleteReport              (DeleteReportRequest)         returns (google.protobuf.Empty);

  // Filtered list RPCs
  rpc ListReportsBySituationId  (ListReportsBySituationIdRequest) returns (ListReportsResponse);
  rpc ListReportsByCaseId       (ListReportsByCaseIdRequest)      returns (ListReportsResponse);

  // Comments
  rpc AddComment                (AddCommentRequest)           returns (Comment);
  rpc GetComments               (GetCommentsRequest)          returns (GetCommentsResponse);
  rpc UpdateComment             (UpdateCommentRequest)        returns (Comment);
  rpc DeleteComment             (DeleteCommentRequest)        returns (google.protobuf.Empty);
  rpc ResolveComment            (ResolveCommentRequest)       returns (Comment);

  // Review workflow
  rpc SubmitForReview           (SubmitForReviewRequest)      returns (Report);
  rpc AddReviewRound            (AddReviewRoundRequest)       returns (ReviewRound);
  rpc GetReviewRound            (GetReviewRoundRequest)       returns (ReviewRound);
  rpc UpdateReviewRound         (UpdateReviewRoundRequest)    returns (ReviewRound);
  rpc DeleteReviewRound         (DeleteReviewRoundRequest)    returns (google.protobuf.Empty);
  rpc ApproveReviewRound        (ApproveReviewRoundRequest)   returns (ReviewRound);
  rpc RequestChanges            (RequestChangesRequest)       returns (ReviewRound);
  rpc ListReviewRoundsForReport (ListReviewRoundsForReportRequest) returns (ListReviewRoundsForReportResponse);
  rpc GetEligibleReviewers      (GetEligibleReviewersRequest)     returns (GetEligibleReviewersResponse);

  // JSON metadata
  rpc UpdateAdditionalInfoJson  (UpdateAdditionalInfoJsonRequest) returns (UpdateAdditionalInfoJsonResponse);
  rpc GetAdditionalInfo         (GetAdditionalInfoRequest)        returns (GetAdditionalInfoResponse);

  // Versioning
  rpc GetReportVersion          (GetReportVersionRequest)      returns (ReportSnapshot);
  rpc ListReportVersions        (ListReportVersionsRequest)    returns (ListReportVersionsResponse);

  // Add the new search RPC
  rpc SearchReports             (SearchReportsRequest)            returns (SearchReportsResponse);

  // Relation CRUD
  rpc CreateRelation            (CreateRelationRequest)       returns (Relation);
  rpc GetRelation               (GetRelationRequest)          returns (Relation);
  rpc UpdateRelation            (UpdateRelationRequest)         returns (Relation);
  rpc DeleteRelation            (DeleteRelationRequest)         returns (google.protobuf.Empty);
  rpc ListRelations             (ListRelationsRequest)          returns (ListRelationsResponse);
}

// Package herosentry provides context capture functionality for the Hero Sentry SDK.
// This file contains functions for automatically capturing runtime context,
// authentication information, and request metadata to enrich spans and errors.
//
// The capture system extracts information from multiple sources:
// - Runtime stack for code location
// - Hero context for user and organization data
// - HTTP headers for request correlation
// - Environment variables for deployment context
//
// All captured data is automatically added to spans and errors to provide
// rich debugging information in Sentry.
package herosentry

import (
	cmncontext "common/context"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"os"
	"runtime"
	"strconv"
	"strings"
	"sync"

	"github.com/getsentry/sentry-go"
)

// warnOnce ensures we only log the "not initialized" warning once per service
var warnOnce sync.Once

// errorFingerprint represents a unique identifier for an error within a trace.
// It only uses the error's string representation to detect duplicates,
// ignoring the message so the same error with different messages is still deduplicated.
//
// Example: An error "invalid email format" captured in usecase layer with message
// "validation failed" will have the same fingerprint as when captured in connect
// layer with message "CreateSituation failed", preventing duplicate Sentry events.
type errorFingerprint string

// capturedErrorsMap stores the errors that have been captured in the current trace.
// This prevents duplicate captures when the same error flows through multiple layers.
// The map is initialized at request entry points (interceptors/middleware) and
// shared across all layers via context, allowing them to coordinate deduplication.
type capturedErrorsMap struct {
	mu     sync.RWMutex
	errors map[errorFingerprint]bool // true if error has been sent to Sentry
}

// initializeCapturedErrorsMap creates and adds a new captured errors map to the context.
// This MUST be called at the start of each request/transaction to enable deduplication.
//
// Called by:
//   - RPCServiceInterceptor: When creating new transactions or continuing distributed traces
//   - HTTPMiddleware: For all HTTP requests
//   - StartTransaction: For manually created transactions
//   - StartSpan: When creating root transactions
//
// The returned context contains a pointer to the map, which is shared across all layers.
func initializeCapturedErrorsMap(ctx context.Context) context.Context {
	capturedMap := &capturedErrorsMap{
		errors: make(map[errorFingerprint]bool),
	}
	return context.WithValue(ctx, capturedErrorsContextKey, capturedMap)
}

// getCapturedErrorsMap retrieves the captured errors map from context.
// Returns nil if not found (which means deduplication is not active for this request).
//
// When nil is returned, CaptureException will:
//   - Allow the error to be captured (no deduplication)
//   - This ensures backward compatibility for code not using interceptors
func getCapturedErrorsMap(ctx context.Context) *capturedErrorsMap {
	if value := ctx.Value(capturedErrorsContextKey); value != nil {
		if capturedMap, ok := value.(*capturedErrorsMap); ok {
			return capturedMap
		}
	}
	return nil
}

// markErrorAsCaptured marks an error as captured in the context to prevent duplicate captures.
// This modifies the captured errors map in place since it's a pointer.
func markErrorAsCaptured(ctx context.Context, err error) {
	if err == nil {
		return
	}

	capturedMap := getCapturedErrorsMap(ctx)
	if capturedMap == nil {
		// No deduplication map in context, skip marking
		return
	}

	// Create fingerprint using only error string (ignoring message)
	fingerprint := errorFingerprint(err.Error())

	// Mark as captured
	capturedMap.mu.Lock()
	capturedMap.errors[fingerprint] = true
	capturedMap.mu.Unlock()
}

// isErrorAlreadyCaptured checks if an error has already been captured in the current trace.
// This prevents the same error from being sent to Sentry multiple times.
func isErrorAlreadyCaptured(ctx context.Context, err error) bool {
	if err == nil {
		return false
	}

	capturedMap := getCapturedErrorsMap(ctx)
	if capturedMap == nil {
		// No deduplication map in context, allow capture
		return false
	}

	// Create fingerprint to check (ignoring message)
	fingerprint := errorFingerprint(err.Error())

	// Check if already captured
	capturedMap.mu.RLock()
	captured := capturedMap.errors[fingerprint]
	capturedMap.mu.RUnlock()

	return captured
}

// shouldCaptureErrorWithType determines if an error should be captured based on its type and configuration.
// This function checks the error type against the configured filters.
//
// Parameters:
//   - err: The error to check
//   - errorType: The explicit category of the error
//
// Returns:
//   - bool: true if the error should be captured, false if it should be filtered
//
// The function returns true (capture) by default for backward compatibility.
// Errors are only filtered if explicitly configured during Init().
func shouldCaptureErrorWithType(err error, errorType ErrorType) bool {
	if err == nil {
		return false
	}

	// Get the current configuration
	config := getErrorCaptureConfig()
	if config == nil {
		// No config means capture everything (backward compatibility)
		return true
	}

	// Check based on explicit error type if provided
	if errorType != ErrorTypeUnknown {
		switch errorType {
		case ErrorTypeValidation:
			return config.CaptureValidationErrors
		case ErrorTypeNotFound:
			return config.CaptureNotFoundErrors
		case ErrorTypeConflict:
			return config.CaptureConflictErrors
		case ErrorTypeUnauthorized:
			return config.CaptureUnauthorizedErrors
		case ErrorTypeForbidden:
			return config.CaptureForbiddenErrors
		case ErrorTypeDatabase:
			return config.CaptureDatabaseErrors
		case ErrorTypeExternal:
			return config.CaptureExternalErrors
		case ErrorTypeInternal:
			return config.CaptureInternalErrors
		}
	}

	// Fall back to the old shouldCaptureError logic for backward compatibility
	return shouldCaptureError(err)
}

// shouldCaptureError determines if an error should be captured based on configuration.
// This function checks the error type against the configured filters.
//
// Parameters:
//   - err: The error to check
//
// Returns:
//   - bool: true if the error should be captured, false if it should be filtered
//
// The function returns true (capture) by default for backward compatibility.
// Errors are only filtered if explicitly configured during Init().
// DEPRECATED: Use shouldCaptureErrorWithType instead for explicit error categorization.
func shouldCaptureError(err error) bool {
	if err == nil {
		return false
	}

	// Get the current configuration
	config := getErrorCaptureConfig()
	if config == nil {
		// No config means capture everything (backward compatibility)
		return true
	}

	// Check for DomainError type using reflection to avoid import cycle
	// DomainError has fields: Type (int), Message (string), Err (error)
	errType := fmt.Sprintf("%T", err)
	if strings.Contains(errType, "DomainError") {
		// The DomainError.Error() method formats errors with specific patterns
		// We can detect the error type from these patterns
		errStr := err.Error()

		// Check for not found errors (ErrorTypeNotFound)
		// Pattern: "{resource} not found"
		if strings.HasSuffix(errStr, " not found") || strings.Contains(errStr, " not found:") {
			return config.CaptureNotFoundErrors
		}

		// Check for validation errors (ErrorTypeValidation)
		// Pattern: "validation error on {field}: {message}"
		if strings.Contains(errStr, "validation error on ") {
			return config.CaptureValidationErrors
		}

		// Check for conflict errors (ErrorTypeConflict)
		// Pattern: "{resource} already exists"
		if strings.HasSuffix(errStr, " already exists") || strings.Contains(errStr, " already exists:") {
			return config.CaptureConflictErrors
		}

		// If we can't determine the specific DomainError type, fall through to pattern matching
	}

	// Check for standard database not found errors
	if errors.Is(err, sql.ErrNoRows) {
		return config.CaptureNotFoundErrors
	}

	// Check error message patterns for common cases
	errMsg := strings.ToLower(err.Error())

	// Not found patterns
	if strings.Contains(errMsg, "not found") || strings.Contains(errMsg, "does not exist") {
		return config.CaptureNotFoundErrors
	}

	// Validation patterns
	if strings.Contains(errMsg, "invalid") || strings.Contains(errMsg, "validation") ||
		strings.Contains(errMsg, "required") || strings.Contains(errMsg, "must be") {
		return config.CaptureValidationErrors
	}

	// Conflict patterns
	if strings.Contains(errMsg, "already exists") || strings.Contains(errMsg, "duplicate") ||
		strings.Contains(errMsg, "conflict") {
		return config.CaptureConflictErrors
	}

	// Permission patterns
	if strings.Contains(errMsg, "unauthorized") || strings.Contains(errMsg, "not authorized") {
		return config.CaptureUnauthorizedErrors
	}
	if strings.Contains(errMsg, "forbidden") || strings.Contains(errMsg, "permission") ||
		strings.Contains(errMsg, "access denied") {
		return config.CaptureForbiddenErrors
	}

	// Default: capture all other errors
	return true
}

// captureRuntimeInfo captures file, line, and function information from the caller.
// This provides code location context for debugging in Sentry.
//
// Parameters:
//   - skipFrames: Number of stack frames to skip (usually 2 for the capture function and its caller)
//
// Returns:
//   - fileName: Source file path, relative to working directory if possible
//   - lineNumber: Line number in the source file
//   - functionName: Function name, cleaned for readability
//
// Example output:
//
//	fileName: "services/workflow/internal/usecase.go"
//	lineNumber: 42
//	functionName: "(*OrderUsecase).CreateOrder"
//
// The function handles various edge cases:
// - Returns "unknown" values if runtime information is unavailable
// - Cleans up function names to remove package paths
// - Makes file paths relative to improve readability
func captureRuntimeInfo(skipFrames int) (fileName string, lineNumber int, functionName string) {
	// Get caller information from runtime
	programCounter, fileName, lineNumber, success := runtime.Caller(skipFrames)
	if !success {
		// Unable to get runtime info, return defaults
		return "unknown", 0, "unknown"
	}

	// Extract function name from program counter
	function := runtime.FuncForPC(programCounter)
	if function != nil {
		functionName = function.Name()

		// Clean up function name for readability
		// Original: "github.com/company/hero-core/services/workflow/internal.(*OrderUsecase).CreateOrder"
		// After cleanup: "(*OrderUsecase).CreateOrder"

		// Remove package path (everything before last slash)
		if lastSlashIndex := strings.LastIndex(functionName, "/"); lastSlashIndex >= 0 {
			functionName = functionName[lastSlashIndex+1:]
		}

		// Remove remaining package name before first dot
		if dotIndex := strings.Index(functionName, "."); dotIndex >= 0 {
			functionName = functionName[dotIndex+1:]
		}
	}

	// Make file path relative to working directory for better readability
	// This converts absolute paths to relative ones in Sentry UI
	if workingDirectory, workingDirError := os.Getwd(); workingDirError == nil {
		if strings.HasPrefix(fileName, workingDirectory) {
			// Remove working directory prefix
			fileName = strings.TrimPrefix(fileName, workingDirectory)
			// Remove leading slash
			fileName = strings.TrimPrefix(fileName, "/")
		}
	}

	return fileName, lineNumber, functionName
}

// captureAuthContext extracts authentication context from the Hero context.
// This captures user identity and organization information automatically.
//
// Parameters:
//   - context: The context containing Hero authentication data
//
// Returns:
//   - username: User identifier (e.g., "cognito:abc123", "bot:worker-service")
//   - organizationID: Organization ID from JWT claims
//   - ipAddress: Client IP address from request headers
//
// Example outputs:
//
//	username: "cognito:550e8400-e29b-41d4-a716-446655440000"
//	organizationID: 42
//	ipAddress: "*************"
//
// The function uses Hero's context helpers which extract data from:
// - JWT tokens in Authorization headers
// - X-Forwarded-For headers for IP addresses
// - Custom Hero headers for service-to-service calls
func captureAuthContext(context context.Context) (username string, organizationID int32, ipAddress string) {
	// Extract username from context
	// Format: "cognito:sub" for users, "bot:service-name" for services
	username = cmncontext.GetUsername(context)

	// Extract organization ID from JWT claims
	organizationID = cmncontext.GetOrgId(context)

	// Extract client IP address from headers
	// Checks X-Forwarded-For, X-Real-IP, etc.
	ipAddress = cmncontext.GetIPAddress(context)

	return username, organizationID, ipAddress
}

// captureRequestContext extracts request-related information from context.
// This captures request correlation IDs for tracing requests across systems.
//
// Parameters:
//   - context: The context potentially containing HTTP request data
//
// Returns:
//   - requestID: Request correlation ID from headers, or empty string
//
// The function checks common header names used by various systems:
// - X-Request-ID: Standard request ID header
// - X-Amzn-Request-Id: AWS API Gateway request ID
// - X-Correlation-ID: Alternative correlation header
//
// Example output:
//
//	requestID: "550e8400-e29b-41d4-a716-446655440000"
func captureRequestContext(context context.Context) (requestID string) {
	// Extract HTTP request from context if available
	if httpRequest, isHTTPRequest := context.Value(httpRequestContextKey).(*http.Request); isHTTPRequest {
		// Check common request ID headers in order of preference

		// Standard request ID header
		requestID = httpRequest.Header.Get("X-Request-ID")
		if requestID == "" {
			// AWS API Gateway request ID
			requestID = httpRequest.Header.Get("X-Amzn-Request-Id")
		}
		if requestID == "" {
			// Alternative correlation header
			requestID = httpRequest.Header.Get("X-Correlation-ID")
		}
	}

	return requestID
}

// captureEnvironment gets the environment from environment variable.
// This determines the deployment environment for filtering in Sentry.
//
// Returns:
//   - string: Environment name ("demo-1", "prod-1", "development")
//
// The function reads the ENVIRONMENT variable and defaults to "development"
// if not set. This matches the behavior in the Init() function.
//
// Example outputs:
//   - "prod-1" (when ENVIRONMENT=prod-1)
//   - "development" (when ENVIRONMENT is not set)
func captureEnvironment() string {
	environment := os.Getenv("ENVIRONMENT")
	if environment == "" {
		// Default to development for safety
		return "development"
	}
	return environment
}

// enrichSpanWithContext adds all auto-captured context to a span.
// This is the main function that populates spans with rich debugging information.
//
// Parameters:
//   - span: The internal span to enrich
//   - context: The context containing authentication and request data
//   - skipFrames: Stack frames to skip for accurate code location
//
// The function captures and adds:
// - Code location (file, line, function)
// - User context (username, org ID, IP)
// - Request context (request ID)
// - Environment information
// - Service name
//
// All data is added as tags for searchability in Sentry.
//
// Example tags added:
//
//	"code.file": "services/workflow/internal/usecase.go"
//	"code.line": "42"
//	"code.function": "(*OrderUsecase).CreateOrder"
//	"user.username": "cognito:abc123"
//	"user.type": "cognito"
//	"org.id": "42"
//	"user.ip_address": "*************"
//	"request.id": "550e8400-e29b-41d4-a716"
//	"environment": "production"
//	"service": "workflow-service"
func enrichSpanWithContext(span *internalSpan, context context.Context, skipFrames int) {
	// Capture all context information
	// Each capture function is designed to never fail

	// 1. Code location for debugging
	span.file, span.line, span.function = captureRuntimeInfo(skipFrames)

	// 2. Authentication and authorization context
	span.username, span.orgID, span.ipAddress = captureAuthContext(context)

	// 3. Request correlation information
	span.requestID = captureRequestContext(context)

	// 4. Deployment environment
	span.environment = captureEnvironment()

	// Add all captured data as searchable tags
	if span.Span != nil {
		// Code location tags for finding where spans originate
		span.SetTag("code.file", span.file)
		span.SetTag("code.line", strconv.Itoa(span.line))
		span.SetTag("code.function", span.function)

		// User authentication tags
		if span.username != "" {
			span.SetTag("user.username", span.username)

			// Parse username format to determine user type
			// This helps filter by user type in Sentry
			if strings.HasPrefix(span.username, "cognito:") {
				// Cognito authenticated user
				span.SetTag("user.type", "cognito")
				span.SetTag("user.cognito_id", strings.TrimPrefix(span.username, "cognito:"))
			} else if strings.HasPrefix(span.username, "bot:") {
				// Service-to-service authentication
				span.SetTag("user.type", "bot")
				span.SetTag("user.bot_name", strings.TrimPrefix(span.username, "bot:"))
			} else {
				// API key or other authentication
				span.SetTag("user.type", "api")
			}
		} else {
			// Explicitly mark when user is not found
			span.SetTag("user.username", "not_found")
		}

		// Organization context for multi-tenant filtering
		if span.orgID > 0 {
			span.SetTag("org.id", strconv.Itoa(int(span.orgID)))
		} else {
			// Explicitly mark when org ID is not found
			span.SetTag("org.id", "not_found")
		}

		// IP address for security and debugging
		if span.ipAddress != "" {
			span.SetTag("user.ip_address", span.ipAddress)
		} else {
			// Explicitly mark when IP address is not found
			span.SetTag("user.ip_address", "not_found")
		}

		// Request correlation for distributed debugging
		if span.requestID != "" {
			span.SetTag("request.id", span.requestID)
		}

		// Environment tag (also set globally, but included for consistency)
		span.SetTag("environment", span.environment)

		// Service identifier for filtering by service
		span.SetTag("service", getServiceName())
	}
}

// CaptureException sends an error to Sentry with full context and an optional message.
// This is the primary method for reporting errors in Hero services.
//
// The function automatically captures:
// - Error type and message
// - Stack trace
// - User context (username, org, IP)
// - Code location where error was reported
// - Request correlation ID
// - Environment and service information
//
// Parameters:
//   - context: The context containing authentication and request data
//   - errorToCapture: The error to report to Sentry
//   - args: Variable arguments that can include:
//   - message (string): Additional context message
//   - forceCapture (bool): Override configured filters and always capture
//
// The function is safe to call with nil errors or when Sentry is not initialized.
// In these cases, it's a no-op.
//
// Example usage:
//
//	// Without message
//	order, err := db.GetOrder(ctx, orderID)
//	if err != nil {
//	    herosentry.CaptureException(ctx, err)
//	    return nil, fmt.Errorf("failed to get order: %w", err)
//	}
//
//	// With message for additional context
//	if err := processPayment(ctx, payment); err != nil {
//	    herosentry.CaptureException(ctx, err, "Payment processing failed for order "+orderID)
//	    return nil, err
//	}
//
//	// Force capture even if configured to filter
//	if err := validateEmail(email); err != nil {
//	    // This validation error is critical, force capture despite config
//	    herosentry.CaptureException(ctx, err, "Critical email validation failure", true)
//	    return err
//	}
//
// Example Sentry output:
//
//	Message: Payment processing failed for order ORD-12345
//	Error: pq: relation "orders" does not exist
//	Type: *pq.Error
//
//	Tags:
//	  error.file: services/workflow/internal/repo.go
//	  error.line: 145
//	  error.function: (*OrderRepo).GetByID
//	  user.username: cognito:abc123
//	  org.id: 42
//	  request.id: 550e8400-e29b-41d4
//	  error.type: *pq.Error
//
//	User:
//	  Username: cognito:abc123
//	  IP: *************
//
// Best practices:
// - Always capture errors at the point they occur
// - Include context about what operation failed
// - Use forceCapture sparingly, only for critical errors that must be tracked
// - Configure error filtering at service initialization for routine errors
func CaptureException(ctx context.Context, errorToCapture error, args ...interface{}) {
	// Skip if no error
	if errorToCapture == nil {
		return
	}

	// Warn if Sentry not initialized
	if !isInitialized() {
		// Log warning only once per service to avoid log spam
		warnOnce.Do(func() {
			fmt.Printf("WARNING: herosentry.CaptureException called but herosentry is not initialized. Errors will not be sent to Sentry. Call herosentry.Init() in your main function to enable observability.\n")
		})
		return
	}

	// Parse variable arguments
	var message string
	var forceCapture bool
	var errorType ErrorType = ErrorTypeUnknown

	for _, arg := range args {
		switch v := arg.(type) {
		case string:
			message = v
		case bool:
			forceCapture = v
		case ErrorType:
			errorType = v
		}
	}

	// Check if error should be captured based on configuration
	// Skip this check if forceCapture is true
	if !forceCapture && !shouldCaptureErrorWithType(errorToCapture, errorType) {
		// Error is filtered by configuration, skip capture
		return
	}

	// Check if this error has already been captured in this trace
	// This prevents the same error from being sent multiple times as it flows through layers:
	// Repository -> UseCase -> Connect Handler -> Middleware
	if isErrorAlreadyCaptured(ctx, errorToCapture) {
		// Skip duplicate capture - error was already sent to Sentry
		// The first capture (closest to error origin) preserves the most relevant context
		return
	}

	// Get Sentry hub from context or use global hub
	sentryHub := sentry.GetHubFromContext(ctx)
	if sentryHub == nil {
		sentryHub = sentry.CurrentHub()
	}

	// Clone hub to avoid modifying shared state
	sentryHub = sentryHub.Clone()

	// Configure scope with all context information
	sentryHub.WithScope(func(scope *sentry.Scope) {
		// 1. Set user context for the error
		username, organizationID, ipAddress := captureAuthContext(ctx)
		if username != "" {
			// Set user in Sentry's user context
			scope.SetUser(sentry.User{
				Username:  username,
				IPAddress: ipAddress,
			})
			// Also add as tag for searching
			scope.SetTag("user.username", username)
		} else {
			// Explicitly mark when user is not found
			scope.SetTag("user.username", "not_found")
		}

		// 2. Add organization context
		if organizationID > 0 {
			scope.SetTag("org.id", strconv.Itoa(int(organizationID)))
		} else {
			// Explicitly mark when org ID is not found
			scope.SetTag("org.id", "not_found")
		}

		// Add IP address tag
		if ipAddress != "" {
			scope.SetTag("user.ip_address", ipAddress)
		} else {
			// Explicitly mark when IP address is not found
			scope.SetTag("user.ip_address", "not_found")
		}

		// 3. Capture where the error was reported from
		// Skip 4 frames: captureRuntimeInfo, closure, WithScope, and CaptureException
		fileName, lineNumber, functionName := captureRuntimeInfo(4)
		scope.SetTag("error.file", fileName)
		scope.SetTag("error.line", strconv.Itoa(lineNumber))
		scope.SetTag("error.function", functionName)

		// 4. Add request correlation
		if requestID := captureRequestContext(ctx); requestID != "" {
			scope.SetTag("request.id", requestID)
		}

		// 5. Add error type for filtering
		// Example: "*pq.Error", "*errors.errorString"
		scope.SetTag("error.type", fmt.Sprintf("%T", errorToCapture))

		// 6. Add error category for better filtering and analysis
		scope.SetTag("error.category", errorType.String())

		// 7. Add user message if provided
		if message != "" {
			scope.SetContext("Additional Context", map[string]interface{}{
				"message": message,
			})
		}

		// 8. Format the error title for better readability using event processor
		// This modifies how the error appears in Sentry's UI
		scope.AddEventProcessor(func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
			if event == nil {
				return event
			}

			service := getServiceName()
			if service == "" {
				service = "Unknown"
			}

			// Modify exception titles to be more readable
			for i := range event.Exception {
				exception := &event.Exception[i]

				// Determine the title based on available information
				var formattedTitle string

				// Priority 1: Use custom message if provided
				if message != "" {
					formattedTitle = fmt.Sprintf("[%s] %s", service, message)
				} else if exception.Value != "" {
					// Priority 2: Use exception value (error message)
					formattedTitle = fmt.Sprintf("[%s] %s", service, exception.Value)
				} else {
					// Priority 3: Fallback to error string
					formattedTitle = fmt.Sprintf("[%s] %s", service, errorToCapture.Error())
				}

				// Override the exception type (which is used as the issue title in Sentry)
				exception.Type = formattedTitle

				// Store original type for reference
				if event.Extra == nil {
					event.Extra = make(map[string]interface{})
				}
				event.Extra["original_error_type"] = fmt.Sprintf("%T", errorToCapture)
			}

			// Also set the message for non-exception events
			if len(event.Exception) == 0 && event.Message != "" {
				event.Message = fmt.Sprintf("[%s] %s", service, event.Message)
			}

			// Add service tag for easy filtering
			if event.Tags == nil {
				event.Tags = make(map[string]string)
			}
			event.Tags["service"] = service

			return event
		})

		// Send the error to Sentry with all context
		sentryHub.CaptureException(errorToCapture)

		// Mark error as captured to prevent duplicates
		// The captured errors map is modified in place (it's a pointer in context),
		// so all subsequent calls with the same context will see this error as captured
		markErrorAsCaptured(ctx, errorToCapture)
	})
}

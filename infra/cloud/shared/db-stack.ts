import * as cdk from "aws-cdk-lib";
import * as cloudwatch from "aws-cdk-lib/aws-cloudwatch";
import * as actions from "aws-cdk-lib/aws-cloudwatch-actions";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as kms from 'aws-cdk-lib/aws-kms';
import * as rds from "aws-cdk-lib/aws-rds";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as sns from "aws-cdk-lib/aws-sns";
import { Construct } from "constructs";
import { AppConfig } from "../../cloud-config/config";

export interface PostgresRdsStackProps extends cdk.StackProps {
  dbName: string;
  username: string;
  vpc: ec2.Vpc;
  config: AppConfig;
  dbKey: kms.IKey;
  pagerDutyTopics?: {
    critical: sns.ITopic;
    high: sns.ITopic;
    info: sns.ITopic;
  }
}

export class PostgresRdsStack extends cdk.Stack {
  public readonly dbSecret: secretsmanager.ISecret;
  public readonly userSecret: secretsmanager.ISecret;
  public readonly dbInstance: rds.DatabaseInstance;
  constructor(scope: Construct, id: string, props: PostgresRdsStackProps) {
    super(scope, id, props);

    const { dbName, username, vpc, config, dbKey } = props;


    // Create Security Group for the RDS Instance
    const dbSecurityGroup = new ec2.SecurityGroup(this, `${id}RdsSecurityGroup`, {
      vpc,
      description: "Allow PostgreSQL access",
      allowAllOutbound: true,
    });

    // Open port 5432 for database connections
    dbSecurityGroup.addIngressRule(ec2.Peer.ipv4(vpc.vpcCidrBlock), ec2.Port.tcp(5432), "Allow Postgres access from within the VPC");

    // Create Secrets Manager credentials for RDS
    let secretName = 'DBCredentials';
    if (dbName !== 'hero') {
      secretName = dbName + 'DBCredentials';
    }
    const dbSecret = new secretsmanager.Secret(this, secretName, {
      secretName: "Postgres" + secretName,
      generateSecretString: {
        secretStringTemplate: JSON.stringify({ username: "postgres" }),
        generateStringKey: "password",
        excludePunctuation: true,
        includeSpace: false,
        passwordLength: 16,
      },
    });

    // Create a new RDS instance
    const rdsInstance = new rds.DatabaseInstance(this, `${id}RdsInstance`, {
      engine: rds.DatabaseInstanceEngine.postgres({
        version: rds.PostgresEngineVersion.VER_17,
      }),
      instanceType: ec2.InstanceType.of(
        config.db[dbName as keyof typeof config.db].instanceClass as ec2.InstanceClass,
        config.db[dbName as keyof typeof config.db].instanceSize as ec2.InstanceSize,
      ),
      vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
      },
      credentials: rds.Credentials.fromSecret(dbSecret),
      databaseName: dbName,
      securityGroups: [dbSecurityGroup],
      publiclyAccessible: false,
      storageEncrypted: true,
      storageEncryptionKey: dbKey,
      autoMinorVersionUpgrade: true,
      iamAuthentication: true,
      multiAz: config.db[dbName as keyof typeof config.db].multiAz,
      allowMajorVersionUpgrade: false,
      backupRetention: cdk.Duration.days(7),
      deleteAutomatedBackups: false,
      removalPolicy: cdk.RemovalPolicy.SNAPSHOT,
    });

    // HACK - force the server secret name to be the same as it was before
    // this stack became parameterized. otherwise, the secret will be replaced
    const usernameFirstLetterCapitalized = username.charAt(0).toUpperCase() + username.slice(1);
    // Create a new secret for the server user
    const userSecret = new secretsmanager.Secret(this, usernameFirstLetterCapitalized + 'UserSecret', {
      secretName: usernameFirstLetterCapitalized + 'UserSecret',
      generateSecretString: {
        secretStringTemplate: cdk.Stack.of(this).toJsonString({
          username,
          host: rdsInstance.instanceEndpoint.hostname,
          port: rdsInstance.instanceEndpoint.port,
          dbname: dbName,
        }),
        generateStringKey: 'password',
        excludePunctuation: true,
        includeSpace: false,
        passwordLength: 16,
      },
    });

    // 6. Output the RDS Endpoint
    new cdk.CfnOutput(this, "DBEndpoint", {
      value: rdsInstance.instanceEndpoint.hostname,
      description: "RDS PostgreSQL Endpoint",
    });

    // 7. Output the server user secret
    new cdk.CfnOutput(this, "ServerUserSecretOutput", {
      value: userSecret.secretArn,
      description: "User Secret ARN",
    });

    this.dbSecret = dbSecret;
    this.userSecret = userSecret;
    this.dbInstance = rdsInstance;

    // CloudWatch Alarms for primary DB stability and performance
    const alarmPeriod = cdk.Duration.minutes(5);

    // Determine DB connection threshold based on instance size
    const instanceSizeFromConfig = String(
      config.db[dbName as keyof typeof config.db].instanceSize
    ).toLowerCase();
    const defaultMaxConnectionsByInstanceSize: Record<string, number> = {
      micro: 100,
      small: 200,
      medium: 450,
      large: 900,
      xlarge: 1800,
      '2xlarge': 3600,
      '4xlarge': 2000,
      '8xlarge': 4000,
      '12xlarge': 5000,
      '16xlarge': 5000,
      '24xlarge': 5000,
      '32xlarge': 5000,
    };
    const baseConnections = defaultMaxConnectionsByInstanceSize[instanceSizeFromConfig];
    const dbConnectionsThreshold = baseConnections ? Math.floor(baseConnections * 0.8) : 100;

    const cpuHighAlarm = new cloudwatch.Alarm(this, `${id}CpuUtilizationHigh`, {
      alarmName: `${dbName}-db-cpu-utilization-high`,
      alarmDescription: `High CPU utilization on ${dbName} primary DB instance`,
      metric: rdsInstance.metricCPUUtilization({
        period: alarmPeriod,
        statistic: 'Average',
      }),
      threshold: 80,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const freeStorageLowAlarm = new cloudwatch.Alarm(this, `${id}FreeStorageSpaceLow`, {
      alarmName: `${dbName}-db-free-storage-space-low`,
      alarmDescription: `Low free storage space on ${dbName} primary DB instance`,
      metric: rdsInstance.metricFreeStorageSpace({
        period: alarmPeriod,
        statistic: 'Average',
      }),
      // 1 GiB in bytes
      threshold: 1 * 1024 * 1024 * 1024,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.LESS_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const freeableMemoryLowAlarm = new cloudwatch.Alarm(this, `${id}FreeableMemoryLow`, {
      alarmName: `${dbName}-db-freeable-memory-low`,
      alarmDescription: `Low freeable memory on ${dbName} primary DB instance`,
      metric: rdsInstance.metricFreeableMemory({
        period: alarmPeriod,
        statistic: 'Average',
      }),
      // 100 MiB in bytes
      threshold: 100 * 1024 * 1024,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.LESS_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const dbConnectionsHighAlarm = new cloudwatch.Alarm(this, `${id}DatabaseConnectionsHigh`, {
      alarmName: `${dbName}-db-connections-high`,
      alarmDescription: `High number of database connections on ${dbName} primary DB instance`,
      metric: rdsInstance.metricDatabaseConnections({
        period: alarmPeriod,
        statistic: 'Average',
      }),
      // Threshold varies by instance size (hard-coded map above)
      threshold: dbConnectionsThreshold,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const readLatencyHighAlarm = new cloudwatch.Alarm(this, `${id}ReadLatencyHigh`, {
      alarmName: `${dbName}-db-read-latency-high`,
      alarmDescription: `High read latency on ${dbName} primary DB instance`,
      metric: new cloudwatch.Metric({
        namespace: 'AWS/RDS',
        metricName: 'ReadLatency',
        dimensionsMap: { DBInstanceIdentifier: rdsInstance.instanceIdentifier },
        period: alarmPeriod,
        statistic: 'Average',
      }),
      // RDS latency metrics are in seconds. 0.05s = 50ms
      threshold: 0.05,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const writeLatencyHighAlarm = new cloudwatch.Alarm(this, `${id}WriteLatencyHigh`, {
      alarmName: `${dbName}-db-write-latency-high`,
      alarmDescription: `High write latency on ${dbName} primary DB instance`,
      metric: new cloudwatch.Metric({
        namespace: 'AWS/RDS',
        metricName: 'WriteLatency',
        dimensionsMap: { DBInstanceIdentifier: rdsInstance.instanceIdentifier },
        period: alarmPeriod,
        statistic: 'Average',
      }),
      threshold: 0.05,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const diskQueueDepthHighAlarm = new cloudwatch.Alarm(this, `${id}DiskQueueDepthHigh`, {
      alarmName: `${dbName}-db-disk-queue-depth-high`,
      alarmDescription: `High disk queue depth on ${dbName} primary DB instance`,
      metric: new cloudwatch.Metric({
        namespace: 'AWS/RDS',
        metricName: 'DiskQueueDepth',
        dimensionsMap: { DBInstanceIdentifier: rdsInstance.instanceIdentifier },
        period: alarmPeriod,
        statistic: 'Average',
      }),
      threshold: 64,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    const swapUsageHighAlarm = new cloudwatch.Alarm(this, `${id}SwapUsageHigh`, {
      alarmName: `${dbName}-db-swap-usage-high`,
      alarmDescription: `High swap usage on ${dbName} primary DB instance`,
      metric: new cloudwatch.Metric({
        namespace: 'AWS/RDS',
        metricName: 'SwapUsage',
        dimensionsMap: { DBInstanceIdentifier: rdsInstance.instanceIdentifier },
        period: alarmPeriod,
        statistic: 'Average',
      }),
      // 100 MiB in bytes
      threshold: 100 * 1024 * 1024,
      evaluationPeriods: 3,
      datapointsToAlarm: 3,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    // Wire DB alarms to centralized PagerDuty Alerts topics by severity (passed via props)
    const criticalAction = props.pagerDutyTopics ? new actions.SnsAction(props.pagerDutyTopics.critical) : undefined;
    const highAction = props.pagerDutyTopics ? new actions.SnsAction(props.pagerDutyTopics.high) : undefined;
    const infoAction = props.pagerDutyTopics ? new actions.SnsAction(props.pagerDutyTopics.info) : undefined;

    // Map alarms to severity topics
    if (highAction) { cpuHighAlarm.addAlarmAction(highAction); cpuHighAlarm.addOkAction(highAction); }

    if (criticalAction) { freeStorageLowAlarm.addAlarmAction(criticalAction); freeStorageLowAlarm.addOkAction(criticalAction); }

    if (highAction) { freeableMemoryLowAlarm.addAlarmAction(highAction); freeableMemoryLowAlarm.addOkAction(highAction); }

    if (highAction) { dbConnectionsHighAlarm.addAlarmAction(highAction); dbConnectionsHighAlarm.addOkAction(highAction); }

    if (highAction) { readLatencyHighAlarm.addAlarmAction(highAction); readLatencyHighAlarm.addOkAction(highAction); }

    if (highAction) { writeLatencyHighAlarm.addAlarmAction(highAction); writeLatencyHighAlarm.addOkAction(highAction); }

    if (highAction) { diskQueueDepthHighAlarm.addAlarmAction(highAction); diskQueueDepthHighAlarm.addOkAction(highAction); }

    if (infoAction) { swapUsageHighAlarm.addAlarmAction(infoAction); swapUsageHighAlarm.addOkAction(infoAction); }
  }
}
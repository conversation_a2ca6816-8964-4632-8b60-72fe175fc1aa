package main

import (
	database "common/database"
	"common/herosentry"
	"common/middleware"
	"context"
	"database/sql"
	"errors"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"

	filerepository "filerepository/internal"
	filerepositoryRepository "filerepository/internal/data"
)

// fatalWithFlush ensures Sentry events are sent before exiting
func fatalWithFlush(err error, msg string) {
	herosentry.CaptureException(context.Background(), err, herosentry.ErrorTypeInternal, msg)
	herosentry.Flush()
	log.Fatalf("%s: %v", msg, err)
}

func main() {
	// Initialize herosentry for error tracking and performance monitoring
	// Filter out validation and not-found errors to reduce noise
	falseVal := false
	err := herosentry.Init("filerepository-service", herosentry.Config{
		CustomSamplingRules: map[string]float64{
			// 1% sampling for all List operations
			"*Service.List*": 0.01,
		},
		// Filter out expected errors to reduce Sentry noise
		CaptureValidationErrors: &falseVal, // Don't capture validation/bad request errors
		CaptureNotFoundErrors:   &falseVal, // Don't capture 404/not found errors
	})
	if err != nil {
		log.Fatalf("Herosentry initialization failed: %v", err)
	}
	defer herosentry.Flush() //nolint:gocritic // fatalWithFlush handles flush before exit

	baseMux := http.NewServeMux()

	// Initialize PostgreSQL database
	databaseURL, err := database.CreateDBURL()
	if err != nil {
		fatalWithFlush(err, "Failed to create database URL")
	}

	postGresDB, err := sql.Open("postgres", databaseURL)
	if err != nil {
		fatalWithFlush(err, "Failed to open postgres database")
	}

	// Configure connection pool - file repository handles metadata and S3 operations
	// File ops are less frequent but may have longer transactions
	postGresDB.SetMaxOpenConns(40) // Moderate allocation for file metadata
	postGresDB.SetMaxIdleConns(10) // 25% idle connections
	postGresDB.SetConnMaxLifetime(5 * time.Minute)
	postGresDB.SetConnMaxIdleTime(90 * time.Second)

	// Initialize AWS S3 client
	ctx := context.Background()
	awsConfig, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-west-2"))
	if err != nil {
		fatalWithFlush(err, "Failed to load AWS config")
	}
	s3Client := s3.NewFromConfig(awsConfig)

	// Get S3 bucket name from environment
	s3BucketName := os.Getenv("AWS_S3_BUCKET_NAME")
	if s3BucketName == "" {
		fatalWithFlush(errors.New("AWS_S3_BUCKET_NAME environment variable is required"), "AWS_S3_BUCKET_NAME environment variable is required")
	}

	// Initialize FileRepository Repository
	filerepositoryRepo, filerepositoryDB, err := filerepositoryRepository.NewFileRepositoryRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize filerepository repository")
	}

	// Register all endpoints with S3 dependencies
	filerepository.RegisterRoutes(baseMux, filerepositoryDB, filerepositoryRepo, s3Client, s3BucketName)

	// Wrap with database pool monitoring middleware
	mux := herosentry.DBPoolMiddleware(postGresDB)(baseMux)

	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.filerepository.v1.FileRepositoryService",
		},
		HealthResponse: "FileRepository service is healthy",
	})

	// Create the server with health endpoints
	skipPerms := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"

	srv, err := middleware.NewServerWithHealth(
		mux,
		healthMux,
		!skipPerms,
	)
	if err != nil {
		fatalWithFlush(err, "Failed to create server")
	}

	if err := middleware.StartServer(srv); err != nil {
		fatalWithFlush(err, "Failed to serve")
	}
}

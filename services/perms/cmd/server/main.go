package main

import (
	database "common/database"
	"common/herosentry"
	"common/middleware"
	"context"
	"database/sql"
	"log"
	"net/http"
	"os"
	"time"

	perms "perms/internal"
	permsRepository "perms/internal/data"
	"perms/internal/setup"

	clients "common/clients/services"
)

// fatalWithFlush ensures Sentry events are sent before exiting
func fatalWithFlush(err error, msg string) {
	herosentry.CaptureException(context.Background(), err, herosentry.ErrorTypeInternal, msg)
	herosentry.Flush()
	log.Fatalf("%s: %v", msg, err)
}

func main() {
	// Initialize herosentry for error tracking and performance monitoring
	// Filter out validation and not-found errors to reduce noise
	falseVal := false
	err := herosentry.Init("perms-service", herosentry.Config{
		// Filter out expected errors to reduce Sentry noise
		CaptureValidationErrors: &falseVal, // Don't capture validation/bad request errors
		CaptureNotFoundErrors:   &falseVal, // Don't capture 404/not found errors
	})
	if err != nil {
		log.Fatalf("Herosentry initialization failed: %v", err)
	}
	defer herosentry.Flush() //nolint:gocritic // fatalWithFlush handles flush before exit

	baseMux := http.NewServeMux()

	databaseURL, err := database.CreateDBURL()
	if err != nil {
		fatalWithFlush(err, "Failed to create database URL")
	}
	postGresDB, openError := sql.Open("postgres", databaseURL)
	if openError != nil {
		fatalWithFlush(openError, "Failed to open postgres database")
	}

	// Configure connection pool - perms service handles authorization checks
	// Critical service: every API call requires permission checks
	postGresDB.SetMaxOpenConns(100) // High allocation for auth bottleneck prevention
	postGresDB.SetMaxIdleConns(25)  // 25% idle for burst handling
	postGresDB.SetConnMaxLifetime(5 * time.Minute)
	postGresDB.SetConnMaxIdleTime(90 * time.Second)

	assetClient := clients.NewAssetsClient(os.Getenv("WORKFLOW_SERVICE_URL"), herosentry.RPCClientInterceptor())

	// Initialize Perms Repository
	permRepo, permDB, err := permsRepository.NewPermsRepository(postGresDB)
	if err != nil {
		postGresDB.Close()
		fatalWithFlush(err, "Failed to initialize perms repository")
	}

	// Initialize setup
	setupResult, err := setup.InitializeStoreAndModel(setup.SetupConfig{
		PermsDB:   permDB,
		PermsRepo: permRepo,
		ModelPath: "./model.json",
		FgaApiUrl: os.Getenv("FGA_API_URL"),
	})
	if err != nil {
		postGresDB.Close()
		fatalWithFlush(err, "Failed to initialize OpenFGA setup")
	}

	// Register all endpoints on the base mux first
	perms.RegisterRoutes(baseMux, permDB, permRepo, assetClient, setupResult.FgaClient)

	// Wrap mux with database pool monitoring middleware
	mux := herosentry.DBPoolMiddleware(postGresDB)(baseMux)

	// Additional endpoints.
	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.permissions.v1.PermissionService",
		},
		HealthResponse: "YES HOW CAN I HELP YOU",
	})

	skipPerms := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"

	srv, err := middleware.NewServerWithHealth(
		mux,
		healthMux,
		!skipPerms,
	)
	if err != nil {
		fatalWithFlush(err, "Failed to create server")
	}

	if err := middleware.StartServer(srv); err != nil {
		postGresDB.Close()
		fatalWithFlush(err, "Failed to serve")
	}
}

package usecase

import (
	"context"
	"fmt"

	_ "github.com/lib/pq"

	cmncontext "common/context"
	"common/herosentry"
	assetproto "proto/hero/assets/v2"

	"connectrpc.com/connect"
	openfga "github.com/openfga/go-sdk/client"
)

// Define all object types the user might have direct relations with, for cleanup purposes
var UserObjectRelations = []ObjectType{ObjectTypeRole, ObjectTypeAction}

func (usecase *PermsUseCase) AddAssetToRole(ctx context.Context, assetId string, roleId string, roleName string, orgIdOverride int32) error {
	// Start span for adding asset to role
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.AddAssetToRole")
	defer finish()

	// Add context
	span.SetTag("asset.id", assetId)
	if roleId != "" {
		span.SetTag("role.id", roleId)
	}
	if roleName != "" {
		span.SetTag("role.name", roleName)
	}

	// first fetch the asset
	asset, err := usecase.assetClient.GetAsset(spanContext, connect.NewRequest(&assetproto.GetAssetRequest{Id: assetId}))
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to get asset %s", assetId))
		return fmt.Errorf("failed to get asset: %w", err)
	}
	if asset == nil {
		return fmt.Errorf("asset not found")
	}
	userId := assetToUserId(asset.Msg.Asset)

	return usecase.AddUserToRole(spanContext, userId, roleId, roleName, orgIdOverride)
}

func (usecase *PermsUseCase) AddUserToRole(ctx context.Context, userId UserId, roleId string, roleName string, orgIdOverride int32) error {
	// Start span for adding user to role
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.AddUserToRole")
	defer finish()

	// Add context
	span.SetTag("user.id", string(userId))
	if roleId != "" {
		span.SetTag("role.id", roleId)
	}
	if roleName != "" {
		span.SetTag("role.name", roleName)
	}

	// and fetch the role to confirm it exists
	if roleId != "" && roleName != "" {
		return fmt.Errorf("role_id and role_name cannot both be provided")
	}
	if roleId == "" && roleName == "" {
		return fmt.Errorf("role_id or role_name is required")
	}
	if roleId != "" {
		_, err := usecase.permsRepo.GetRole(spanContext, nil, roleId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get role %s", roleId))
			return fmt.Errorf("failed to get role: %w", err)
		}
	} else {
		role, err := usecase.permsRepo.GetRoleByName(spanContext, nil, roleName)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get role by name %s", roleName))
			return fmt.Errorf("failed to get role: %w", err)
		}
		roleId = role.Id
	}

	// Start a transaction for the outbox pattern
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for adding user to role")
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer deferRollback(tx)

	orgId := cmncontext.GetOrgId(spanContext)
	// allow meta org to specify an orgIdOverride
	// so that it can bootstrap the first admin user in a new org
	var userIdTupleKey string
	if orgId == -1 {
		userIdTupleKey = userToFGAUserOrgOverride(userId, orgIdOverride)
	} else {
		userIdTupleKey = userToFGAUser(spanContext, userId)
	}

	writes := []openfga.ClientTupleKey{
		{
			User:     userIdTupleKey,
			Relation: string(RelationMember),
			Object:   roleToFGAObject(roleId),
		},
	}

	// write the writes and deletes to the outbox
	var outboxId string
	outboxId, err = usecase.writeFGAOutbox(spanContext, tx, writes, nil)
	if err != nil {
		return fmt.Errorf("failed to apply role writes for '%s': %w", roleId, err)
	}

	// Log the deletion with filtered role details
	changes := map[string]interface{}{
		"assigned_role": roleId,
	}
	err = usecase.permsRepo.LogRoleAssignmentOperation(spanContext, tx, roleId, string(userId), "ASSIGN_ROLE", changes)
	if err != nil {
		return fmt.Errorf("failed to log role deletion: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction for role '%s': %w", roleId, err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return fmt.Errorf("failed to process FGA outbox for role '%s': %w", roleId, err)
		}
	}

	return nil
}

func (usecase *PermsUseCase) GetAssetRoles(ctx context.Context, assetId string, orgId int32) ([]string, error) {
	// Start span for getting asset roles
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.GetAssetRoles")
	defer finish()

	// Add context
	span.SetTag("asset.id", assetId)
	span.SetTag("org.id", fmt.Sprintf("%d", orgId))

	// first fetch the asset to check if it exists
	asset, err := usecase.assetClient.GetAsset(spanContext, connect.NewRequest(&assetproto.GetAssetRequest{Id: assetId}))
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to get asset %s", assetId))
		return nil, fmt.Errorf("failed to get asset: %w", err)
	}
	if asset == nil {
		return nil, fmt.Errorf("asset not found")
	}
	return usecase.GetUserRoles(spanContext, assetToUserId(asset.Msg.Asset), orgId)
}

func (usecase *PermsUseCase) GetUserRoles(ctx context.Context, userId UserId, orgId int32) ([]string, error) {
	// Start span for getting user roles
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.GetUserRoles")
	defer finish()

	// Add context
	span.SetTag("user.id", string(userId))
	span.SetTag("org.id", fmt.Sprintf("%d", orgId))

	var userIdTupleKey string
	if orgId != 0 {
		// allow an override to request roles for a specific org
		// used to determine if a user is a guest in an org
		userIdTupleKey = userToFGAUserOrgOverride(userId, orgId)
	} else {
		// otherwise, default to the asset's primary org
		userIdTupleKey = userToFGAUser(spanContext, userId)
	}
	roleType := string(ObjectTypeRole)
	roles, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &userIdTupleKey,
			Object: &roleType,
		}).
		Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	// extract the simple role list from the tuples
	roleList := make([]string, 0)
	for _, tuple := range roles.Tuples {
		roleId := FGARoleToRoleId(tuple.Key.Object)
		roleList = append(roleList, roleId)
	}

	// Track number of roles found
	span.SetTag("roles.count", fmt.Sprintf("%d", len(roleList)))

	return roleList, nil
}

func (usecase *PermsUseCase) RemoveUserFromRole(ctx context.Context, userId UserId, roleId string) error {
	// Start span for removing user from role
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.RemoveUserFromRole")
	defer finish()

	// Add context
	span.SetTag("user.id", string(userId))
	span.SetTag("role.id", roleId)

	// first fetch the role to check if it exists
	_, err := usecase.permsRepo.GetRole(spanContext, nil, roleId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get role %s", roleId))
		return err
	}
	roles, err := usecase.GetUserRoles(spanContext, userId, 0)
	if err != nil {
		return fmt.Errorf("failed to get user roles: %w", err)
	}
	hasRole := false
	for _, role := range roles {
		if role == roleId {
			hasRole = true
			break
		}
	}
	if !hasRole {
		return fmt.Errorf("user does not have the role")
	}

	// Start a transaction for the outbox pattern
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for removing user from role")
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer deferRollback(tx)

	deletes := []openfga.ClientTupleKeyWithoutCondition{
		{
			User:     userToFGAUser(spanContext, userId),
			Relation: string(RelationMember),
			Object:   roleToFGAObject(roleId),
		},
	}

	// write the writes and deletes to the outbox
	var outboxId string
	outboxId, err = usecase.writeFGAOutbox(spanContext, tx, nil, deletes)
	if err != nil {
		return fmt.Errorf("failed to apply role writes for '%s': %w", roleId, err)
	}

	// Log the deletion with filtered role details
	changes := map[string]interface{}{
		"removed_role": roleId,
	}
	err = usecase.permsRepo.LogRoleAssignmentOperation(spanContext, tx, roleId, string(userId), "REMOVE_ROLE", changes)
	if err != nil {
		return fmt.Errorf("failed to log role deletion: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction for role '%s': %w", roleId, err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return fmt.Errorf("failed to process FGA outbox for role '%s': %w", roleId, err)
		}
	}

	return nil
}

func (usecase *PermsUseCase) RemoveAssetFromRole(ctx context.Context, assetId string, roleId string) error {
	// Start span for removing asset from role
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.RemoveAssetFromRole")
	defer finish()

	// Add context
	span.SetTag("asset.id", assetId)
	span.SetTag("role.id", roleId)

	// first fetch the asset to check if it exists
	asset, err := usecase.assetClient.GetAsset(spanContext, connect.NewRequest(&assetproto.GetAssetRequest{Id: assetId}))
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to get asset %s", assetId))
		return fmt.Errorf("failed to get asset: %w", err)
	}
	if asset == nil {
		return fmt.Errorf("asset not found")
	}

	userId := assetToUserId(asset.Msg.Asset)

	return usecase.RemoveUserFromRole(spanContext, userId, roleId)
}

func (usecase *PermsUseCase) DeleteAsset(ctx context.Context, assetId string) error {
	// Start span for deleting asset
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.DeleteAsset")
	defer finish()

	// Add context
	span.SetTag("asset.id", assetId)

	// first fetch the asset to check if it exists
	asset, err := usecase.assetClient.GetAsset(spanContext, connect.NewRequest(&assetproto.GetAssetRequest{Id: assetId}))
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to get asset %s", assetId))
		return fmt.Errorf("failed to get asset: %w", err)
	}
	if asset == nil {
		return fmt.Errorf("asset not found")
	}
	userId := assetToUserId(asset.Msg.Asset)

	return usecase.DeleteUser(spanContext, userId)
}

func (usecase *PermsUseCase) DeleteUser(ctx context.Context, userId UserId) error {
	// Start span for deleting user
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.DeleteUser")
	defer finish()

	// Add context
	span.SetTag("user.id", string(userId))

	// Start a transaction for the outbox pattern
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for deleting user")
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer deferRollback(tx)

	// Get all tuples to delete
	allDeletes := make([]openfga.ClientTupleKeyWithoutCondition, 0)

	// Get role assignment tuples
	roles, err := usecase.GetUserRoles(spanContext, userId, 0)
	if err != nil {
		return fmt.Errorf("failed to get user roles: %w", err)
	}
	for _, roleId := range roles {
		allDeletes = append(allDeletes, openfga.ClientTupleKeyWithoutCondition{
			User:     userToFGAUser(spanContext, userId),
			Relation: string(RelationMember),
			Object:   roleToFGAObject(roleId),
		})
	}

	// Get object_tier override tuples
	objectTierObjectType := string(ObjectTypeObjectTier)
	userString := userToFGAUser(spanContext, userId)
	objectTierTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &userString,
			Object: &objectTierObjectType,
		}).
		Execute()
	if err != nil {
		return fmt.Errorf("failed to read object_tier tuples: %w", err)
	}
	for _, tuple := range objectTierTuples.Tuples {
		allDeletes = append(allDeletes, openfga.ClientTupleKeyWithoutCondition{
			User:     tuple.Key.User,
			Relation: tuple.Key.Relation,
			Object:   tuple.Key.Object,
		})
	}

	if len(allDeletes) == 0 {
		return nil
	}

	// write the writes and deletes to the outbox
	var outboxId string
	outboxId, err = usecase.writeFGAOutbox(spanContext, tx, nil, allDeletes)
	if err != nil {
		return fmt.Errorf("failed to apply role writes for '%s': %w", userId, err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction for asset '%s': %w", userId, err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return fmt.Errorf("failed to process FGA outbox for asset '%s': %w", userId, err)
		}
	}

	return nil
}

// These thin wrappers allow us to expose user self-controlled role changes,
// only for usage by the dispatch role.
func (usecase *PermsUseCase) DispatchRoleOn(ctx context.Context) error {
	// Start span for dispatch role on
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.DispatchRoleOn")
	defer finish()

	// check if the user is already in the dispatch role
	userId := UserId(cmncontext.GetUsername(spanContext))

	// Add context
	span.SetTag("user.id", string(userId))

	// here we need to pull the dispatch role from the database
	// either the default dispatch role or the one specified on the org
	roleId, err := usecase.permsRepo.GetOrgDispatchRole(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get org dispatch role")
		return fmt.Errorf("failed to get org dispatch role: %w", err)
	}
	if roleId == "" {
		return fmt.Errorf("dispatch role not found")
	}

	span.SetTag("role.id", roleId)

	return usecase.AddUserToRole(spanContext, userId, roleId, "", 0)
}

func (usecase *PermsUseCase) DispatchRoleOff(ctx context.Context) error {
	// Start span for dispatch role off
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.DispatchRoleOff")
	defer finish()

	userId := UserId(cmncontext.GetUsername(spanContext))

	// Add context
	span.SetTag("user.id", string(userId))

	// here we need to pull the dispatch role from the database
	// either the default dispatch role or the one specified on the org
	roleId, err := usecase.permsRepo.GetOrgDispatchRole(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get org dispatch role")
		return fmt.Errorf("failed to get org dispatch role: %w", err)
	}
	if roleId == "" {
		return fmt.Errorf("dispatch role not found")
	}

	span.SetTag("role.id", roleId)

	return usecase.RemoveUserFromRole(spanContext, userId, roleId)
}

package usecase

import (
	"context"
	"fmt"
	"log"
	"reflect"
	"strings"

	"common/herosentry"

	_ "github.com/lib/pq"

	pb "proto/hero/permissions/v1"
	"proto/hero/permissions/v1/permissionsconnect"

	"proto/hero/assets/v2/assetsconnect"
	"proto/hero/cases/v1/casesconnect"
	"proto/hero/communications/v1/conversationconnect"
	"proto/hero/entity/v1/entityconnect"
	"proto/hero/filerepository/v1/filerepositoryconnect"
	"proto/hero/orders/v2/ordersconnect"
	"proto/hero/orgs/v1/orgsconnect"
	"proto/hero/property/v1/propertyconnect"
	"proto/hero/reports/v2/reportsconnect"
	"proto/hero/sensors/v1/sensorsconnect"
	"proto/hero/situations/v2/situationsconnect"

	"proto/hero/etl/v1/etlconnect"
	"proto/hero/featureflags/v1/featureflagsconnect"

	openfga_model "github.com/openfga/go-sdk"
	openfga "github.com/openfga/go-sdk/client"
)

func (usecase *PermsUseCase) ListActionsByCategory(ctx context.Context) ([]*pb.Category, error) {
	// Start span for action listing
	_, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.ListActionsByCategory")
	defer finish()

	// Define the list of proto service handler interfaces to inspect.
	serviceTypes := []reflect.Type{
		// auth services
		reflect.TypeOf((*permissionsconnect.PermissionServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*orgsconnect.OrgsServiceHandler)(nil)).Elem(),

		// workflow services
		reflect.TypeOf((*assetsconnect.AssetRegistryServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*situationsconnect.SituationServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*ordersconnect.OrderServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*entityconnect.EntityServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*reportsconnect.ReportServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*casesconnect.CaseServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*propertyconnect.PropertyServiceHandler)(nil)).Elem(),

		// file services
		reflect.TypeOf((*filerepositoryconnect.FileRepositoryServiceHandler)(nil)).Elem(),

		// communications services
		reflect.TypeOf((*sensorsconnect.CameraServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*sensorsconnect.CameraFeedServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*sensorsconnect.CameraOrchestrationServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*conversationconnect.VideoCallServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*conversationconnect.ChatServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*conversationconnect.CellularCallServiceHandler)(nil)).Elem(),
		reflect.TypeOf((*conversationconnect.PTTServiceHandler)(nil)).Elem(),

		// feature flags services
		reflect.TypeOf((*featureflagsconnect.FeatureFlagsServiceHandler)(nil)).Elem(),

		// etl services
		reflect.TypeOf((*etlconnect.ETLServiceHandler)(nil)).Elem(),
	}

	categoriesMap := make(map[string]*pb.Category)

	for _, serviceType := range serviceTypes {
		if serviceType == nil || serviceType.Kind() != reflect.Interface {
			log.Printf("Warning: Skipping non-interface or nil type: %v", serviceType)
			continue
		}

		// Extract category name from the handler type name (e.g., "PermissionServiceHandler" -> "Permission")
		categoryName := strings.TrimSuffix(serviceType.Name(), "ServiceHandler")
		if categoryName == "" {
			log.Printf("Warning: Could not determine category name for type: %v", serviceType)
			continue
		}

		category, exists := categoriesMap[categoryName]
		if !exists {
			category = &pb.Category{Name: categoryName, Actions: make([]string, 0)}
			categoriesMap[categoryName] = category
		}

		// Iterate through the methods of the service interface
		for i := 0; i < serviceType.NumMethod(); i++ {
			method := serviceType.Method(i)
			actionName := method.Name

			if strings.HasPrefix(actionName, "mustEmbedUnimplemented") || strings.HasPrefix(actionName, "XXX_") {
				continue // Skip internal/unimplemented methods
			}

			category.Actions = append(category.Actions, actionName)
		}
	}

	// add extra category for non-proto endpoints
	// for now this is just twilio webhooks
	twilioCategory := &pb.Category{
		Name: "TwilioWebhook",
		Actions: []string{
			"callstatus",
			"voice",
			"twiml/connectAgent",
			"twilio/agent-dial-status",
			"waithold",
		},
	}
	categoriesMap["TwilioWebhook"] = twilioCategory

	// Convert map to slice
	categoriesList := make([]*pb.Category, 0, len(categoriesMap))
	for _, category := range categoriesMap {
		categoriesList = append(categoriesList, category)
	}

	// Track number of categories found
	span.SetTag("categories.count", fmt.Sprintf("%d", len(categoriesList)))

	return categoriesList, nil
}

func categoryToCategoryTuples(category *pb.PermissionCategory, permissionSetId string) []openfga.ClientTupleKey {
	tuples := make([]openfga.ClientTupleKey, 0)
	if category.CanDoAll {
		tuples = append(tuples, openfga.ClientTupleKey{
			// these tuples flow from the permission set to the users,
			// through the roles linked on the includes relation
			User:     permissionSetToUser(permissionSetId),
			Relation: string(RelationInSet),
			Object:   string(ObjectTypeCategory) + category.Name,
		})
		// return early, we don't need to write the actions
		return tuples
	}
	// actions -> tuples
	for _, action := range category.Actions {
		// Check if action.CanDoAction is explicitly true before adding the write
		if action.CanDoAction {
			tuples = append(tuples, openfga.ClientTupleKey{
				User:     permissionSetToFGAObject(permissionSetId),
				Relation: string(RelationSet),
				Object:   string(ObjectTypeAction) + category.Name + "." + action.Name,
			})
		}
	}
	return tuples
}

func categoryToRoleTuples(category *pb.PermissionCategory, roleId string) []openfga.ClientTupleKey {
	tuples := make([]openfga.ClientTupleKey, 0)
	if category.CanDoAll {
		tuples = append(tuples, openfga.ClientTupleKey{
			User:     roleToUser(roleId),
			Relation: string(RelationCanDoAll),
			Object:   string(ObjectTypeCategory) + category.Name,
		})
		// return early, we don't need to write the actions
		return tuples
	}
	// actions -> tuples
	for _, action := range category.Actions {
		// Check if action.CanDoAction is explicitly true before adding the write
		if action.CanDoAction {
			tuples = append(tuples, openfga.ClientTupleKey{
				User:     roleToUser(roleId),
				Relation: string(RelationCanDoAction),
				Object:   string(ObjectTypeAction) + category.Name + "." + action.Name,
			})
		}
	}
	return tuples
}

func tuplesToCategory(categoryTuples []openfga_model.Tuple, actionTuples []openfga_model.Tuple) []*pb.PermissionCategory {
	// group the tuples by category and action
	actions := make(map[string]map[string]bool)
	categoriesMap := make(map[string]bool)
	for _, tuple := range categoryTuples {
		category := strings.TrimPrefix(tuple.Key.Object, string(ObjectTypeCategory))
		categoriesMap[category] = true
	}
	for _, tuple := range actionTuples {
		actionString := strings.TrimPrefix(tuple.Key.Object, string(ObjectTypeAction))
		actionParts := strings.Split(actionString, ".")
		category := actionParts[0]
		action := actionParts[1]
		if actions[category] == nil {
			actions[category] = make(map[string]bool)
		}
		actions[category][action] = tuple.Key.Condition == nil
	}

	// now, fill the categories with the actions
	categories := make([]*pb.PermissionCategory, 0)
	for category, actions := range actions {
		categories = append(categories, &pb.PermissionCategory{
			Name: category,
		})
		for action, allowed := range actions {
			categories[len(categories)-1].Actions = append(categories[len(categories)-1].Actions, &pb.Action{
				Name:        action,
				CanDoAction: allowed,
			})
		}
	}

	// then set can_do_all for all categories
	for category := range categoriesMap {
		// if the category is already on role, then find it and set can_do_all to true
		alreadyOnPermissionSet := false
		for _, c := range categories {
			if c.Name == category {
				c.CanDoAll = true
				alreadyOnPermissionSet = true
				break
			}
		}
		if !alreadyOnPermissionSet {
			categories = append(categories, &pb.PermissionCategory{
				Name:     category,
				CanDoAll: true,
			})
		}
	}
	return categories
}

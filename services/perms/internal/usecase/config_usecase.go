package usecase

import (
	"common/herosentry"
	"context"
	permsRepository "perms/internal/data"
)

// GetAuthModel retrieves the stored authorization model details from the repository.
func (usecase *PermsUseCase) GetPermsConfig(ctx context.Context) (*permsRepository.PermsConfig, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "PermsUseCase.GetPermsConfig")
	defer finish()

	return usecase.permsRepo.GetPermsConfig(spanContext, nil)
}

// SetAuthModel saves the authorization model ID and hash to the repository.
func (usecase *PermsUseCase) SetPermsConfig(ctx context.Context, storeId string, modelId string, hash string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.SetPermsConfig")
	defer finish()
	span.SetTag("store.id", storeId)
	span.SetTag("model.id", modelId)

	authModel := &permsRepository.PermsConfig{
		StoreId: storeId,
		ModelId: modelId,
		Hash:    hash,
	}
	return usecase.permsRepo.SetPermsConfig(spanContext, nil, authModel)
}

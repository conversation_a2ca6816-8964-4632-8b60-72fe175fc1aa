package usecase

import (
	"context"
	"fmt"

	"common/herosentry"

	openfga "github.com/openfga/go-sdk/client"
	// pb "proto/hero/permissions/v1"
)

func (usecase *PermsUseCase) CheckPermission(ctx context.Context, category string, action string, objectId string) (bool, error) {
	// Start span for permission check
	spanCtx, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.CheckPermission")
	defer finish()

	// Add permission check context
	span.SetTag("permission.category", category)
	span.SetTag("permission.action", action)
	if objectId != "" {
		span.SetTag("permission.object_id", objectId)
	}

	userString := getUserIdFromContext(spanCtx)
	span.SetTag("permission.user", userString)

	actionString := string(RelationCanDoAction)

	objectString := requestToFGAObject(category, action, objectId)
	// Doing this at runtime is not strictly necessary, but avoids data syncing or cleanup issues
	// If this becomes a a performance issue, we can start persisting/updating these at server startup
	contextualTuples := allContextualTuplesForAction(category, action, objectId)

	allowed, err := usecase.fgaClientWrapped.Check(spanCtx, userString, actionString, objectString, contextualTuples)
	if err != nil {
		herosentry.CaptureException(spanCtx, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to check permission for %s %s", category, action))
		return false, fmt.Errorf("error checking permission: %w", err)
	}

	// Track permission result
	span.SetTag("permission.allowed", fmt.Sprintf("%t", allowed))

	return allowed, nil
}

func (usecase *PermsUseCase) BatchCheckPermission(ctx context.Context, category string, action string, objectIds []string) (map[string]bool, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.BatchCheckPermission")
	defer finish()
	span.SetTag("category", category)
	span.SetTag("action", action)
	span.SetTag("object_count", fmt.Sprintf("%d", len(objectIds)))

	// if no objectIds are provided, return an empty map
	// otherwise, BatchCheckPermission will return an error
	if len(objectIds) == 0 {
		return nil, nil
	}

	// get user id from context
	userString := getUserIdFromContext(spanContext)

	checks := make([]openfga.ClientBatchCheckItem, 0)
	for _, objectId := range objectIds {
		object := requestToFGAObject(category, action, objectId)
		contextualTuples := allContextualTuplesForAction(category, action, objectId)
		checks = append(checks, openfga.ClientBatchCheckItem{
			User:             userString,
			Relation:         string(RelationCanDoAction),
			Object:           object,
			ContextualTuples: contextualTuples,
			CorrelationId:    objectId,
		})
	}
	results, err := usecase.fgaClientWrapped.BatchCheckPermission(spanContext, checks)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to batch check permissions for %s %s", category, action))
		return nil, fmt.Errorf("error checking permissions: %w", err)
	}
	return results, nil
}

package usecase

import (
	"context"
	"fmt"

	"common/herosentry"

	"github.com/google/uuid"
	_ "github.com/lib/pq"

	"strings"

	"log"
	pb "proto/hero/permissions/v1"

	assetproto "proto/hero/assets/v2"
	"slices"

	"database/sql"

	"connectrpc.com/connect"
	openfga_model "github.com/openfga/go-sdk"
	openfga "github.com/openfga/go-sdk/client"
)

func (usecase *PermsUseCase) CreateRole(ctx context.Context, role *pb.Role) (*pb.Role, error) {
	// Start span for role creation
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.CreateRole")
	defer finish()

	if role == nil {
		return nil, fmt.Errorf("role is nil")
	}
	if role.Name == "" {
		return nil, fmt.Errorf("role name is empty")
	}

	// Add role context
	span.SetTag("role.name", role.Name)

	role.Id = uuid.New().String()
	writes := make([]openfga.ClientTupleKey, 0)

	// start a transaction so we can atomically write:
	// - role metadata in the postgres db
	// - role tuples in the fga store outbox
	// - log the role creation in the postgres db
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for role creation")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer deferRollback(tx)

	// we store the definition of the role in the fga store
	// and then just store the role metadata (id, name, etc) in the postgres db
	err = usecase.permsRepo.CreateRole(spanContext, tx, role)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create role %s", role.Name))
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	// Track created role ID
	span.SetTag("role.id", role.Id)

	// permission sets -> tuples
	for _, permissionSet := range role.PermissionSets {
		writes = append(writes, openfga.ClientTupleKey{
			User:     roleToUser(role.Id),
			Relation: string(RelationIncludes),
			Object:   string(ObjectTypePermissionSet) + permissionSet.Id,
		})
	}

	// categories -> tuples
	for _, category := range role.Categories {
		writes = append(writes, categoryToRoleTuples(category, role.Id)...)
	}

	// write the writes to the outbox
	var outboxId string
	if len(writes) > 0 {
		outboxId, err = usecase.writeFGAOutbox(spanContext, tx, writes, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to apply role writes for '%s': %w", role.Id, err)
		}
	}

	// Log the role creation with filtered role details
	changes := map[string]interface{}{
		"role": filterActions(role),
	}
	err = usecase.permsRepo.LogRoleOperation(spanContext, tx, role.Id, "CREATE", changes)
	if err != nil {
		return nil, fmt.Errorf("failed to log role creation: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return nil, fmt.Errorf("failed to process FGA outbox for role '%s': %w", role.Id, err)
		}
	}

	return role, nil
}

func (usecase *PermsUseCase) UpdateRole(ctx context.Context, desiredRole *pb.Role) (*pb.Role, error) {
	// Start span for role update
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.UpdateRole")
	defer finish()

	// Add role context
	if desiredRole != nil {
		span.SetTag("role.id", desiredRole.Id)
		span.SetTag("role.name", desiredRole.Name)
	}

	// Get the existing role to compare changes
	existingRole, err := usecase.GetRole(spanContext, desiredRole.Id)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get existing role %s", desiredRole.Id))
		return nil, err
	}

	currentActionMap, currentCategoryMap, currentPermissionSetMap := roleToMap(existingRole)
	desiredActionMap, desiredCategoryMap, desiredPermissionSetMap := roleToMap(desiredRole)

	writes := make([]openfga.ClientTupleKey, 0)
	deletes := make([]openfga.ClientTupleKeyWithoutCondition, 0)

	roleMembers := roleToUser(desiredRole.Id)

	// calculate deletes: check current permissions against desired state
	for category, actions := range currentActionMap {
		for action, hasAction := range actions {
			if hasAction {
				// Check if this permission exists and is true in the desired state
				if desiredActions, ok := desiredActionMap[category]; !ok || !desiredActions[action] || desiredCategoryMap[category] {
					// If it doesn't exist or is false in desired state, delete it
					deletes = append(deletes, openfga.ClientTupleKeyWithoutCondition{
						User:     roleMembers,
						Relation: string(RelationCanDoAction),
						Object:   categoryActionToFGAObject(category, action),
					})
				}
			}
		}
	}

	// and for the top-level category perms too
	for category, hasCategory := range currentCategoryMap {
		if hasCategory {
			// check if the desired state is missing this category, if so, delete it
			if !desiredCategoryMap[category] {
				deletes = append(deletes, openfga.ClientTupleKeyWithoutCondition{
					User:     roleMembers,
					Relation: string(RelationCanDoAll),
					Object:   categoryToFGAObject(category),
				})
			}
		}
	}

	// and for the permission sets
	for psId := range currentPermissionSetMap {
		if !desiredPermissionSetMap[psId] {
			deletes = append(deletes, openfga.ClientTupleKeyWithoutCondition{
				User:     roleMembers,
				Relation: string(RelationIncludes),
				Object:   permissionSetToFGAObject(psId),
			})
		}
	}

	// 3. Calculate Writes: Check desired permissions against current state
	for category, actions := range desiredActionMap {
		for action, hasDesiredPerm := range actions {
			// if the category is already in the set, we don't need to write the action
			if hasDesiredPerm && !desiredCategoryMap[category] {
				// Check if this permission exists and is true in the current state
				if currentActions, ok := currentActionMap[category]; !ok || !currentActions[action] {
					// If it doesn't exist or is false in current state, write it
					writes = append(writes, openfga.ClientTupleKey{
						User:     roleMembers,
						Relation: string(RelationCanDoAction),
						Object:   categoryActionToFGAObject(category, action),
					})
				}
			}
		}
	}

	// and again for the top-level category perms
	for category, hasDesiredPerm := range desiredCategoryMap {
		if hasDesiredPerm {
			// check if the current state is missing this category, if so, write it
			if !currentCategoryMap[category] {
				writes = append(writes, openfga.ClientTupleKey{
					User:     roleMembers,
					Relation: string(RelationCanDoAll),
					Object:   categoryToFGAObject(category),
				})
			}
		}
	}

	// and for the permission sets
	for psId := range desiredPermissionSetMap {
		if !currentPermissionSetMap[psId] {
			writes = append(writes, openfga.ClientTupleKey{
				User:     roleMembers,
				Relation: string(RelationIncludes),
				Object:   permissionSetToFGAObject(psId),
			})
		}
	}

	// Return early if there are no changes
	if len(writes) == 0 && len(deletes) == 0 {
		log.Printf("UpdateRole: No changes detected for role '%s'. Skipping FGA write.", desiredRole.Id)
		return existingRole, nil
	}

	// inputs
	// - writes, deletes
	// - log type
	// - new value, optional old value (changes)

	// Start a transaction to atomically write
	// - outbox records
	// - log the role update
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for role update")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer deferRollback(tx)

	err = usecase.permsRepo.UpdateRole(spanContext, tx, desiredRole)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update role %s", desiredRole.Id))
		return nil, fmt.Errorf("failed to update role: %w", err)
	}

	// write the writes and deletes to the outbox
	var outboxId string
	if len(writes) > 0 || len(deletes) > 0 {
		outboxId, err = usecase.writeFGAOutbox(spanContext, tx, writes, deletes)
		if err != nil {
			return nil, fmt.Errorf("failed to apply role writes for '%s': %w", desiredRole.Id, err)
		}
	}

	// Log the update with filtered old and new role details
	changes := map[string]interface{}{
		"old_role": filterActions(existingRole),
		"new_role": filterActions(desiredRole),
	}
	err = usecase.permsRepo.LogRoleOperation(spanContext, tx, desiredRole.Id, "UPDATE", changes)
	if err != nil {
		return nil, fmt.Errorf("failed to log role update: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return nil, fmt.Errorf("failed to process FGA outbox for role '%s': %w", desiredRole.Id, err)
		}
	}

	return desiredRole, nil
}

func (usecase *PermsUseCase) DeleteRole(ctx context.Context, roleId string) error {
	// Start span for role deletion
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.DeleteRole")
	defer finish()

	// Add role context
	span.SetTag("role.id", roleId)

	// Get the role before deleting to log its details
	role, err := usecase.GetRole(spanContext, roleId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get role %s for deletion", roleId))
		return fmt.Errorf("failed to get role '%s': %w", roleId, err)
	}

	span.SetTag("role.name", role.Name)

	// start a transaction so we can atomically write:
	// - role metadata in the postgres db
	// - role tuples in the fga store outbox
	// - log the role deletion in the postgres db
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for role deletion")
		return fmt.Errorf("failed to begin transaction for role '%s': %w", roleId, err)
	}
	defer deferRollback(tx)

	err = usecase.permsRepo.DeleteRole(spanContext, tx, roleId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete role %s", roleId))
		return fmt.Errorf("failed to delete role '%s': %w", roleId, err)
	}

	// remove all tuples where role is the object,
	// such as assigning a user to the role
	roleObject := roleToFGAObject(roleId)
	objectTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			Object: &roleObject,
		}).
		Execute()
	if err != nil {
		return fmt.Errorf("failed to read tuples for role '%s': %w", roleId, err)
	}

	// the rest of the tuples are where the role is the user,
	// such as assigning a permission set to the role
	// these require a separate read request for each object type
	roleUser := roleToUser(roleId)

	// Get all category tuples to delete
	categoryObjectType := string(ObjectTypeCategory)
	categoryTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &roleUser,
			Object: &categoryObjectType,
		}).
		Execute()
	if err != nil {
		return fmt.Errorf("failed to read category tuples for role '%s': %w", roleId, err)
	}

	// Get all action tuples to delete
	actionObjectType := string(ObjectTypeAction)
	actionTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &roleUser,
			Object: &actionObjectType,
		}).
		Execute()
	if err != nil {
		return fmt.Errorf("failed to read action tuples for role '%s': %w", roleId, err)
	}

	// Get all permission set tuples to delete
	permissionSetObjectType := string(ObjectTypePermissionSet)
	permissionSetTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &roleUser,
			Object: &permissionSetObjectType,
		}).
		Execute()
	if err != nil {
		return fmt.Errorf("failed to read permission set tuples for role '%s': %w", roleId, err)
	}

	objectTierType := string(ObjectTypeObjectTier)
	objectTierTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &roleUser,
			Object: &objectTierType,
		}).
		Execute()
	if err != nil {
		return fmt.Errorf("failed to read object tier tuples for role '%s': %w", roleId, err)
	}

	tuplesToDelete := make([]openfga.ClientTupleKeyWithoutCondition, 0)
	for _, tuple := range objectTuples.Tuples {
		tuplesToDelete = append(tuplesToDelete, openfga.ClientTupleKeyWithoutCondition{
			User:     tuple.Key.User,
			Relation: tuple.Key.Relation,
			Object:   tuple.Key.Object,
		})
	}
	for _, tuple := range categoryTuples.Tuples {
		tuplesToDelete = append(tuplesToDelete, openfga.ClientTupleKeyWithoutCondition{
			User:     tuple.Key.User,
			Relation: tuple.Key.Relation,
			Object:   tuple.Key.Object,
		})
	}
	for _, tuple := range actionTuples.Tuples {
		tuplesToDelete = append(tuplesToDelete, openfga.ClientTupleKeyWithoutCondition{
			User:     tuple.Key.User,
			Relation: tuple.Key.Relation,
			Object:   tuple.Key.Object,
		})
	}
	for _, tuple := range permissionSetTuples.Tuples {
		tuplesToDelete = append(tuplesToDelete, openfga.ClientTupleKeyWithoutCondition{
			User:     tuple.Key.User,
			Relation: tuple.Key.Relation,
			Object:   tuple.Key.Object,
		})
	}
	for _, tuple := range objectTierTuples.Tuples {
		tuplesToDelete = append(tuplesToDelete, openfga.ClientTupleKeyWithoutCondition{
			User:     tuple.Key.User,
			Relation: tuple.Key.Relation,
			Object:   tuple.Key.Object,
		})
	}

	// write the writes and deletes to the outbox
	var outboxId string
	if len(tuplesToDelete) > 0 {
		outboxId, err = usecase.writeFGAOutbox(spanContext, tx, nil, tuplesToDelete)
		if err != nil {
			return fmt.Errorf("failed to apply role writes for '%s': %w", role.Id, err)
		}
	}

	// Log the deletion with filtered role details
	changes := map[string]interface{}{
		"deleted_role": filterActions(role),
	}
	err = usecase.permsRepo.LogRoleOperation(spanContext, tx, roleId, "DELETE", changes)
	if err != nil {
		return fmt.Errorf("failed to log role deletion: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction for role '%s': %w", roleId, err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return fmt.Errorf("failed to process FGA outbox for role '%s': %w", role.Id, err)
		}
	}

	return nil
}

func (usecase *PermsUseCase) ListRoleAssets(ctx context.Context, roleId string) ([]*assetproto.Asset, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.ListRoleAssets")
	defer finish()
	span.SetTag("role.id", roleId)

	assets, err := usecase.assetClient.ListAssets(spanContext, connect.NewRequest(&assetproto.ListAssetsRequest{}))
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to list assets for role %s", roleId))
		return nil, err
	}

	objectType := roleToFGAObject(roleId)
	roleTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			Object: &objectType,
		}).
		Execute()
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, fmt.Sprintf("Failed to read role tuples for role %s", roleId))
		return nil, err
	}

	userIds := make([]string, 0)
	for _, tuple := range roleTuples.Tuples {
		if tuple.Key.Relation == string(RelationMember) {
			userId := strings.TrimPrefix(tuple.Key.User, string(UserTypeUser))
			// remove cognito prefix
			userId = strings.TrimPrefix(userId, "cognito_")
			// remove org id
			log.Printf("userId: %v", userId)
			userId = strings.Split(userId, "_org_")[0]

			userIds = append(userIds, userId)
		}
	}

	assetsWithRole := make([]*assetproto.Asset, 0)

	// for each asset, check if the user has the role by checking if the user is in the list of users
	for _, asset := range assets.Msg.Assets {
		if slices.Contains(userIds, asset.CognitoJwtSub) {
			assetsWithRole = append(assetsWithRole, asset)
		}
	}

	span.SetTag("assets.count", fmt.Sprintf("%d", len(assetsWithRole)))
	return assetsWithRole, nil
}

func roleToMap(role *pb.Role) (map[string]map[string]bool, map[string]bool, map[string]bool) {
	actionMap := make(map[string]map[string]bool)
	categoryMap := make(map[string]bool)
	permissionSetMap := make(map[string]bool)
	if role == nil {
		return actionMap, categoryMap, permissionSetMap
	}
	for _, p := range role.Categories {
		className := p.Name
		categoryMap[className] = p.CanDoAll
		if actionMap[className] == nil {
			actionMap[className] = make(map[string]bool)
		}
		for _, action := range p.Actions {
			actionMap[className][action.Name] = action.CanDoAction
		}
	}
	for _, ps := range role.PermissionSets {
		permissionSetMap[ps.Id] = true
	}
	return actionMap, categoryMap, permissionSetMap
}

func (usecase *PermsUseCase) ListRoles(ctx context.Context, pageSize int32, pageToken string) ([]*pb.Role, string, error) {
	// Start span for listing roles
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.ListRoles")
	defer finish()

	// Add context
	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))

	roles, nextPageToken, err := usecase.permsRepo.ListRoles(spanContext, nil, pageSize, pageToken)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list roles")
		return nil, "", fmt.Errorf("failed to list roles: %w", err)
	}

	pbRoles := make([]*pb.Role, len(roles))
	for i, role := range roles {
		pbRoles[i] = &pb.Role{
			Id:   role.Id,
			Name: role.Name,
		}
	}

	// Hydrate all roles in parallel using batchHydrateRole
	hydratedRoles, err := usecase.batchHydrateRole(spanContext, pbRoles)
	if err != nil {
		return nil, "", fmt.Errorf("failed to hydrate roles: %w", err)
	}

	// Track number of roles found
	span.SetTag("roles.count", fmt.Sprintf("%d", len(hydratedRoles)))

	return hydratedRoles, nextPageToken, nil
}

func (usecase *PermsUseCase) GetRole(ctx context.Context, roleId string) (*pb.Role, error) {
	// Start span for getting role
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.GetRole")
	defer finish()

	// Add role context
	span.SetTag("role.id", roleId)

	// get the role from the postgres db
	role, err := usecase.permsRepo.GetRole(spanContext, nil, roleId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get role %s", roleId))
		return nil, fmt.Errorf("failed to get role '%s': %w", roleId, err)
	}

	// get the role set from the fga store
	role, err = usecase.hydrateRole(spanContext, role)
	if err != nil {
		return nil, fmt.Errorf("failed to hydrate role '%s': %w", roleId, err)
	}

	return role, nil
}

func (usecase *PermsUseCase) hydrateRole(ctx context.Context, role *pb.Role) (*pb.Role, error) {
	// Start span for hydrating role
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.hydrateRole")
	defer finish()

	// Add role context
	span.SetTag("role.id", role.Id)
	span.SetTag("role.name", role.Name)

	roleMembers := roleToUser(role.Id)

	// we need to read the tuples for the role, for actions, and categories
	actionType := string(ObjectTypeAction)
	actionTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &roleMembers,
			Object: &actionType,
		}).
		Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to read action tuples for role '%s': %w", role.Id, err)
	}

	// filter out object-level action tuples
	// these object-level overrides are not relevant to the role definition
	actionTuplesFiltered := make([]openfga_model.Tuple, 0)
	for _, tuple := range actionTuples.Tuples {
		tupleSplit := strings.Split(tuple.Key.Object, ".")
		// object-level actions will have three parts (category.action.object_id), so we skip those
		if len(tupleSplit) != 3 {
			actionTuplesFiltered = append(actionTuplesFiltered, tuple)
		}
	}

	// now, read the category tuples
	categoryType := string(ObjectTypeCategory)
	categoryTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &roleMembers,
			Object: &categoryType,
		}).
		Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to read category tuples for role '%s': %w", role.Id, err)
	}

	// now, read the permission set tuples
	permissionSetType := string(ObjectTypePermissionSet)
	permissionSetTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &roleMembers,
			Object: &permissionSetType,
		}).
		Execute()
	if err != nil {
		return nil, err
	}

	categories := tuplesToCategory(categoryTuples.Tuples, actionTuples.Tuples)
	role.Categories = categories

	// now, fill in the permission sets
	for _, tuple := range permissionSetTuples.Tuples {
		permissionSetId := FGAPermissionSetToPermissionSetId(tuple.Key.Object)
		permissionSet, err := usecase.GetPermissionSet(spanContext, permissionSetId)
		if err != nil {
			return nil, fmt.Errorf("failed to get permission set '%s': %w", permissionSetId, err)
		}
		role.PermissionSets = append(role.PermissionSets, permissionSet)
	}

	return role, nil
}

func (usecase *PermsUseCase) batchHydrateRole(ctx context.Context, roles []*pb.Role) ([]*pb.Role, error) {
	type result struct {
		role *pb.Role
		err  error
	}

	// Create a channel to receive results
	resultChan := make(chan result, len(roles))

	// Launch a goroutine for each role
	for _, role := range roles {
		go func(r *pb.Role) {
			hydratedRole, err := usecase.hydrateRole(ctx, r)
			resultChan <- result{role: hydratedRole, err: err}
		}(role)
	}

	// Collect results
	hydratedRoles := make([]*pb.Role, len(roles))
	for i := 0; i < len(roles); i++ {
		res := <-resultChan
		if res.err != nil {
			return nil, fmt.Errorf("failed to hydrate role: %w", res.err)
		}
		hydratedRoles[i] = res.role
	}

	return hydratedRoles, nil
}

// deferRollback handles the deferred rollback of a transaction, logging any errors
func deferRollback(tx *sql.Tx) {
	if err := tx.Rollback(); err != nil {
		log.Printf("Error rolling back transaction: %v", err)
	}
}

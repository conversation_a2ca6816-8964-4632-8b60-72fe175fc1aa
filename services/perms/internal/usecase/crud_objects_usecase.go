package usecase

import (
	"context"
	"fmt"

	"common/herosentry"
	pb "proto/hero/permissions/v1"
)

// This file is a collection of thin wrappers around the object permission functions
// whereas the lower level object functions are not exposed, these are

// *******************************************************
// Reports
// *******************************************************
func (usecase *PermsUseCase) GetReportRolePermission(ctx context.Context, reportId string, roleId string) (pb.ObjectPermission, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.GetReportRolePermission")
	defer finish()
	span.SetTag("report.id", reportId)
	span.SetTag("role.id", roleId)

	permissions, err := usecase.ObjectPermissionsForThisRoleAndObject(spanContext, roleId, pb.ObjectType_OBJECT_TYPE_REPORT, reportId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to get report role permission for report %s and role %s", reportId, roleId))
		return pb.ObjectPermission_OBJECT_PERMISSION_UNSPECIFIED, fmt.Errorf("failed to get report role permission: %w", err)
	}
	return permissions, nil
}

func (usecase *PermsUseCase) UpdateReportRolePermission(ctx context.Context, reportId string, roleId string, permission pb.ObjectPermission) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.UpdateReportRolePermission")
	defer finish()
	span.SetTag("report.id", reportId)
	span.SetTag("role.id", roleId)
	span.SetTag("permission", permission.String())

	err := usecase.SetObjectPermissionsForThisRoleAndObject(spanContext, roleId, pb.ObjectType_OBJECT_TYPE_REPORT, reportId, permission)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to update report role permission")
		return fmt.Errorf("failed to update report role permission: %w", err)
	}
	return nil
}

func (usecase *PermsUseCase) ListReportRolePermissions(ctx context.Context, reportId string) ([]*pb.ObjectViewer, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.ListReportRolePermissions")
	defer finish()
	span.SetTag("report.id", reportId)

	permissions, err := usecase.ListObjectRolePermissions(spanContext, pb.ObjectType_OBJECT_TYPE_REPORT, reportId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to list report role permissions")
		return nil, fmt.Errorf("failed to list report role permissions: %w", err)
	}
	return permissions, nil
}

// *******************************************************
// Situations
// *******************************************************

func (usecase *PermsUseCase) GetSituationRolePermission(ctx context.Context, situationId string, roleId string) (pb.ObjectPermission, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.GetSituationRolePermission")
	defer finish()
	span.SetTag("situation.id", situationId)
	span.SetTag("role.id", roleId)

	permissions, err := usecase.ObjectPermissionsForThisRoleAndObject(spanContext, roleId, pb.ObjectType_OBJECT_TYPE_SITUATION, situationId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to get situation role permission for situation %s and role %s", situationId, roleId))
		return pb.ObjectPermission_OBJECT_PERMISSION_UNSPECIFIED, fmt.Errorf("failed to get situation role permission: %w", err)
	}
	return permissions, nil
}

func (usecase *PermsUseCase) UpdateSituationRolePermission(ctx context.Context, situationId string, roleId string, permission pb.ObjectPermission) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.UpdateSituationRolePermission")
	defer finish()
	span.SetTag("situation.id", situationId)
	span.SetTag("role.id", roleId)
	span.SetTag("permission", permission.String())

	err := usecase.SetObjectPermissionsForThisRoleAndObject(spanContext, roleId, pb.ObjectType_OBJECT_TYPE_SITUATION, situationId, permission)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to update situation role permission")
		return fmt.Errorf("failed to update situation role permission: %w", err)
	}
	return nil
}

func (usecase *PermsUseCase) ListSituationRolePermissions(ctx context.Context, situationId string) ([]*pb.ObjectViewer, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.ListSituationRolePermissions")
	defer finish()
	span.SetTag("situation.id", situationId)

	permissions, err := usecase.ListObjectRolePermissions(spanContext, pb.ObjectType_OBJECT_TYPE_SITUATION, situationId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to list situation role permissions")
		return nil, fmt.Errorf("failed to list situation role permissions: %w", err)
	}
	return permissions, nil
}

// *******************************************************
// Cases
// *******************************************************

func (usecase *PermsUseCase) GetCaseRolePermission(ctx context.Context, caseId string, roleId string) (pb.ObjectPermission, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.GetCaseRolePermission")
	defer finish()
	span.SetTag("case.id", caseId)
	span.SetTag("role.id", roleId)

	permissions, err := usecase.ObjectPermissionsForThisRoleAndObject(spanContext, roleId, pb.ObjectType_OBJECT_TYPE_CASE, caseId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to get case role permission for case %s and role %s", caseId, roleId))
		return pb.ObjectPermission_OBJECT_PERMISSION_UNSPECIFIED, fmt.Errorf("failed to get case role permission: %w", err)
	}
	return permissions, nil
}

func (usecase *PermsUseCase) UpdateCaseRolePermission(ctx context.Context, caseId string, roleId string, permission pb.ObjectPermission) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.UpdateCaseRolePermission")
	defer finish()
	span.SetTag("case.id", caseId)
	span.SetTag("role.id", roleId)
	span.SetTag("permission", permission.String())

	err := usecase.SetObjectPermissionsForThisRoleAndObject(spanContext, roleId, pb.ObjectType_OBJECT_TYPE_CASE, caseId, permission)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to update case role permission")
		return fmt.Errorf("failed to update case role permission: %w", err)
	}
	return nil
}

func (usecase *PermsUseCase) ListCaseRolePermissions(ctx context.Context, caseId string) ([]*pb.ObjectViewer, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.ListCaseRolePermissions")
	defer finish()
	span.SetTag("case.id", caseId)

	permissions, err := usecase.ListObjectRolePermissions(spanContext, pb.ObjectType_OBJECT_TYPE_CASE, caseId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to list case role permissions")
		return nil, fmt.Errorf("failed to list case role permissions: %w", err)
	}
	return permissions, nil
}

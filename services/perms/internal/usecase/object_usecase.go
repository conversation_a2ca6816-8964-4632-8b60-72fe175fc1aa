package usecase

import (
	"context"
	"fmt"
	"log"
	"strings"

	"common/herosentry"
	pb "proto/hero/permissions/v1"

	openfga "github.com/openfga/go-sdk/client"
)

// PermissionMapping represents a mapping of permissions for a specific object type
type ObjectPermissionOverrideMapping struct {
	Mappings map[pb.ObjectPermission]ObjectPermissionOverride
}

// ObjectPermissionOverride represents a permission option and its associated actions
type ObjectPermissionOverride struct {
	Actions []string
}

// checkPermissionLevel checks if all actions for a given permission level are allowed
func (usecase *PermsUseCase) checkPermissionLevel(actionsAllowed map[string]bool, permission ObjectPermissionOverride, objectId string) bool {
	for _, categoryAction := range permission.Actions {
		actionAssembled := string(ObjectTypeAction) + categoryAction + "." + objectId
		if !actionsAllowed[actionAssembled] {
			log.Printf("action %s not allowed for object %s and category %s and permission %s", actionAssembled, objectId, categoryAction, permission)
			return false
		}
	}
	return true
}

// If this method becomes too expensive, we can look into
// splitting the object tier tuples into separate object and tier tuples,
// and adjust the model accordingly.

// For now this works fine
func (usecase *PermsUseCase) ObjectPermissionsForThisRoleAndObject(ctx context.Context, roleId string, documentType pb.ObjectType, objectId string) (pb.ObjectPermission, error) {
	// Start span for object permission check
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.ObjectPermissionsForThisRoleAndObject")
	defer finish()

	// Add context
	span.SetTag("role.id", roleId)
	span.SetTag("object.type", documentType.String())
	span.SetTag("object.id", objectId)

	// if we have an object tier override, return that
	relationPointer := string(RelationMember)
	userPointer := roleToUser(roleId)
	for permission := range DocumentTypeToPermissionOptions[documentType].Mappings {
		tier_assembled := objectPermissionToFGAObject(documentType, objectId, permission)
		preexisting_object_tier, err := usecase.fgaClient.Read(spanContext).Body(openfga.ClientReadRequest{
			User:     &userPointer,
			Relation: &relationPointer,
			Object:   &tier_assembled,
		}).Execute()
		if err != nil {
			return pb.ObjectPermission_OBJECT_PERMISSION_UNSPECIFIED, err
		}
		if len(preexisting_object_tier.Tuples) > 0 {
			// found the object tier, return it
			objectSplit := strings.Split(preexisting_object_tier.Tuples[0].Key.Object, ".")
			_, permissionType, _ := objectSplit[0], objectSplit[1], objectSplit[2] // since we expect [object_type, permission_type, object_id]
			permissionValue := pb.ObjectPermission_value[permissionType]
			return pb.ObjectPermission(permissionValue), nil
		}
	}

	// otherwise, check the api level permissions directly
	actionsToCheck := DocumentTypeToPermissionOptions[documentType].Mappings[pb.ObjectPermission_OBJECT_PERMISSION_CAN_MANAGE].Actions

	contextualTuples := make([]openfga.ClientContextualTupleKey, 0)
	for _, action := range actionsToCheck {
		actionSplit := strings.Split(action, ".")
		category := actionSplit[0]
		action = actionSplit[1]
		contextualTuples = append(contextualTuples, contextualTuplesForObject(category, action, objectId)...)
		// add the contextual tuple to link the action to the category
		contextualTuples = append(contextualTuples, contextualTupleForCategory(category, action)...)
	}

	body := openfga.ClientListObjectsRequest{
		User:             roleToUser(roleId),
		Relation:         string(RelationCanDoAction),
		Type:             "action", // no colon here
		ContextualTuples: contextualTuples,
	}

	objects, err := usecase.fgaClient.ListObjects(spanContext).Body(body).Execute()
	if err != nil {
		return pb.ObjectPermission_OBJECT_PERMISSION_UNSPECIFIED, err
	}

	// reorganize the response in prep for the transformations
	actionsAllowed := make(map[string]bool)
	for _, object := range objects.Objects {
		actionsAllowed[object] = true
	}

	// Check permissions in order of most to least permissive
	permissionLevels := []pb.ObjectPermission{
		pb.ObjectPermission_OBJECT_PERMISSION_CAN_MANAGE,
		pb.ObjectPermission_OBJECT_PERMISSION_CAN_EDIT,
		pb.ObjectPermission_OBJECT_PERMISSION_CAN_VIEW,
		pb.ObjectPermission_OBJECT_PERMISSION_CAN_FIND,
	}

	for _, permissionLevel := range permissionLevels {
		if permission, exists := DocumentTypeToPermissionOptions[documentType].Mappings[permissionLevel]; exists {
			if usecase.checkPermissionLevel(actionsAllowed, permission, objectId) {
				// Track permission result
				span.SetTag("object.permission", permissionLevel.String())
				return permissionLevel, nil
			}
		}
	}

	// Check if all manage actions are blocked
	managePermission := DocumentTypeToPermissionOptions[documentType].Mappings[pb.ObjectPermission_OBJECT_PERMISSION_CAN_MANAGE]
	allBlocked := true
	for _, action := range managePermission.Actions {
		actionAssembled := string(ObjectTypeAction) + action + "." + objectId
		if actionsAllowed[actionAssembled] {
			allBlocked = false
			break
		}
	}
	if allBlocked {
		// Track permission result
		span.SetTag("object.permission", "BLOCKED")
		return pb.ObjectPermission_OBJECT_PERMISSION_BLOCKED, nil
	}

	// Track permission result
	span.SetTag("object.permission", "UNSPECIFIED")
	return pb.ObjectPermission_OBJECT_PERMISSION_UNSPECIFIED, nil
}

func (usecase *PermsUseCase) SetObjectPermissionsForThisRoleAndObject(ctx context.Context, roleId string, documentType pb.ObjectType, objectId string, desiredPermission pb.ObjectPermission) error {
	// Start span for setting object permissions
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.SetObjectPermissionsForThisRoleAndObject")
	defer finish()

	// Add context
	span.SetTag("role.id", roleId)
	span.SetTag("object.type", documentType.String())
	span.SetTag("object.id", objectId)
	span.SetTag("permission.desired", desiredPermission.String())

	currentPermissionTier, err := usecase.ObjectPermissionsForThisRoleAndObject(spanContext, roleId, documentType, objectId)
	if err != nil {
		return fmt.Errorf("failed to get current permission tier for role '%s' and object '%s': %w", roleId, objectId, err)
	}

	if currentPermissionTier == desiredPermission {
		return nil
	}

	permissionsToEnsureRemoved := make([]pb.ObjectPermission, 0)
	for permission := range DocumentTypeToPermissionOptions[documentType].Mappings {
		if permission != desiredPermission {
			permissionsToEnsureRemoved = append(permissionsToEnsureRemoved, permission)
		}
	}
	// Write the object tier override that we want to set
	writes := make([]openfga.ClientTupleKey, 0)
	objectOverride := objectPermissionToFGAObject(documentType, objectId, desiredPermission)
	writes = append(writes, openfga.ClientTupleKey{
		User:     roleToUser(roleId),
		Relation: string(RelationMember),
		Object:   objectOverride,
	})

	// delete any existing object tier overrides for this object
	relationPointer := string(RelationMember)
	userPointer := roleToUser(roleId)
	deletes := make([]openfga.ClientTupleKeyWithoutCondition, 0)
	for _, permission := range permissionsToEnsureRemoved {
		objectOverride := objectPermissionToFGAObject(documentType, objectId, permission)
		preexisting_object_tier, err := usecase.fgaClient.Read(spanContext).Body(openfga.ClientReadRequest{
			User:     &userPointer,
			Relation: &relationPointer,
			Object:   &objectOverride,
		}).Execute()
		if err != nil {
			return fmt.Errorf("failed to read existing object tier override for role '%s' and object '%s': %w", roleId, objectId, err)
		}
		if len(preexisting_object_tier.Tuples) > 0 {
			deletes = append(deletes, openfga.ClientTupleKeyWithoutCondition{
				User:     userPointer,
				Relation: relationPointer,
				Object:   objectOverride,
			})
		}
	}

	// start a transaction so we can write to both the fga store and the postgres db in one atomic operation
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for object permission update")
		return fmt.Errorf("failed to begin transaction for role '%s': %w", roleId, err)
	}
	defer deferRollback(tx)

	// write the writes and deletes to the outbox
	var outboxId string
	outboxId, err = usecase.writeFGAOutbox(spanContext, tx, writes, deletes)
	if err != nil {
		return fmt.Errorf("failed to apply role writes for '%s': %w", roleId, err)
	}

	// Log the deletion with filtered role details
	changes := map[string]interface{}{
		"old_permission_tier": currentPermissionTier,
		"new_permission_tier": desiredPermission,
	}
	err = usecase.permsRepo.LogObjectRoleAssignmentOperation(spanContext, tx, string(documentType), objectId, roleId, "UPDATE_PERMISSION", changes)
	if err != nil {
		return fmt.Errorf("failed to log role deletion: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction for role '%s': %w", roleId, err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return fmt.Errorf("failed to process FGA outbox for role '%s': %w", roleId, err)
		}
	}

	return nil
}

func (usecase *PermsUseCase) ListObjectRolePermissions(ctx context.Context, objectType pb.ObjectType, objectId string) ([]*pb.ObjectViewer, error) {
	// Start span for listing object role permissions
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.ListObjectRolePermissions")
	defer finish()

	// Add context
	span.SetTag("object.type", objectType.String())
	span.SetTag("object.id", objectId)

	// Use a reasonable page size for internal pagination
	const pageSize = 100
	var allObjectViewers []*pb.ObjectViewer
	var pageToken string

	for {
		// load roles with pagination
		roles, nextPageToken, err := usecase.ListRoles(spanContext, pageSize, pageToken)
		if err != nil {
			return nil, err
		}

		// Process this page of roles
		for _, role := range roles {
			permission, err := usecase.ObjectPermissionsForThisRoleAndObject(spanContext, role.Id, objectType, objectId)
			if err != nil {
				return nil, err
			}

			allObjectViewers = append(allObjectViewers, &pb.ObjectViewer{
				RoleId:     role.Id,
				RoleName:   role.Name,
				Permission: permission,
			})
		}

		// If there's no next page token, we're done
		if nextPageToken == "" {
			break
		}
		pageToken = nextPageToken
	}

	// Track number of object viewers found
	span.SetTag("object_viewers.count", fmt.Sprintf("%d", len(allObjectViewers)))

	return allObjectViewers, nil
}

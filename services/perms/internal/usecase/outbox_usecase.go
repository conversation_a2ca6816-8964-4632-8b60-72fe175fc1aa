package usecase

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"common/herosentry"

	openfga "github.com/openfga/go-sdk/client"
)

func (usecase *PermsUseCase) writeFGAOutbox(ctx context.Context, tx *sql.Tx, writes []openfga.ClientTupleKey, deletes []openfga.ClientTupleKeyWithoutCondition) (string, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "PermsUseCase.writeFGAOutbox")
	defer finish()

	wrPayload, err := json.Marshal(writes)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal FGA writes")
		return "", fmt.Errorf("failed to marshal FGA writes: %w", err)
	}
	delPayload, err := json.Marshal(deletes)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal FGA deletes")
		return "", fmt.<PERSON>rro<PERSON>("failed to marshal FGA deletes: %w", err)
	}

	outboxId, err := usecase.permsRepo.CreateFGAOutbox(spanContext, tx, wrPayload, delPayload)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to create FGA outbox entry")
		return "", fmt.Errorf("failed to create FGA outbox entry: %w", err)
	}

	return outboxId, nil
}

func (usecase *PermsUseCase) processFGAOutbox(ctx context.Context, outboxId string) error {
	spanContext, _, finish := herosentry.StartSpan(ctx, "PermsUseCase.processFGAOutbox")
	defer finish()

	outbox, err := usecase.permsRepo.GetFGAOutbox(spanContext, nil, outboxId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get FGA outbox entry")
		return fmt.Errorf("failed to get FGA outbox entry: %w", err)
	}

	if outbox.Completed {
		return nil
	}

	var writes []openfga.ClientTupleKey
	if len(outbox.WritePayload) > 0 {
		if err := json.Unmarshal(outbox.WritePayload, &writes); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to unmarshal write payload")
			return fmt.Errorf("failed to unmarshal write payload: %w", err)
		}
	}

	var deletes []openfga.ClientTupleKeyWithoutCondition
	if len(outbox.DeletePayload) > 0 {
		if err := json.Unmarshal(outbox.DeletePayload, &deletes); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to unmarshal delete payload")
			return fmt.Errorf("failed to unmarshal delete payload: %w", err)
		}
	}

	err = usecase.fgaClientWrapped.BatchWriteFGA(spanContext, writes, deletes)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal, "Failed to write FGA outbox entry")
		return fmt.Errorf("failed to write FGA outbox entry: %w", err)
	}

	err = usecase.permsRepo.MarkFGAOutboxCompleted(spanContext, nil, outboxId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to mark FGA outbox entry as completed")
		return fmt.Errorf("failed to mark FGA outbox entry as completed: %w", err)
	}

	return nil
}

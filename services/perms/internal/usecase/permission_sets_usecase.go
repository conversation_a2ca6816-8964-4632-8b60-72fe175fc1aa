package usecase

import (
	"context"
	"fmt"
	"log"

	"common/herosentry"

	"github.com/google/uuid"
	_ "github.com/lib/pq"

	pb "proto/hero/permissions/v1"

	openfga "github.com/openfga/go-sdk/client"
)

func (usecase *PermsUseCase) CreatePermissionSet(ctx context.Context, permissionSet *pb.PermissionSet) (*pb.PermissionSet, error) {
	// Start span for permission set creation
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.CreatePermissionSet")
	defer finish()

	if permissionSet == nil {
		return nil, fmt.Errorf("permission set is nil")
	}

	// Add permission set context
	if permissionSet.Name != "" {
		span.SetTag("permission_set.name", permissionSet.Name)
	}

	permissionSet.Id = uuid.New().String()
	writes := make([]openfga.ClientTupleKey, 0)

	// start a transaction so we can write to both the fga store and the postgres db in one atomic operation
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for permission set creation")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer deferRollback(tx)

	// we store the definition of the role in the fga store
	// and then just store the role name in the postgres db
	err = usecase.permsRepo.CreatePermissionSet(spanContext, tx, permissionSet)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create permission set %s", permissionSet.Name))
		return nil, fmt.Errorf("failed to create permission set: %w", err)
	}

	// Track created permission set ID
	span.SetTag("permission_set.id", permissionSet.Id)

	// categories -> tuples
	for _, category := range permissionSet.Categories {
		writes = append(writes, categoryToCategoryTuples(category, permissionSet.Id)...)
	}

	// write the writes and deletes to the outbox
	var outboxId string
	if len(writes) > 0 {
		outboxId, err = usecase.writeFGAOutbox(spanContext, tx, writes, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to apply role writes for '%s': %w", permissionSet.Id, err)
		}
	}

	// Log the role creation with filtered role details
	changes := map[string]interface{}{
		"permissionSet": filterActions(permissionSet),
	}
	err = usecase.permsRepo.LogPermissionSetOperation(spanContext, tx, permissionSet.Id, "CREATE", changes)
	if err != nil {
		return nil, fmt.Errorf("failed to log role creation: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction for permission set '%s': %w", permissionSet.Id, err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return nil, fmt.Errorf("failed to process FGA outbox for permission set '%s': %w", permissionSet.Id, err)
		}
	}

	return permissionSet, nil
}

func (usecase *PermsUseCase) UpdatePermissionSet(ctx context.Context, desiredPermissionSet *pb.PermissionSet) (*pb.PermissionSet, error) {
	// Start span for permission set update
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.UpdatePermissionSet")
	defer finish()

	// Add permission set context
	if desiredPermissionSet != nil {
		span.SetTag("permission_set.id", desiredPermissionSet.Id)
		span.SetTag("permission_set.name", desiredPermissionSet.Name)
	}

	// Get the current state of the permission set
	currentPermissionSet, err := usecase.GetPermissionSet(spanContext, desiredPermissionSet.Id)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get current permission set %s", desiredPermissionSet.Id))
		return nil, fmt.Errorf("failed to get current role state for '%s': %w", desiredPermissionSet.Id, err)
	}

	currentActionMap, currentCategoryMap := permissionSetToMap(currentPermissionSet)
	desiredActionMap, desiredCategoryMap := permissionSetToMap(desiredPermissionSet)

	writes := make([]openfga.ClientTupleKey, 0)
	deletes := make([]openfga.ClientTupleKeyWithoutCondition, 0)

	actionUser := string(UserTypePermissionSet) + desiredPermissionSet.Id

	// calculate deletes: check current permissions against desired state
	for category, actions := range currentActionMap {
		for action, hasCurrentPerm := range actions {
			if hasCurrentPerm {
				// if the action was previously written, but is now no longer desired or
				// we are giving categorical access, then delete it
				if desiredActions, ok := desiredActionMap[category]; !ok || !desiredActions[action] || desiredCategoryMap[category] {
					// If it doesn't exist or is false in desired state, delete it
					deletes = append(deletes, openfga.ClientTupleKeyWithoutCondition{
						User:     actionUser,
						Relation: string(RelationSet),
						Object:   categoryActionToFGAObject(category, action),
					})
				}
			}
		}
	}

	categoryUser := permissionSetToUser(desiredPermissionSet.Id)

	// and for the top-level category perms too
	for className, hasCurrentPerm := range currentCategoryMap {
		if hasCurrentPerm {
			// check if the desired state is missing this category, if so, delete it
			if !desiredCategoryMap[className] {
				deletes = append(deletes, openfga.ClientTupleKeyWithoutCondition{
					User:     categoryUser,
					Relation: string(RelationInSet),
					Object:   categoryToFGAObject(className),
				})
			}
		}
	}

	// Write the categories
	for category, hasDesiredPerm := range desiredCategoryMap {
		if hasDesiredPerm {
			// check if the current state is missing this category, if so, write it
			if !currentCategoryMap[category] {
				writes = append(writes, openfga.ClientTupleKey{
					User:     categoryUser,
					Relation: string(RelationInSet),
					Object:   categoryToFGAObject(category),
				})
			}
		}
	}

	// Write the actions, skipping any categories that are already in the set
	for category, actions := range desiredActionMap {
		for action, hasDesiredPerm := range actions {
			// if the category is already in the set, we don't need to write the action
			if hasDesiredPerm && !desiredCategoryMap[category] {
				// Check if this permission exists and is true in the current state
				if currentActions, ok := currentActionMap[category]; !ok || !currentActions[action] {
					// If it doesn't exist or is false in current state, write it
					writes = append(writes, openfga.ClientTupleKey{
						User:     actionUser,
						Relation: string(RelationSet),
						Object:   categoryActionToFGAObject(category, action),
					})
				}
			}
		}
	}

	// 4. Execute Write only if there are changes
	if len(writes) == 0 && len(deletes) == 0 {
		log.Printf("UpdatePermissionSet: No changes detected for permission set '%s'. Skipping FGA write.", desiredPermissionSet.Id)
		return currentPermissionSet, nil
	}

	// Start a transaction for the outbox pattern
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for permission set update")
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer deferRollback(tx)

	// Log the update with filtered old and new role details
	changes := map[string]interface{}{
		"old_permission_set": filterActions(currentPermissionSet),
		"new_permission_set": filterActions(desiredPermissionSet),
	}
	err = usecase.permsRepo.LogPermissionSetOperation(spanContext, tx, desiredPermissionSet.Id, "UPDATE", changes)
	if err != nil {
		return nil, fmt.Errorf("failed to log permission set update: %w", err)
	}

	log.Printf("UpdatePermissionSet: Applying changes for permission set '%s'. Writes=%d, Deletes=%d", desiredPermissionSet.Id, len(writes), len(deletes))

	// write the writes and deletes to the outbox
	outboxId, err := usecase.writeFGAOutbox(spanContext, tx, writes, deletes)
	if err != nil {
		return nil, fmt.Errorf("failed to apply permission set writes for '%s': %w", desiredPermissionSet.Id, err)
	}

	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction for permission set '%s': %w", desiredPermissionSet.Id, err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return nil, fmt.Errorf("failed to process FGA outbox for permission set '%s': %w", desiredPermissionSet.Id, err)
		}
	}

	return desiredPermissionSet, nil
}

func (usecase *PermsUseCase) DeletePermissionSet(ctx context.Context, id string) error {
	// Start span for permission set deletion
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.DeletePermissionSet")
	defer finish()

	// Add permission set context
	span.SetTag("permission_set.id", id)

	// Get the permission set before deleting to log its details
	permissionSet, err := usecase.GetPermissionSet(spanContext, id)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get permission set %s for deletion", id))
		return fmt.Errorf("failed to get permission set '%s': %w", id, err)
	}

	span.SetTag("permission_set.name", permissionSet.Name)

	// start a transaction so we can write to both the fga store and the postgres db in one atomic operation
	tx, err := usecase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for permission set deletion")
		return fmt.Errorf("failed to begin transaction for permission set '%s': %w", id, err)
	}
	defer deferRollback(tx)

	err = usecase.permsRepo.DeletePermissionSet(spanContext, tx, &pb.PermissionSet{Id: id})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete permission set %s", id))
		return fmt.Errorf("failed to delete permission set '%s': %w", id, err)
	}

	// get all tuples where the permission set is the object
	permissionSetObject := permissionSetToFGAObject(id)
	objectTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			Object: &permissionSetObject,
		}).
		Execute()
	if err != nil {
		return fmt.Errorf("failed to read tuples for permission set '%s': %w", id, err)
	}

	// remove all tuples where permissionSet is the user (e.g. role:admin#member)
	permissionSetUser := permissionSetToUser(id)
	permissionSetDirectUser := string(UserTypePermissionSet) + id

	// Get all category tuples to delete
	categoryObjectType := string(ObjectTypeCategory)
	categoryTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &permissionSetUser,
			Object: &categoryObjectType,
		}).
		Execute()
	if err != nil {
		return fmt.Errorf("failed to read category tuples for permission set '%s': %w", id, err)
	}

	// Get all action tuples to delete
	// actions are assigned to permission sets directly, so we
	// don't need to include the relation string here
	actionObjectType := string(ObjectTypeAction)
	actionTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &permissionSetDirectUser,
			Object: &actionObjectType,
		}).
		Execute()
	if err != nil {
		return fmt.Errorf("failed to read action tuples for permission set '%s': %w", id, err)
	}

	combinedDeletes := make([]openfga.ClientTupleKeyWithoutCondition, 0)
	for _, tuple := range objectTuples.Tuples {
		combinedDeletes = append(combinedDeletes, openfga.ClientTupleKeyWithoutCondition{
			User:     tuple.Key.User,
			Relation: tuple.Key.Relation,
			Object:   tuple.Key.Object,
		})
	}
	for _, tuple := range actionTuples.Tuples {
		combinedDeletes = append(combinedDeletes, openfga.ClientTupleKeyWithoutCondition{
			User:     tuple.Key.User,
			Relation: tuple.Key.Relation,
			Object:   tuple.Key.Object,
		})
	}
	for _, tuple := range categoryTuples.Tuples {
		combinedDeletes = append(combinedDeletes, openfga.ClientTupleKeyWithoutCondition{
			User:     tuple.Key.User,
			Relation: tuple.Key.Relation,
			Object:   tuple.Key.Object,
		})
	}

	// Log the update with filtered old and new role details
	changes := map[string]interface{}{
		"deleted_permission_set": filterActions(permissionSet),
	}
	err = usecase.permsRepo.LogPermissionSetOperation(spanContext, tx, permissionSet.Id, "DELETE", changes)
	if err != nil {
		return fmt.Errorf("failed to log permission set deletion: %w", err)
	}

	log.Printf("DeletePermissionSet: Applying changes for permission set '%s'. Deletes=%d", permissionSet.Id, len(combinedDeletes))

	// write the writes and deletes to the outbox
	outboxId, err := usecase.writeFGAOutbox(spanContext, tx, nil, combinedDeletes)
	if err != nil {
		return fmt.Errorf("failed to apply permission set writes for '%s': %w", permissionSet.Id, err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction for permission set '%s': %w", permissionSet.Id, err)
	}

	// now we push to fga
	if outboxId != "" {
		err = usecase.processFGAOutbox(spanContext, outboxId)
		if err != nil {
			return fmt.Errorf("failed to process FGA outbox for permission set '%s': %w", permissionSet.Id, err)
		}
	}

	return nil
}

func (usecase *PermsUseCase) GetPermissionSet(ctx context.Context, id string) (*pb.PermissionSet, error) {
	// Start span for getting permission set
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.GetPermissionSet")
	defer finish()

	// Add permission set context
	span.SetTag("permission_set.id", id)

	permissionSet, err := usecase.permsRepo.GetPermissionSet(spanContext, nil, &pb.PermissionSet{Id: id})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get permission set %s", id))
		return nil, err
	}
	permissionSet, err = usecase.hydratePermissionSet(spanContext, permissionSet)
	if err != nil {
		return nil, fmt.Errorf("failed to hydrate permission set '%s': %w", id, err)
	}
	return permissionSet, nil
}

func (usecase *PermsUseCase) ListPermissionSets(ctx context.Context, pageSize int32, pageToken string) ([]*pb.PermissionSet, string, error) {
	// Start span for listing permission sets
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.ListPermissionSets")
	defer finish()

	// Add context
	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))

	permissionSetsDB, nextPageToken, err := usecase.permsRepo.ListPermissionSets(spanContext, nil, pageSize, pageToken)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list permission sets")
		return nil, "", err
	}

	// hydrate the permission sets in parallel
	hydratedPermissionSets, err := usecase.batchHydratePermissionSet(spanContext, permissionSetsDB)
	if err != nil {
		return nil, "", fmt.Errorf("failed to hydrate permission sets: %w", err)
	}

	// Track number of permission sets found
	span.SetTag("permission_sets.count", fmt.Sprintf("%d", len(hydratedPermissionSets)))

	return hydratedPermissionSets, nextPageToken, nil
}

func (usecase *PermsUseCase) hydratePermissionSet(ctx context.Context, permissionSet *pb.PermissionSet) (*pb.PermissionSet, error) {
	// Start span for hydrating permission set
	spanContext, span, finish := herosentry.StartSpan(ctx, "PermsUseCase.hydratePermissionSet")
	defer finish()

	// Add permission set context
	span.SetTag("permission_set.id", permissionSet.Id)
	span.SetTag("permission_set.name", permissionSet.Name)

	permssionSetUser := permissionSetToUser(permissionSet.Id)

	// actions are assigned to permission sets directly, so we
	// don't need to include the relation string here
	permissionSetIdFull := string(UserTypePermissionSet) + permissionSet.Id
	// we need to read the tuples for the permission set, for actions and categories
	actionType := string(ObjectTypeAction)
	actionTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &permissionSetIdFull,
			Object: &actionType,
		}).
		Execute()
	if err != nil {
		return nil, err
	}

	categoryType := string(ObjectTypeCategory)
	categoryTuples, err := usecase.fgaClient.Read(spanContext).
		Body(openfga.ClientReadRequest{
			User:   &permssionSetUser,
			Object: &categoryType,
		}).
		Execute()
	if err != nil {
		return nil, err
	}

	categories := tuplesToCategory(categoryTuples.Tuples, actionTuples.Tuples)
	permissionSet.Categories = categories

	return permissionSet, nil
}

func (usecase *PermsUseCase) batchHydratePermissionSet(ctx context.Context, permissionSets []*pb.PermissionSet) ([]*pb.PermissionSet, error) {
	type result struct {
		permissionSet *pb.PermissionSet
		err           error
	}

	// Create a channel to receive results
	resultChan := make(chan result, len(permissionSets))

	// Launch a goroutine for each role
	for _, permissionSet := range permissionSets {
		go func(permissionSet *pb.PermissionSet) {
			hydratedPermissionSet, err := usecase.hydratePermissionSet(ctx, permissionSet)
			resultChan <- result{permissionSet: hydratedPermissionSet, err: err}
		}(permissionSet)
	}

	// Collect results
	hydratedPermissionSets := make([]*pb.PermissionSet, len(permissionSets))
	for i := 0; i < len(permissionSets); i++ {
		res := <-resultChan
		if res.err != nil {
			return nil, fmt.Errorf("failed to hydrate permission set: %w", res.err)
		}
		hydratedPermissionSets[i] = res.permissionSet
	}

	return hydratedPermissionSets, nil
}

func permissionSetToMap(permissionSet *pb.PermissionSet) (map[string]map[string]bool, map[string]bool) {
	actionMap := make(map[string]map[string]bool)
	categoryMap := make(map[string]bool)
	if permissionSet == nil {
		return actionMap, categoryMap
	}
	for _, p := range permissionSet.Categories {
		className := p.Name
		categoryMap[className] = p.CanDoAll
		if actionMap[className] == nil {
			actionMap[className] = make(map[string]bool)
		}
		for _, action := range p.Actions {
			actionMap[className][action.Name] = action.CanDoAction
		}
	}
	return actionMap, categoryMap
}

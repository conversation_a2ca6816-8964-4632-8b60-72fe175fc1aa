package connect

import (
	"context"
	"errors"

	connecthelper "common/connect"
	"common/herosentry"
	"perms/internal/usecase"
	pb "proto/hero/permissions/v1"

	"connectrpc.com/connect"
)

// PermsServer implements the permission service.
type PermsServer struct {
	permsUseCase *usecase.PermsUseCase
}

// NewPermsServer creates a new PermsServer.
func NewPermsServer(permsUseCase *usecase.PermsUseCase) *PermsServer {
	return &PermsServer{
		permsUseCase: permsUseCase,
	}
}

func (permsServer *PermsServer) ListRoleAssets(
	ctx context.Context,
	req *connect.Request[pb.ListRoleAssetsRequest],
) (*connect.Response[pb.ListRoleAssetsResponse], error) {
	assets, err := permsServer.permsUseCase.ListRoleAssets(ctx, req.Msg.RoleId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListRoleAssets")
	}
	response := connect.NewResponse(&pb.ListRoleAssetsResponse{
		Assets: assets,
	})
	return response, nil
}

func (permsServer *PermsServer) ListActionsByCategory(
	ctx context.Context,
	req *connect.Request[pb.ListActionsByCategoryRequest],
) (*connect.Response[pb.ListActionsByCategoryResponse], error) {
	categories, err := permsServer.permsUseCase.ListActionsByCategory(ctx)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListActionsByCategory")
	}
	response := connect.NewResponse(&pb.ListActionsByCategoryResponse{
		Categories: categories,
	})
	return response, nil
}

// ListRoles lists all roles.
func (permsServer *PermsServer) ListRoles(
	ctx context.Context,
	req *connect.Request[pb.ListRolesRequest],
) (*connect.Response[pb.ListRolesResponse], error) {
	roles, nextPageToken, err := permsServer.permsUseCase.ListRoles(ctx, req.Msg.PageSize, req.Msg.PageToken)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListRoles")
	}
	response := connect.NewResponse(&pb.ListRolesResponse{
		Roles:         roles,
		NextPageToken: nextPageToken,
	})
	return response, nil
}

// CreateRole creates a new role definition.
func (permsServer *PermsServer) CreateRole(
	ctx context.Context,
	req *connect.Request[pb.CreateRoleRequest],
) (*connect.Response[pb.CreateRoleResponse], error) {
	role, err := permsServer.permsUseCase.CreateRole(ctx, req.Msg.Role)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "CreateRole")
	}
	response := connect.NewResponse(&pb.CreateRoleResponse{
		Role: role,
	})
	return response, nil
}

// GetRole retrieves a specific role definition.
func (permsServer *PermsServer) GetRole(
	ctx context.Context,
	req *connect.Request[pb.GetRoleRequest],
) (*connect.Response[pb.GetRoleResponse], error) {
	role, err := permsServer.permsUseCase.GetRole(ctx, req.Msg.RoleId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetRole")
	}
	response := connect.NewResponse(&pb.GetRoleResponse{
		Role: role,
	})
	return response, nil
}

// UpdateRole updates an existing role definition.
func (permsServer *PermsServer) UpdateRole(
	ctx context.Context,
	req *connect.Request[pb.UpdateRoleRequest],
) (*connect.Response[pb.UpdateRoleResponse], error) {
	role, err := permsServer.permsUseCase.UpdateRole(ctx, req.Msg.Role)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateRole")
	}
	response := connect.NewResponse(&pb.UpdateRoleResponse{
		Role: role,
	})
	return response, nil
}

// DeleteRole deletes a role definition and associated tuples.
func (permsServer *PermsServer) DeleteRole(
	ctx context.Context,
	req *connect.Request[pb.DeleteRoleRequest],
) (*connect.Response[pb.DeleteRoleResponse], error) {
	err := permsServer.permsUseCase.DeleteRole(ctx, req.Msg.RoleId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeleteRole")
	}
	response := connect.NewResponse(&pb.DeleteRoleResponse{})
	return response, nil
}

// GetAssetRoles retrieves the roles for an asset.
func (permsServer *PermsServer) GetAssetRoles(
	ctx context.Context,
	req *connect.Request[pb.GetAssetRolesRequest],
) (*connect.Response[pb.GetAssetRolesResponse], error) {
	roles, err := permsServer.permsUseCase.GetAssetRoles(ctx, req.Msg.AssetId, req.Msg.OrgId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetAssetRoles")
	}
	response := connect.NewResponse(&pb.GetAssetRolesResponse{
		Roles: roles,
	})
	return response, nil
}

// GetUserRoles retrieves the roles for a user.
func (permsServer *PermsServer) GetUserRoles(
	ctx context.Context,
	req *connect.Request[pb.GetUserRolesRequest],
) (*connect.Response[pb.GetUserRolesResponse], error) {
	roles, err := permsServer.permsUseCase.GetUserRoles(ctx, usecase.UserId(req.Msg.UserId), req.Msg.OrgId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetUserRoles")
	}
	response := connect.NewResponse(&pb.GetUserRolesResponse{
		Roles: roles,
	})
	return response, nil
}

func (permsServer *PermsServer) AddUserToRole(
	ctx context.Context,
	req *connect.Request[pb.AddUserToRoleRequest],
) (*connect.Response[pb.AddUserToRoleResponse], error) {
	err := permsServer.permsUseCase.AddUserToRole(ctx, usecase.UserId(req.Msg.UserId), req.Msg.RoleId, req.Msg.RoleName, req.Msg.OrgIdOverride)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddUserToRole")
	}
	response := connect.NewResponse(&pb.AddUserToRoleResponse{})
	return response, nil
}

func (permsServer *PermsServer) RemoveUserFromRole(
	ctx context.Context,
	req *connect.Request[pb.RemoveUserFromRoleRequest],
) (*connect.Response[pb.RemoveUserFromRoleResponse], error) {
	err := permsServer.permsUseCase.RemoveUserFromRole(ctx, usecase.UserId(req.Msg.UserId), req.Msg.RoleId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RemoveUserFromRole")
	}
	response := connect.NewResponse(&pb.RemoveUserFromRoleResponse{})
	return response, nil
}

// AddAssetToRole adds an asset to a role.
func (permsServer *PermsServer) AddAssetToRole(
	ctx context.Context,
	req *connect.Request[pb.AddAssetToRoleRequest],
) (*connect.Response[pb.AddAssetToRoleResponse], error) {
	err := permsServer.permsUseCase.AddAssetToRole(ctx, req.Msg.AssetId, req.Msg.RoleId, req.Msg.RoleName, req.Msg.OrgIdOverride)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddAssetToRole")
	}
	response := connect.NewResponse(&pb.AddAssetToRoleResponse{})
	return response, nil
}

// RemoveAssetFromRole removes an asset from a role.
func (permsServer *PermsServer) RemoveAssetFromRole(
	ctx context.Context,
	req *connect.Request[pb.RemoveAssetFromRoleRequest],
) (*connect.Response[pb.RemoveAssetFromRoleResponse], error) {
	err := permsServer.permsUseCase.RemoveAssetFromRole(ctx, req.Msg.AssetId, req.Msg.RoleId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RemoveAssetFromRole")
	}
	response := connect.NewResponse(&pb.RemoveAssetFromRoleResponse{})
	return response, nil
}

// DeleteAsset removes a asset from all roles/objects.
func (permsServer *PermsServer) DeleteAsset(
	ctx context.Context,
	req *connect.Request[pb.DeleteAssetRequest],
) (*connect.Response[pb.DeleteAssetResponse], error) {
	err := permsServer.permsUseCase.DeleteAsset(ctx, req.Msg.AssetId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeleteAsset")
	}
	response := connect.NewResponse(&pb.DeleteAssetResponse{})
	return response, nil
}

func (permsServer *PermsServer) AddCognitoUserToRole(
	ctx context.Context,
	req *connect.Request[pb.AddCognitoUserToRoleRequest],
) (*connect.Response[pb.AddCognitoUserToRoleResponse], error) {
	if req.Msg.CognitoSubId == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("cognito_sub_id is required"), "AddCognitoUserToRole", herosentry.ErrorTypeValidation)
	}
	if req.Msg.RoleId == "" && req.Msg.RoleName == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("role_id or role_name is required"), "AddCognitoUserToRole", herosentry.ErrorTypeValidation)
	}
	userId := "cognito:" + req.Msg.CognitoSubId
	err := permsServer.permsUseCase.AddUserToRole(ctx, usecase.UserId(userId), req.Msg.RoleId, req.Msg.RoleName, req.Msg.OrgIdOverride)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddCognitoUserToRole")
	}
	response := connect.NewResponse(&pb.AddCognitoUserToRoleResponse{})
	return response, nil
}

// CheckPermission checks if a user has a specific permission.
func (permsServer *PermsServer) CheckPermission(
	ctx context.Context,
	req *connect.Request[pb.CheckPermissionRequest],
) (*connect.Response[pb.CheckPermissionResponse], error) {
	allowed, err := permsServer.permsUseCase.CheckPermission(ctx, req.Msg.Category, req.Msg.Action, req.Msg.ObjectId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "CheckPermission")
	}
	response := connect.NewResponse(&pb.CheckPermissionResponse{
		Allowed: allowed,
	})
	return response, nil
}

func (permsServer *PermsServer) CreatePermissionSet(
	ctx context.Context,
	req *connect.Request[pb.CreatePermissionSetRequest],
) (*connect.Response[pb.CreatePermissionSetResponse], error) {
	permissionSet, err := permsServer.permsUseCase.CreatePermissionSet(ctx, req.Msg.PermissionSet)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "CreatePermissionSet")
	}
	response := connect.NewResponse(&pb.CreatePermissionSetResponse{
		PermissionSet: permissionSet,
	})
	return response, nil
}

func (permsServer *PermsServer) GetPermissionSet(
	ctx context.Context,
	req *connect.Request[pb.GetPermissionSetRequest],
) (*connect.Response[pb.GetPermissionSetResponse], error) {
	permissionSet, err := permsServer.permsUseCase.GetPermissionSet(ctx, req.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetPermissionSet")
	}
	response := connect.NewResponse(&pb.GetPermissionSetResponse{
		PermissionSet: permissionSet,
	})
	return response, nil
}

func (permsServer *PermsServer) ListPermissionSets(
	ctx context.Context,
	req *connect.Request[pb.ListPermissionSetsRequest],
) (*connect.Response[pb.ListPermissionSetsResponse], error) {
	sets, nextPageToken, err := permsServer.permsUseCase.ListPermissionSets(ctx, req.Msg.PageSize, req.Msg.PageToken)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListPermissionSets")
	}
	response := connect.NewResponse(&pb.ListPermissionSetsResponse{
		PermissionSets: sets,
		NextPageToken:  nextPageToken,
	})
	return response, nil
}

func (permsServer *PermsServer) UpdatePermissionSet(
	ctx context.Context,
	req *connect.Request[pb.UpdatePermissionSetRequest],
) (*connect.Response[pb.UpdatePermissionSetResponse], error) {
	permissionSet, err := permsServer.permsUseCase.UpdatePermissionSet(ctx, req.Msg.PermissionSet)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdatePermissionSet")
	}
	response := connect.NewResponse(&pb.UpdatePermissionSetResponse{
		PermissionSet: permissionSet,
	})
	return response, nil
}

func (permsServer *PermsServer) DeletePermissionSet(
	ctx context.Context,
	req *connect.Request[pb.DeletePermissionSetRequest],
) (*connect.Response[pb.DeletePermissionSetResponse], error) {
	err := permsServer.permsUseCase.DeletePermissionSet(ctx, req.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeletePermissionSet")
	}
	response := connect.NewResponse(&pb.DeletePermissionSetResponse{})
	return response, nil
}

func (permsServer *PermsServer) ListReportRolePermissions(
	ctx context.Context,
	req *connect.Request[pb.ListReportRolePermissionsRequest],
) (*connect.Response[pb.ListReportRolePermissionsResponse], error) {
	objectViewers, err := permsServer.permsUseCase.ListReportRolePermissions(ctx, req.Msg.ReportId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListReportRolePermissions")
	}
	response := connect.NewResponse(&pb.ListReportRolePermissionsResponse{
		ObjectViewers: objectViewers,
	})
	return response, nil
}

func (permsServer *PermsServer) GetReportRolePermission(
	ctx context.Context,
	req *connect.Request[pb.GetReportRolePermissionRequest],
) (*connect.Response[pb.GetReportRolePermissionResponse], error) {
	permission, err := permsServer.permsUseCase.GetReportRolePermission(ctx, req.Msg.ReportId, req.Msg.RoleId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetReportRolePermission")
	}
	response := connect.NewResponse(&pb.GetReportRolePermissionResponse{
		Permission: permission,
	})
	return response, nil
}

func (permsServer *PermsServer) UpdateReportRolePermission(
	ctx context.Context,
	req *connect.Request[pb.UpdateReportRolePermissionRequest],
) (*connect.Response[pb.UpdateReportRolePermissionResponse], error) {
	err := permsServer.permsUseCase.UpdateReportRolePermission(ctx, req.Msg.ReportId, req.Msg.RoleId, req.Msg.Permission)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateReportRolePermission")
	}
	response := connect.NewResponse(&pb.UpdateReportRolePermissionResponse{})
	return response, nil
}

func (permsServer *PermsServer) ListSituationRolePermissions(
	ctx context.Context,
	req *connect.Request[pb.ListSituationRolePermissionsRequest],
) (*connect.Response[pb.ListSituationRolePermissionsResponse], error) {
	objectViewers, err := permsServer.permsUseCase.ListSituationRolePermissions(ctx, req.Msg.SituationId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListSituationRolePermissions")
	}
	response := connect.NewResponse(&pb.ListSituationRolePermissionsResponse{
		ObjectViewers: objectViewers,
	})
	return response, nil
}

func (permsServer *PermsServer) GetSituationRolePermission(
	ctx context.Context,
	req *connect.Request[pb.GetSituationRolePermissionRequest],
) (*connect.Response[pb.GetSituationRolePermissionResponse], error) {
	permission, err := permsServer.permsUseCase.GetSituationRolePermission(ctx, req.Msg.SituationId, req.Msg.RoleId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetSituationRolePermission")
	}
	response := connect.NewResponse(&pb.GetSituationRolePermissionResponse{
		Permission: permission,
	})
	return response, nil
}

func (permsServer *PermsServer) UpdateSituationRolePermission(
	ctx context.Context,
	req *connect.Request[pb.UpdateSituationRolePermissionRequest],
) (*connect.Response[pb.UpdateSituationRolePermissionResponse], error) {
	err := permsServer.permsUseCase.UpdateSituationRolePermission(ctx, req.Msg.SituationId, req.Msg.RoleId, req.Msg.Permission)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateSituationRolePermission")
	}
	response := connect.NewResponse(&pb.UpdateSituationRolePermissionResponse{})
	return response, nil
}

func (permsServer *PermsServer) ListCaseRolePermissions(
	ctx context.Context,
	req *connect.Request[pb.ListCaseRolePermissionsRequest],
) (*connect.Response[pb.ListCaseRolePermissionsResponse], error) {
	objectViewers, err := permsServer.permsUseCase.ListCaseRolePermissions(ctx, req.Msg.CaseId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCaseRolePermissions")
	}
	response := connect.NewResponse(&pb.ListCaseRolePermissionsResponse{
		ObjectViewers: objectViewers,
	})
	return response, nil
}

func (permsServer *PermsServer) GetCaseRolePermission(
	ctx context.Context,
	req *connect.Request[pb.GetCaseRolePermissionRequest],
) (*connect.Response[pb.GetCaseRolePermissionResponse], error) {
	permission, err := permsServer.permsUseCase.GetCaseRolePermission(ctx, req.Msg.CaseId, req.Msg.RoleId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetCaseRolePermission")
	}
	response := connect.NewResponse(&pb.GetCaseRolePermissionResponse{
		Permission: permission,
	})
	return response, nil
}

func (permsServer *PermsServer) UpdateCaseRolePermission(
	ctx context.Context,
	req *connect.Request[pb.UpdateCaseRolePermissionRequest],
) (*connect.Response[pb.UpdateCaseRolePermissionResponse], error) {
	err := permsServer.permsUseCase.UpdateCaseRolePermission(ctx, req.Msg.CaseId, req.Msg.RoleId, req.Msg.Permission)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateCaseRolePermission")
	}
	response := connect.NewResponse(&pb.UpdateCaseRolePermissionResponse{})
	return response, nil
}

func (permsServer *PermsServer) BatchCheckPermission(
	ctx context.Context,
	req *connect.Request[pb.BatchCheckPermissionRequest],
) (*connect.Response[pb.BatchCheckPermissionResponse], error) {
	mapp, err := permsServer.permsUseCase.BatchCheckPermission(ctx, req.Msg.Category, req.Msg.Action, req.Msg.ObjectIds)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "BatchCheckPermission")
	}
	response := connect.NewResponse(&pb.BatchCheckPermissionResponse{
		Results: mapp,
	})
	return response, nil
}

func (permsServer *PermsServer) DispatchRoleOn(
	ctx context.Context,
	req *connect.Request[pb.DispatchRoleOnRequest],
) (*connect.Response[pb.DispatchRoleOnResponse], error) {
	err := permsServer.permsUseCase.DispatchRoleOn(ctx)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DispatchRoleOn")
	}
	response := connect.NewResponse(&pb.DispatchRoleOnResponse{})
	return response, nil
}

func (permsServer *PermsServer) DispatchRoleOff(
	ctx context.Context,
	req *connect.Request[pb.DispatchRoleOffRequest],
) (*connect.Response[pb.DispatchRoleOffResponse], error) {
	err := permsServer.permsUseCase.DispatchRoleOff(ctx)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DispatchRoleOff")
	}
	response := connect.NewResponse(&pb.DispatchRoleOffResponse{})
	return response, nil
}

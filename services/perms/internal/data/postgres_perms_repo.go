package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	pb "proto/hero/permissions/v1"

	"github.com/google/uuid"

	cmncontext "common/context"
	database "common/database"
	"common/herosentry"
)

const globalAuthModelName = "global_auth_model"

// postgresPermsRepository implements PermsRepository using PostgreSQL
type postgresPermsRepository struct {
	db *sql.DB
}

// LogRoleOperation logs a role operation to the audit table
func (r *postgresPermsRepository) LogRoleOperation(ctx context.Context, tx *sql.Tx, roleId string, operation string, changes interface{}) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.LogRoleOperation")
	defer finish()
	span.SetTag("role.id", roleId)
	span.SetTag("operation", operation)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		userID := cmncontext.GetUsername(spanContext)
		orgID := cmncontext.GetOrgId(spanContext)
		changesJSON, err := json.Marshal(changes)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal changes for role audit")
			return fmt.Errorf("failed to marshal changes: %w", err)
		}

		_, err = tx.ExecContext(spanContext, `
			INSERT INTO roles_audit (id, org_id, role_id, operation, changes, user_id)
			VALUES (gen_random_uuid(), $1, $2, $3, $4, $5)
		`, orgID, roleId, operation, changesJSON, userID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to log role operation for role %s", roleId))
			return fmt.Errorf("failed to log role operation: %w", err)
		}
		return nil
	})
}

func (r *postgresPermsRepository) LogPermissionSetOperation(ctx context.Context, tx *sql.Tx, permissionSetId string, operation string, changes interface{}) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.LogPermissionSetOperation")
	defer finish()
	span.SetTag("permission_set.id", permissionSetId)
	span.SetTag("operation", operation)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		userID := cmncontext.GetUsername(spanContext)
		orgID := cmncontext.GetOrgId(spanContext)
		changesJSON, err := json.Marshal(changes)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal changes for permission set audit")
			return fmt.Errorf("failed to marshal changes: %w", err)
		}

		_, err = tx.ExecContext(spanContext, `
			INSERT INTO permission_sets_audit (id, org_id, permission_set_id, operation, changes, user_id)
			VALUES (gen_random_uuid(), $1, $2, $3, $4, $5)
		`, orgID, permissionSetId, operation, changesJSON, userID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to log permission set operation for permission set %s", permissionSetId))
			return fmt.Errorf("failed to log permission set operation: %w", err)
		}
		return nil
	})
}

func (r *postgresPermsRepository) LogRoleAssignmentOperation(ctx context.Context, tx *sql.Tx, roleId string, affectedUserId string, operation string, changes interface{}) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.LogRoleAssignmentOperation")
	defer finish()
	span.SetTag("role.id", roleId)
	span.SetTag("operation", operation)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		userID := cmncontext.GetUsername(spanContext)
		orgID := cmncontext.GetOrgId(spanContext)
		changesJSON, err := json.Marshal(changes)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal changes for role assignment audit")
			return fmt.Errorf("failed to marshal changes: %w", err)
		}

		_, err = tx.ExecContext(spanContext, `
			INSERT INTO role_assignment_audit (id, org_id, role_id, operation, changes, user_id, affected_user_id)
			VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6)
		`, orgID, roleId, operation, changesJSON, userID, affectedUserId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to log role assignment operation for role %s and user %s", roleId, affectedUserId))
			return fmt.Errorf("failed to log permission set operation: %w", err)
		}
		return nil
	})
}

func (r *postgresPermsRepository) LogObjectUserAssignmentOperation(ctx context.Context, tx *sql.Tx, objectType string, objectId string, affectedUserId string, operation string, changes interface{}) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.LogObjectUserAssignmentOperation")
	defer finish()
	span.SetTag("object.type", objectType)
	span.SetTag("object.id", objectId)
	span.SetTag("operation", operation)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		userID := cmncontext.GetUsername(spanContext)
		orgID := cmncontext.GetOrgId(spanContext)
		changesJSON, err := json.Marshal(changes)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal changes for object user assignment audit")
			return fmt.Errorf("failed to marshal changes: %w", err)
		}

		_, err = tx.ExecContext(spanContext, `
			INSERT INTO object_user_assignment_audit (id, org_id, object_type, object_id, operation, changes, user_id, affected_user_id)
			VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7)
		`, orgID, objectType, objectId, operation, changesJSON, userID, affectedUserId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to log object user assignment operation for %s %s", objectType, objectId))
			return fmt.Errorf("failed to log permission set operation: %w", err)
		}
		return nil
	})
}

func (r *postgresPermsRepository) LogObjectRoleAssignmentOperation(ctx context.Context, tx *sql.Tx, objectType string, objectId string, affectedRoleId string, operation string, changes interface{}) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.LogObjectRoleAssignmentOperation")
	defer finish()
	span.SetTag("object.type", objectType)
	span.SetTag("object.id", objectId)
	span.SetTag("affected_role.id", affectedRoleId)
	span.SetTag("operation", operation)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		userID := cmncontext.GetUsername(spanContext)
		orgID := cmncontext.GetOrgId(spanContext)
		changesJSON, err := json.Marshal(changes)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal changes for object role assignment audit")
			return fmt.Errorf("failed to marshal changes: %w", err)
		}

		_, err = tx.ExecContext(spanContext, `
			INSERT INTO object_role_assignment_audit (id, org_id, object_type, object_id, operation, changes, user_id, affected_role_id)
			VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7)
		`, orgID, objectType, objectId, operation, changesJSON, userID, affectedRoleId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to log object role assignment operation for %s %s", objectType, objectId))
			return fmt.Errorf("failed to log permission set operation: %w", err)
		}
		return nil
	})
}

func (r *postgresPermsRepository) CreateRole(ctx context.Context, tx *sql.Tx, role *pb.Role) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.CreateRole")
	defer finish()
	span.SetTag("role.id", role.Id)
	span.SetTag("role.name", role.Name)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		orgID := cmncontext.GetOrgId(spanContext)
		// allow meta org to create roles in other orgs (necessary for bootstrap)
		if orgID == -1 {
			orgID = role.OrgId
		}
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		_, err := tx.ExecContext(spanContext, "INSERT INTO roles (id, name, org_id) VALUES ($1, $2, $3)", role.Id, role.Name, orgID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create role %s", role.Id))
		}
		return err
	})
}

// UpdateRole updates an existing role
func (r *postgresPermsRepository) UpdateRole(ctx context.Context, tx *sql.Tx, role *pb.Role) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.UpdateRole")
	defer finish()
	span.SetTag("role.id", role.Id)
	span.SetTag("role.name", role.Name)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		_, err := tx.ExecContext(spanContext,
			"UPDATE roles SET name = $1, updated_at = NOW() WHERE id = $2 AND deleted_at IS NULL",
			role.Name, role.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update role %s", role.Id))
		}
		return err
	})
}

func (r *postgresPermsRepository) GetRole(ctx context.Context, tx *sql.Tx, roleId string) (*pb.Role, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.GetRole")
	defer finish()
	span.SetTag("role.id", roleId)

	return database.WithSession(r.db, spanContext, tx, func(tx *sql.Tx) (*pb.Role, error) {
		var name string
		err := tx.QueryRowContext(spanContext, "SELECT name FROM roles WHERE id = $1 AND deleted_at IS NULL", roleId).Scan(&name)
		if err != nil && err != sql.ErrNoRows {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get role %s", roleId))
		}
		return &pb.Role{Id: roleId, Name: name}, err
	})
}

func (r *postgresPermsRepository) GetRoleByName(ctx context.Context, tx *sql.Tx, roleName string) (*pb.Role, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.GetRoleByName")
	defer finish()
	span.SetTag("role.name", roleName)

	return database.WithSession(r.db, spanContext, tx, func(tx *sql.Tx) (*pb.Role, error) {
		var id string
		err := tx.QueryRowContext(spanContext, "SELECT id FROM roles WHERE name = $1 AND deleted_at IS NULL", roleName).Scan(&id)
		if err != nil && err != sql.ErrNoRows {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get role by name %s", roleName))
		}
		return &pb.Role{Id: id, Name: roleName}, err
	})
}

func (r *postgresPermsRepository) DeleteRole(ctx context.Context, tx *sql.Tx, roleId string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.DeleteRole")
	defer finish()
	span.SetTag("role.id", roleId)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		_, err := tx.ExecContext(spanContext, "UPDATE roles SET deleted_at = NOW() WHERE id = $1", roleId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete role %s", roleId))
		}
		return err
	})
}

func (r *postgresPermsRepository) ListRoles(ctx context.Context, tx *sql.Tx, pageSize int32, pageToken string) ([]*pb.Role, string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.ListRoles")
	defer finish()
	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("page_token", pageToken)

	var roles []*pb.Role
	var nextPageToken string
	err := database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		// If pageSize is 0, use a default value
		if pageSize <= 0 {
			pageSize = 100
		}

		// Add 1 to pageSize to check if there are more results
		limit := pageSize + 1

		query := "SELECT id, name FROM roles WHERE deleted_at IS NULL"
		if pageToken != "" {
			query += " AND id > $1"
			query += " ORDER BY id LIMIT $2"
		} else {
			query += " ORDER BY id LIMIT $1"
		}

		var rows *sql.Rows
		var err error
		if pageToken != "" {
			rows, err = tx.QueryContext(spanContext, query, pageToken, limit)
		} else {
			rows, err = tx.QueryContext(spanContext, query, limit)
		}
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list roles")
			return err
		}
		defer rows.Close()

		roles = make([]*pb.Role, 0)
		count := 0

		for rows.Next() {
			var id string
			var name string
			err := rows.Scan(&id, &name)
			if err != nil {
				return err
			}
			count++
			if count > int(pageSize) {
				nextPageToken = id
				break
			}
			roles = append(roles, &pb.Role{Id: id, Name: name})
		}

		if err := rows.Err(); err != nil {
			return err
		}

		return nil
	})

	if err == nil {
		span.SetTag("roles.count", fmt.Sprintf("%d", len(roles)))
	}
	return roles, nextPageToken, err
}

// --- PermissionSet Methods ---
func (r *postgresPermsRepository) CreatePermissionSet(ctx context.Context, tx *sql.Tx, permissionSet *pb.PermissionSet) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.CreatePermissionSet")
	defer finish()
	span.SetTag("permission_set.name", permissionSet.Name)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		orgID := cmncontext.GetOrgId(spanContext)
		newID := uuid.New().String()
		var dbDescription sql.NullString
		if permissionSet.Description != "" {
			dbDescription = sql.NullString{String: permissionSet.Description, Valid: true}
		}

		_, err := tx.ExecContext(spanContext,
			`INSERT INTO permission_sets (id, org_id, name, description)
			 VALUES ($1, $2, $3, $4)`,
			newID,
			orgID,
			permissionSet.Name,
			dbDescription,
		)
		if err == nil {
			permissionSet.Id = newID
			span.SetTag("permission_set.id", newID)
		} else {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create permission set %s", permissionSet.Name))
		}
		return err
	})
}

func (r *postgresPermsRepository) UpdatePermissionSet(ctx context.Context, tx *sql.Tx, permissionSet *pb.PermissionSet) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.UpdatePermissionSet")
	defer finish()
	span.SetTag("permission_set.id", permissionSet.Id)
	span.SetTag("permission_set.name", permissionSet.Name)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		var dbDescription sql.NullString
		if permissionSet.Description != "" {
			dbDescription = sql.NullString{String: permissionSet.Description, Valid: true}
		}

		_, err := tx.ExecContext(spanContext,
			`UPDATE permission_sets 
			 SET name = $1, 
			     description = $2, 
			     updated_at = NOW() 
			 WHERE id = $3 AND deleted_at IS NULL`,
			permissionSet.Name,
			dbDescription,
			permissionSet.Id,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update permission set %s", permissionSet.Id))
		}
		return err
	})
}

func (r *postgresPermsRepository) GetPermissionSet(ctx context.Context, tx *sql.Tx, permissionSet *pb.PermissionSet) (*pb.PermissionSet, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.GetPermissionSet")
	defer finish()
	span.SetTag("permission_set.id", permissionSet.Id)

	return database.WithSession(r.db, spanContext, tx, func(tx *sql.Tx) (*pb.PermissionSet, error) {
		ps := &pb.PermissionSet{}
		var createdAt sql.NullTime
		var updatedAt sql.NullTime
		var description sql.NullString
		err := tx.QueryRowContext(spanContext,
			`SELECT id, name, description, created_at, updated_at
			 FROM permission_sets WHERE id = $1 AND deleted_at IS NULL`,
			permissionSet.Id,
		).Scan(
			&ps.Id,
			&ps.Name,
			&description,
			&createdAt,
			&updatedAt,
		)

		if err != nil {
			if err != sql.ErrNoRows {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get permission set %s", permissionSet.Id))
			}
			return nil, err
		}
		if description.Valid {
			ps.Description = description.String
		}

		return ps, nil
	})
}

func (r *postgresPermsRepository) ListPermissionSets(ctx context.Context, tx *sql.Tx, pageSize int32, pageToken string) ([]*pb.PermissionSet, string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.ListPermissionSets")
	defer finish()
	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("page_token", pageToken)

	var sets []*pb.PermissionSet
	var nextPageToken string
	err := database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		// If pageSize is 0, use a default value
		if pageSize <= 0 {
			pageSize = 100
		}

		// Add 1 to pageSize to check if there are more results
		limit := pageSize + 1

		query := `SELECT id, name, description, created_at, updated_at FROM permission_sets WHERE deleted_at IS NULL`
		if pageToken != "" {
			query += " AND id > $1"
			query += " ORDER BY id LIMIT $2"
		} else {
			query += " ORDER BY id LIMIT $1"
		}

		var rows *sql.Rows
		var err error
		if pageToken != "" {
			rows, err = tx.QueryContext(spanContext, query, pageToken, limit)
		} else {
			rows, err = tx.QueryContext(spanContext, query, limit)
		}
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list permission sets")
			return err
		}
		defer rows.Close()

		sets = make([]*pb.PermissionSet, 0)
		count := 0

		for rows.Next() {
			ps := &pb.PermissionSet{}
			var createdAt sql.NullTime
			var updatedAt sql.NullTime
			var description sql.NullString
			err := rows.Scan(
				&ps.Id,
				&ps.Name,
				&description,
				&createdAt,
				&updatedAt,
			)
			if err != nil {
				return err
			}
			count++
			if count > int(pageSize) {
				nextPageToken = ps.Id
				break
			}
			if description.Valid {
				ps.Description = description.String
			}
			sets = append(sets, ps)
		}

		if err := rows.Err(); err != nil {
			return err
		}

		return nil
	})

	if err == nil {
		span.SetTag("permission_sets.count", fmt.Sprintf("%d", len(sets)))
	}
	return sets, nextPageToken, err
}

func (r *postgresPermsRepository) DeletePermissionSet(ctx context.Context, tx *sql.Tx, permissionSet *pb.PermissionSet) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.DeletePermissionSet")
	defer finish()
	span.SetTag("permission_set.id", permissionSet.Id)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		_, err := tx.ExecContext(spanContext,
			`UPDATE permission_sets SET deleted_at = NOW() WHERE id = $1`,
			permissionSet.Id,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete permission set %s", permissionSet.Id))
		}
		return err
	})
}

// --- AuthModel Methods ---

func (r *postgresPermsRepository) GetPermsConfig(ctx context.Context, tx *sql.Tx) (*PermsConfig, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.GetPermsConfig")
	defer finish()

	return database.WithSession(r.db, spanContext, tx, func(tx *sql.Tx) (*PermsConfig, error) {
		model := &PermsConfig{Name: globalAuthModelName}
		var hash string
		var createdAt time.Time
		var updatedAt sql.NullTime

		err := tx.QueryRowContext(spanContext,
			`SELECT model_id, store_id, hash, created_at, updated_at FROM perms_config WHERE name = $1`,
			globalAuthModelName,
		).Scan(
			&model.ModelId,
			&model.StoreId,
			&hash,
			&createdAt,
			&updatedAt,
		)

		if err != nil {
			if err == sql.ErrNoRows {
				return nil, nil
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get perms config")
			return nil, err
		}

		model.Hash = hash
		model.CreatedAt = createdAt
		if updatedAt.Valid {
			model.UpdatedAt = updatedAt.Time
		}

		return model, nil
	})
}

func (r *postgresPermsRepository) SetPermsConfig(ctx context.Context, tx *sql.Tx, authModel *PermsConfig) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.SetPermsConfig")
	defer finish()
	span.SetTag("model.id", authModel.ModelId)
	span.SetTag("store.id", authModel.StoreId)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		_, err := tx.ExecContext(spanContext,
			`INSERT INTO perms_config (model_id, store_id, name, hash, updated_at)
			 VALUES ($1, $2, $3, $4, NOW())
			 ON CONFLICT (name) DO UPDATE
			 SET hash = EXCLUDED.hash,
			 	model_id = EXCLUDED.model_id,
				 store_id = EXCLUDED.store_id,
			     updated_at = NOW()`,
			authModel.ModelId,
			authModel.StoreId,
			globalAuthModelName,
			authModel.Hash,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to set perms config")
		}
		return err
	})
}

func (r *postgresPermsRepository) GetOrgDispatchRole(ctx context.Context, tx *sql.Tx) (string, error) {
	spanContext, _, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.GetOrgDispatchRole")
	defer finish()

	return database.WithSession(r.db, spanContext, tx, func(tx *sql.Tx) (string, error) {
		var roleId string
		err := tx.QueryRowContext(spanContext, "SELECT dispatch_role_id FROM orgs").Scan(&roleId)
		if err != nil && err != sql.ErrNoRows {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get org dispatch role")
		}
		return roleId, err
	})
}

// Outbox methods
func (r *postgresPermsRepository) CreateFGAOutbox(ctx context.Context, tx *sql.Tx, writePayload []byte, deletePayload []byte) (string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.CreateFGAOutbox")
	defer finish()
	span.SetTag("write_payload.size", fmt.Sprintf("%d", len(writePayload)))
	span.SetTag("delete_payload.size", fmt.Sprintf("%d", len(deletePayload)))

	var id string
	err := database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		err := tx.QueryRowContext(spanContext,
			`INSERT INTO fga_outbox (write_payload, delete_payload) VALUES ($1, $2) RETURNING id`,
			writePayload,
			deletePayload,
		).Scan(&id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to create FGA outbox entry")
		}
		return err
	})

	if err == nil {
		span.SetTag("outbox.id", id)
	}
	return id, err
}

func (r *postgresPermsRepository) MarkFGAOutboxCompleted(ctx context.Context, tx *sql.Tx, id string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.MarkFGAOutboxCompleted")
	defer finish()
	span.SetTag("outbox.id", id)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		_, err := tx.ExecContext(spanContext,
			`UPDATE fga_outbox SET completed = true WHERE id = $1`,
			id,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to mark FGA outbox %s as completed", id))
		}
		return err
	})
}

func (r *postgresPermsRepository) MarkFGAOutboxFailed(ctx context.Context, tx *sql.Tx, id string, errorMessage string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PostgresPermsRepo.MarkFGAOutboxFailed")
	defer finish()
	span.SetTag("outbox.id", id)
	span.SetTag("error.message", errorMessage)

	return database.WithSessionErr(r.db, spanContext, tx, func(tx *sql.Tx) error {
		_, err := tx.ExecContext(spanContext,
			`UPDATE fga_outbox 
			 SET error_count = error_count + 1,
			     error_message = $1
			 WHERE id = $2`,
			errorMessage,
			id,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to mark FGA outbox %s as failed", id))
		}
		return err
	})
}

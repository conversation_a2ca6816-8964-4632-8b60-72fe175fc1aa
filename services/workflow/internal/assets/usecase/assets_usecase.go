package usecase

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"common/herosentry"
	commonUtils "common/utils"
	assets "proto/hero/assets/v2"
	assetRepository "workflow/internal/assets/data"
	workflowUtils "workflow/internal/common/utils"
	orderRepository "workflow/internal/orders/data"
	situationRepository "workflow/internal/situations/data"

	zello "common/clients/zello"

	_ "modernc.org/sqlite" // required to support transaction for in-memory db

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/kms"
)

const FixedResourceTypeAsset = "ASSET"

// AssetUseCase defines the use-case layer for asset operations.
type AssetUseCase struct {
	database      *sql.DB // Only needed for transactional operations.
	assetRepo     assetRepository.AssetRepository
	situationRepo situationRepository.SituationRepository
	orderRepo     orderRepository.OrderRepository
	kmsClient     *kms.Client
	kmsKeyArn     string
}

// NewAssetUseCase creates a new AssetUseCase.
func NewAssetUseCase(
	database *sql.DB,
	assetRepo assetRepository.AssetRepository,
	situationRepo situationRepository.SituationRepository,
	orderRepo orderRepository.OrderRepository) (*AssetUseCase, error) {
	// For in-memory assetRepository, we need a dummy DB to support transactions.
	if database == nil {
		return nil, errors.New("database is nil: cannot initialize AssetUseCase")
	}
	ctx := context.Background()
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-west-2"))
	if err != nil {
		log.Fatalf("failed to load AWS config: %v", err)
	}

	kmsKeyArn := os.Getenv("KMS_KEY_ARN")
	if kmsKeyArn == "" {
		kmsKeyArn = "test-key-arn"
	}

	kmsClient := kms.NewFromConfig(cfg)

	return &AssetUseCase{
		database:      database,
		assetRepo:     assetRepo,
		situationRepo: situationRepo,
		orderRepo:     orderRepo,
		kmsClient:     kmsClient,
		kmsKeyArn:     kmsKeyArn,
	}, nil
}

// CreateAsset creates a new asset.
func (assetUseCase *AssetUseCase) CreateAsset(ctx context.Context, assetRecord *assets.Asset) error {
	// Start span for business logic
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.CreateAsset")
	defer finishSpan()

	// Add business context
	span.SetTag("asset.type", assetRecord.Type.String())
	span.SetTag("asset.status", assetRecord.Status.String())
	span.SetTag("org.id", fmt.Sprintf("%d", assetRecord.OrgId))

	// Prevent setting is_internal through create API
	if assetRecord.IsInternal {
		err := fmt.Errorf("is_internal field cannot be set through create API")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return err
	}

	if assetRecord.OrgId == 0 {
		err := fmt.Errorf("organization ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return err
	}
	if assetRecord.AdditionalInfoJson == "" {
		assetRecord.AdditionalInfoJson = "{}"
	}
	if assetRecord.ContactEmail != "" && !commonUtils.IsValidEmail(assetRecord.ContactEmail) {
		err := fmt.Errorf("invalid email format")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return err
	}
	if assetRecord.ContactNo != "" {
		standardized, err := commonUtils.StandardizeUSPhoneNumber(assetRecord.ContactNo)
		if err != nil {
			return err
		}
		assetRecord.ContactNo = standardized
	}
	assetRecord.ResourceType = FixedResourceTypeAsset

	// No transaction is needed for a simple insert.
	return assetUseCase.assetRepo.CreateAsset(spanContext, nil, assetRecord)
}

// GetZelloChannels returns the list of Zello channels for the organization.
func (assetUseCase *AssetUseCase) GetZelloChannels(ctx context.Context) ([]*assets.ZelloChannel, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.GetZelloChannels")
	defer finishSpan()

	span.SetTag("operation", "get_zello_channels")

	return assetUseCase.assetRepo.GetZelloChannels(spanContext, nil)
}

// CreateResponderAsset creates the asset and registers the responder with Zello,
// then encrypts and stores the Zello credentials using AWS KMS.
func (assetUseCase *AssetUseCase) CreateResponderAsset(ctx context.Context, a *assets.Asset) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.CreateResponderAsset")
	defer finishSpan()

	span.SetTag("asset.type", a.Type.String())
	span.SetTag("asset.status", a.Status.String())
	span.SetTag("org.id", fmt.Sprintf("%d", a.OrgId))

	if a.OrgId == 0 {
		err := fmt.Errorf("organization ID is required")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return err
	}
	if a.ContactNo != "" {
		standardized, err := commonUtils.StandardizeUSPhoneNumber(a.ContactNo)
		if err != nil {
			err := fmt.Errorf("CreateResponder: invalid phone number: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return err
		}
		a.ContactNo = standardized
	}

	err := assetUseCase.CreateAsset(spanContext, a)
	if err != nil {
		err := fmt.Errorf("CreateResponder: error creating asset: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}

	// get the zello channels for the organization
	channelSpanContext, channelSpan, finishChannelSpan := herosentry.StartSpan(spanContext, "get_organization_zello_channels")
	channelSpan.SetTag("org.id", fmt.Sprintf("%d", a.OrgId))
	defer finishChannelSpan()

	zelloChannels, err := assetUseCase.GetZelloChannels(channelSpanContext)
	if err != nil || len(zelloChannels) == 0 {
		err := fmt.Errorf("CreateResponder: error getting Zello channels: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal)
		return err
	}
	orgZelloChannel := ""
	for _, channel := range zelloChannels {
		if channel.OrgId == a.OrgId {
			orgZelloChannel = channel.ZelloChannelId
			break
		}
	}
	channelSpan.SetTag("channels.found", fmt.Sprintf("%d", len(zelloChannels)))
	channelSpan.SetTag("org.channel.found", fmt.Sprintf("%t", orgZelloChannel != ""))
	// if there is a zello channel for this org, create zello creds
	// for this asset and add the asset to the zello channel
	if orgZelloChannel != "" {
		zelloSpanContext, zelloSpan, finishZelloSpan := herosentry.StartSpan(spanContext, "create_zello_user_and_credentials")
		zelloSpan.SetTag("zello.channel", orgZelloChannel)
		zelloSpan.SetTag("asset.name", a.Name)
		defer finishZelloSpan()

		zelloClient, err := zello.NewZelloClient()
		if err != nil {
			err := fmt.Errorf("CreateResponder: error creating Zello client: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal)
			return err
		}
		defer func() {
			if err := zelloClient.Logout(); err != nil {
				fmt.Printf("could not logout Zello client: %v", err)
			}
		}()

		// Create Zello user
		_, userSpan, finishUserSpan := herosentry.StartSpan(zelloSpanContext, "create_zello_user")
		userSpan.SetTag("zello.username", a.Name)
		defer finishUserSpan()

		zelloUsername := a.Name // for now, use name (switch to autogen id once we have our own mobile app )
		_, password, err := zelloClient.CreateUser(zelloUsername)
		if err != nil {
			err := fmt.Errorf("CreateResponder: error creating Zello user: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal)
			return err
		}

		// Configure Zello channel membership
		_, channelMgmtSpan, finishChannelMgmtSpan := herosentry.StartSpan(zelloSpanContext, "configure_zello_channels")
		channelMgmtSpan.SetTag("zello.username", zelloUsername)
		channelMgmtSpan.SetTag("zello.target_channel", orgZelloChannel)
		defer finishChannelMgmtSpan()

		// remove the user from the default channel
		_, err = zelloClient.RemoveFromChannel("Everyone", []string{zelloUsername})
		if err != nil {
			err := fmt.Errorf("CreateResponder: error removing user from default channel: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal)
			return err
		}
		// TODO - for some VERY inexplicable reason, AddToChannels works locally but not in AWS
		// so we're just going to add the user to the first channel for now
		_, err = zelloClient.AddToChannel(orgZelloChannel, []string{zelloUsername})
		if err != nil {
			err := fmt.Errorf("CreateResponder: error adding user to default channel: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal)
			return err
		}

		// Encrypt and store Zello credentials
		encryptSpanContext, encryptSpan, finishEncryptSpan := herosentry.StartSpan(zelloSpanContext, "encrypt_zello_credentials")
		encryptSpan.SetTag("asset.id", a.Id)
		defer finishEncryptSpan()

		// Encrypt the Zello password using AWS KMS
		input := &kms.EncryptInput{
			KeyId:     aws.String(assetUseCase.kmsKeyArn),
			Plaintext: []byte(password),
		}
		result, err := assetUseCase.kmsClient.Encrypt(encryptSpanContext, input)
		if err != nil {
			err := fmt.Errorf("failed to encrypt data: %v", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeExternal)
			return err
		}
		// Save the encrypted Zello credentials using base64 encoding
		err = assetUseCase.assetRepo.CreateZelloCreds(encryptSpanContext, nil, &assets.ZelloCreds{
			OrgId:             a.OrgId,
			AssetId:           a.Id,
			Username:          zelloUsername,
			EncryptedPassword: base64.StdEncoding.EncodeToString(result.CiphertextBlob),
		})
		if err != nil {
			err := fmt.Errorf("CreateResponder: error saving Zello creds: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}
	}

	return nil
}

// GetAsset retrieves an asset by its ID.
func (assetUseCase *AssetUseCase) GetAsset(ctx context.Context, assetID string) (*assets.Asset, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.GetAsset")
	defer finishSpan()

	span.SetTag("asset.id", assetID)

	return assetUseCase.assetRepo.GetAsset(spanContext, nil, assetID)
}

// GetAssetPrivate retrieves the asset and its associated encrypted Zello credentials,
// then decrypts the password using AWS KMS.
func (assetUseCase *AssetUseCase) GetAssetPrivate(ctx context.Context, assetId string) (*assets.GetAssetPrivateResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.GetAssetPrivate")
	defer finishSpan()

	span.SetTag("asset.id", assetId)

	// Retrieve asset from the repository
	assetSpanContext, assetSpan, finishAssetSpan := herosentry.StartSpan(spanContext, "retrieve_asset_record")
	assetSpan.SetTag("asset.id", assetId)
	defer finishAssetSpan()

	asset, err := assetUseCase.assetRepo.GetAsset(assetSpanContext, nil, assetId)
	if err != nil {
		return nil, err
	}
	if asset != nil {
		assetSpan.SetTag("asset.type", asset.Type.String())
		assetSpan.SetTag("asset.org_id", fmt.Sprintf("%d", asset.OrgId))
	}

	// Load the Zello credentials
	credsSpanContext, credsSpan, finishCredsSpan := herosentry.StartSpan(spanContext, "retrieve_zello_credentials")
	credsSpan.SetTag("asset.id", assetId)
	defer finishCredsSpan()

	zelloCreds, err := assetUseCase.assetRepo.GetZelloCreds(credsSpanContext, nil, assetId)
	if err != nil {
		return nil, err
	}
	if zelloCreds != nil {
		credsSpan.SetTag("zello.username", zelloCreds.Username)
	}

	// Decrypt the Zello credentials
	decryptSpanContext, decryptSpan, finishDecryptSpan := herosentry.StartSpan(spanContext, "decrypt_zello_password")
	decryptSpan.SetTag("asset.id", assetId)
	defer finishDecryptSpan()

	ciphertextBlob, err := base64.StdEncoding.DecodeString(zelloCreds.EncryptedPassword)
	if err != nil {
		err := fmt.Errorf("failed to decode base64 encrypted password: %v", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return nil, err
	}
	input := &kms.DecryptInput{
		CiphertextBlob: ciphertextBlob,
	}
	result, err := assetUseCase.kmsClient.Decrypt(decryptSpanContext, input)
	if err != nil {
		err := fmt.Errorf("failed to decrypt data: %v", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return nil, err
	}

	// Update the Zello credentials with the decrypted password
	zelloCreds.Password = string(result.Plaintext)
	zelloCreds.EncryptedPassword = "" // don't return the encrypted password

	return &assets.GetAssetPrivateResponse{
		Asset:      asset,
		ZelloCreds: zelloCreds,
	}, nil
}

// GetAssetByCognitoSub retrieves an asset by its Cognito JWT sub.
func (assetUseCase *AssetUseCase) GetAssetByCognitoSub(ctx context.Context, cognitoSub string) (*assets.Asset, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.GetAssetByCognitoSub")
	defer finishSpan()

	span.SetTag("cognito.sub", cognitoSub)

	return assetUseCase.assetRepo.GetAssetByCognitoSub(spanContext, nil, cognitoSub)
}

// DeleteAsset deletes an asset.
func (assetUseCase *AssetUseCase) DeleteAsset(ctx context.Context, assetID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.DeleteAsset")
	defer finishSpan()

	span.SetTag("asset.id", assetID)

	return assetUseCase.assetRepo.DeleteAsset(spanContext, nil, assetID)
}

// ListAssets returns a paginated list of assets with optional filtering and ordering.
func (assetUseCase *AssetUseCase) ListAssets(ctx context.Context, pageSize int, pageToken string, assetType assets.AssetType, assetStatus assets.AssetStatus, orderBy string) ([]*assets.Asset, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.ListAssets")
	defer finishSpan()

	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("asset.type", assetType.String())
	span.SetTag("asset.status", assetStatus.String())

	if pageSize <= 0 {
		pageSize = 100
	}
	paginatedAssets, err := assetUseCase.assetRepo.ListAssets(spanContext, nil, pageSize, pageToken, assetType, assetStatus, orderBy)
	if err != nil {
		return nil, "", err
	}
	return paginatedAssets.Assets, paginatedAssets.NextPageToken, nil
}

// UpdateAsset updates only the fields that are provided (non‑default) in updatedAsset.
// For scalar fields, a non‑zero (or non-empty) value is considered present.
// This function uses a transaction so that if any step fails, all changes are rolled back.
func (assetUseCase *AssetUseCase) UpdateAsset(ctx context.Context, updatedAsset *assets.Asset) (*assets.Asset, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.UpdateAsset")
	defer finishSpan()

	span.SetTag("asset.id", updatedAsset.Id)
	span.SetTag("asset.type", updatedAsset.Type.String())
	span.SetTag("asset.status", updatedAsset.Status.String())

	// Begin a transaction.
	transaction, beginError := assetUseCase.database.BeginTx(spanContext, nil)
	if beginError != nil {
		return nil, beginError
	}

	// Retrieve the existing asset within the transaction.
	retrieveSpanContext, retrieveSpan, finishRetrieveSpan := herosentry.StartSpan(spanContext, "retrieve_existing_asset")
	retrieveSpan.SetTag("asset.id", updatedAsset.Id)
	defer finishRetrieveSpan()

	existingAsset, retrievalError := assetUseCase.assetRepo.GetAsset(retrieveSpanContext, transaction, updatedAsset.Id)
	if retrievalError != nil {
		_ = transaction.Rollback()
		return nil, retrievalError
	}
	if existingAsset != nil {
		retrieveSpan.SetTag("existing.type", existingAsset.Type.String())
		retrieveSpan.SetTag("existing.status", existingAsset.Status.String())
	}

	// Store the original is_internal value to prevent modification
	originalIsInternal := existingAsset.IsInternal

	// Validate and merge field updates
	_, validateSpan, finishValidateSpan := herosentry.StartSpan(spanContext, "validate_and_merge_fields")
	validateSpan.SetTag("asset.id", updatedAsset.Id)
	defer finishValidateSpan()

	var fieldsUpdated []string

	// Update only fields that are "present" (non‑default) in updatedAsset.
	if updatedAsset.Name != "" {
		existingAsset.Name = updatedAsset.Name
		fieldsUpdated = append(fieldsUpdated, "name")
	}
	if updatedAsset.CognitoJwtSub != "" {
		existingAsset.CognitoJwtSub = updatedAsset.CognitoJwtSub
		fieldsUpdated = append(fieldsUpdated, "cognito_jwt_sub")
	}
	if updatedAsset.Type != assets.AssetType_ASSET_TYPE_UNSPECIFIED {
		existingAsset.Type = updatedAsset.Type
		fieldsUpdated = append(fieldsUpdated, "type")
	}
	if updatedAsset.Status != assets.AssetStatus_ASSET_STATUS_UNSPECIFIED {
		existingAsset.Status = updatedAsset.Status
		fieldsUpdated = append(fieldsUpdated, "status")
	}
	if updatedAsset.ContactNo != "" {
		standardized, err := commonUtils.StandardizeUSPhoneNumber(updatedAsset.ContactNo)
		if err != nil {
			_ = transaction.Rollback()
			return nil, err
		}
		existingAsset.ContactNo = standardized
		fieldsUpdated = append(fieldsUpdated, "contact_no")
	}
	if updatedAsset.ContactEmail != "" {
		if !commonUtils.IsValidEmail(updatedAsset.ContactEmail) {
			_ = transaction.Rollback()
			err := fmt.Errorf("invalid email format")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, err
		}
		existingAsset.ContactEmail = updatedAsset.ContactEmail
		fieldsUpdated = append(fieldsUpdated, "contact_email")
	}
	if updatedAsset.Latitude != 0 {
		existingAsset.Latitude = updatedAsset.Latitude
		fieldsUpdated = append(fieldsUpdated, "latitude")
	}
	if updatedAsset.Longitude != 0 {
		existingAsset.Longitude = updatedAsset.Longitude
		fieldsUpdated = append(fieldsUpdated, "longitude")
	}
	if updatedAsset.LocationUpdateTime != "" {
		existingAsset.LocationUpdateTime = updatedAsset.LocationUpdateTime
		fieldsUpdated = append(fieldsUpdated, "location_update_time")
	}
	if updatedAsset.AdditionalInfoJson != "" {
		existingAsset.AdditionalInfoJson = updatedAsset.AdditionalInfoJson
		fieldsUpdated = append(fieldsUpdated, "additional_info_json")
	}
	if updatedAsset.ResourceType != "" && updatedAsset.ResourceType != existingAsset.ResourceType {
		_ = transaction.Rollback()
		err := fmt.Errorf("resource type cannot be modified after creation")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	// Always update the update time.
	existingAsset.UpdateTime = commonUtils.TimeToISO8601String(time.Now())

	// Ensure is_internal is preserved from the original value
	existingAsset.IsInternal = originalIsInternal

	validateSpan.SetTag("fields.updated", strings.Join(fieldsUpdated, ","))
	validateSpan.SetTag("fields.count", fmt.Sprintf("%d", len(fieldsUpdated)))

	// Persist the updated asset within the transaction.
	persistSpanContext, persistSpan, finishPersistSpan := herosentry.StartSpan(spanContext, "persist_asset_updates")
	persistSpan.SetTag("asset.id", existingAsset.Id)
	persistSpan.SetTag("asset.type", existingAsset.Type.String())
	persistSpan.SetTag("asset.status", existingAsset.Status.String())
	defer finishPersistSpan()

	updatedAsset, updateError := assetUseCase.assetRepo.UpdateAsset(persistSpanContext, transaction, existingAsset)
	if updateError != nil {
		_ = transaction.Rollback()
		return nil, updateError
	}

	// Commit the transaction.
	if commitError := transaction.Commit(); commitError != nil {
		return nil, commitError
	}

	return updatedAsset, nil
}

// AddAdditionalInfo adds additional JSON info to an asset by merging the provided JSON.
func (assetUseCase *AssetUseCase) AddAdditionalInfo(ctx context.Context, id string, newInfo string) (string, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.AddAdditionalInfo")
	defer finishSpan()

	span.SetTag("asset.id", id)

	// Begin a transaction.
	tx, err := assetUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		return "", "", err
	}

	// Retrieve the existing asset within the transaction.
	assetRecord, err := assetUseCase.assetRepo.GetAsset(spanContext, tx, id)
	if err != nil {
		_ = tx.Rollback()
		return "", "", err
	}

	// Unmarshal the existing additional info.
	var existingMap map[string]interface{}
	if err = json.Unmarshal([]byte(assetRecord.AdditionalInfoJson), &existingMap); err != nil {
		_ = tx.Rollback()
		err := fmt.Errorf("failed to parse existing additional info: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return "", "", err
	}

	// Unmarshal the new additional info.
	var newMap map[string]interface{}
	if err = json.Unmarshal([]byte(newInfo), &newMap); err != nil {
		_ = tx.Rollback()
		err := fmt.Errorf("failed to parse new additional info: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return "", "", err
	}

	// Merge newMap into existingMap.
	workflowUtils.MergeJSON(existingMap, newMap)

	// Marshal the merged map back to JSON.
	mergedBytes, err := json.Marshal(existingMap)
	if err != nil {
		_ = tx.Rollback()
		err := fmt.Errorf("failed to marshal merged additional info: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return "", "", err
	}
	mergedInfo := string(mergedBytes)

	// Use the new repository method to update only the additional info.
	_, err = assetUseCase.assetRepo.UpdateAdditionalInfoJSON(spanContext, tx, id, mergedInfo)
	if err != nil {
		_ = tx.Rollback()
		return "", "", err
	}

	// Commit the transaction.
	if err := tx.Commit(); err != nil {
		return "", "", err
	}

	return id, mergedInfo, nil
}

// SetAssetInternalStatus updates only the isInternal flag of an asset.
func (assetUseCase *AssetUseCase) SetAssetInternalStatus(ctx context.Context, assetID string, isInternal bool) (*assets.Asset, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.SetAssetInternalStatus")
	defer finishSpan()

	span.SetTag("asset.id", assetID)
	span.SetTag("asset.is_internal", fmt.Sprintf("%t", isInternal))

	tx, err := assetUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		return nil, err
	}

	err = assetUseCase.assetRepo.SetAssetInternalStatus(spanContext, tx, assetID, isInternal)
	if err != nil {
		_ = tx.Rollback()
		return nil, err
	}

	updatedAsset, err := assetUseCase.assetRepo.GetAsset(spanContext, tx, assetID)
	if err != nil {
		_ = tx.Rollback()
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return updatedAsset, nil
}

// ListAssetsByPhoneNumber returns all assets associated with a phone number.
func (assetUseCase *AssetUseCase) ListAssetsByPhoneNumber(ctx context.Context, phoneNumber string) ([]*assets.Asset, error) {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.ListAssetsByPhoneNumber")
	defer finishSpan()

	return assetUseCase.assetRepo.ListAssetsByPhoneNumber(spanContext, nil, phoneNumber)
}

// SearchAssets performs advanced search on assets with comprehensive filtering and search capabilities.
// This method provides a high-level interface for asset search functionality, supporting:
// - Text search across multiple fields (id, name, contact_no, contact_email)
// - Exact filtering by asset type and status
// - Date range filtering on all timestamp fields
// - Geographic bounding box searches
// - Configurable ordering and pagination
// - Search result highlighting
//
// The method validates input parameters and delegates to the repository layer for execution.
// No transaction is needed for read-only search operations.
func (assetUseCase *AssetUseCase) SearchAssets(ctx context.Context, searchRequest *assets.SearchAssetsRequest) (*assets.SearchAssetsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "AssetUseCase.SearchAssets")
	defer finishSpan()

	span.SetTag("search.query", searchRequest.Query)
	span.SetTag("search.page_size", fmt.Sprintf("%d", searchRequest.PageSize))

	// Validate search request parameters
	if searchRequest == nil {
		err := fmt.Errorf("search request cannot be nil")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
		return nil, err
	}

	// Set default page size if not specified or invalid
	if searchRequest.PageSize <= 0 {
		searchRequest.PageSize = 20 // Default page size for reasonable response times
	}
	if searchRequest.PageSize > 100 {
		searchRequest.PageSize = 100 // Maximum page size to prevent resource exhaustion
	}

	// Delegate to repository layer for search execution
	// No transaction needed for read-only search operations
	return assetUseCase.assetRepo.SearchAssets(spanContext, nil, searchRequest)
}

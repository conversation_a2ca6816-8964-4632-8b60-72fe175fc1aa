package connect

import (
	"context"
	"fmt"
	"time"

	connecthelper "common/connect"
	"common/herosentry"
	situations "proto/hero/situations/v2"
	"workflow/internal/situations/usecase"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/emptypb"
)

// SituationServer implements the SituationService RPCs.
type SituationServer struct {
	situationUseCase *usecase.SituationUseCase
}

// NewSituationServer creates a new SituationServer.
func NewSituationServer(situationUseCase *usecase.SituationUseCase) *SituationServer {
	return &SituationServer{
		situationUseCase: situationUseCase,
	}
}

// CreateSituation creates a new situation.
func (server *SituationServer) CreateSituation(
	requestContext context.Context,
	createRequest *connect.Request[situations.CreateSituationRequest],
) (*connect.Response[situations.CreateSituationResponse], error) {
	createdSituation, err := server.situationUseCase.CreateSituation(requestContext, createRequest.Msg.Situation)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "CreateSituation")
	}
	responsePayload := &situations.CreateSituationResponse{
		Situation: createdSituation,
	}
	response := connect.NewResponse(responsePayload)
	return response, nil
}

// GetSituation retrieves a situation by its ID.
func (server *SituationServer) GetSituation(
	requestContext context.Context,
	getRequest *connect.Request[situations.GetSituationRequest],
) (*connect.Response[situations.Situation], error) {
	retrievedSituation, err := server.situationUseCase.GetSituation(requestContext, getRequest.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "GetSituation")
	}
	response := connect.NewResponse(retrievedSituation)
	return response, nil
}

// UpdateSituation updates an existing situation.
func (server *SituationServer) UpdateSituation(
	requestContext context.Context,
	updateRequest *connect.Request[situations.UpdateSituationRequest],
) (*connect.Response[situations.Situation], error) {
	updatedSituation, err := server.situationUseCase.UpdateSituation(requestContext, updateRequest.Msg.Situation)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "UpdateSituation")
	}
	response := connect.NewResponse(updatedSituation)
	return response, nil
}

// ListSituations returns a paginated list of situations.
func (server *SituationServer) ListSituations(
	requestContext context.Context,
	listRequest *connect.Request[situations.ListSituationsRequest],
) (*connect.Response[situations.ListSituationsResponse], error) {
	response, err := server.situationUseCase.ListSituations(
		requestContext,
		int(listRequest.Msg.PageSize),
		listRequest.Msg.PageToken,
		listRequest.Msg.Status,
		listRequest.Msg.TriggerSource,
		listRequest.Msg.Type,
		listRequest.Msg.OrderBy,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListSituations")
	}
	return connect.NewResponse(response), nil
}

// ListSituationsForAsset returns a paginated list of situations for a given asset.
// ListSituationsForAsset returns a paginated list of situations associated with a specific asset.
func (server *SituationServer) ListSituationsForAsset(
	requestContext context.Context,
	listRequest *connect.Request[situations.ListSituationsForAssetRequest],
) (*connect.Response[situations.ListSituationsForAssetResponse], error) {
	// Convert string timestamps from the request to time.Time objects.
	var createdAfter time.Time
	var createdBefore time.Time
	var err error

	if listRequest.Msg.CreatedAfter != "" {
		createdAfter, err = time.Parse(time.RFC3339, listRequest.Msg.CreatedAfter)
		if err != nil {
			// Add context to current span for validation errors
			if span := herosentry.CurrentSpan(requestContext); span != nil {
				span.SetTag("validation.error", "invalid_timestamp")
				span.SetData("validation.field", "created_after")
				span.SetData("validation.value", listRequest.Msg.CreatedAfter)
			}
			return nil, connecthelper.AsConnectError(requestContext, fmt.Errorf("invalid created_after timestamp format: %w", err), "ListSituationsForAsset", herosentry.ErrorTypeValidation)
		}
	}
	if listRequest.Msg.CreatedBefore != "" {
		createdBefore, err = time.Parse(time.RFC3339, listRequest.Msg.CreatedBefore)
		if err != nil {
			// Add context to current span for validation errors
			if span := herosentry.CurrentSpan(requestContext); span != nil {
				span.SetTag("validation.error", "invalid_timestamp")
				span.SetData("validation.field", "created_before")
				span.SetData("validation.value", listRequest.Msg.CreatedBefore)
			}
			return nil, connecthelper.AsConnectError(requestContext, fmt.Errorf("invalid created_before timestamp format: %w", err), "ListSituationsForAsset", herosentry.ErrorTypeValidation)
		}
	}

	response, err := server.situationUseCase.ListSituationsForAsset(
		requestContext,
		int(listRequest.Msg.PageSize),
		listRequest.Msg.PageToken,
		listRequest.Msg.AssetId,
		listRequest.Msg.Status,
		listRequest.Msg.Type,
		listRequest.Msg.TriggerSource,
		listRequest.Msg.Priority,
		listRequest.Msg.ReporterId,
		createdAfter,
		createdBefore,
		listRequest.Msg.MinLatitude,
		listRequest.Msg.MaxLatitude,
		listRequest.Msg.MinLongitude,
		listRequest.Msg.MaxLongitude,
		listRequest.Msg.Tags,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListSituationsForAsset")
	}

	return connect.NewResponse(response), nil
}

// DeleteSituation deletes a situation by its ID.
func (server *SituationServer) DeleteSituation(
	requestContext context.Context,
	deleteRequest *connect.Request[situations.DeleteSituationRequest],
) (*connect.Response[emptypb.Empty], error) {
	err := server.situationUseCase.DeleteSituation(requestContext, deleteRequest.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "DeleteSituation")
	}
	response := connect.NewResponse(&emptypb.Empty{})
	return response, nil
}

// AddSituationUpdate adds an update entry to an existing situation.
func (server *SituationServer) AddSituationUpdate(
	requestContext context.Context,
	updateEntryRequest *connect.Request[situations.AddSituationUpdateRequest],
) (*connect.Response[situations.Situation], error) {
	updatedSituation, err := server.situationUseCase.AddSituationUpdate(
		requestContext,
		updateEntryRequest.Msg.Id,
		updateEntryRequest.Msg.Update,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddSituationUpdate")
	}
	response := connect.NewResponse(updatedSituation)
	return response, nil
}

// RemoveSituationUpdate removes an update entry from an existing situation.
func (server *SituationServer) RemoveSituationUpdate(
	requestContext context.Context,
	removeUpdateRequest *connect.Request[situations.RemoveSituationUpdateRequest],
) (*connect.Response[situations.Situation], error) {
	updatedSituation, err := server.situationUseCase.RemoveSituationUpdate(
		requestContext,
		removeUpdateRequest.Msg.Id,
		removeUpdateRequest.Msg.Update,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RemoveSituationUpdate")
	}
	response := connect.NewResponse(updatedSituation)
	return response, nil
}

// AddSituationTag adds a tag to an existing situation.
func (server *SituationServer) AddSituationTag(
	requestContext context.Context,
	addTagRequest *connect.Request[situations.AddSituationTagRequest],
) (*connect.Response[situations.Situation], error) {
	updatedSituation, err := server.situationUseCase.AddSituationTag(
		requestContext,
		addTagRequest.Msg.Id,
		addTagRequest.Msg.Tag,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddSituationTag")
	}
	response := connect.NewResponse(updatedSituation)
	return response, nil
}

// RemoveSituationTag removes a tag from an existing situation.
func (server *SituationServer) RemoveSituationTag(
	requestContext context.Context,
	removeTagRequest *connect.Request[situations.RemoveSituationTagRequest],
) (*connect.Response[situations.Situation], error) {
	updatedSituation, err := server.situationUseCase.RemoveSituationTag(
		requestContext,
		removeTagRequest.Msg.Id,
		removeTagRequest.Msg.Tag,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RemoveSituationTag")
	}
	response := connect.NewResponse(updatedSituation)
	return response, nil
}

// AddRelatedSituation adds a related situation reference.
func (server *SituationServer) AddRelatedSituation(
	requestContext context.Context,
	addRelatedRequest *connect.Request[situations.AddRelatedSituationRequest],
) (*connect.Response[situations.Situation], error) {
	updatedSituation, err := server.situationUseCase.AddRelatedSituation(
		requestContext,
		addRelatedRequest.Msg.Id,
		addRelatedRequest.Msg.RelatedSituationId,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddRelatedSituation")
	}
	response := connect.NewResponse(updatedSituation)
	return response, nil
}

// RemoveRelatedSituation removes a related situation reference.
func (server *SituationServer) RemoveRelatedSituation(
	requestContext context.Context,
	removeRelatedRequest *connect.Request[situations.RemoveRelatedSituationRequest],
) (*connect.Response[situations.Situation], error) {
	updatedSituation, err := server.situationUseCase.RemoveRelatedSituation(
		requestContext,
		removeRelatedRequest.Msg.Id,
		removeRelatedRequest.Msg.RelatedSituationId,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RemoveRelatedSituation")
	}
	response := connect.NewResponse(updatedSituation)
	return response, nil
}

// AddMediaAttachmentForSituation adds a media attachment to an existing situation.
func (server *SituationServer) AddMediaAttachmentForSituation(
	requestContext context.Context,
	addMediaRequest *connect.Request[situations.AddMediaAttachmentForSituationRequest],
) (*connect.Response[situations.Situation], error) {
	updatedSituation, err := server.situationUseCase.AddMediaAttachmentForSituation(
		requestContext,
		addMediaRequest.Msg.Id,
		addMediaRequest.Msg.MediaAttachment,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddMediaAttachmentForSituation")
	}
	response := connect.NewResponse(updatedSituation)
	return response, nil
}

// RemoveMediaAttachmentForSituation removes a media attachment from an existing situation.
func (server *SituationServer) RemoveMediaAttachmentForSituation(
	requestContext context.Context,
	removeMediaRequest *connect.Request[situations.RemoveMediaAttachmentForSituationRequest],
) (*connect.Response[situations.Situation], error) {
	updatedSituation, err := server.situationUseCase.RemoveMediaAttachmentForSituation(
		requestContext,
		removeMediaRequest.Msg.Id,
		removeMediaRequest.Msg.MediaAttachment,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RemoveMediaAttachmentForSituation")
	}
	response := connect.NewResponse(updatedSituation)
	return response, nil
}

// AddAdditionalInfo merges additional JSON info into an existing situation.
func (server *SituationServer) AddAdditionalInfo(
	requestContext context.Context,
	addInfoRequest *connect.Request[situations.AddAdditionalInfoRequest],
) (*connect.Response[situations.AddAdditionalInfoResponse], error) {
	id, mergedInfo, err := server.situationUseCase.AddAdditionalInfo(
		requestContext,
		addInfoRequest.Msg.Id,
		addInfoRequest.Msg.AdditionalInfoJson,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddAdditionalInfo")
	}
	responsePayload := &situations.AddAdditionalInfoResponse{
		Id:                 id,
		AdditionalInfoJson: mergedInfo,
	}
	response := connect.NewResponse(responsePayload)
	return response, nil
}

// SearchSituations provides robust search capabilities with multiple filtering options.
func (server *SituationServer) SearchSituations(
	requestContext context.Context,
	searchRequest *connect.Request[situations.SearchSituationsRequest],
) (*connect.Response[situations.SearchSituationsResponse], error) {
	response, err := server.situationUseCase.SearchSituations(
		requestContext,
		searchRequest.Msg,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "SearchSituations")
	}

	return connect.NewResponse(response), nil
}

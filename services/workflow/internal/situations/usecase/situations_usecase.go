package usecase

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	_ "modernc.org/sqlite"

	clients "common/clients/services"
	"common/herosentry"
	commonUtils "common/utils"
	situations "proto/hero/situations/v2"
	assetRepository "workflow/internal/assets/data"
	workflowUtils "workflow/internal/common/utils"
	orderRepository "workflow/internal/orders/data"
	situationRepository "workflow/internal/situations/data"

	permspb "proto/hero/permissions/v1"

	"connectrpc.com/connect"
)

// SituationUseCase defines the use‑case layer for situation operations.
type SituationUseCase struct {
	database      *sql.DB // Only needed for transactional operations.
	assetRepo     assetRepository.AssetRepository
	situationRepo situationRepository.SituationRepository
	orderRepo     orderRepository.OrderRepository
	permsClient   clients.PermissionClient
}

// NewSituationUseCase creates a new SituationUseCase.
func NewSituationUseCase(
	database *sql.DB,
	assetRepo assetRepository.AssetRepository,
	situationRepo situationRepository.SituationRepository,
	orderRepo orderRepository.OrderRepository,
	permsClient clients.PermissionClient,
) (*SituationUseCase, error) {
	// If no database is provided, open an in‑memory SQLite DB (required to support transactions in in‑memory mode).
	if database == nil {
		return nil, errors.New("database is nil: cannot initialize SituationUseCase")
	}

	return &SituationUseCase{
		database:      database,
		assetRepo:     assetRepo,
		situationRepo: situationRepo,
		orderRepo:     orderRepo,
		permsClient:   permsClient,
	}, nil
}

// CreateSituation creates a new situation.
func (situationUseCase *SituationUseCase) CreateSituation(requestContext context.Context, newSituation *situations.Situation) (*situations.Situation, error) {
	// Start span for business logic
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.CreateSituation")
	defer finishSpan()

	// Add business context
	span.SetTag("situation.type", newSituation.Type.String())
	span.SetTag("situation.priority", fmt.Sprintf("%d", newSituation.Priority))
	span.SetTag("situation.reporter_id", newSituation.ReporterId)

	// Set default resource type and initial status.
	newSituation.ResourceType = "SITUATION"
	if newSituation.Status == situations.SituationStatus_SITUATION_STATUS_UNSPECIFIED {
		newSituation.Status = situations.SituationStatus_SITUATION_STATUS_CREATED
	}
	// Set a default empty JSON object if AdditionalInfoJson is not provided.
	if newSituation.AdditionalInfoJson == "" {
		newSituation.AdditionalInfoJson = "{}"
	}

	// Validate contact email if provided.
	if newSituation.ContactEmail != "" && !commonUtils.IsValidEmail(newSituation.ContactEmail) {
		validationError := fmt.Errorf("invalid email format")
		herosentry.CaptureException(spanContext, validationError, herosentry.ErrorTypeValidation, "Email validation failed for situation creation")
		return nil, validationError
	}

	// Begin a transaction since side effects need to be part of the same commit.
	transaction, transactionError := situationUseCase.database.BeginTx(spanContext, nil)
	if transactionError != nil {
		herosentry.CaptureException(spanContext, transactionError, herosentry.ErrorTypeDatabase, "Failed to begin transaction for situation creation")
		return nil, transactionError
	}

	// Set current time for timestamps
	currentTime := time.Now()
	newSituation.CreateTime = commonUtils.TimeToISO8601String(currentTime)
	newSituation.UpdateTime = commonUtils.TimeToISO8601String(currentTime)

	// Insert the new situation using the situationRepo.
	createdSituation, err := situationUseCase.situationRepo.CreateSituation(spanContext, transaction, newSituation)
	if err != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to create situation in repository")
		return nil, err
	}

	// Track created situation ID
	span.SetTag("situation.id", createdSituation.Id)

	// Check and execute side effects.
	sideEffectChecker := &SituationSideEffectChecker{}
	sideEffects, upcomingChangedSituation := sideEffectChecker.CheckSituationStatusChangeSideEffect(newSituation)

	if len(sideEffects) > 0 {
		// Start span for side effects
		sideEffectContext, sideEffectSpan, finishSideEffect := herosentry.StartSpan(spanContext, "SituationUseCase.ExecuteSideEffects")
		defer finishSideEffect()
		sideEffectSpan.SetTag("side_effects.count", fmt.Sprintf("%d", len(sideEffects)))

		sideEffectExecutor := NewSituationSideEffectExecutor()
		for _, effect := range sideEffects {
			if err := sideEffectExecutor.ExecuteSideEffect(sideEffectContext, transaction, effect, upcomingChangedSituation, situationUseCase); err != nil {
				_ = transaction.Rollback()
				herosentry.CaptureException(sideEffectContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Side effect execution failed: %v", effect))
				return nil, fmt.Errorf("side effect execution failed: %w", err)
			}
		}
	}

	// Commit the transaction.
	if err := transaction.Commit(); err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to commit transaction for situation creation")
		return nil, err
	}

	return createdSituation, nil
}

// GetSituation retrieves a situation by its ID.
func (situationUseCase *SituationUseCase) GetSituation(requestContext context.Context, situationID string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.GetSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)

	situation, err := situationUseCase.situationRepo.GetSituation(spanContext, nil, situationID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get situation %s", situationID))
		return nil, err
	}

	// Add retrieved situation details
	if situation != nil {
		span.SetTag("situation.type", situation.Type.String())
		span.SetTag("situation.status", situation.Status.String())
	}

	return situation, nil
}

// DeleteSituation deletes a situation.
func (situationUseCase *SituationUseCase) DeleteSituation(requestContext context.Context, situationID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.DeleteSituation")
	defer finishSpan()
	span.SetTag("situation.id", situationID)

	err := situationUseCase.situationRepo.DeleteSituation(spanContext, nil, situationID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete situation")
		return err
	}
	return nil
}

// ListSituations returns a paginated list of situations with optional filtering and ordering.
func (situationUseCase *SituationUseCase) ListSituations(
	requestContext context.Context,
	pageSize int,
	pageToken string,
	status situations.SituationStatus,
	triggerSource situations.TriggerSource,
	situationType situations.SituationType,
	orderBy string,
) (*situations.ListSituationsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.ListSituations")
	defer finishSpan()

	if pageSize <= 0 {
		pageSize = 100
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("filter.status", status.String())
	span.SetTag("filter.trigger_source", triggerSource.String())
	span.SetTag("filter.situation_type", situationType.String())
	span.SetTag("filter.order_by", orderBy)

	result, err := situationUseCase.situationRepo.ListSituations(
		spanContext,
		nil,
		pageSize,
		pageToken,
		status,
		triggerSource,
		situationType,
		orderBy,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list situations")
		return nil, err
	}

	resultFiltered, err := situationUseCase.filterSituations(spanContext, result.Situations, "ListSituations")
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to filter situations")
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(resultFiltered)))

	return &situations.ListSituationsResponse{
		Situations:    resultFiltered,
		NextPageToken: result.NextPageToken,
		TotalResults:  result.TotalResults,
	}, nil
}

// ListSituationsForAsset retrieves a paginated list of situations "touched" by a given asset,
// applying the same rich filters as the repository.
func (situationUseCase *SituationUseCase) ListSituationsForAsset(
	ctx context.Context,
	pageSize int,
	pageToken string,
	assetID string,
	status situations.SituationStatus,
	situationType situations.SituationType,
	triggerSource situations.TriggerSource,
	priority int32,
	reporterID string,
	createdAfter time.Time,
	createdBefore time.Time,
	minimumLatitude float64,
	maximumLatitude float64,
	minimumLongitude float64,
	maximumLongitude float64,
	tags []string,
) (*situations.ListSituationsForAssetResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "SituationUseCase.ListSituationsForAsset")
	defer finishSpan()

	if pageSize <= 0 {
		pageSize = 100
	}

	span.SetTag("asset.id", assetID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("filter.status", status.String())
	span.SetTag("filter.situation_type", situationType.String())
	span.SetTag("filter.trigger_source", triggerSource.String())
	span.SetTag("filter.priority", fmt.Sprintf("%d", priority))
	span.SetTag("filter.reporter_id", reporterID)
	span.SetTag("filter.tags_count", fmt.Sprintf("%d", len(tags)))

	result, err := situationUseCase.situationRepo.ListSituationsForAsset(
		spanContext,
		nil, // no transaction
		assetID,
		status,
		situationType,
		triggerSource,
		priority,
		reporterID,
		createdAfter,
		createdBefore,
		minimumLatitude,
		maximumLatitude,
		minimumLongitude,
		maximumLongitude,
		tags,
		pageSize,
		pageToken,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list situations for asset")
		return nil, err
	}

	resultFiltered, err := situationUseCase.filterSituations(spanContext, result.Situations, "ListSituationsForAsset")
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to filter situations")
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(resultFiltered)))

	return &situations.ListSituationsForAssetResponse{
		Situations:    resultFiltered,
		NextPageToken: result.NextPageToken,
		TotalResults:  result.TotalResults,
	}, nil
}

// UpdateSituation updates only the fields that are provided (non‑default) in updatedSituation.
// For scalar fields, a non‑zero (or non‑empty) value is considered present.
// This function uses a transaction so that if any step fails, all changes are rolled back.
func (situationUseCase *SituationUseCase) UpdateSituation(requestContext context.Context, updatedSituation *situations.Situation) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.UpdateSituation")
	defer finishSpan()

	span.SetTag("situation.id", updatedSituation.Id)
	span.SetTag("situation.status", updatedSituation.Status.String())
	span.SetTag("situation.type", updatedSituation.Type.String())

	// Begin a transaction.
	transaction, transactionError := situationUseCase.database.BeginTx(spanContext, nil)
	if transactionError != nil {
		herosentry.CaptureException(spanContext, transactionError, herosentry.ErrorTypeDatabase, "Failed to begin transaction for situation update")
		return nil, transactionError
	}

	// Retrieve the existing situation within the transaction.
	existingSituation, retrievalError := situationUseCase.situationRepo.GetSituation(spanContext, transaction, updatedSituation.Id)
	if retrievalError != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, retrievalError, herosentry.ErrorTypeDatabase, "Failed to retrieve existing situation")
		return nil, retrievalError
	}

	// ----- Immutable Fields Check -----
	// Check if the immutable ID is being changed.
	if updatedSituation.Id != "" && updatedSituation.Id != existingSituation.Id {
		_ = transaction.Rollback()
		immutableError := fmt.Errorf("immutable field 'id' cannot be updated")
		herosentry.CaptureException(spanContext, immutableError, herosentry.ErrorTypeValidation, "Attempted to update immutable field")
		return nil, immutableError
	}
	// Check if create_time is provided and differs from the existing value.
	if updatedSituation.CreateTime != "" && updatedSituation.CreateTime != existingSituation.CreateTime {
		_ = transaction.Rollback()
		immutableError := fmt.Errorf("immutable field 'create_time' cannot be updated")
		herosentry.CaptureException(spanContext, immutableError, herosentry.ErrorTypeValidation, "Attempted to update immutable field")
		return nil, immutableError
	}
	// Check if trigger_source is provided (and not the default value) and differs from the existing value.
	if updatedSituation.TriggerSource != situations.TriggerSource_TRIGGER_SOURCE_UNKNOWN && updatedSituation.TriggerSource != existingSituation.TriggerSource {
		_ = transaction.Rollback()
		immutableError := fmt.Errorf("immutable field 'trigger_source' cannot be updated")
		herosentry.CaptureException(spanContext, immutableError, herosentry.ErrorTypeValidation, "Attempted to update immutable field")
		return nil, immutableError
	}
	// Prevent modification of the resource type.
	if updatedSituation.ResourceType != "" && updatedSituation.ResourceType != existingSituation.ResourceType {
		_ = transaction.Rollback()
		immutableError := fmt.Errorf("resource type cannot be modified after creation")
		herosentry.CaptureException(spanContext, immutableError, herosentry.ErrorTypeValidation, "Attempted to update immutable field")
		return nil, immutableError
	}
	// ----- End Immutable Fields Check -----

	// Update only fields that are provided.
	if updatedSituation.Title != "" {
		existingSituation.Title = updatedSituation.Title
	}
	if updatedSituation.Priority != 0 {
		existingSituation.Priority = updatedSituation.Priority
	}
	// Only update the type if it is explicitly set (non‑zero).
	if updatedSituation.Type != situations.SituationType_SITUATION_TYPE_UNSPECIFIED {
		existingSituation.Type = updatedSituation.Type
	}
	if updatedSituation.Description != "" {
		existingSituation.Description = updatedSituation.Description
	}

	// For status, if non‑default.
	if updatedSituation.Status != situations.SituationStatus(0) {
		existingSituation.Status = updatedSituation.Status
	}
	if updatedSituation.ReporterId != "" {
		existingSituation.ReporterId = updatedSituation.ReporterId
	}
	if updatedSituation.ReporterName != "" {
		existingSituation.ReporterName = updatedSituation.ReporterName
	}
	if updatedSituation.ContactNo != "" {
		existingSituation.ContactNo = updatedSituation.ContactNo
	}
	if updatedSituation.ContactEmail != "" {
		if !commonUtils.IsValidEmail(updatedSituation.ContactEmail) {
			_ = transaction.Rollback()
			validationError := fmt.Errorf("invalid email format")
			herosentry.CaptureException(spanContext, validationError, herosentry.ErrorTypeValidation, "Email validation failed for situation update")
			return nil, validationError
		}
		existingSituation.ContactEmail = updatedSituation.ContactEmail
	}
	// Update address and record address update time.
	if updatedSituation.Address != "" {
		existingSituation.Address = updatedSituation.Address
	}
	// Always use the provided AddressUpdateTime if it exists, otherwise set current time if address changed
	if updatedSituation.AddressUpdateTime != "" {
		existingSituation.AddressUpdateTime = updatedSituation.AddressUpdateTime
	} else if updatedSituation.Address != "" {
		existingSituation.AddressUpdateTime = commonUtils.TimeToISO8601String(time.Now())
	}
	if updatedSituation.Latitude != 0 {
		existingSituation.Latitude = updatedSituation.Latitude
	}
	if updatedSituation.Longitude != 0 {
		existingSituation.Longitude = updatedSituation.Longitude
	}
	if updatedSituation.DueTime != "" {
		existingSituation.DueTime = updatedSituation.DueTime
	}

	if updatedSituation.IncidentTime != "" {
		existingSituation.IncidentTime = updatedSituation.IncidentTime
	}

	if updatedSituation.ResolvedTime != "" {
		existingSituation.ResolvedTime = updatedSituation.ResolvedTime
	}
	// Allow updating automation_enabled.
	existingSituation.AutomationEnabled = updatedSituation.AutomationEnabled

	if len(updatedSituation.Updates) > 0 {
		// Here, we choose to replace the existing update history.
		existingSituation.Updates = updatedSituation.Updates
	}

	// Update Tags.
	if len(updatedSituation.Tags) > 0 {
		existingSituation.Tags = updatedSituation.Tags
	}

	// Update Related Situation IDs.
	if len(updatedSituation.RelatedSituationsIds) > 0 {
		existingSituation.RelatedSituationsIds = updatedSituation.RelatedSituationsIds
	}

	// Update Media Attachments.
	if len(updatedSituation.MediaAttachments) > 0 {
		existingSituation.MediaAttachments = updatedSituation.MediaAttachments
	}

	// Update AdditionalInfoJson if provided.
	if updatedSituation.AdditionalInfoJson != "" {
		existingSituation.AdditionalInfoJson = updatedSituation.AdditionalInfoJson
	}

	// Always update the update time.
	existingSituation.UpdateTime = commonUtils.TimeToISO8601String(time.Now())

	// Persist the updated situation within the transaction.
	updatedSituation, updateError := situationUseCase.situationRepo.UpdateSituation(spanContext, transaction, existingSituation)
	if updateError != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, updateError, herosentry.ErrorTypeDatabase, "Failed to update situation in repository")
		return nil, updateError
	}

	// Check and execute side effects.
	sideEffectChecker := &SituationSideEffectChecker{}
	sideEffects, upcomingChangedSituation := sideEffectChecker.CheckSituationStatusChangeSideEffect(existingSituation)

	if len(sideEffects) > 0 {
		// Start span for side effects
		sideEffectContext, sideEffectSpan, finishSideEffect := herosentry.StartSpan(spanContext, "SituationUseCase.ExecuteSideEffects")
		defer finishSideEffect()
		sideEffectSpan.SetTag("side_effects.count", fmt.Sprintf("%d", len(sideEffects)))

		sideEffectExecutor := NewSituationSideEffectExecutor()
		for _, effect := range sideEffects {
			if err := sideEffectExecutor.ExecuteSideEffect(sideEffectContext, transaction, effect, upcomingChangedSituation, situationUseCase); err != nil {
				_ = transaction.Rollback()
				herosentry.CaptureException(sideEffectContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Side effect execution failed: %v", effect))
				return nil, fmt.Errorf("side effect execution failed: %w", err)
			}
		}
	}

	// Commit the transaction.
	if commitError := transaction.Commit(); commitError != nil {
		herosentry.CaptureException(spanContext, commitError, herosentry.ErrorTypeDatabase, "Failed to commit transaction for situation update")
		return nil, commitError
	}

	return updatedSituation, nil
}

// AddAdditionalInfo merges the provided JSON into an existing situation's additional_info_json.
func (situationUseCase *SituationUseCase) AddAdditionalInfo(ctx context.Context, id string, newInfo string) (string, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "SituationUseCase.AddAdditionalInfo")
	defer finishSpan()

	span.SetTag("situation.id", id)

	// Begin a transaction.
	tx, err := situationUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for additional info")
		return "", "", err
	}

	// Retrieve the existing situation within the transaction.
	situationRecord, err := situationUseCase.situationRepo.GetSituation(spanContext, tx, id)
	if err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to retrieve situation for additional info")
		return "", "", err
	}

	// Unmarshal the existing additional info.
	var existingMap map[string]interface{}
	if err = json.Unmarshal([]byte(situationRecord.AdditionalInfoJson), &existingMap); err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to parse existing additional info JSON")
		return "", "", fmt.Errorf("failed to parse existing additional info: %w", err)
	}

	// Unmarshal the new additional info.
	var newMap map[string]interface{}
	if err = json.Unmarshal([]byte(newInfo), &newMap); err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to parse new additional info JSON")
		return "", "", fmt.Errorf("failed to parse new additional info: %w", err)
	}

	// Merge the new info into the existing map.
	workflowUtils.MergeJSON(existingMap, newMap)

	// Marshal the merged map back into JSON.
	mergedBytes, err := json.Marshal(existingMap)
	if err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal merged additional info JSON")
		return "", "", fmt.Errorf("failed to marshal merged additional info: %w", err)
	}
	mergedInfo := string(mergedBytes)

	updatedID, err := situationUseCase.situationRepo.UpdateAdditionalInfoJSON(spanContext, tx, id, mergedInfo)
	if err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update additional info in repository")
		return "", "", err
	}

	// Commit the transaction.
	if err := tx.Commit(); err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to commit transaction for additional info")
		return "", "", err
	}

	return updatedID, mergedInfo, nil
}

// Fine-grained operations:

// AddSituationUpdate adds an update entry to an existing situation.
func (situationUseCase *SituationUseCase) AddSituationUpdate(requestContext context.Context, situationID string, updateEntry *situations.UpdateEntry) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.AddSituationUpdate")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("update.source", updateEntry.UpdateSource.String())

	situation, err := situationUseCase.situationRepo.AddSituationUpdate(spanContext, nil, situationID, updateEntry)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add situation update")
		return nil, err
	}
	return situation, nil
}

// RemoveSituationUpdate removes an update entry from an existing situation.
func (situationUseCase *SituationUseCase) RemoveSituationUpdate(requestContext context.Context, situationID string, updateEntry *situations.UpdateEntry) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.RemoveSituationUpdate")
	defer finishSpan()

	span.SetTag("situation.id", situationID)

	situation, err := situationUseCase.situationRepo.RemoveSituationUpdate(spanContext, nil, situationID, updateEntry)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to remove situation update")
		return nil, err
	}
	return situation, nil
}

// AddSituationTag adds a tag to an existing situation.
func (situationUseCase *SituationUseCase) AddSituationTag(requestContext context.Context, situationID string, tag string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.AddSituationTag")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("tag", tag)

	situation, err := situationUseCase.situationRepo.AddSituationTag(spanContext, nil, situationID, tag)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add situation tag")
		return nil, err
	}
	return situation, nil
}

// RemoveSituationTag removes a tag from an existing situation.
func (situationUseCase *SituationUseCase) RemoveSituationTag(requestContext context.Context, situationID string, tag string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.RemoveSituationTag")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("tag", tag)

	situation, err := situationUseCase.situationRepo.RemoveSituationTag(spanContext, nil, situationID, tag)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to remove situation tag")
		return nil, err
	}
	return situation, nil
}

// AddRelatedSituation adds a related situation reference.
func (situationUseCase *SituationUseCase) AddRelatedSituation(requestContext context.Context, situationID string, relatedSituationID string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.AddRelatedSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("related_situation.id", relatedSituationID)

	situation, err := situationUseCase.situationRepo.AddRelatedSituation(spanContext, nil, situationID, relatedSituationID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add related situation")
		return nil, err
	}
	return situation, nil
}

// RemoveRelatedSituation removes a related situation reference.
func (situationUseCase *SituationUseCase) RemoveRelatedSituation(requestContext context.Context, situationID string, relatedSituationID string) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.RemoveRelatedSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("related_situation.id", relatedSituationID)

	situation, err := situationUseCase.situationRepo.RemoveRelatedSituation(spanContext, nil, situationID, relatedSituationID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to remove related situation")
		return nil, err
	}
	return situation, nil
}

// AddMediaAttachmentForSituation adds a media attachment to an existing situation.
func (situationUseCase *SituationUseCase) AddMediaAttachmentForSituation(requestContext context.Context, situationID string, mediaAttachment *situations.MediaAttachment) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.AddMediaAttachmentForSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("media.content_type", mediaAttachment.ContentType)
	span.SetTag("media.attachment_id", mediaAttachment.AttachmentId)

	situation, err := situationUseCase.situationRepo.AddMediaAttachmentForSituation(spanContext, nil, situationID, mediaAttachment)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add media attachment")
		return nil, err
	}
	return situation, nil
}

// RemoveMediaAttachmentForSituation removes a media attachment from an existing situation.
func (situationUseCase *SituationUseCase) RemoveMediaAttachmentForSituation(requestContext context.Context, situationID string, mediaAttachment *situations.MediaAttachment) (*situations.Situation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.RemoveMediaAttachmentForSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("media.content_type", mediaAttachment.ContentType)

	situation, err := situationUseCase.situationRepo.RemoveMediaAttachmentForSituation(spanContext, nil, situationID, mediaAttachment)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to remove media attachment")
		return nil, err
	}
	return situation, nil
}

// SearchSituations provides rich search capabilities with multiple filtering options.
func (situationUseCase *SituationUseCase) SearchSituations(
	requestContext context.Context,
	searchReq *situations.SearchSituationsRequest,
) (*situations.SearchSituationsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "SituationUseCase.SearchSituations")
	defer finishSpan()

	if searchReq.PageSize <= 0 {
		searchReq.PageSize = 100
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", searchReq.PageSize))
	span.SetTag("filter.status_count", fmt.Sprintf("%d", len(searchReq.Status)))
	span.SetTag("filter.type_count", fmt.Sprintf("%d", len(searchReq.Type)))
	span.SetTag("search.query", searchReq.Query)

	result, err := situationUseCase.situationRepo.SearchSituations(
		spanContext,
		nil, // no transaction
		searchReq,
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to search situations")
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(result.Situations)))

	return result, nil
}

// helper function to filter situations based on permissions
func (situationUseCase *SituationUseCase) filterSituations(ctx context.Context, situations_ []*situations.Situation, action string) ([]*situations.Situation, error) {
	resultFiltered := []*situations.Situation{}

	// for each situation, check if the user has permission to view it
	objectIds := make([]string, 0)
	for _, situation := range situations_ {
		objectIds = append(objectIds, situation.Id)
	}
	permRequest := connect.NewRequest(&permspb.BatchCheckPermissionRequest{
		Category:  "Situation",
		Action:    action,
		ObjectIds: objectIds,
	})
	perms, err := situationUseCase.permsClient.BatchCheckPermission(ctx, permRequest)
	if err != nil {
		return nil, err
	}
	for _, situation := range situations_ {
		if perms.Msg.Results[situation.Id] {
			resultFiltered = append(resultFiltered, situation)
		}
	}
	return resultFiltered, nil
}

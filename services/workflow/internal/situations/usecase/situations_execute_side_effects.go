// situations_execute_side_effects.go
package usecase

import (
	"context"
	"database/sql"
	"fmt"

	"common/herosentry"
	"proto/hero/assets/v2"
	orders "proto/hero/orders/v2"
	"proto/hero/situations/v2"
)

// SituationSideEffectExecutor handles execution of side effects for situations.
type SituationSideEffectExecutor struct {
	// Add any dependencies (e.g., repositories, services) as needed.
}

// NewSituationSideEffectExecutor creates a new SituationSideEffectExecutor.
func NewSituationSideEffectExecutor() *SituationSideEffectExecutor {
	return &SituationSideEffectExecutor{}
}

// ExecuteSideEffect executes the given side effect for the provided situation within the same transaction.
// The tx parameter represents the current transaction.
func (exec *SituationSideEffectExecutor) ExecuteSideEffect(ctx context.Context, tx *sql.Tx, effect SituationSideEffectType, updatedSituation *situations.Situation, situationUseCase *SituationUseCase) error {
	// Add side effect details to current span
	if span := herosentry.CurrentSpan(ctx); span != nil {
		span.SetTag("side_effect.type", fmt.Sprintf("%v", effect))
		span.SetTag("side_effect.situation_id", updatedSituation.Id)
		span.SetData("side_effect.situation_status", updatedSituation.Status.String())
	}

	switch effect {
	case SituationSideEffect_CreateTriageOrder:
		return exec.tryCreatingTriageOrder(ctx, tx, updatedSituation, situationUseCase)
	case SituationSideEffect_CreateReportOrder:
		return exec.tryCreatingReportOrder(ctx, tx, updatedSituation, situationUseCase)
	case SituationSideEffect_CompleteAllNonReportOrdersAndMarkingAssetAvailable:
		return exec.tryCompletingAllNonReportOrdersAndMarkingAssetAvailable(ctx, tx, updatedSituation, situationUseCase)
	default:
		// No side effect to execute.
		return nil
	}
}

// tryCreatingTriageOrder contains the logic to create a triage order within the transaction.
// It uses the order repository to persist the new order.
func (exec *SituationSideEffectExecutor) tryCreatingTriageOrder(ctx context.Context, tx *sql.Tx, updatedSituation *situations.Situation, situationUseCase *SituationUseCase) error {
	fmt.Printf("Executing side effect within transaction: Creating triage order for situation ID %v\n", updatedSituation.Id)

	appropirateTriageOrderType := orders.OrderType_ORDER_TYPE_TRIAGE_MEMBER_REPORT

	if updatedSituation.TriggerSource == situations.TriggerSource_TRIGGER_SOURCE_AUTOMATED_ALERT ||
		updatedSituation.TriggerSource == situations.TriggerSource_TRIGGER_SOURCE_CAMERA_EVENT ||
		updatedSituation.TriggerSource == situations.TriggerSource_TRIGGER_SOURCE_IOT_DEVICE {
		appropirateTriageOrderType = orders.OrderType_ORDER_TYPE_TRIAGE_CAMERA_INCIDENT
	} else if updatedSituation.TriggerSource == situations.TriggerSource_TRIGGER_SOURCE_RADIO {
		appropirateTriageOrderType = orders.OrderType_ORDER_TYPE_TRIAGE_AGENT_REPORT
	}

	// Collect all orders for the situation to check for existing triage orders
	allOrdersForSituation := []*orders.Order{}
	pageToken := ""
	const pageSize = 50

	for {
		ordersList, err := situationUseCase.orderRepo.ListOrdersForSituation(ctx, nil, updatedSituation.Id, pageSize, pageToken, orders.OrderStatus_ORDER_STATUS_CREATED)
		if err != nil {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to list orders for situation %s", updatedSituation.Id))
			return fmt.Errorf("error listing orders for situation: %w", err)
		}
		allOrdersForSituation = append(allOrdersForSituation, ordersList.Orders...)
		if ordersList.PageToken == "" {
			break
		}
		pageToken = ordersList.PageToken
	}

	// Check all orders for an existing triage order
	for _, order := range allOrdersForSituation {
		if order.Type == orders.OrderType_ORDER_TYPE_TRIAGE_MEMBER_REPORT ||
			order.Type == orders.OrderType_ORDER_TYPE_TRIAGE_AGENT_REPORT ||
			order.Type == orders.OrderType_ORDER_TYPE_TRIAGE_CAMERA_INCIDENT {
			// Triage order already exists, so no need to create a new one.
			return nil
		}
	}

	// If we got here, no triage order exists for the situation; so creating one.
	order := &orders.Order{
		SituationId: updatedSituation.Id,
		Type:        appropirateTriageOrderType,
		Status:      orders.OrderStatus_ORDER_STATUS_CREATED,
		CreatedBy:   situations.UpdateSource_UPDATE_SOURCE_API_SIDE_EFFECT,
	}
	if _, err := situationUseCase.orderRepo.CreateOrder(ctx, tx, order); err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create triage order for situation %s", updatedSituation.Id))
		return err
	}

	// Log successful creation to span
	if span := herosentry.CurrentSpan(ctx); span != nil {
		span.SetTag("side_effect.order_created", "true")
		span.SetTag("side_effect.order_type", appropirateTriageOrderType.String())
	}

	return nil
}

// tryCreatingReportOrder contains the logic to create a report order within the transaction.
func (exec *SituationSideEffectExecutor) tryCreatingReportOrder(ctx context.Context, tx *sql.Tx, updatedSituation *situations.Situation, situationUseCase *SituationUseCase) error {
	_ = ctx              // mark ctx as used to avoid linter warnings
	_ = tx               // mark tx as used to avoid linter warnings
	_ = situationUseCase // mark situationUseCase as used to avoid linter warnings
	fmt.Printf("Executing side effect within transaction: Creating report order for situation ID %v\n", updatedSituation.Id)
	// TODO : Need product definition for report order.
	return nil
}

// tryCompletingAllNonReportOrders contains the logic to complete all non-report orders when a situation is resolved.
func (exec *SituationSideEffectExecutor) tryCompletingAllNonReportOrdersAndMarkingAssetAvailable(ctx context.Context, tx *sql.Tx, updatedSituation *situations.Situation, situationUseCase *SituationUseCase) error {
	fmt.Printf("Executing side effect within transaction: Completing all non-report orders for situation ID %v\n", updatedSituation.Id)

	// Initialize pagination.
	pageToken := ""
	const pageSize = 50

	// First, accumulate all orders in a slice.
	allOrdersForSituation := []*orders.Order{}
	for {
		// There is a reason I am passing nil for the transaction here.
		// This is because I want to read the orders in a read-only manner.
		// I don't want to lock the orders table for reading while I am also updating it.
		ordersSlice, err := situationUseCase.orderRepo.ListOrdersForSituation(ctx, nil, updatedSituation.Id, pageSize, pageToken, orders.OrderStatus_ORDER_STATUS_UNSPECIFIED)
		if err != nil {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to list orders for situation %s", updatedSituation.Id))
			return fmt.Errorf("failed to list orders for situation %v: %w", updatedSituation.Id, err)
		}
		// Append orders from this page.
		allOrdersForSituation = append(allOrdersForSituation, ordersSlice.Orders...)

		// Break if there are no more pages.
		if ordersSlice.PageToken == "" {
			break
		}
		pageToken = ordersSlice.PageToken
	}

	// Now, iterate over the accumulated orders and complete them as needed.
	for _, order := range allOrdersForSituation {
		switch order.Type {
		case orders.OrderType_ORDER_TYPE_TRIAGE_MEMBER_REPORT,
			orders.OrderType_ORDER_TYPE_ASSIST_MEMBER,
			orders.OrderType_ORDER_TYPE_ASSIGN_AGENT:
			// Only complete orders that haven't already been completed.
			if order.Status != orders.OrderStatus_ORDER_STATUS_COMPLETED {
				if _, err := situationUseCase.orderRepo.CompleteOrder(ctx, tx, order.Id); err != nil {
					herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to complete order %s", order.Id))
					return fmt.Errorf("failed to complete order %v: %w", order.Id, err)
				}
				if order.AssetId != "" {
					// Try marking the asset as available.
					if err := exec.tryMarkingAssetAvailable(ctx, tx, situationUseCase, order.AssetId); err != nil {
						herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to mark asset %s as available", order.AssetId))
						return fmt.Errorf("failed to mark asset %v as available: %w", order.AssetId, err)
					}
				}
			}
		}
	}

	return nil
}

// tryMarkingAssetAvailable contains the logic to mark an asset as available.
func (exec *SituationSideEffectExecutor) tryMarkingAssetAvailable(ctx context.Context, tx *sql.Tx, situationUseCase *SituationUseCase, assetId string) error {
	// Transaction is nil for GetAsset because this is a non-blocking call.
	asset, err := situationUseCase.assetRepo.GetAsset(ctx, nil, assetId)
	if err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase, "Failed to retrieve asset")
		return fmt.Errorf("failed to retrieve asset %v: %w", assetId, err)
	}
	if asset == nil {
		err := fmt.Errorf("asset %v not found", assetId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound, "Asset not found", true)
		return err
	}
	// Check if the asset is already available.
	if asset.Status == assets.AssetStatus_ASSET_STATUS_AVAILABLE {
		fmt.Printf("Asset %v is already available; no action needed.\n", assetId)
		return nil
	}
	if asset.Status != assets.AssetStatus_ASSET_STATUS_BUSY {
		fmt.Printf("Asset %v is not in busy status; no action needed.\n", assetId)
		return nil
	}

	// List active orders for the asset.
	allActiveOrders := []*orders.Order{}
	pageToken := ""
	const pageSize = 50

	for {
		activeOrders, err := situationUseCase.orderRepo.ListActiveAssignedOrdersForAsset(ctx, tx, assetId, pageSize, pageToken)
		if err != nil {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to list active orders for asset %s", assetId))
			return fmt.Errorf("error listing active orders for asset %v: %w", assetId, err)
		}
		allActiveOrders = append(allActiveOrders, activeOrders.Orders...)
		if activeOrders.PageToken == "" {
			break
		}
		pageToken = activeOrders.PageToken
	}

	// Check if there is any other ASSIST_MEMBER order for the same asset.
	activeAssistOrderExists := false
	for _, order := range allActiveOrders {
		if order.Type == orders.OrderType_ORDER_TYPE_ASSIST_MEMBER &&
			(order.Status == orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED ||
				order.Status == orders.OrderStatus_ORDER_STATUS_IN_PROGRESS ||
				order.Status == orders.OrderStatus_ORDER_STATUS_CREATED) {
			activeAssistOrderExists = true
			break
		}
	}

	// If there is another active ASSIST_MEMBER order, we keep the asset busy.
	if activeAssistOrderExists {
		fmt.Printf("Other active ASSIST_MEMBER orders found for asset %v; asset remains busy.\n", assetId)
		return nil
	}

	// No active ASSIST_MEMBER order exists; mark the asset as available.

	// Update the asset status to available.
	asset.Status = assets.AssetStatus_ASSET_STATUS_AVAILABLE
	if _, err := situationUseCase.assetRepo.UpdateAsset(ctx, tx, asset); err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update asset %s status to available", asset.Id))
		return fmt.Errorf("failed to update asset %v: %w", asset.Id, err)
	}
	return nil
}

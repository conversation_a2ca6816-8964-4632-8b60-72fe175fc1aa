package usecase

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"common/herosentry"
	commonUtils "common/utils"
	assets "proto/hero/assets/v2"
	orders "proto/hero/orders/v2"
	"proto/hero/situations/v2"
	assetRepository "workflow/internal/assets/data"
	orderRepository "workflow/internal/orders/data"
	reportRepository "workflow/internal/reports/data"
	situationRepository "workflow/internal/situations/data"

	commonWorkflowUtils "workflow/internal/common/utils"

	_ "modernc.org/sqlite" // required to support transaction for in-memory db
)

// defaultPageSize is the default number of items to return in a paginated list.
const defaultPageSize = 100

// OrderUseCase defines the use-case layer for order operations.
type OrderUseCase struct {
	database      *sql.DB // Only needed for transactional operations.
	assetRepo     assetRepository.AssetRepository
	situationRepo situationRepository.SituationRepository
	orderRepo     orderRepository.OrderRepository
	reportRepo    reportRepository.ReportRepository
}

// NewOrderUseCase creates a new OrderUseCase.
func NewOrderUseCase(database *sql.DB,
	assetRepo assetRepository.AssetRepository,
	situationRepo situationRepository.SituationRepository,
	orderRepo orderRepository.OrderRepository,
	reportRepo reportRepository.ReportRepository,
) (*OrderUseCase, error) {
	// For in-memory orderRepo, we need a dummy DB to support transactions.
	if database == nil {
		return nil, fmt.Errorf("database is nil: cannot initialize OrderUseCase")
	}
	return &OrderUseCase{
		database:      database,
		assetRepo:     assetRepo,
		situationRepo: situationRepo,
		orderRepo:     orderRepo,
		reportRepo:    reportRepo,
	}, nil
}

// CreateOrder creates a new order using a transaction to ensure atomic multi‑table updates.
// Side effects are checked and executed after creation.
func (orderUseCase *OrderUseCase) CreateOrder(requestContext context.Context, order *orders.Order) (*orders.Order, error) {
	// Start span for business logic
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.CreateOrder")
	defer finishSpan()

	// Add business context
	span.SetTag("order.type", order.Type.String())
	span.SetTag("order.priority", fmt.Sprintf("%d", order.Priority))
	span.SetTag("order.asset_id", order.AssetId)

	transaction, err := orderUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for order creation")
		return nil, err
	}

	if order.AdditionalInfoJson == "" {
		order.AdditionalInfoJson = "{}"
	}

	if order.Status == orders.OrderStatus_ORDER_STATUS_UNSPECIFIED {
		order.Status = orders.OrderStatus_ORDER_STATUS_CREATED
	}

	createdOrder, err := orderUseCase.orderRepo.CreateOrder(spanContext, transaction, order)
	if err != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to create order in repository")
		return nil, err
	}

	// Track created order ID
	span.SetTag("order.id", createdOrder.Id)

	// Check and execute side effects.
	sideEffectChecker := &OrderSideEffectChecker{}
	sideEffects, upcomingOrder := sideEffectChecker.CheckOrderStatusChangeSideEffect(order)

	if len(sideEffects) > 0 {
		// Start span for side effects
		sideEffectContext, sideEffectSpan, finishSideEffect := herosentry.StartSpan(spanContext, "OrderUseCase.ExecuteSideEffects")
		defer finishSideEffect()
		sideEffectSpan.SetTag("side_effects.count", fmt.Sprintf("%d", len(sideEffects)))

		sideEffectExecutor := NewOrderSideEffectExecutor()
		for _, effect := range sideEffects {
			if err := sideEffectExecutor.ExecuteSideEffect(sideEffectContext, transaction, effect, upcomingOrder, orderUseCase); err != nil {
				_ = transaction.Rollback()
				herosentry.CaptureException(sideEffectContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Side effect execution failed: %v", effect))
				return nil, fmt.Errorf("failed to execute side effect: %w", err)
			}
		}
	}

	if err := transaction.Commit(); err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to commit transaction for order creation")
		return nil, err
	}

	return createdOrder, nil
}

// GetOrder retrieves an order by its ID.
func (orderUseCase *OrderUseCase) GetOrder(requestContext context.Context, orderID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.GetOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	order, err := orderUseCase.orderRepo.GetOrder(spanContext, nil, orderID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get order %s", orderID))
		return nil, err
	}

	// Add retrieved order details
	if order != nil {
		span.SetTag("order.type", order.Type.String())
		span.SetTag("order.status", order.Status.String())
		span.SetTag("order.asset_id", order.AssetId)
	}

	return order, nil
}

// UpdateOrder updates an existing order and, if needed, checks and executes side effects.
func (orderUseCase *OrderUseCase) UpdateOrder(
	requestContext context.Context,
	updatedOrder *orders.Order,
) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.UpdateOrder")
	defer finishSpan()

	span.SetTag("order.id", updatedOrder.Id)
	span.SetTag("order.status", updatedOrder.Status.String())
	span.SetTag("order.type", updatedOrder.Type.String())

	transaction, beginError := orderUseCase.database.BeginTx(spanContext, nil)
	if beginError != nil {
		herosentry.CaptureException(spanContext, beginError, herosentry.ErrorTypeDatabase, "Failed to begin transaction for order update")
		return nil, beginError
	}

	// Retrieve the current order record within the transaction.
	existingOrder, getOrderError := orderUseCase.orderRepo.GetOrder(spanContext, transaction, updatedOrder.Id)
	if getOrderError != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, getOrderError, herosentry.ErrorTypeDatabase, "Failed to retrieve existing order")
		return nil, getOrderError
	}

	// Update fields only if a non‑default value is provided.
	if updatedOrder.SituationId != "" {
		existingOrder.SituationId = updatedOrder.SituationId
	}
	if updatedOrder.AssetId != "" {
		existingOrder.AssetId = updatedOrder.AssetId
	}
	if updatedOrder.Type != orders.OrderType_ORDER_TYPE_UNSPECIFIED {
		existingOrder.Type = updatedOrder.Type
	}
	if updatedOrder.Status != orders.OrderStatus(0) {
		existingOrder.Status = updatedOrder.Status
	}
	if updatedOrder.Instructions != "" {
		existingOrder.Instructions = updatedOrder.Instructions
	}
	if updatedOrder.Priority != 0 {
		existingOrder.Priority = updatedOrder.Priority
	}
	if updatedOrder.AdditionalInfoJson != "" {
		existingOrder.AdditionalInfoJson = updatedOrder.AdditionalInfoJson
	}
	if updatedOrder.TypeSpecificStatus != "" {
		existingOrder.TypeSpecificStatus = updatedOrder.TypeSpecificStatus
	}
	if updatedOrder.Notes != "" {
		existingOrder.Notes = updatedOrder.Notes
	}

	// Optional timestamp fields: update if provided (non-empty string).
	if updatedOrder.CompletionTime != "" {
		existingOrder.CompletionTime = updatedOrder.CompletionTime
	}
	if updatedOrder.AssignedTime != "" {
		existingOrder.AssignedTime = updatedOrder.AssignedTime
	}
	if updatedOrder.AcknowledgedTime != "" {
		existingOrder.AcknowledgedTime = updatedOrder.AcknowledgedTime
	}
	if updatedOrder.EstimatedCompletionTime != "" {
		existingOrder.EstimatedCompletionTime = updatedOrder.EstimatedCompletionTime
	}
	if updatedOrder.SnoozeUntil != "" {
		existingOrder.SnoozeUntil = updatedOrder.SnoozeUntil
	}

	// Other string fields.
	if updatedOrder.CancellationOrRejectionReason != "" {
		existingOrder.CancellationOrRejectionReason = updatedOrder.CancellationOrRejectionReason
	}
	if updatedOrder.Title != "" {
		existingOrder.Title = updatedOrder.Title
	}
	if updatedOrder.SnoozeReason != "" {
		existingOrder.SnoozeReason = updatedOrder.SnoozeReason
	}
	if updatedOrder.ResourceType != "" {
		existingOrder.ResourceType = updatedOrder.ResourceType
	}

	// For integer fields, update if non‑zero.
	if updatedOrder.RetryCount != 0 {
		existingOrder.RetryCount = updatedOrder.RetryCount
	}
	if updatedOrder.SnoozeCount != 0 {
		existingOrder.SnoozeCount = updatedOrder.SnoozeCount
	}

	// For enums that have a default "unspecified" value.
	if updatedOrder.CreatedBy != situations.UpdateSource_UPDATE_SOURCE_UNKNOWN {
		existingOrder.CreatedBy = updatedOrder.CreatedBy
	}

	// For repeated fields, replace if a non‑empty slice is provided.
	if len(updatedOrder.AllowedAssetTypes) > 0 {
		existingOrder.AllowedAssetTypes = updatedOrder.AllowedAssetTypes
	}
	filteredBlacklisted := make([]string, 0, len(updatedOrder.BlacklistedAssetIds))
	for _, id := range updatedOrder.BlacklistedAssetIds {
		if id != "" {
			filteredBlacklisted = append(filteredBlacklisted, id)
		}
	}
	if len(filteredBlacklisted) > 0 {
		existingOrder.BlacklistedAssetIds = filteredBlacklisted
	}
	if len(updatedOrder.Updates) > 0 {
		existingOrder.Updates = updatedOrder.Updates
	}

	if updatedOrder.Permissions != nil {
		existingOrder.Permissions = updatedOrder.Permissions
	}

	// Always update the update time.
	existingOrder.UpdateTime = commonUtils.TimeToISO8601String(time.Now())

	// Save the updated order.
	updatedOrder, updateError := orderUseCase.orderRepo.UpdateOrder(spanContext, transaction, existingOrder)
	if updateError != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, updateError, herosentry.ErrorTypeDatabase, "Failed to update order in repository")
		return nil, updateError
	}

	// Check and execute side effects.
	sideEffectChecker := &OrderSideEffectChecker{}
	sideEffects, upcomingOrder := sideEffectChecker.CheckOrderStatusChangeSideEffect(existingOrder)

	if len(sideEffects) > 0 {
		// Start span for side effects
		sideEffectContext, sideEffectSpan, finishSideEffect := herosentry.StartSpan(spanContext, "OrderUseCase.ExecuteSideEffects")
		defer finishSideEffect()
		sideEffectSpan.SetTag("side_effects.count", fmt.Sprintf("%d", len(sideEffects)))

		sideEffectExecutor := NewOrderSideEffectExecutor()
		for _, effect := range sideEffects {
			if err := sideEffectExecutor.ExecuteSideEffect(sideEffectContext, transaction, effect, upcomingOrder, orderUseCase); err != nil {
				_ = transaction.Rollback()
				herosentry.CaptureException(sideEffectContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Side effect execution failed: %v", effect))
				return nil, fmt.Errorf("failed to execute side effect: %w", err)
			}
		}
	}

	if commitError := transaction.Commit(); commitError != nil {
		herosentry.CaptureException(spanContext, commitError, herosentry.ErrorTypeDatabase, "Failed to commit transaction for order update")
		return nil, commitError
	}
	return updatedOrder, nil
}

// UpdateOrderPermissions updates the permissions for an existing order.
func (orderUseCase *OrderUseCase) UpdateOrderPermissions(requestContext context.Context, orderID string, permissions *orders.OrderPermissions) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.UpdateOrderPermissions")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	transaction, err := orderUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for permissions update")
		return nil, err
	}

	updatedOrder, err := orderUseCase.orderRepo.UpdateOrderPermissions(spanContext, transaction, orderID, permissions)
	if err != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update order permissions")
		return nil, err
	}

	if commitError := transaction.Commit(); commitError != nil {
		herosentry.CaptureException(spanContext, commitError, herosentry.ErrorTypeDatabase, "Failed to commit transaction for permissions update")
		return nil, commitError
	}
	return updatedOrder, nil
}

// DeleteOrder deletes an order by its ID.
func (orderUseCase *OrderUseCase) DeleteOrder(requestContext context.Context, orderID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.DeleteOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	err := orderUseCase.orderRepo.DeleteOrder(spanContext, nil, orderID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete order")
		return err
	}
	return nil
}

// ListOrders returns a paginated list of orders with optional filtering by status and order type, and ordering.
func (orderUseCase *OrderUseCase) ListOrders(
	requestContext context.Context,
	pageSize int,
	pageToken string,
	status orders.OrderStatus,
	orderType orders.OrderType,
	orderBy string,
) ([]*orders.Order, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.ListOrders")
	defer finishSpan()

	if pageSize <= 0 {
		pageSize = defaultPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("filter.status", status.String())
	span.SetTag("filter.order_type", orderType.String())
	span.SetTag("filter.order_by", orderBy)

	result, err := orderUseCase.orderRepo.ListOrders(spanContext, nil, pageSize, pageToken, status, orderType, orderBy)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list orders")
		return nil, "", err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(result.Orders)))

	return result.Orders, result.PageToken, nil
}

// AddOrderUpdate adds a new update entry to an order.
func (orderUseCase *OrderUseCase) AddOrderUpdate(requestContext context.Context, orderID string, updateEntry *orders.OrderUpdateEntry) error {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.AddOrderUpdate")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	err := orderUseCase.orderRepo.AddOrderUpdate(spanContext, nil, orderID, updateEntry)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add order update")
		return err
	}
	return nil
}

// RemoveOrderUpdate removes an update entry from an order.
func (orderUseCase *OrderUseCase) RemoveOrderUpdate(requestContext context.Context, orderID string, updateEntry *orders.OrderUpdateEntry) error {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.RemoveOrderUpdate")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	err := orderUseCase.orderRepo.RemoveOrderUpdate(spanContext, nil, orderID, updateEntry)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to remove order update")
		return err
	}
	return nil
}

// AddOrderStatusUpdate adds a status update entry to an order.
func (orderUseCase *OrderUseCase) AddOrderStatusUpdate(requestContext context.Context, orderID string, statusUpdate *orders.OrderStatusUpdateEntry) error {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.AddOrderStatusUpdate")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("status.previous", statusUpdate.PreviousStatus.String())
	span.SetTag("status.new", statusUpdate.NewStatus.String())

	err := orderUseCase.orderRepo.AddOrderStatusUpdate(spanContext, nil, orderID, statusUpdate)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add order status update")
		return err
	}
	return nil
}

// AcknowledgeOrder sets the order status to ACKNOWLEDGED.
func (orderUseCase *OrderUseCase) AcknowledgeOrder(requestContext context.Context, orderID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.AcknowledgeOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	transaction, err := orderUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for acknowledge order")
		return nil, err
	}

	updatedOrder, err := orderUseCase.orderRepo.AcknowledgeOrder(spanContext, transaction, orderID)
	if err != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to acknowledge order")
		return nil, err
	}

	// Check and execute side effects.
	sideEffectChecker := &OrderSideEffectChecker{}
	sideEffects, upcomingOrder := sideEffectChecker.CheckOrderStatusChangeSideEffect(updatedOrder)

	if len(sideEffects) > 0 {
		// Start span for side effects
		sideEffectContext, sideEffectSpan, finishSideEffect := herosentry.StartSpan(spanContext, "OrderUseCase.ExecuteSideEffects")
		defer finishSideEffect()
		sideEffectSpan.SetTag("side_effects.count", fmt.Sprintf("%d", len(sideEffects)))

		sideEffectExecutor := NewOrderSideEffectExecutor()
		for _, effect := range sideEffects {
			if err := sideEffectExecutor.ExecuteSideEffect(sideEffectContext, transaction, effect, upcomingOrder, orderUseCase); err != nil {
				_ = transaction.Rollback()
				herosentry.CaptureException(sideEffectContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Side effect execution failed: %v", effect))
				return nil, fmt.Errorf("failed to execute side effect: %w", err)
			}
		}
	}

	if commitError := transaction.Commit(); commitError != nil {
		herosentry.CaptureException(spanContext, commitError, herosentry.ErrorTypeDatabase, "Failed to commit transaction for acknowledge order")
		return nil, commitError
	}
	return updatedOrder, nil
}

// RejectOrder sets the order status to REJECTED.
func (orderUseCase *OrderUseCase) RejectOrder(requestContext context.Context, orderID, rejectionReason string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.RejectOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	transaction, err := orderUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for reject order")
		return nil, err
	}

	updatedOrder, err := orderUseCase.orderRepo.RejectOrder(spanContext, transaction, orderID, rejectionReason)
	if err != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to reject order")
		return nil, err
	}

	// Check and execute side effects.
	sideEffectChecker := &OrderSideEffectChecker{}
	sideEffects, upcomingOrder := sideEffectChecker.CheckOrderStatusChangeSideEffect(updatedOrder)

	if len(sideEffects) > 0 {
		// Start span for side effects
		sideEffectContext, sideEffectSpan, finishSideEffect := herosentry.StartSpan(spanContext, "OrderUseCase.ExecuteSideEffects")
		defer finishSideEffect()
		sideEffectSpan.SetTag("side_effects.count", fmt.Sprintf("%d", len(sideEffects)))

		sideEffectExecutor := NewOrderSideEffectExecutor()
		for _, effect := range sideEffects {
			if err := sideEffectExecutor.ExecuteSideEffect(sideEffectContext, transaction, effect, upcomingOrder, orderUseCase); err != nil {
				_ = transaction.Rollback()
				herosentry.CaptureException(sideEffectContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Side effect execution failed: %v", effect))
				return nil, fmt.Errorf("failed to execute side effect: %w", err)
			}
		}
	}

	if commitError := transaction.Commit(); commitError != nil {
		herosentry.CaptureException(spanContext, commitError, herosentry.ErrorTypeDatabase, "Failed to commit transaction for reject order")
		return nil, commitError
	}
	return updatedOrder, nil
}

// SnoozeOrder sets the order status to SNOOZED.
func (orderUseCase *OrderUseCase) SnoozeOrder(requestContext context.Context, orderID, snoozeReason string, snoozeUntil string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.SnoozeOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	transaction, err := orderUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for snooze order")
		return nil, err
	}

	// Parse ISO8601 string to time.Time
	snoozeTime, err := commonUtils.ISO8601StringToTime(snoozeUntil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation, "Invalid snooze until time format")
		return nil, fmt.Errorf("invalid snooze until time format: %w", err)
	}

	updatedOrder, err := orderUseCase.orderRepo.SnoozeOrder(spanContext, transaction, orderID, snoozeReason, snoozeTime)
	if err != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to snooze order")
		return nil, err
	}

	// Check and execute side effects.
	sideEffectChecker := &OrderSideEffectChecker{}
	sideEffects, upcomingOrder := sideEffectChecker.CheckOrderStatusChangeSideEffect(updatedOrder)

	if len(sideEffects) > 0 {
		// Start span for side effects
		sideEffectContext, sideEffectSpan, finishSideEffect := herosentry.StartSpan(spanContext, "OrderUseCase.ExecuteSideEffects")
		defer finishSideEffect()
		sideEffectSpan.SetTag("side_effects.count", fmt.Sprintf("%d", len(sideEffects)))

		sideEffectExecutor := NewOrderSideEffectExecutor()
		for _, effect := range sideEffects {
			if err := sideEffectExecutor.ExecuteSideEffect(sideEffectContext, transaction, effect, upcomingOrder, orderUseCase); err != nil {
				_ = transaction.Rollback()
				herosentry.CaptureException(sideEffectContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Side effect execution failed: %v", effect))
				return nil, fmt.Errorf("failed to execute side effect: %w", err)
			}
		}
	}

	if commitError := transaction.Commit(); commitError != nil {
		herosentry.CaptureException(spanContext, commitError, herosentry.ErrorTypeDatabase, "Failed to commit transaction for snooze order")
		return nil, commitError
	}
	return updatedOrder, nil
}

// CancelOrder sets the order status to CANCELLED.
func (orderUseCase *OrderUseCase) CancelOrder(requestContext context.Context, orderID, cancellationReason string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.CancelOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	transaction, err := orderUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for cancel order")
		return nil, err
	}

	updatedOrder, err := orderUseCase.orderRepo.CancelOrder(spanContext, transaction, orderID, cancellationReason)
	if err != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to cancel order")
		return nil, err
	}

	// Check and execute side effects.
	sideEffectChecker := &OrderSideEffectChecker{}
	sideEffects, upcomingOrder := sideEffectChecker.CheckOrderStatusChangeSideEffect(updatedOrder)

	if len(sideEffects) > 0 {
		// Start span for side effects
		sideEffectContext, sideEffectSpan, finishSideEffect := herosentry.StartSpan(spanContext, "OrderUseCase.ExecuteSideEffects")
		defer finishSideEffect()
		sideEffectSpan.SetTag("side_effects.count", fmt.Sprintf("%d", len(sideEffects)))

		sideEffectExecutor := NewOrderSideEffectExecutor()
		for _, effect := range sideEffects {
			if err := sideEffectExecutor.ExecuteSideEffect(sideEffectContext, transaction, effect, upcomingOrder, orderUseCase); err != nil {
				_ = transaction.Rollback()
				herosentry.CaptureException(sideEffectContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Side effect execution failed: %v", effect))
				return nil, fmt.Errorf("failed to execute side effect: %w", err)
			}
		}
	}

	if commitError := transaction.Commit(); commitError != nil {
		herosentry.CaptureException(spanContext, commitError, herosentry.ErrorTypeDatabase, "Failed to commit transaction for cancel order")
		return nil, commitError
	}
	return updatedOrder, nil
}

// CompleteOrder sets the order status to COMPLETED.
func (orderUseCase *OrderUseCase) CompleteOrder(requestContext context.Context, orderID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.CompleteOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	transaction, err := orderUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for complete order")
		return nil, err
	}

	updatedOrder, err := orderUseCase.orderRepo.CompleteOrder(spanContext, transaction, orderID)
	if err != nil {
		_ = transaction.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to complete order")
		return nil, err
	}

	// Check and execute side effects.
	sideEffectChecker := &OrderSideEffectChecker{}
	sideEffects, upcomingOrder := sideEffectChecker.CheckOrderStatusChangeSideEffect(updatedOrder)

	if len(sideEffects) > 0 {
		// Start span for side effects
		sideEffectContext, sideEffectSpan, finishSideEffect := herosentry.StartSpan(spanContext, "OrderUseCase.ExecuteSideEffects")
		defer finishSideEffect()
		sideEffectSpan.SetTag("side_effects.count", fmt.Sprintf("%d", len(sideEffects)))

		sideEffectExecutor := NewOrderSideEffectExecutor()
		for _, effect := range sideEffects {
			if err := sideEffectExecutor.ExecuteSideEffect(sideEffectContext, transaction, effect, upcomingOrder, orderUseCase); err != nil {
				_ = transaction.Rollback()
				herosentry.CaptureException(sideEffectContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Side effect execution failed: %v", effect))
				return nil, fmt.Errorf("failed to execute side effect: %w", err)
			}
		}
	}

	if commitError := transaction.Commit(); commitError != nil {
		herosentry.CaptureException(spanContext, commitError, herosentry.ErrorTypeDatabase, "Failed to commit transaction for complete order")
		return nil, commitError
	}
	return updatedOrder, nil
}

// AddAllowedAssetType adds an allowed asset type to an order.
func (orderUseCase *OrderUseCase) AddAllowedAssetType(requestContext context.Context, orderID string, allowedAssetType assets.AssetType) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.AddAllowedAssetType")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("asset_type", allowedAssetType.String())

	order, err := orderUseCase.orderRepo.AddAllowedAssetType(spanContext, nil, orderID, allowedAssetType)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add allowed asset type")
		return nil, err
	}
	return order, nil
}

// RemoveAllowedAssetType removes an allowed asset type from an order.
func (orderUseCase *OrderUseCase) RemoveAllowedAssetType(requestContext context.Context, orderID string, allowedAssetType assets.AssetType) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.RemoveAllowedAssetType")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("asset_type", allowedAssetType.String())

	order, err := orderUseCase.orderRepo.RemoveAllowedAssetType(spanContext, nil, orderID, allowedAssetType)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to remove allowed asset type")
		return nil, err
	}
	return order, nil
}

// AddBlacklistedAssetId adds an asset ID to the blacklisted list for an order.
func (orderUseCase *OrderUseCase) AddBlacklistedAssetId(requestContext context.Context, orderID, assetID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.AddBlacklistedAssetId")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("asset.id", assetID)

	order, err := orderUseCase.orderRepo.AddBlacklistedAssetId(spanContext, nil, orderID, assetID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add blacklisted asset")
		return nil, err
	}
	return order, nil
}

// RemoveBlacklistedAssetId removes an asset ID from the blacklisted list for an order.
func (orderUseCase *OrderUseCase) RemoveBlacklistedAssetId(requestContext context.Context, orderID, assetID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.RemoveBlacklistedAssetId")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("asset.id", assetID)

	order, err := orderUseCase.orderRepo.RemoveBlacklistedAssetId(spanContext, nil, orderID, assetID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to remove blacklisted asset")
		return nil, err
	}
	return order, nil
}

// ListActiveAssignedOrdersForAsset returns orders for the given asset that are active (statuses: CREATED, ACKNOWLEDGED, SNOOZED, IN_PROGRESS).
func (orderUseCase *OrderUseCase) ListActiveAssignedOrdersForAsset(requestContext context.Context, assetID string, pageSize int, pageToken string) ([]*orders.Order, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.ListActiveAssignedOrdersForAsset")
	defer finishSpan()

	if pageSize <= 0 {
		pageSize = defaultPageSize
	}

	span.SetTag("asset.id", assetID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))

	result, err := orderUseCase.orderRepo.ListActiveAssignedOrdersForAsset(spanContext, nil, assetID, pageSize, pageToken)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list active assigned orders for asset")
		return nil, "", err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(result.Orders)))

	return result.Orders, result.PageToken, nil
}

// ListNewOrdersForAsset returns orders for the given asset that are in the CREATED status.
func (orderUseCase *OrderUseCase) ListNewOrdersForAsset(requestContext context.Context, assetID string, pageSize int, pageToken string) ([]*orders.Order, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.ListNewOrdersForAsset")
	defer finishSpan()

	if pageSize <= 0 {
		pageSize = defaultPageSize
	}

	span.SetTag("asset.id", assetID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))

	result, err := orderUseCase.orderRepo.ListNewOrdersForAsset(spanContext, nil, assetID, pageSize, pageToken)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list new orders for asset")
		return nil, "", err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(result.Orders)))

	return result.Orders, result.PageToken, nil
}

// ListOrdersForSituation returns a paginated list of orders for the given situation, including an optional status filter.
func (orderUseCase *OrderUseCase) ListOrdersForSituation(requestContext context.Context, situationID string, pageSize int, pageToken string, status orders.OrderStatus) ([]*orders.Order, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.ListOrdersForSituation")
	defer finishSpan()

	if pageSize <= 0 {
		pageSize = defaultPageSize
	}

	span.SetTag("situation.id", situationID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("filter.status", status.String())

	result, err := orderUseCase.orderRepo.ListOrdersForSituation(spanContext, nil, situationID, pageSize, pageToken, status)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list orders for situation")
		return nil, "", err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(result.Orders)))

	return result.Orders, result.PageToken, nil
}

// ListOrdersForAsset lists orders for an asset with an optional filter by order status.
func (orderUseCase *OrderUseCase) ListOrdersForAsset(requestContext context.Context, assetID string, pageSize int, pageToken string, status orders.OrderStatus) ([]*orders.Order, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.ListOrdersForAsset")
	defer finishSpan()

	if pageSize <= 0 {
		pageSize = defaultPageSize
	}

	span.SetTag("asset.id", assetID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("filter.status", status.String())

	result, err := orderUseCase.orderRepo.ListOrdersForAsset(spanContext, nil, assetID, pageSize, pageToken, status)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list orders for asset")
		return nil, "", err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(result.Orders)))

	return result.Orders, result.PageToken, nil
}

// ListOrdersForReport returns a paginated list of orders for a given report with optional status filter.
func (orderUseCase *OrderUseCase) ListOrdersForReport(requestContext context.Context, reportID string, pageSize int, pageToken string, status orders.OrderStatus) ([]*orders.Order, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.ListOrdersForReport")
	defer finishSpan()

	if pageSize <= 0 {
		pageSize = defaultPageSize
	}

	span.SetTag("report.id", reportID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("filter.status", status.String())

	result, err := orderUseCase.orderRepo.ListOrdersForReport(spanContext, nil, reportID, pageSize, pageToken, status)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list orders for report")
		return nil, "", err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(result.Orders)))

	return result.Orders, result.PageToken, nil
}

// ListOrdersForReviewRound returns a paginated list of orders for a given review round with optional status filter.
func (orderUseCase *OrderUseCase) ListOrdersForReviewRound(requestContext context.Context, reviewRoundID string, pageSize int, pageToken string, status orders.OrderStatus) ([]*orders.Order, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "OrderUseCase.ListOrdersForReviewRound")
	defer finishSpan()

	if pageSize <= 0 {
		pageSize = defaultPageSize
	}

	span.SetTag("review_round.id", reviewRoundID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("filter.status", status.String())

	result, err := orderUseCase.orderRepo.ListOrdersForReviewRound(spanContext, nil, reviewRoundID, pageSize, pageToken, status)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list orders for review round")
		return nil, "", err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(result.Orders)))

	return result.Orders, result.PageToken, nil
}

// AddAdditionalInfo adds additional JSON info to an order by merging the provided JSON.
// It retrieves the existing order, merges the new info with the existing AdditionalInfoJson,
// updates the order's update time, persists the change, and commits the transaction.
func (orderUseCase *OrderUseCase) AddAdditionalInfo(ctx context.Context, id string, newInfo string) (string, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderUseCase.AddAdditionalInfo")
	defer finishSpan()

	span.SetTag("order.id", id)

	// Begin a transaction.
	tx, err := orderUseCase.database.BeginTx(spanContext, nil)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction for additional info")
		return "", "", err
	}

	// Retrieve the existing order within the transaction.
	orderRecord, err := orderUseCase.orderRepo.GetOrder(spanContext, tx, id)
	if err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to retrieve order for additional info")
		return "", "", err
	}

	// Unmarshal the existing additional info.
	var existingMap map[string]interface{}
	if err = json.Unmarshal([]byte(orderRecord.AdditionalInfoJson), &existingMap); err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to parse existing additional info JSON")
		return "", "", fmt.Errorf("failed to parse existing additional info: %w", err)
	}

	// Unmarshal the new additional info.
	var newMap map[string]interface{}
	if err = json.Unmarshal([]byte(newInfo), &newMap); err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to parse new additional info JSON")
		return "", "", fmt.Errorf("failed to parse new additional info: %w", err)
	}

	// Merge newMap into existingMap.
	commonWorkflowUtils.MergeJSON(existingMap, newMap)

	// Marshal the merged map back to JSON.
	mergedBytes, err := json.Marshal(existingMap)
	if err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal merged additional info JSON")
		return "", "", fmt.Errorf("failed to marshal merged additional info: %w", err)
	}
	mergedInfo := string(mergedBytes)

	// Use the new repository method to persist the change.
	_, err = orderUseCase.orderRepo.UpdateAdditionalInfoJSON(spanContext, tx, id, mergedInfo)
	if err != nil {
		_ = tx.Rollback()
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update additional info in repository")
		return "", "", err
	}

	// Commit the transaction.
	if err := tx.Commit(); err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to commit transaction for additional info")
		return "", "", err
	}

	return id, mergedInfo, nil
}

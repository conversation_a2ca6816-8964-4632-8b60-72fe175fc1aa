package connect

import (
	"context"
	"fmt"
	"log/slog"

	connecthelper "common/connect"
	"common/herosentry"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/emptypb"

	entitypb "proto/hero/entity/v1"
	entityconnect "proto/hero/entity/v1/entityconnect"
	"workflow/internal/entity/usecase"
)

// EntityServer implements the generated Connect server interface for the EntityService.
type EntityServer struct {
	entityconnect.UnimplementedEntityServiceHandler                        // Embed the unimplemented handler
	entityUseCase                                   *usecase.EntityUseCase // Use case layer for business logic
	logger                                          *slog.Logger           // Logger instance
}

// NewEntityServer creates a new instance of the EntityServer.
func NewEntityServer(useCase *usecase.EntityUseCase, logger *slog.Logger) *EntityServer {
	if logger == nil {
		logger = slog.Default()
	}
	return &EntityServer{
		entityUseCase: useCase,
		logger:        logger.With("component", "EntityServer"),
	}
}

// --- Entity CRUD Operations ---

// CreateEntity handles the request to create a new entity.
func (s *EntityServer) CreateEntity(
	executionContext context.Context,
	request *connect.Request[entitypb.CreateEntityRequest],
) (*connect.Response[entitypb.CreateEntityResponse], error) {
	s.logger.InfoContext(executionContext, "CreateEntity called")
	if request.Msg == nil || request.Msg.Entity == nil {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or entity is nil"), "CreateEntity", herosentry.ErrorTypeValidation)
	}

	createdEntity, operationError := s.entityUseCase.CreateEntity(executionContext, request.Msg.Entity)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to create entity", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "CreateEntity")
	}

	response := connect.NewResponse(&entitypb.CreateEntityResponse{
		Entity: createdEntity,
	})
	return response, nil
}

// GetLatestEntity handles the request to retrieve the latest version of an entity.
func (s *EntityServer) GetLatestEntity(
	executionContext context.Context,
	request *connect.Request[entitypb.GetLatestEntityRequest],
) (*connect.Response[entitypb.Entity], error) {
	s.logger.InfoContext(executionContext, "GetLatestEntity called", slog.String("entity_id", request.Msg.GetId()))
	if request.Msg == nil || request.Msg.Id == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or entity ID is missing"), "GetLatestEntity", herosentry.ErrorTypeValidation)
	}

	retrievedEntity, operationError := s.entityUseCase.GetLatestEntity(executionContext, request.Msg.Id)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to get latest entity", slog.String("entity_id", request.Msg.Id), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "GetLatestEntity")
	}

	response := connect.NewResponse(retrievedEntity)
	return response, nil
}

// BatchGetLatestEntities handles the request to retrieve multiple entities by their IDs.
func (s *EntityServer) BatchGetLatestEntities(
	executionContext context.Context,
	request *connect.Request[entitypb.BatchGetLatestEntitiesRequest],
) (*connect.Response[entitypb.BatchGetLatestEntitiesResponse], error) {
	s.logger.InfoContext(executionContext, "BatchGetLatestEntities called", slog.Int("entity_count", len(request.Msg.GetIds())))
	if request.Msg == nil || len(request.Msg.GetIds()) == 0 {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or entity IDs are missing"), "BatchGetLatestEntities", herosentry.ErrorTypeValidation)
	}

	entities, operationError := s.entityUseCase.BatchGetLatestEntities(executionContext, request.Msg.GetIds())
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to batch get latest entities", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "BatchGetLatestEntities")
	}

	response := connect.NewResponse(&entitypb.BatchGetLatestEntitiesResponse{
		Entities: entities,
	})
	return response, nil
}

// GetEntityByVersion handles the request to retrieve a specific version of an entity.
func (s *EntityServer) GetEntityByVersion(
	executionContext context.Context,
	request *connect.Request[entitypb.GetEntityByVersionRequest],
) (*connect.Response[entitypb.Entity], error) {
	s.logger.InfoContext(
		executionContext,
		"GetEntityByVersion called",
		slog.String("entity_id", request.Msg.GetId()),
		slog.Int("version", int(request.Msg.GetVersion())),
	)
	if request.Msg == nil || request.Msg.Id == "" || request.Msg.Version <= 0 {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, entity ID, or valid version is missing"), "GetEntityByVersion", herosentry.ErrorTypeValidation)
	}

	retrievedEntity, operationError := s.entityUseCase.GetEntityByVersion(executionContext, request.Msg.GetId(), int32(request.Msg.GetVersion()))
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to get entity by version",
			slog.String("entity_id", request.Msg.GetId()),
			slog.Int("version", int(request.Msg.GetVersion())),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "GetEntityByVersion")
	}

	response := connect.NewResponse(retrievedEntity)
	return response, nil
}

// GetLatestActiveEntity handles the request to retrieve the latest active version of an entity.
func (s *EntityServer) GetLatestActiveEntity(
	executionContext context.Context,
	request *connect.Request[entitypb.GetLatestActiveEntityRequest],
) (*connect.Response[entitypb.Entity], error) {
	s.logger.InfoContext(executionContext, "GetLatestActiveEntity called", slog.String("entity_id", request.Msg.GetId()))
	if request.Msg == nil || request.Msg.Id == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or entity ID is missing"), "GetLatestActiveEntity", herosentry.ErrorTypeValidation)
	}

	retrievedEntity, operationError := s.entityUseCase.GetLatestActiveEntity(executionContext, request.Msg.GetId())
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to get latest active entity", slog.String("entity_id", request.Msg.GetId()), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "GetLatestActiveEntity")
	}

	response := connect.NewResponse(retrievedEntity)
	return response, nil
}

// UpdateEntity handles the request to update an existing entity.
func (s *EntityServer) UpdateEntity(
	executionContext context.Context,
	request *connect.Request[entitypb.UpdateEntityRequest],
) (*connect.Response[entitypb.Entity], error) {
	s.logger.InfoContext(executionContext, "UpdateEntity called", slog.String("entity_id", request.Msg.GetEntity().GetId()))
	if request.Msg == nil || request.Msg.Entity == nil || request.Msg.Entity.Id == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, entity, or entity ID is missing"), "UpdateEntity", herosentry.ErrorTypeValidation)
	}

	updatedEntity, operationError := s.entityUseCase.UpdateEntity(executionContext, request.Msg)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to update entity", slog.String("entity_id", request.Msg.Entity.Id), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "UpdateEntity")
	}

	response := connect.NewResponse(updatedEntity)
	return response, nil
}

// ListLatestEntities handles the request to list latest entity versions.
func (s *EntityServer) ListLatestEntities(
	executionContext context.Context,
	request *connect.Request[entitypb.ListLatestEntitiesRequest],
) (*connect.Response[entitypb.ListLatestEntitiesResponse], error) {
	s.logger.InfoContext(
		executionContext,
		"ListLatestEntities called",
		slog.Int("page_size", int(request.Msg.GetPageSize())),
		slog.String("page_token", request.Msg.GetPageToken()),
		slog.String("entity_type_filter", request.Msg.GetEntityType().String()),
	)
	if request.Msg == nil {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message is nil"), "ListLatestEntities", herosentry.ErrorTypeValidation)
	}

	entityList, nextPageToken, operationError := s.entityUseCase.ListLatestEntities(
		executionContext,
		request.Msg.PageSize,
		request.Msg.PageToken,
		request.Msg.EntityType,
	)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to list latest entities", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "ListLatestEntities")
	}

	response := connect.NewResponse(&entitypb.ListLatestEntitiesResponse{
		Entities:      entityList,
		NextPageToken: nextPageToken,
	})
	return response, nil
}

// DeleteAllVersionsOfEntity handles the request to delete all versions of an entity.
func (s *EntityServer) DeleteAllVersionsOfEntity(
	executionContext context.Context,
	request *connect.Request[entitypb.DeleteAllVersionsOfEntityRequest],
) (*connect.Response[emptypb.Empty], error) {
	s.logger.InfoContext(executionContext, "DeleteAllVersionsOfEntity called", slog.String("entity_id", request.Msg.GetId()))
	if request.Msg == nil || request.Msg.Id == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or entity ID is missing"), "DeleteAllVersionsOfEntity", herosentry.ErrorTypeValidation)
	}

	operationError := s.entityUseCase.DeleteAllVersionsOfEntity(executionContext, request.Msg.GetId())
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to delete all versions of entity", slog.String("entity_id", request.Msg.GetId()), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "DeleteAllVersionsOfEntity")
	}

	response := connect.NewResponse(&emptypb.Empty{})
	return response, nil
}

// DeleteSpecificVersionOfEntity handles the request to delete a specific entity version.
func (s *EntityServer) DeleteSpecificVersionOfEntity(
	executionContext context.Context,
	request *connect.Request[entitypb.DeleteSpecificVersionOfEntityRequest],
) (*connect.Response[emptypb.Empty], error) {
	s.logger.InfoContext(
		executionContext,
		"DeleteSpecificVersionOfEntity called",
		slog.String("entity_id", request.Msg.GetId()),
		slog.Int("version", int(request.Msg.GetVersion())),
	)
	if request.Msg == nil || request.Msg.Id == "" || request.Msg.Version <= 0 {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, entity ID, or valid version is missing"), "DeleteSpecificVersionOfEntity", herosentry.ErrorTypeValidation)
	}

	operationError := s.entityUseCase.DeleteSpecificVersionOfEntity(executionContext, request.Msg.GetId(), int32(request.Msg.GetVersion()))
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to delete specific version of entity",
			slog.String("entity_id", request.Msg.GetId()),
			slog.Int("version", int(request.Msg.GetVersion())),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "DeleteSpecificVersionOfEntity")
	}

	response := connect.NewResponse(&emptypb.Empty{})
	return response, nil
}

// ListAllVersionsOfEntity handles the request to list all historical versions of an entity.
func (s *EntityServer) ListAllVersionsOfEntity(
	executionContext context.Context,
	request *connect.Request[entitypb.ListAllVersionsOfEntityRequest],
) (*connect.Response[entitypb.ListAllVersionsOfEntityResponse], error) {
	s.logger.InfoContext(executionContext, "ListAllVersionsOfEntity called", slog.String("entity_id", request.Msg.GetEntityId()))
	if request.Msg == nil || request.Msg.EntityId == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or entity ID is missing"), "ListAllVersionsOfEntity", herosentry.ErrorTypeValidation)
	}

	versionList, operationError := s.entityUseCase.ListAllVersionsOfEntity(executionContext, request.Msg.GetEntityId())
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to list all versions of entity", slog.String("entity_id", request.Msg.GetEntityId()), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "ListAllVersionsOfEntity")
	}

	response := connect.NewResponse(&entitypb.ListAllVersionsOfEntityResponse{
		Versions: versionList,
	})
	return response, nil
}

// --- Entity Bulk Operations ---

// BulkCreateEntities handles the request to create multiple entities in a single transaction.
func (s *EntityServer) BulkCreateEntities(
	executionContext context.Context,
	request *connect.Request[entitypb.BulkCreateEntitiesRequest],
) (*connect.Response[entitypb.BulkCreateEntitiesResponse], error) {
	s.logger.InfoContext(executionContext, "BulkCreateEntities called", slog.Int("count", len(request.Msg.GetEntities())))
	if request.Msg == nil || len(request.Msg.Entities) == 0 {
		if request.Msg == nil {
			return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message is nil"), "BulkCreateEntities", herosentry.ErrorTypeValidation)
		}
		return connect.NewResponse(&entitypb.BulkCreateEntitiesResponse{Entities: []*entitypb.Entity{}}), nil
	}

	createdEntities, operationError := s.entityUseCase.BulkCreateEntities(executionContext, request.Msg.Entities)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to bulk create entities", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "BulkCreateEntities")
	}

	response := connect.NewResponse(&entitypb.BulkCreateEntitiesResponse{
		Entities: createdEntities,
	})
	return response, nil
}

// BulkUpdateEntities handles the request to update multiple entities in a single transaction.
func (s *EntityServer) BulkUpdateEntities(
	executionContext context.Context,
	request *connect.Request[entitypb.BulkUpdateEntitiesRequest],
) (*connect.Response[entitypb.BulkUpdateEntitiesResponse], error) {
	s.logger.InfoContext(executionContext, "BulkUpdateEntities called", slog.Int("count", len(request.Msg.GetEntities())))
	if request.Msg == nil || len(request.Msg.Entities) == 0 {
		if request.Msg == nil {
			return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message is nil"), "BulkUpdateEntities", herosentry.ErrorTypeValidation)
		}
		return connect.NewResponse(&entitypb.BulkUpdateEntitiesResponse{Entities: []*entitypb.Entity{}}), nil
	}

	updatedEntities, operationError := s.entityUseCase.BulkUpdateEntities(executionContext, request.Msg.Entities)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to bulk update entities", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "BulkUpdateEntities")
	}

	response := connect.NewResponse(&entitypb.BulkUpdateEntitiesResponse{
		Entities: updatedEntities,
	})
	return response, nil
}

// BulkDeleteEntities handles the request to delete multiple entities in a single transaction.
func (s *EntityServer) BulkDeleteEntities(
	executionContext context.Context,
	request *connect.Request[entitypb.BulkDeleteEntitiesRequest],
) (*connect.Response[emptypb.Empty], error) {
	s.logger.InfoContext(executionContext, "BulkDeleteEntities called", slog.Int("count", len(request.Msg.GetIds())))
	if request.Msg == nil || len(request.Msg.Ids) == 0 {
		if request.Msg == nil {
			return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message is nil"), "BulkDeleteEntities", herosentry.ErrorTypeValidation)
		}
		return connect.NewResponse(&emptypb.Empty{}), nil
	}

	operationError := s.entityUseCase.BulkDeleteEntities(executionContext, request.Msg.GetIds())
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to bulk delete entities", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "BulkDeleteEntities")
	}

	response := connect.NewResponse(&emptypb.Empty{})
	return response, nil
}

// --- Entity Restore/Undo and Diff ---

// RestoreEntityVersion handles the request to restore an entity to a previous version.
func (s *EntityServer) RestoreEntityVersion(
	executionContext context.Context,
	request *connect.Request[entitypb.RestoreEntityVersionRequest],
) (*connect.Response[entitypb.RestoreEntityVersionResponse], error) {
	s.logger.InfoContext(
		executionContext,
		"RestoreEntityVersion called",
		slog.String("entity_id", request.Msg.GetId()),
		slog.Int("version", int(request.Msg.GetVersion())),
	)
	if request.Msg == nil || request.Msg.Id == "" || request.Msg.Version <= 0 {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, entity ID, or valid version is missing"), "RestoreEntityVersion", herosentry.ErrorTypeValidation)
	}

	restoredEntity, operationError := s.entityUseCase.RestoreEntityVersion(executionContext, request.Msg.GetId(), int32(request.Msg.GetVersion()))
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to restore entity version",
			slog.String("entity_id", request.Msg.GetId()),
			slog.Int("version", int(request.Msg.GetVersion())),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "RestoreEntityVersion")
	}

	response := connect.NewResponse(&entitypb.RestoreEntityVersionResponse{
		Entity: restoredEntity,
	})
	return response, nil
}

// DiffEntityVersions handles the request to compute the difference between two entity versions.
func (s *EntityServer) DiffEntityVersions(
	executionContext context.Context,
	request *connect.Request[entitypb.DiffEntityVersionsRequest],
) (*connect.Response[entitypb.DiffEntityVersionsResponse], error) {
	s.logger.InfoContext(
		executionContext,
		"DiffEntityVersions called",
		slog.String("entity_id", request.Msg.GetId()),
		slog.Int("version1", int(request.Msg.GetVersion1())),
		slog.Int("version2", int(request.Msg.GetVersion2())),
	)
	if request.Msg == nil || request.Msg.Id == "" || request.Msg.Version1 <= 0 || request.Msg.Version2 <= 0 {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, entity ID, or valid versions are missing"), "DiffEntityVersions", herosentry.ErrorTypeValidation)
	}

	diffResult, operationError := s.entityUseCase.DiffEntityVersions(
		executionContext,
		request.Msg.GetId(),
		int32(request.Msg.GetVersion1()),
		int32(request.Msg.GetVersion2()),
	)
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to diff entity versions",
			slog.String("entity_id", request.Msg.GetId()),
			slog.Int("version1", int(request.Msg.GetVersion1())),
			slog.Int("version2", int(request.Msg.GetVersion2())),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "DiffEntityVersions")
	}

	response := connect.NewResponse(&entitypb.DiffEntityVersionsResponse{
		DiffResult: diffResult,
	})
	return response, nil
}

// CheckEntityPermissions handles the request to check permissions for an entity.
func (s *EntityServer) CheckEntityPermissions(
	executionContext context.Context,
	request *connect.Request[entitypb.CheckEntityPermissionsRequest],
) (*connect.Response[entitypb.CheckEntityPermissionsResponse], error) {
	s.logger.InfoContext(
		executionContext,
		"CheckEntityPermissions called",
		slog.String("entity_id", request.Msg.GetEntityId()),
		slog.String("user_id", request.Msg.GetUserId()),
		slog.String("action", request.Msg.GetAction()),
	)
	if request.Msg == nil || request.Msg.EntityId == "" || request.Msg.UserId == "" || request.Msg.Action == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or required fields (entity_id, user_id, action) are missing"), "CheckEntityPermissions", herosentry.ErrorTypeValidation)
	}

	permissionAllowed, operationError := s.entityUseCase.CheckEntityPermissions(
		executionContext,
		request.Msg.GetEntityId(),
		request.Msg.GetUserId(),
		request.Msg.GetAction(),
	)
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to check entity permissions",
			slog.String("entity_id", request.Msg.GetEntityId()),
			slog.String("user_id", request.Msg.GetUserId()),
			slog.String("action", request.Msg.GetAction()),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "CheckEntityPermissions")
	}

	response := connect.NewResponse(&entitypb.CheckEntityPermissionsResponse{
		Allowed: permissionAllowed,
	})
	return response, nil
}

// --- Entity Search Operations ---

// SearchEntities handles the request to perform a comprehensive search across entities.
func (s *EntityServer) SearchEntities(
	executionContext context.Context,
	request *connect.Request[entitypb.SearchEntitiesRequest],
) (*connect.Response[entitypb.SearchEntitiesResponse], error) {
	s.logger.InfoContext(
		executionContext,
		"SearchEntities called",
		slog.String("query", request.Msg.GetQuery()),
		slog.Int("page_size", int(request.Msg.GetPageSize())),
		slog.String("page_token", request.Msg.GetPageToken()),
		slog.String("order_by", request.Msg.GetOrderBy().String()),
	)
	if request.Msg == nil {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message is nil"), "SearchEntities", herosentry.ErrorTypeValidation)
	}

	searchResponse, operationError := s.entityUseCase.SearchEntities(executionContext, request.Msg)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to search entities", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "SearchEntities")
	}

	response := connect.NewResponse(searchResponse)
	return response, nil
}

// --- Entity Schema CRUD Operations ---

// CreateEntitySchema handles the request to create a new entity schema.
func (s *EntityServer) CreateEntitySchema(
	executionContext context.Context,
	request *connect.Request[entitypb.CreateEntitySchemaRequest],
) (*connect.Response[entitypb.CreateEntitySchemaResponse], error) {
	s.logger.InfoContext(executionContext, "CreateEntitySchema called")
	if request.Msg == nil || request.Msg.Schema == nil {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or schema is nil"), "CreateEntitySchema", herosentry.ErrorTypeValidation)
	}

	createdSchema, operationError := s.entityUseCase.CreateEntitySchema(executionContext, request.Msg.Schema)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to create entity schema", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "CreateEntitySchema")
	}

	response := connect.NewResponse(&entitypb.CreateEntitySchemaResponse{
		Schema: createdSchema,
	})
	return response, nil
}

// GetLatestEntitySchema handles the request to retrieve the latest version of an entity schema.
func (s *EntityServer) GetLatestEntitySchema(
	executionContext context.Context,
	request *connect.Request[entitypb.GetLatestEntitySchemaRequest],
) (*connect.Response[entitypb.EntitySchema], error) {
	s.logger.InfoContext(executionContext, "GetLatestEntitySchema called", slog.String("schema_id", request.Msg.GetId()))
	if request.Msg == nil || request.Msg.Id == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or schema ID is missing"), "GetLatestEntitySchema", herosentry.ErrorTypeValidation)
	}

	retrievedSchema, operationError := s.entityUseCase.GetLatestEntitySchema(executionContext, request.Msg.GetId())
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to get latest entity schema", slog.String("schema_id", request.Msg.GetId()), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "GetLatestEntitySchema")
	}

	response := connect.NewResponse(retrievedSchema)
	return response, nil
}

// GetLatestActiveEntitySchema handles the request to retrieve the latest active version of an entity schema.
func (s *EntityServer) GetLatestActiveEntitySchema(
	executionContext context.Context,
	request *connect.Request[entitypb.GetLatestActiveEntitySchemaRequest],
) (*connect.Response[entitypb.EntitySchema], error) {
	s.logger.InfoContext(executionContext, "GetLatestActiveEntitySchema called", slog.String("schema_id", request.Msg.GetId()))
	if request.Msg == nil || request.Msg.Id == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or schema ID is missing"), "GetLatestActiveEntitySchema", herosentry.ErrorTypeValidation)
	}

	retrievedSchema, operationError := s.entityUseCase.GetLatestActiveEntitySchema(executionContext, request.Msg.GetId())
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to get latest active entity schema", slog.String("schema_id", request.Msg.GetId()), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "GetLatestActiveEntitySchema")
	}

	response := connect.NewResponse(retrievedSchema)
	return response, nil
}

// GetEntitySchemaByVersion handles the request to retrieve a specific version of an entity schema.
func (s *EntityServer) GetEntitySchemaByVersion(
	executionContext context.Context,
	request *connect.Request[entitypb.GetEntitySchemaByVersionRequest],
) (*connect.Response[entitypb.EntitySchema], error) {
	s.logger.InfoContext(
		executionContext,
		"GetEntitySchemaByVersion called",
		slog.String("schema_id", request.Msg.GetId()),
		slog.Int("version", int(request.Msg.GetVersion())),
	)
	if request.Msg == nil || request.Msg.Id == "" || request.Msg.Version <= 0 {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, schema ID, or valid version is missing"), "GetEntitySchemaByVersion", herosentry.ErrorTypeValidation)
	}

	retrievedSchema, operationError := s.entityUseCase.GetEntitySchemaByVersion(executionContext, request.Msg.GetId(), int32(request.Msg.GetVersion()))
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to get entity schema by version",
			slog.String("schema_id", request.Msg.GetId()),
			slog.Int("version", int(request.Msg.GetVersion())),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "GetEntitySchemaByVersion")
	}

	response := connect.NewResponse(retrievedSchema)
	return response, nil
}

// UpdateEntitySchema handles the request to update an existing entity schema.
func (s *EntityServer) UpdateEntitySchema(
	executionContext context.Context,
	request *connect.Request[entitypb.UpdateEntitySchemaRequest],
) (*connect.Response[entitypb.EntitySchema], error) {
	s.logger.InfoContext(executionContext, "UpdateEntitySchema called", slog.String("schema_id", request.Msg.GetSchema().GetId()))
	if request.Msg == nil || request.Msg.Schema == nil || request.Msg.Schema.Id == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, schema, or schema ID is missing"), "UpdateEntitySchema", herosentry.ErrorTypeValidation)
	}

	updatedSchema, operationError := s.entityUseCase.UpdateEntitySchema(executionContext, request.Msg)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to update entity schema", slog.String("schema_id", request.Msg.Schema.Id), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "UpdateEntitySchema")
	}

	response := connect.NewResponse(updatedSchema)
	return response, nil
}

// DeleteAllVersionsOfEntitySchema handles the request to delete all versions of an entity schema.
func (s *EntityServer) DeleteAllVersionsOfEntitySchema(
	executionContext context.Context,
	request *connect.Request[entitypb.DeleteAllVersionsOfEntitySchemaRequest],
) (*connect.Response[emptypb.Empty], error) {
	s.logger.InfoContext(executionContext, "DeleteAllVersionsOfEntitySchema called", slog.String("schema_id", request.Msg.GetId()))
	if request.Msg == nil || request.Msg.Id == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or schema ID is missing"), "DeleteAllVersionsOfEntitySchema", herosentry.ErrorTypeValidation)
	}

	operationError := s.entityUseCase.DeleteAllVersionsOfEntitySchema(executionContext, request.Msg.GetId())
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to delete all versions of entity schema", slog.String("schema_id", request.Msg.GetId()), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "DeleteAllVersionsOfEntitySchema")
	}

	response := connect.NewResponse(&emptypb.Empty{})
	return response, nil
}

// DeleteSpecificVersionOfEntitySchema handles the request to delete a specific version of an entity schema.
func (s *EntityServer) DeleteSpecificVersionOfEntitySchema(
	executionContext context.Context,
	request *connect.Request[entitypb.DeleteSpecificVersionOfEntitySchemaRequest],
) (*connect.Response[emptypb.Empty], error) {
	s.logger.InfoContext(
		executionContext,
		"DeleteSpecificVersionOfEntitySchema called",
		slog.String("schema_id", request.Msg.GetId()),
		slog.Int("version", int(request.Msg.GetVersion())),
	)
	if request.Msg == nil || request.Msg.Id == "" || request.Msg.Version <= 0 {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, schema ID, or valid version is missing"), "DeleteSpecificVersionOfEntitySchema", herosentry.ErrorTypeValidation)
	}

	operationError := s.entityUseCase.DeleteSpecificVersionOfEntitySchema(executionContext, request.Msg.GetId(), int32(request.Msg.GetVersion()))
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to delete specific version of entity schema",
			slog.String("schema_id", request.Msg.GetId()),
			slog.Int("version", int(request.Msg.GetVersion())),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "DeleteSpecificVersionOfEntitySchema")
	}

	response := connect.NewResponse(&emptypb.Empty{})
	return response, nil
}

// ListLatestEntitySchemas handles the request to list the latest versions of entity schemas.
func (s *EntityServer) ListLatestEntitySchemas(
	executionContext context.Context,
	request *connect.Request[entitypb.ListLatestEntitySchemasRequest],
) (*connect.Response[entitypb.ListLatestEntitySchemasResponse], error) {
	s.logger.InfoContext(
		executionContext,
		"ListLatestEntitySchemas called",
		slog.Int("page_size", int(request.Msg.GetPageSize())),
		slog.String("page_token", request.Msg.GetPageToken()),
		slog.String("entity_type_filter", request.Msg.GetEntityType().String()),
	)
	if request.Msg == nil {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message is nil"), "ListLatestEntitySchemas", herosentry.ErrorTypeValidation)
	}

	schemaList, nextPageToken, operationError := s.entityUseCase.ListLatestEntitySchemas(
		executionContext,
		request.Msg.PageSize,
		request.Msg.PageToken,
		request.Msg.EntityType,
	)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to list latest entity schemas", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "ListLatestEntitySchemas")
	}

	response := connect.NewResponse(&entitypb.ListLatestEntitySchemasResponse{
		Schemas:       schemaList,
		NextPageToken: nextPageToken,
	})
	return response, nil
}

// ListAllVersionsOfEntitySchema handles the request to list all historical versions of an entity schema.
func (s *EntityServer) ListAllVersionsOfEntitySchema(
	executionContext context.Context,
	request *connect.Request[entitypb.ListAllVersionsOfEntitySchemaRequest],
) (*connect.Response[entitypb.ListAllVersionsOfEntitySchemaResponse], error) {
	s.logger.InfoContext(executionContext, "ListAllVersionsOfEntitySchema called", slog.String("schema_id", request.Msg.GetSchemaId()))
	if request.Msg == nil || request.Msg.SchemaId == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or schema ID is missing"), "ListAllVersionsOfEntitySchema", herosentry.ErrorTypeValidation)
	}

	versionList, operationError := s.entityUseCase.ListAllVersionsOfEntitySchema(executionContext, request.Msg.GetSchemaId())
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to list all versions of entity schema", slog.String("schema_id", request.Msg.GetSchemaId()), slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "ListAllVersionsOfEntitySchema")
	}

	response := connect.NewResponse(&entitypb.ListAllVersionsOfEntitySchemaResponse{
		SchemaVersions: versionList,
	})
	return response, nil
}

// --- Entity Schema Bulk Operations ---

// BulkCreateEntitySchemas handles the request to create multiple entity schemas.
func (s *EntityServer) BulkCreateEntitySchemas(
	executionContext context.Context,
	request *connect.Request[entitypb.BulkCreateEntitySchemasRequest],
) (*connect.Response[entitypb.BulkCreateEntitySchemasResponse], error) {
	s.logger.InfoContext(executionContext, "BulkCreateEntitySchemas called", slog.Int("count", len(request.Msg.GetSchemas())))
	if request.Msg == nil || len(request.Msg.Schemas) == 0 {
		if request.Msg == nil {
			return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message is nil"), "BulkCreateEntitySchemas", herosentry.ErrorTypeValidation)
		}
		return connect.NewResponse(&entitypb.BulkCreateEntitySchemasResponse{Schemas: []*entitypb.EntitySchema{}}), nil
	}

	createdSchemas, operationError := s.entityUseCase.BulkCreateEntitySchemas(executionContext, request.Msg.Schemas)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to bulk create entity schemas", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "BulkCreateEntitySchemas")
	}

	response := connect.NewResponse(&entitypb.BulkCreateEntitySchemasResponse{
		Schemas: createdSchemas,
	})
	return response, nil
}

// BulkUpdateEntitySchemas handles the request to update multiple entity schemas.
func (s *EntityServer) BulkUpdateEntitySchemas(
	executionContext context.Context,
	request *connect.Request[entitypb.BulkUpdateEntitySchemasRequest],
) (*connect.Response[entitypb.BulkUpdateEntitySchemasResponse], error) {
	s.logger.InfoContext(executionContext, "BulkUpdateEntitySchemas called", slog.Int("count", len(request.Msg.GetSchemas())))
	if request.Msg == nil || len(request.Msg.Schemas) == 0 {
		if request.Msg == nil {
			return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message is nil"), "BulkUpdateEntitySchemas", herosentry.ErrorTypeValidation)
		}
		return connect.NewResponse(&entitypb.BulkUpdateEntitySchemasResponse{Schemas: []*entitypb.EntitySchema{}}), nil
	}

	updatedSchemas, operationError := s.entityUseCase.BulkUpdateEntitySchemas(executionContext, request.Msg.Schemas)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to bulk update entity schemas", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "BulkUpdateEntitySchemas")
	}

	response := connect.NewResponse(&entitypb.BulkUpdateEntitySchemasResponse{
		Schemas: updatedSchemas,
	})
	return response, nil
}

// BulkDeleteEntitySchemas handles the request to delete multiple entity schemas.
func (s *EntityServer) BulkDeleteEntitySchemas(
	executionContext context.Context,
	request *connect.Request[entitypb.BulkDeleteEntitySchemasRequest],
) (*connect.Response[emptypb.Empty], error) {
	s.logger.InfoContext(executionContext, "BulkDeleteEntitySchemas called", slog.Int("count", len(request.Msg.GetIds())))
	if request.Msg == nil || len(request.Msg.Ids) == 0 {
		if request.Msg == nil {
			return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message is nil"), "BulkDeleteEntitySchemas", herosentry.ErrorTypeValidation)
		}
		return connect.NewResponse(&emptypb.Empty{}), nil
	}

	operationError := s.entityUseCase.BulkDeleteEntitySchemas(executionContext, request.Msg.Ids)
	if operationError != nil {
		s.logger.ErrorContext(executionContext, "Failed to bulk delete entity schemas", slog.Any("error", operationError))
		return nil, connecthelper.AsConnectError(executionContext, operationError, "BulkDeleteEntitySchemas")
	}

	response := connect.NewResponse(&emptypb.Empty{})
	return response, nil
}

// RestoreEntitySchemaVersion handles the request to restore an entity schema to a previous version.
func (s *EntityServer) RestoreEntitySchemaVersion(
	executionContext context.Context,
	request *connect.Request[entitypb.RestoreEntitySchemaVersionRequest],
) (*connect.Response[entitypb.RestoreEntitySchemaVersionResponse], error) {
	s.logger.InfoContext(
		executionContext,
		"RestoreEntitySchemaVersion called",
		slog.String("schema_id", request.Msg.GetId()),
		slog.Int("version", int(request.Msg.GetVersion())),
	)
	if request.Msg == nil || request.Msg.Id == "" || request.Msg.Version <= 0 {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, schema ID, or valid version is missing"), "RestoreEntitySchemaVersion", herosentry.ErrorTypeValidation)
	}

	restoredSchema, operationError := s.entityUseCase.RestoreEntitySchemaVersion(executionContext, request.Msg.GetId(), int32(request.Msg.GetVersion()))
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to restore entity schema version",
			slog.String("schema_id", request.Msg.GetId()),
			slog.Int("version", int(request.Msg.GetVersion())),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "RestoreEntitySchemaVersion")
	}

	response := connect.NewResponse(&entitypb.RestoreEntitySchemaVersionResponse{
		Schema: restoredSchema,
	})
	return response, nil
}

// DiffEntitySchemaVersions handles the request to compute the difference between two entity schema versions.
func (s *EntityServer) DiffEntitySchemaVersions(
	executionContext context.Context,
	request *connect.Request[entitypb.DiffEntitySchemaVersionsRequest],
) (*connect.Response[entitypb.DiffEntitySchemaVersionsResponse], error) {
	s.logger.InfoContext(
		executionContext,
		"DiffEntitySchemaVersions called",
		slog.String("schema_id", request.Msg.GetId()),
		slog.Int("version1", int(request.Msg.GetVersion1())),
		slog.Int("version2", int(request.Msg.GetVersion2())),
	)
	if request.Msg == nil || request.Msg.Id == "" || request.Msg.Version1 <= 0 || request.Msg.Version2 <= 0 {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message, schema ID, or valid versions are missing"), "DiffEntitySchemaVersions", herosentry.ErrorTypeValidation)
	}

	diffResult, operationError := s.entityUseCase.DiffEntitySchemaVersions(
		executionContext,
		request.Msg.GetId(),
		int32(request.Msg.GetVersion1()),
		int32(request.Msg.GetVersion2()),
	)
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to diff entity schema versions",
			slog.String("schema_id", request.Msg.GetId()),
			slog.Int("version1", int(request.Msg.GetVersion1())),
			slog.Int("version2", int(request.Msg.GetVersion2())),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "DiffEntitySchemaVersions")
	}

	response := connect.NewResponse(&entitypb.DiffEntitySchemaVersionsResponse{
		DiffResult: diffResult,
	})
	return response, nil
}

// CheckEntitySchemaPermissions handles the request to check permissions for an entity schema.
func (s *EntityServer) CheckEntitySchemaPermissions(
	executionContext context.Context,
	request *connect.Request[entitypb.CheckEntitySchemaPermissionsRequest],
) (*connect.Response[entitypb.CheckEntitySchemaPermissionsResponse], error) {
	s.logger.InfoContext(
		executionContext,
		"CheckEntitySchemaPermissions called",
		slog.String("schema_id", request.Msg.GetSchemaId()),
		slog.String("user_id", request.Msg.GetUserId()),
		slog.String("action", request.Msg.GetAction()),
	)
	if request.Msg == nil || request.Msg.SchemaId == "" || request.Msg.UserId == "" || request.Msg.Action == "" {
		return nil, connecthelper.AsConnectError(executionContext, fmt.Errorf("request message or required fields (schema_id, user_id, action) are missing"), "CheckEntitySchemaPermissions", herosentry.ErrorTypeValidation)
	}

	permissionAllowed, operationError := s.entityUseCase.CheckEntitySchemaPermissions(
		executionContext,
		request.Msg.GetSchemaId(),
		request.Msg.GetUserId(),
		request.Msg.GetAction(),
	)
	if operationError != nil {
		s.logger.ErrorContext(
			executionContext,
			"Failed to check entity schema permissions",
			slog.String("schema_id", request.Msg.GetSchemaId()),
			slog.String("user_id", request.Msg.GetUserId()),
			slog.String("action", request.Msg.GetAction()),
			slog.Any("error", operationError),
		)
		return nil, connecthelper.AsConnectError(executionContext, operationError, "CheckEntitySchemaPermissions")
	}

	response := connect.NewResponse(&entitypb.CheckEntitySchemaPermissionsResponse{
		Allowed: permissionAllowed,
	})
	return response, nil
}

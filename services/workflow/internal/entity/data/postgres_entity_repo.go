package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"
	database "common/database"
	"common/herosentry"
	workflowUtils "workflow/internal/common/utils"

	entity "proto/hero/entity/v1"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/sergi/go-diff/diffmatchpatch"
	"google.golang.org/protobuf/proto" // Used for deep cloning proto messages.
)

// Entity search field constants to avoid repetition
const (
	FieldReferenceRelationType = "reference_relation_type"
	FieldReferenceDisplayName  = "reference_display_name"
	FieldData                  = "data"
	FieldTags                  = "tags"
	FieldID                    = "id"
)

// nullIfEmpty converts an empty string to SQL NULL, otherwise returns the string.
// This is useful for inserting optional string fields into the database.
func nullIfEmpty(s string) interface{} {
	if s == "" {
		return nil
	}
	return s
}

// PostgresEntityRepository provides methods for interacting with entity and entity schema data
// stored in a PostgreSQL database.
type PostgresEntityRepository struct {
	database *sql.DB // The database connection pool.
}

// NewPostgresEntityRepository creates a new instance of PostgresEntityRepository.
func NewPostgresEntityRepository(database *sql.DB) *PostgresEntityRepository {
	return &PostgresEntityRepository{database: database}
}

// -------------------------------
// Entity CRUD Operations
// -------------------------------

// CreateEntity inserts a new entity record into the 'entities' table and
// creates an initial version history record in the 'entity_versions' table.
// It operates within a transaction provided by the `database.WithSession` helper.
func (repository *PostgresEntityRepository) CreateEntity(ctx context.Context, tx *sql.Tx, entityToCreate *entity.Entity) (*entity.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.CreateEntity")
	defer finishSpan()

	if entityToCreate != nil {
		span.SetTag("entity.type", entityToCreate.EntityType.String())
		span.SetTag("entity.org_id", fmt.Sprintf("%d", entityToCreate.OrgId))
	}

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.Entity, error) {
		// Step 1: Prepare the entity record for insertion.
		// Ensure entity has an ID.
		if entityToCreate.Id == "" {
			entityToCreate.Id = uuid.New().String()
		}

		// Retrieve organization ID from context.
		orgID := cmncontext.GetOrgId(spanContext)

		// Set timestamps and initial version.
		currentTime := time.Now()
		entityToCreate.CreateTime = currentTime.Format(time.RFC3339Nano)
		entityToCreate.UpdateTime = currentTime.Format(time.RFC3339Nano)
		entityToCreate.CreatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx) // TODO: Handle error if necessary
		entityToCreate.UpdatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx) // TODO: Handle error if necessary
		if entityToCreate.Version == 0 {
			entityToCreate.Version = 1 // Initial version is 1.
		}

		// Convert protobuf Struct data to JSON for storage.
		entityDataJSON, err := workflowUtils.StructToJSON(entityToCreate.Data)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, fmt.Errorf("failed to marshal entity data to JSON: %w", err)
		}

		// Step 2: Insert the entity record into the 'entities' table.
		insertEntityQuery := `
			INSERT INTO entities (
				id, org_id, schema_id, schema_version, data, create_time, update_time,
				entity_type, created_by, updated_by, version, status, tags
			)
			VALUES (
				$1, $2, $3, $4, $5, $6, $7,
				$8, $9, $10, $11, $12, $13
			)
		`
		preparedStatement, err := sessionTx.PrepareContext(spanContext, insertEntityQuery)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to prepare create entity statement")
			return nil, fmt.Errorf("failed to prepare create entity statement: %w", err)
		}
		defer preparedStatement.Close()

		_, err = preparedStatement.ExecContext(spanContext,
			entityToCreate.Id,
			orgID,
			nullIfEmpty(entityToCreate.SchemaId),
			entityToCreate.SchemaVersion,
			entityDataJSON,
			currentTime,
			currentTime,
			int32(entityToCreate.EntityType),
			entityToCreate.CreatedBy,
			entityToCreate.UpdatedBy,
			entityToCreate.Version,
			int32(entityToCreate.Status),
			pq.Array(entityToCreate.Tags),
		)
		if err != nil {
			// Check for specific PostgreSQL errors and provide more meaningful error messages
			if pqErr, ok := err.(*pq.Error); ok {
				switch pqErr.Code {
				case "23505": // unique_violation
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
					return nil, fmt.Errorf("entity already exists (constraint violation): %w", err)
				case "23503": // foreign_key_violation
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
					return nil, fmt.Errorf("entity references a non-existent resource: %w", err)
				case "23502": // not_null_violation
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
					return nil, fmt.Errorf("entity missing required field: %w", err)
				}
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute create entity statement")
			return nil, fmt.Errorf("failed to execute create entity statement: %w", err)
		}

		// Step 3: Insert the initial version history record into 'entity_versions'.
		entityVersionSnapshotJSON, err := workflowUtils.EntityToJSON(entityToCreate)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, fmt.Errorf("failed to marshal entity snapshot for version history: %w", err)
		}
		insertVersionQuery := `
			INSERT INTO entity_versions (
				entity_id, version, entity_snapshot, modified_by, modified_time, change_comment, org_id
			)
			VALUES ($1, $2, $3, $4, $5, 'Created', $6) -- Initial change comment is 'Created'
		`
		_, err = sessionTx.ExecContext(spanContext, insertVersionQuery,
			entityToCreate.Id,
			entityToCreate.Version,
			entityVersionSnapshotJSON,
			entityToCreate.CreatedBy,
			currentTime.Format(time.RFC3339Nano),
			orgID,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert initial entity version history")
			return nil, fmt.Errorf("failed to insert initial entity version history: %w", err)
		}

		// Step 4: Retrieve and return the newly created entity from the database.
		// This ensures the returned object reflects the actual stored state.
		return repository.GetLatestEntity(spanContext, sessionTx, entityToCreate.Id)
	})
}

// GetLatestEntity retrieves the most recent version of a specific entity from the 'entities' table.
func (repository *PostgresEntityRepository) GetLatestEntity(ctx context.Context, tx *sql.Tx, entityID string) (*entity.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.GetLatestEntity")
	defer finishSpan()

	span.SetTag("entity.id", entityID)

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.Entity, error) {
		// Step 1: Define and execute the query to fetch the entity.
		query := `
			SELECT id, org_id, schema_id, schema_version, data, create_time, update_time,
				   entity_type, created_by, updated_by, version, status, tags, resource_type
			FROM entities
			WHERE id = $1
		`
		row := sessionTx.QueryRowContext(spanContext, query, entityID)

		// Step 2: Scan the result into an entity struct.
		var fetchedEntity entity.Entity
		var orgID int32
		var entityDataJSON string
		var entityTypeValue, statusValue int32
		var tags []string
		var nullableSchemaID sql.NullString  // Use sql.NullString for schema_id
		var createTime, updateTime time.Time // Scan as time.Time first.

		scanErr := row.Scan(
			&fetchedEntity.Id,
			&orgID,
			&nullableSchemaID, // Scan into nullableSchemaID
			&fetchedEntity.SchemaVersion,
			&entityDataJSON, // Scan data as JSON string.
			&createTime,
			&updateTime,
			&entityTypeValue, // Scan enums as their underlying integer type.
			&fetchedEntity.CreatedBy,
			&fetchedEntity.UpdatedBy,
			&fetchedEntity.Version,
			&statusValue,
			pq.Array(&tags), // Use pq.Array helper for PostgreSQL arrays.
			&fetchedEntity.ResourceType,
		)

		// Step 3: Handle scan errors, specifically 'not found'.
		if scanErr != nil {
			if errors.Is(scanErr, sql.ErrNoRows) {
				herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, fmt.Sprintf("Entity with ID '%s' not found", entityID))
				return nil, fmt.Errorf("entity with ID '%s' not found", entityID) // More specific error.
			}
			herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan entity row")
			return nil, fmt.Errorf("failed to scan entity row: %w", scanErr)
		}

		// Step 4: Post-process scanned data (format timestamps, convert enums, handle nulls, unmarshal JSON).
		fetchedEntity.CreateTime = createTime.Format(time.RFC3339Nano)
		fetchedEntity.UpdateTime = updateTime.Format(time.RFC3339Nano)
		fetchedEntity.EntityType = entity.EntityType(entityTypeValue)
		fetchedEntity.Status = entity.RecordStatus(statusValue)
		fetchedEntity.Tags = tags
		fetchedEntity.OrgId = orgID

		// Assign schema_id from nullableSchemaID
		if nullableSchemaID.Valid {
			fetchedEntity.SchemaId = nullableSchemaID.String
		} else {
			fetchedEntity.SchemaId = ""
		}

		// Convert the stored JSON data back to a protobuf Struct.
		structData, err := workflowUtils.JSONToStruct(entityDataJSON)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, fmt.Errorf("failed to unmarshal entity data JSON for ID '%s': %w", entityID, err)
		}
		fetchedEntity.Data = structData

		// Step 5: Return the populated entity struct.
		return &fetchedEntity, nil
	})
}

// BatchGetLatestEntities retrieves multiple entities by their IDs in a single operation.
// BatchGetLatestEntities retrieves multiple entities by their IDs in a single operation.
func (repository *PostgresEntityRepository) BatchGetLatestEntities(
	ctx context.Context,
	tx *sql.Tx,
	entityIDs []string,
) ([]*entity.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.BatchGetLatestEntities")
	defer finishSpan()

	span.SetTag("entity.ids_count", fmt.Sprintf("%d", len(entityIDs)))
	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) ([]*entity.Entity, error) {
		const maxBatchSize = 500

		// Early return for empty input
		if len(entityIDs) == 0 {
			return []*entity.Entity{}, nil
		}

		// Chunk very large lists
		if len(entityIDs) > maxBatchSize {
			var allEntities []*entity.Entity
			for startIndex := 0; startIndex < len(entityIDs); startIndex += maxBatchSize {
				endIndex := startIndex + maxBatchSize
				if endIndex > len(entityIDs) {
					endIndex = len(entityIDs)
				}
				batchEntities, err := repository.BatchGetLatestEntities(spanContext, sessionTx, entityIDs[startIndex:endIndex])
				if err != nil {
					return nil, err
				}
				allEntities = append(allEntities, batchEntities...)
			}
			return allEntities, nil
		}

		// Prepare result slice with nil placeholders
		resultEntities := make([]*entity.Entity, len(entityIDs))

		// The query uses a CTE (Common Table Expression) to:
		// 1. Expand the Go slice (text[]) into rows with ordinality:
		//      unnest($1::text[]) WITH ORDINALITY AS t(id, ord)
		//    - "id" is each element of the input array
		//    - "ord" is its original position (1-based index)
		// 2. Join this CTE against the "entities" table on matching IDs.
		//    - Only IDs present in "entities" produce rows; missing IDs are dropped
		// 3. Order by "ord" to return rows in the same order as the input slice
		const queryText = `
			WITH input_list AS (
			SELECT id, ord
			FROM unnest($1::text[]) WITH ORDINALITY AS t(id, ord)
			)
			SELECT
			input_list.ord,
			e.id,
			e.org_id,
			e.schema_id,
			e.schema_version,
			e.data,
			e.create_time,
			e.update_time,
			e.entity_type,
			e.created_by,
			e.updated_by,
			e.version,
			e.status,
			e.tags,
			e.resource_type
			FROM input_list
			JOIN entities AS e
			ON e.id = input_list.id
			ORDER BY input_list.ord;
		`
		rows, queryErr := sessionTx.QueryContext(spanContext, queryText, pq.Array(entityIDs))
		if queryErr != nil {
			herosentry.CaptureException(spanContext, queryErr, herosentry.ErrorTypeDatabase, "Failed to execute batch query")
			return nil, fmt.Errorf("failed to execute batch query: %w", queryErr)
		}
		defer rows.Close()

		for rows.Next() {
			var ordinalIndex int
			var entityRecord entity.Entity
			var organizationID int32
			var rawEntityJSON string
			var entityTypeInt, statusInt int32
			var tagList []string
			var nullableSchemaID sql.NullString
			var createTimeValue, updateTimeValue time.Time

			if scanErr := rows.Scan(
				&ordinalIndex,
				&entityRecord.Id,
				&organizationID,
				&nullableSchemaID,
				&entityRecord.SchemaVersion,
				&rawEntityJSON,
				&createTimeValue,
				&updateTimeValue,
				&entityTypeInt,
				&entityRecord.CreatedBy,
				&entityRecord.UpdatedBy,
				&entityRecord.Version,
				&statusInt,
				pq.Array(&tagList),
				&entityRecord.ResourceType,
			); scanErr != nil {
				herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan entity row")
				return nil, fmt.Errorf("failed to scan entity row: %w", scanErr)
			}

			// Populate metadata fields
			entityRecord.OrgId = organizationID
			entityRecord.CreateTime = createTimeValue.Format(time.RFC3339Nano)
			entityRecord.UpdateTime = updateTimeValue.Format(time.RFC3339Nano)
			entityRecord.EntityType = entity.EntityType(entityTypeInt)
			entityRecord.Status = entity.RecordStatus(statusInt)
			entityRecord.Tags = tagList

			if nullableSchemaID.Valid {
				entityRecord.SchemaId = nullableSchemaID.String
			}

			// Unmarshal JSON into protobuf Struct
			structData, unmarshalErr := workflowUtils.JSONToStruct(rawEntityJSON)
			if unmarshalErr != nil {
				herosentry.CaptureException(ctx, unmarshalErr, herosentry.ErrorTypeInternal)
				return nil, fmt.Errorf("failed to unmarshal entity data for ID '%s': %w", entityRecord.Id, unmarshalErr)
			}
			entityRecord.Data = structData

			// Place into the correct slot (ord is 1-based)
			resultEntities[ordinalIndex-1] = &entityRecord
		}
		if iterationErr := rows.Err(); iterationErr != nil {
			herosentry.CaptureException(ctx, iterationErr, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("error iterating entity rows: %w", iterationErr)
		}

		return resultEntities, nil
	})
}

// UpdateEntity updates an existing entity in the 'entities' table using optimistic locking
// based on the 'version' field. It also inserts a new record into the 'entity_versions' table.
// The original 'created_by' and 'create_time' fields are preserved.
func (repository *PostgresEntityRepository) UpdateEntity(ctx context.Context, tx *sql.Tx, entityToUpdate *entity.Entity) (*entity.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.UpdateEntity")
	defer finishSpan()

	if entityToUpdate != nil {
		span.SetTag("entity.id", entityToUpdate.Id)
		span.SetTag("entity.type", entityToUpdate.EntityType.String())
		span.SetTag("entity.version", fmt.Sprintf("%d", entityToUpdate.Version))
	}
	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.Entity, error) {
		// Step 1: Fetch the current version of the entity for optimistic locking check.
		// Note: GetLatestEntity handles the "not found" case.
		existingEntity, err := repository.GetLatestEntity(spanContext, sessionTx, entityToUpdate.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get existing entity for update (ID: %s)", entityToUpdate.Id))
			return nil, fmt.Errorf("failed to get existing entity for update (ID: %s): %w", entityToUpdate.Id, err)
		}

		// Step 2: Prepare the updated entity record.
		// Increment version number and update timestamp.
		updatedVersion := existingEntity.Version + 1
		entityToUpdate.Version = updatedVersion // Set the version for the update operation.
		currentTime := time.Now()
		entityToUpdate.UpdateTime = currentTime.Format(time.RFC3339Nano)
		// Important: Preserve original creation info.
		entityToUpdate.CreateTime = existingEntity.CreateTime
		entityToUpdate.CreatedBy = existingEntity.CreatedBy
		entityToUpdate.UpdatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx) // TODO: Handle error if necessary

		// Convert protobuf Struct data to JSON for storage.
		entityDataJSON, err := workflowUtils.StructToJSON(entityToUpdate.Data)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal updated entity data to JSON")
			return nil, fmt.Errorf("failed to marshal updated entity data to JSON: %w", err)
		}

		// Step 3: Execute the UPDATE statement with optimistic locking.
		updateQuery := `
			UPDATE entities
			SET schema_id = $1, schema_version = $2, data = $3, update_time = $4,
				entity_type = $5, updated_by = $6, version = $7, status = $8,
				tags = $9
			WHERE id = $10 AND version = $11 -- Optimistic lock condition
		`
		preparedStatement, err := sessionTx.PrepareContext(spanContext, updateQuery)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to prepare update entity statement")
			return nil, fmt.Errorf("failed to prepare update entity statement: %w", err)
		}
		defer preparedStatement.Close()

		result, err := preparedStatement.ExecContext(spanContext,
			nullIfEmpty(entityToUpdate.SchemaId), // $1 - schema_id
			entityToUpdate.SchemaVersion,         // $2 - schema_version
			entityDataJSON,                       // $3 - data
			currentTime,                          // $4 - update_time
			int32(entityToUpdate.EntityType),     // $5 - entity_type
			entityToUpdate.UpdatedBy,             // $6 - updated_by
			entityToUpdate.Version,               // $7 - version
			int32(entityToUpdate.Status),         // $8 - status
			pq.Array(entityToUpdate.Tags),        // $9 - tags
			entityToUpdate.Id,                    // $10 - WHERE id = ?
			existingEntity.Version,               // $11 - WHERE version = ? (optimistic lock)
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute update entity statement")
			return nil, fmt.Errorf("failed to execute update entity statement: %w", err)
		}

		// Step 4: Check if the optimistic lock condition was met.
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to check rows affected after update")
			return nil, fmt.Errorf("failed to check rows affected after update: %w", err)
		}
		if rowsAffected == 0 {
			// This means the entity was modified by another transaction after we read it (Step 1).
			err := fmt.Errorf("update conflict: entity with ID '%s' was modified concurrently (expected version %d)", entityToUpdate.Id, existingEntity.Version)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
			return nil, err
		}

		// Step 5: Insert a new version history record.
		orgID := cmncontext.GetOrgId(spanContext)
		entityVersionSnapshotJSON, err := workflowUtils.EntityToJSON(entityToUpdate) // Use the fully updated entity.
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal entity snapshot for update version history")
			return nil, fmt.Errorf("failed to marshal entity snapshot for update version history: %w", err)
		}
		insertVersionQuery := `
			INSERT INTO entity_versions (
				entity_id, version, entity_snapshot, modified_by, modified_time, change_comment, org_id
			)
			VALUES ($1, $2, $3, $4, $5, 'Updated', $6) -- Default change comment is 'Updated'
		`
		_, err = sessionTx.ExecContext(spanContext, insertVersionQuery,
			entityToUpdate.Id,
			entityToUpdate.Version, // The new version number.
			entityVersionSnapshotJSON,
			entityToUpdate.UpdatedBy,  // The user who performed the update.
			entityToUpdate.UpdateTime, // Use the formatted string time consistent with the entity record.
			orgID,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert entity version history after update")
			return nil, fmt.Errorf("failed to insert entity version history after update: %w", err)
		}

		// Step 6: Retrieve and return the updated entity from the database.
		return repository.GetLatestEntity(spanContext, sessionTx, entityToUpdate.Id)
	})
}

// ListLatestEntities retrieves a paginated list of the most recent versions of entities,
// optionally filtered by entity type. Results are ordered by creation time.
func (repository *PostgresEntityRepository) ListLatestEntities(ctx context.Context, tx *sql.Tx, pageSize int, pageToken string, entityTypeFilter entity.EntityType) (*ListEntitiesResult, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.ListLatestEntities")
	defer finishSpan()

	span.SetTag("entity.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("entity.type_filter", entityTypeFilter.String())
	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*ListEntitiesResult, error) {
		// Step 1: Determine the offset based on the page token.
		offset := 0
		if pageToken != "" {
			// Basic validation: ensure token is a non-negative integer.
			if parsedOffset, err := strconv.Atoi(pageToken); err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			} else {
				// Invalid token format or value, treat as first page.
				offset = 0
			}
		}

		// Step 2: Build the SQL query dynamically based on the filter.
		baseQuery := `
			SELECT id, org_id, schema_id, schema_version, data, create_time, update_time,
				   entity_type, created_by, updated_by, version, status, tags, resource_type
			FROM entities
		`
		var conditions []string
		var args []interface{}
		argCounter := 1

		// Add entity type filter if specified.
		if entityTypeFilter != entity.EntityType_ENTITY_TYPE_UNSPECIFIED {
			conditions = append(conditions, fmt.Sprintf("entity_type = $%d", argCounter))
			args = append(args, int32(entityTypeFilter))
			argCounter++
		}
		// Construct the final query string.
		query := baseQuery
		if len(conditions) > 0 {
			query += " WHERE " + conditions[0] // Assuming only one condition for now. Add JOIN logic if more.
		}
		query += fmt.Sprintf(" ORDER BY create_time DESC, id DESC LIMIT $%d OFFSET $%d", argCounter, argCounter+1)
		args = append(args, pageSize, offset)

		// Step 3: Execute the query.
		rows, err := sessionTx.QueryContext(spanContext, query, args...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query list entities")
			return nil, fmt.Errorf("failed to query list entities: %w", err)
		}
		defer rows.Close()

		// Step 4: Iterate through the results and scan each row.
		var entities []*entity.Entity
		for rows.Next() {
			var currentEntity entity.Entity
			var orgID int32
			var entityDataJSON string
			var entityTypeValue, statusValue int32
			var tags []string
			var nullableSchemaID sql.NullString
			var createTime, updateTime time.Time

			scanErr := rows.Scan(
				&currentEntity.Id,
				&orgID,
				&nullableSchemaID,
				&currentEntity.SchemaVersion,
				&entityDataJSON,
				&createTime,
				&updateTime,
				&entityTypeValue,
				&currentEntity.CreatedBy,
				&currentEntity.UpdatedBy,
				&currentEntity.Version,
				&statusValue,
				pq.Array(&tags),
				&currentEntity.ResourceType,
			)
			if scanErr != nil {
				herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan entity row during list")
				return nil, fmt.Errorf("failed to scan entity row during list: %w", scanErr)
			}

			// Step 5: Post-process scanned data for each entity.
			currentEntity.CreateTime = createTime.Format(time.RFC3339Nano)
			currentEntity.UpdateTime = updateTime.Format(time.RFC3339Nano)
			currentEntity.EntityType = entity.EntityType(entityTypeValue)
			currentEntity.Status = entity.RecordStatus(statusValue)
			currentEntity.OrgId = orgID
			currentEntity.Tags = tags

			// Assign schema_id from nullableSchemaID
			if nullableSchemaID.Valid {
				currentEntity.SchemaId = nullableSchemaID.String
			} else {
				currentEntity.SchemaId = ""
			}

			structData, err := workflowUtils.JSONToStruct(entityDataJSON)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to unmarshal entity data JSON for ID '%s' in list", currentEntity.Id))
				return nil, fmt.Errorf("failed to unmarshal entity data JSON for ID '%s' in list: %w", currentEntity.Id, err)
			}
			currentEntity.Data = structData

			entities = append(entities, &currentEntity)
		}
		if err = rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("error iterating entity list results: %w", err)
		}

		// Step 6: Determine the next page token.
		var nextPageToken string
		if len(entities) == pageSize {
			// If we retrieved a full page, there might be more data.
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		// Step 7: Return the results.
		return &ListEntitiesResult{
			Entities:  entities,
			PageToken: nextPageToken,
		}, nil
	})
}

// DeleteAllVersionsOfEntity permanently deletes an entity record from the 'entities' table
// and all associated version history from the 'entity_versions' table.
// This operation is irreversible.
func (repository *PostgresEntityRepository) DeleteAllVersionsOfEntity(ctx context.Context, tx *sql.Tx, entityID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.DeleteAllVersionsOfEntity")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	return database.WithSessionErr(repository.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		// Step 1: Delete all version history records for the entity.
		// It's generally safer to delete from the referencing table first.
		_, err := sessionTx.ExecContext(spanContext, "DELETE FROM entity_versions WHERE entity_id = $1", entityID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete entity versions for ID '%s'", entityID))
			return fmt.Errorf("failed to delete entity versions for ID '%s': %w", entityID, err)
		}

		// Step 2: Delete the main entity record.
		result, err := sessionTx.ExecContext(spanContext, "DELETE FROM entities WHERE id = $1", entityID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete entity record for ID '%s'", entityID))
			return fmt.Errorf("failed to delete entity record for ID '%s': %w", entityID, err)
		}

		// Step 3: Verify that the entity record was actually deleted.
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			// This error is about getting the RowsAffected count, not the delete itself.
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("failed to check rows affected after deleting entity ID '%s': %w", entityID, err)
		}
		if rowsAffected == 0 {
			// This implies the entity ID didn't exist in the 'entities' table to begin with.
			// Return an error consistent with Get/Update operations.
			err := fmt.Errorf("entity with ID '%s' not found for deletion", entityID)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return err
		}

		// Step 4: Deletion successful.
		return nil
	})
}

// DeleteSpecificVersionOfEntity deletes a single version record from the 'entity_versions' table.
// It does not affect the main 'entities' table or other versions.
// Use with caution, as it breaks the linear history.
func (repository *PostgresEntityRepository) DeleteSpecificVersionOfEntity(ctx context.Context, tx *sql.Tx, entityID string, version int32) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.DeleteSpecificVersionOfEntity")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("entity.version", fmt.Sprintf("%d", version))

	return database.WithSessionErr(repository.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		// Step 1: Execute the delete operation for the specific version.
		result, err := sessionTx.ExecContext(spanContext, "DELETE FROM entity_versions WHERE entity_id = $1 AND version = $2", entityID, version)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete entity version %d for ID '%s'", version, entityID))
			return fmt.Errorf("failed to delete entity version %d for ID '%s': %w", version, entityID, err)
		}

		// Step 2: Verify that the specified version was found and deleted.
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to check rows affected after deleting entity version %d for ID '%s'", version, entityID))
			return fmt.Errorf("failed to check rows affected after deleting entity version %d for ID '%s': %w", version, entityID, err)
		}
		if rowsAffected == 0 {
			err := fmt.Errorf("entity version %d for ID '%s' not found for deletion", version, entityID)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return err
		}

		// Step 3: Deletion successful.
		return nil
	})
}

// ListAllVersionsOfEntity retrieves all historical versions of a specific entity
// from the 'entity_versions' table, ordered by version number ascending.
func (repository *PostgresEntityRepository) ListAllVersionsOfEntity(ctx context.Context, tx *sql.Tx, entityID string) ([]*entity.EntityVersion, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.ListAllVersionsOfEntity")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) ([]*entity.EntityVersion, error) {
		// Step 1: Query the entity_versions table.
		query := `
			SELECT version, entity_snapshot, modified_by, modified_time, change_comment, org_id
			FROM entity_versions
			WHERE entity_id = $1
			ORDER BY version DESC
		`
		rows, err := sessionTx.QueryContext(spanContext, query, entityID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to query entity versions for ID '%s'", entityID))
			return nil, fmt.Errorf("failed to query entity versions for ID '%s': %w", entityID, err)
		}
		defer rows.Close()

		// Step 2: Iterate through results and build the list of versions.
		var entityVersions []*entity.EntityVersion
		for rows.Next() {
			var versionNumber int32
			var entitySnapshotJSON string
			var modifiedBy, modifiedTimeStr, changeComment string
			var orgID int32 // Retrieved but not directly part of EntityVersion proto.

			if err := rows.Scan(&versionNumber, &entitySnapshotJSON, &modifiedBy, &modifiedTimeStr, &changeComment, &orgID); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to scan entity version row for ID '%s'", entityID))
				return nil, fmt.Errorf("failed to scan entity version row for ID '%s': %w", entityID, err)
			}

			// Step 3: Unmarshal the JSON snapshot back into an Entity object.
			snapshotEntity, err := workflowUtils.JSONToEntity(entitySnapshotJSON)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to unmarshal entity snapshot JSON for version %d of ID '%s'", versionNumber, entityID))
				return nil, fmt.Errorf("failed to unmarshal entity snapshot JSON for version %d of ID '%s': %w", versionNumber, entityID, err)
			}

			// Step 4: Append the constructed version object to the list.
			entityVersions = append(entityVersions, &entity.EntityVersion{
				EntityId:       entityID, // Set the entity ID on the version object.
				Version:        versionNumber,
				EntitySnapshot: snapshotEntity,
				ModifiedBy:     modifiedBy,
				ModifiedTime:   modifiedTimeStr, // Keep as string as per proto definition.
				ChangeComment:  changeComment,
			})
		}
		if err = rows.Err(); err != nil { // Check for errors during iteration.
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("error iterating entity version results for ID '%s': %w", entityID, err)
		}

		// Step 5: Return the list of versions. If no versions found, returns an empty list, not an error.
		return entityVersions, nil
	})
}

// BulkCreateEntities creates multiple entity records using optimized bulk INSERT operations.
// This implementation uses multi-row INSERT statements to minimize database round-trips.
// If any creation fails, the transaction is rolled back, and the error is returned.
func (repository *PostgresEntityRepository) BulkCreateEntities(ctx context.Context, tx *sql.Tx, entitiesToCreate []*entity.Entity) ([]*entity.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.BulkCreateEntities")
	defer finishSpan()

	span.SetTag("entity.count", fmt.Sprintf("%d", len(entitiesToCreate)))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) ([]*entity.Entity, error) {
		if len(entitiesToCreate) == 0 {
			return []*entity.Entity{}, nil
		}

		// For very large batches, chunk them to prevent memory/parameter limit issues
		const maxBatchSize = 100
		if len(entitiesToCreate) > maxBatchSize {
			var allCreatedEntities []*entity.Entity
			for i := 0; i < len(entitiesToCreate); i += maxBatchSize {
				end := i + maxBatchSize
				if end > len(entitiesToCreate) {
					end = len(entitiesToCreate)
				}
				batchEntities, err := repository.BulkCreateEntities(spanContext, sessionTx, entitiesToCreate[i:end])
				if err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create entity batch starting at index %d", i))
					return nil, fmt.Errorf("failed to create entity batch starting at index %d: %w", i, err)
				}
				allCreatedEntities = append(allCreatedEntities, batchEntities...)
			}
			return allCreatedEntities, nil
		}

		// Step 1: Prepare entities and collect data for bulk operations
		orgID := cmncontext.GetOrgId(spanContext)
		currentTime := time.Now()

		var entityIDs []string
		var entityValues []interface{}
		var versionValues []interface{}
		entityPlaceholders := make([]string, 0, len(entitiesToCreate))
		versionPlaceholders := make([]string, 0, len(entitiesToCreate))

		for i, entityToCreate := range entitiesToCreate {
			// Generate ID if not provided
			if entityToCreate.Id == "" {
				entityToCreate.Id = uuid.New().String()
			}

			// Set timestamps and initial version
			entityToCreate.CreateTime = currentTime.Format(time.RFC3339Nano)
			entityToCreate.UpdateTime = currentTime.Format(time.RFC3339Nano)
			entityToCreate.CreatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
			entityToCreate.UpdatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
			if entityToCreate.Version == 0 {
				entityToCreate.Version = 1
			}

			// Convert protobuf Struct data to JSON
			entityDataJSON, err := workflowUtils.StructToJSON(entityToCreate.Data)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to marshal entity data to JSON for entity %d", i))
				return nil, fmt.Errorf("failed to marshal entity data to JSON for entity %d: %w", i, err)
			}

			entityIDs = append(entityIDs, entityToCreate.Id)

			// Build entity INSERT values
			baseIndex := i * 13
			entityPlaceholders = append(entityPlaceholders, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
				baseIndex+1, baseIndex+2, baseIndex+3, baseIndex+4, baseIndex+5, baseIndex+6, baseIndex+7,
				baseIndex+8, baseIndex+9, baseIndex+10, baseIndex+11, baseIndex+12, baseIndex+13))

			entityValues = append(entityValues,
				entityToCreate.Id,
				orgID,
				nullIfEmpty(entityToCreate.SchemaId),
				entityToCreate.SchemaVersion,
				entityDataJSON,
				currentTime,
				currentTime,
				int32(entityToCreate.EntityType),
				entityToCreate.CreatedBy,
				entityToCreate.UpdatedBy,
				entityToCreate.Version,
				int32(entityToCreate.Status),
				pq.Array(entityToCreate.Tags),
			)

			// Build version history INSERT values
			entityVersionSnapshotJSON, err := workflowUtils.EntityToJSON(entityToCreate)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to marshal entity snapshot for version history %d", i))
				return nil, fmt.Errorf("failed to marshal entity snapshot for version history %d: %w", i, err)
			}

			versionBaseIndex := i * 7
			versionPlaceholders = append(versionPlaceholders, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d)",
				versionBaseIndex+1, versionBaseIndex+2, versionBaseIndex+3, versionBaseIndex+4, versionBaseIndex+5, versionBaseIndex+6, versionBaseIndex+7))

			versionValues = append(versionValues,
				entityToCreate.Id,
				entityToCreate.Version,
				entityVersionSnapshotJSON,
				entityToCreate.CreatedBy,
				currentTime.Format(time.RFC3339Nano),
				"Created",
				orgID,
			)
		}

		// Step 2: Execute bulk INSERT for entities table
		var entityQueryBuilder strings.Builder
		entityQueryBuilder.WriteString(`
			INSERT INTO entities (
				id, org_id, schema_id, schema_version, data, create_time, update_time,
				entity_type, created_by, updated_by, version, status, tags
			) VALUES `)
		entityQueryBuilder.WriteString(strings.Join(entityPlaceholders, ", "))
		entityInsertQuery := entityQueryBuilder.String()

		_, err := sessionTx.ExecContext(spanContext, entityInsertQuery, entityValues...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk insert entities")
			return nil, fmt.Errorf("failed to bulk insert entities: %w", err)
		}

		// Step 3: Execute bulk INSERT for entity_versions table
		var versionQueryBuilder strings.Builder
		versionQueryBuilder.WriteString(`
			INSERT INTO entity_versions (
				entity_id, version, entity_snapshot, modified_by, modified_time, change_comment, org_id
			) VALUES `)
		versionQueryBuilder.WriteString(strings.Join(versionPlaceholders, ", "))
		versionInsertQuery := versionQueryBuilder.String()

		_, err = sessionTx.ExecContext(spanContext, versionInsertQuery, versionValues...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk insert entity versions")
			return nil, fmt.Errorf("failed to bulk insert entity versions: %w", err)
		}

		// Step 4: Batch retrieve the created entities
		return repository.BatchGetLatestEntities(spanContext, sessionTx, entityIDs)
	})
}

// BulkUpdateEntities updates multiple entity records using optimized batch operations where possible.
// Note: Due to optimistic locking requirements, this still requires individual updates,
// but uses batch retrieval for the final result to minimize database round-trips.
func (repository *PostgresEntityRepository) BulkUpdateEntities(ctx context.Context, tx *sql.Tx, entitiesToUpdate []*entity.Entity) ([]*entity.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.BulkUpdateEntities")
	defer finishSpan()

	span.SetTag("entity.count", fmt.Sprintf("%d", len(entitiesToUpdate)))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) ([]*entity.Entity, error) {
		if len(entitiesToUpdate) == 0 {
			return []*entity.Entity{}, nil
		}

		// For very large batches, chunk them to prevent memory/parameter limit issues
		const maxBatchSize = 50 // Smaller batch size for updates due to complexity
		if len(entitiesToUpdate) > maxBatchSize {
			var allUpdatedEntities []*entity.Entity
			for i := 0; i < len(entitiesToUpdate); i += maxBatchSize {
				end := i + maxBatchSize
				if end > len(entitiesToUpdate) {
					end = len(entitiesToUpdate)
				}
				batchEntities, err := repository.BulkUpdateEntities(spanContext, sessionTx, entitiesToUpdate[i:end])
				if err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update entity batch starting at index %d", i))
					return nil, fmt.Errorf("failed to update entity batch starting at index %d: %w", i, err)
				}
				allUpdatedEntities = append(allUpdatedEntities, batchEntities...)
			}
			return allUpdatedEntities, nil
		}

		// Step 1: Collect entity IDs for batch retrieval at the end
		var entityIDs []string

		// Step 2: Perform individual updates (required due to optimistic locking)
		// Note: This is still N operations, but we optimize the final retrieval
		for i, entityToUpdate := range entitiesToUpdate {
			// Get existing entity for optimistic lock version
			existingEntity, err := repository.GetLatestEntity(spanContext, sessionTx, entityToUpdate.Id)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get existing entity for update at index %d (ID: %s)", i, entityToUpdate.Id))
				return nil, fmt.Errorf("failed to get existing entity for update at index %d (ID: %s): %w", i, entityToUpdate.Id, err)
			}

			// Prepare updated entity record
			updatedVersion := existingEntity.Version + 1
			entityToUpdate.Version = updatedVersion
			currentTime := time.Now()
			entityToUpdate.UpdateTime = currentTime.Format(time.RFC3339Nano)
			entityToUpdate.CreateTime = existingEntity.CreateTime
			entityToUpdate.CreatedBy = existingEntity.CreatedBy
			entityToUpdate.UpdatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)

			// Convert protobuf Struct data to JSON
			entityDataJSON, err := workflowUtils.StructToJSON(entityToUpdate.Data)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to marshal updated entity data to JSON at index %d", i))
				return nil, fmt.Errorf("failed to marshal updated entity data to JSON at index %d: %w", i, err)
			}

			// Execute UPDATE with optimistic lock
			updateQuery := `
				UPDATE entities
				SET schema_id = $1, schema_version = $2, data = $3, update_time = $4,
					entity_type = $5, updated_by = $6, version = $7, status = $8,
					tags = $9
				WHERE id = $10 AND version = $11
			`
			result, err := sessionTx.ExecContext(spanContext, updateQuery,
				nullIfEmpty(entityToUpdate.SchemaId),
				entityToUpdate.SchemaVersion,
				entityDataJSON,
				currentTime,
				int32(entityToUpdate.EntityType),
				entityToUpdate.UpdatedBy,
				entityToUpdate.Version,
				int32(entityToUpdate.Status),
				pq.Array(entityToUpdate.Tags),
				entityToUpdate.Id,
				existingEntity.Version,
			)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to execute update entity statement at index %d", i))
				return nil, fmt.Errorf("failed to execute update entity statement at index %d: %w", i, err)
			}

			// Check optimistic lock result
			rowsAffected, err := result.RowsAffected()
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to check rows affected after entity update at index %d", i))
				return nil, fmt.Errorf("failed to check rows affected after entity update at index %d: %w", i, err)
			}
			if rowsAffected == 0 {
				err := fmt.Errorf("update conflict: entity with ID '%s' was modified concurrently (expected version %d)", entityToUpdate.Id, existingEntity.Version)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
				return nil, err
			}

			// Insert new version history record
			orgID := cmncontext.GetOrgId(spanContext)
			entityVersionSnapshotJSON, err := workflowUtils.EntityToJSON(entityToUpdate)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to marshal entity snapshot for update version history at index %d", i))
				return nil, fmt.Errorf("failed to marshal entity snapshot for update version history at index %d: %w", i, err)
			}
			insertVersionQuery := `
				INSERT INTO entity_versions (
					entity_id, version, entity_snapshot, modified_by, modified_time, change_comment, org_id
				)
				VALUES ($1, $2, $3, $4, $5, 'Updated', $6)
			`
			_, err = sessionTx.ExecContext(spanContext, insertVersionQuery,
				entityToUpdate.Id,
				entityToUpdate.Version,
				entityVersionSnapshotJSON,
				entityToUpdate.UpdatedBy,
				entityToUpdate.UpdateTime,
				orgID,
			)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to insert entity version history after update at index %d", i))
				return nil, fmt.Errorf("failed to insert entity version history after update at index %d: %w", i, err)
			}

			entityIDs = append(entityIDs, entityToUpdate.Id)
		}

		// Step 3: Batch retrieve all updated entities in a single query
		return repository.BatchGetLatestEntities(spanContext, sessionTx, entityIDs)
	})
}

// BulkDeleteEntities deletes multiple entities using optimized batch DELETE operations.
// This implementation uses WHERE id = ANY($1) pattern to minimize database round-trips.
// If any deletion fails, the transaction is rolled back.
func (repository *PostgresEntityRepository) BulkDeleteEntities(ctx context.Context, tx *sql.Tx, entityIDsToDelete []string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.BulkDeleteEntities")
	defer finishSpan()

	span.SetTag("entity.count", fmt.Sprintf("%d", len(entityIDsToDelete)))

	return database.WithSessionErr(repository.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		if len(entityIDsToDelete) == 0 {
			return nil
		}

		// For very large batches, chunk them to prevent parameter limit issues
		const maxBatchSize = 1000
		if len(entityIDsToDelete) > maxBatchSize {
			for i := 0; i < len(entityIDsToDelete); i += maxBatchSize {
				end := i + maxBatchSize
				if end > len(entityIDsToDelete) {
					end = len(entityIDsToDelete)
				}
				err := repository.BulkDeleteEntities(spanContext, sessionTx, entityIDsToDelete[i:end])
				if err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete entity batch starting at index %d", i))
					return fmt.Errorf("failed to delete entity batch starting at index %d: %w", i, err)
				}
			}
			return nil
		}

		// Step 1: Delete all version history records first (safer order)
		// Use batch DELETE with ANY() for efficient bulk deletion
		_, err := sessionTx.ExecContext(spanContext,
			"DELETE FROM entity_versions WHERE entity_id = ANY($1)",
			pq.Array(entityIDsToDelete))
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk delete entity versions")
			return fmt.Errorf("failed to bulk delete entity versions: %w", err)
		}

		// Step 2: Delete the main entity records
		// Use batch DELETE with ANY() for efficient bulk deletion
		result, err := sessionTx.ExecContext(spanContext,
			"DELETE FROM entities WHERE id = ANY($1)",
			pq.Array(entityIDsToDelete))
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk delete entities")
			return fmt.Errorf("failed to bulk delete entities: %w", err)
		}

		// Step 3: Verify that at least some entities were deleted
		// Note: We don't require all entities to exist, as some might have been deleted already
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to check rows affected after bulk entity deletion")
			return fmt.Errorf("failed to check rows affected after bulk entity deletion: %w", err)
		}

		// If no rows were affected, it means none of the specified entities existed
		if rowsAffected == 0 {
			err := fmt.Errorf("no entities found for deletion from the provided %d entity IDs", len(entityIDsToDelete))
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return err
		}

		return nil // Bulk deletion successful
	})
}

// RestoreEntityVersion reverts the current state of an entity in the 'entities' table
// to a snapshot from a previous version stored in 'entity_versions'.
// This creates a *new* version history record indicating the restore operation.
func (repository *PostgresEntityRepository) RestoreEntityVersion(ctx context.Context, tx *sql.Tx, entityID string, targetVersion int32) (*entity.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.RestoreEntityVersion")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("entity.target_version", fmt.Sprintf("%d", targetVersion))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.Entity, error) {
		// Step 1: Retrieve all historical versions to find the target snapshot and the latest version number.
		allVersions, err := repository.ListAllVersionsOfEntity(spanContext, sessionTx, entityID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to list versions for restore (ID: %s)", entityID))
			return nil, fmt.Errorf("failed to list versions for restore (ID: %s): %w", entityID, err)
		}
		if len(allVersions) == 0 {
			err := fmt.Errorf("cannot restore: no versions found for entity ID '%s'", entityID)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return nil, err
		}

		// Step 2: Find the snapshot of the target version.
		var targetVersionSnapshot *entity.Entity
		for _, historicalVersion := range allVersions {
			if historicalVersion.Version == targetVersion {
				// Deep clone the snapshot to avoid modifying the retrieved history data.
				targetVersionSnapshot = proto.Clone(historicalVersion.EntitySnapshot).(*entity.Entity)
				break
			}
		}
		if targetVersionSnapshot == nil {
			err := fmt.Errorf("specified version %d not found for entity ID '%s'", targetVersion, entityID)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return nil, err
		}

		// Step 3: Prepare the entity record for the update based on the restored snapshot.
		// Create a new entity object based on the cloned snapshot.
		restoredEntity := targetVersionSnapshot // Already cloned.
		restoredEntity.Id = entityID            // Ensure ID is correct.

		// Determine the next version number.
		latestVersion := allVersions[0].Version
		nextVersion := latestVersion + 1
		restoredEntity.Version = nextVersion

		// Set the update time for the restore operation.
		restoreTime := time.Now()
		restoredEntity.UpdateTime = restoreTime.Format(time.RFC3339Nano)

		// Here, we are restoring the *data* and associated fields from the snapshot,
		// but the 'entities' table's updated_by should reflect who did the restore.

		// Convert the restored data to JSON.
		restoredEntityDataJSON, err := workflowUtils.StructToJSON(restoredEntity.Data)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal restored entity data to JSON")
			return nil, fmt.Errorf("failed to marshal restored entity data to JSON: %w", err)
		}
		restoredBy, _ := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx) // TODO: We are not handling error, error will result in "" string. So handle error if necessary

		// Step 4: Update the main 'entities' table with the restored data and new version.
		// First query the current entity to get its version
		currentEntity, err := repository.GetLatestEntity(spanContext, sessionTx, entityID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get current entity state for restore (ID: %s)", entityID))
			return nil, fmt.Errorf("failed to get current entity state for restore (ID: %s): %w", entityID, err)
		}

		// Now do the update with optimistic locking
		updateQuery := `
			UPDATE entities
			SET schema_id = $1, schema_version = $2, data = $3, update_time = $4,
				entity_type = $5, updated_by = $6, version = $7, status = $8,
				tags = $9
			WHERE id = $10 AND version = $11 -- Optimistic lock condition
		`
		result, err := sessionTx.ExecContext(spanContext, updateQuery,
			nullIfEmpty(restoredEntity.SchemaId),
			restoredEntity.SchemaVersion,
			restoredEntityDataJSON,
			restoreTime,
			int32(restoredEntity.EntityType),
			restoredBy,
			restoredEntity.Version, // The new version number
			int32(restoredEntity.Status),
			pq.Array(restoredEntity.Tags),
			entityID,              // WHERE id = ?
			currentEntity.Version, // WHERE version = ? (for optimistic locking)
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update entity table during restore")
			return nil, fmt.Errorf("failed to update entity table during restore: %w", err)
		}

		// Check if the optimistic lock condition was met
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to check rows affected after entity restore")
			return nil, fmt.Errorf("failed to check rows affected after entity restore: %w", err)
		}
		if rowsAffected == 0 {
			err := fmt.Errorf("restore conflict: entity with ID '%s' was modified concurrently (expected version %d)",
				entityID, currentEntity.Version)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
			return nil, err
		}

		// Step 5: Insert a new version history record for the restore action.
		orgID := cmncontext.GetOrgId(spanContext)
		restoredEntitySnapshotJSON, err := workflowUtils.EntityToJSON(restoredEntity)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal restored entity snapshot for version history")
			return nil, fmt.Errorf("failed to marshal restored entity snapshot for version history: %w", err)
		}
		insertVersionQuery := `
			INSERT INTO entity_versions (
				entity_id, version, entity_snapshot, modified_by, modified_time, change_comment, org_id
			)
			VALUES ($1, $2, $3, $4, $5, $6, $7)
		`
		changeComment := fmt.Sprintf("Restored to version %d", targetVersion)
		_, err = sessionTx.ExecContext(spanContext, insertVersionQuery,
			entityID,
			restoredEntity.Version, // The new version number.
			restoredEntitySnapshotJSON,
			restoredBy,
			restoredEntity.UpdateTime,
			changeComment,
			orgID,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert restore entity version history")
			return nil, fmt.Errorf("failed to insert restore entity version history: %w", err)
		}

		// Step 6: Return the newly restored entity state by fetching it again.
		return repository.GetLatestEntity(spanContext, sessionTx, entityID)
	})
}

// DiffEntityVersions calculates the textual difference between two historical versions of an entity.
func (repository *PostgresEntityRepository) DiffEntityVersions(ctx context.Context, tx *sql.Tx, entityID string, version1 int32, version2 int32) (string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.DiffEntityVersions")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("entity.version1", fmt.Sprintf("%d", version1))
	span.SetTag("entity.version2", fmt.Sprintf("%d", version2))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (string, error) {
		// Retrieve the two versions of the entity using the transaction from WithSession.
		entity1, err := repository.GetEntityByVersion(spanContext, sessionTx, entityID, version1)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to retrieve entity version %d", version1))
			return "", fmt.Errorf("failed to retrieve entity version %d: %w", version1, err)
		}
		entity2, err := repository.GetEntityByVersion(spanContext, sessionTx, entityID, version2)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to retrieve entity version %d", version2))
			return "", fmt.Errorf("failed to retrieve entity version %d: %w", version2, err)
		}

		// Marshal the entities into pretty-printed JSON.
		// Note: Using the full entity object from the snapshot for diffing.
		json1, err := json.MarshalIndent(entity1, "", "  ")
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to marshal entity version %d", version1))
			return "", fmt.Errorf("failed to marshal entity version %d: %w", version1, err)
		}
		json2, err := json.MarshalIndent(entity2, "", "  ")
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to marshal entity version %d", version2))
			return "", fmt.Errorf("failed to marshal entity version %d: %w", version2, err)
		}

		// Use diffmatchpatch to generate a diff.
		dmp := diffmatchpatch.New()
		diffs := dmp.DiffMain(string(json1), string(json2), false)
		diffText := dmp.DiffPrettyText(diffs)

		return diffText, nil
	})
}

// CheckEntityPermissions verifies if a given user has the necessary permissions
// to perform a specific action on an entity.
// NOTE: This is currently a placeholder stub and always returns true.
func (repository *PostgresEntityRepository) CheckEntityPermissions(ctx context.Context, tx *sql.Tx, entityID string, userID string, action string) (bool, error) {
	_, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.CheckEntityPermissions")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("user.id", userID)
	span.SetTag("permission.action", action)

	// TODO: Implement actual permission checking logic. This likely involves:
	fmt.Printf("Placeholder: Checking permission for user '%s' action '%s' on entity '%s'\n", userID, action, entityID)
	return true, nil // Stub: Always allow for now.
}

// GetEntityByVersion retrieves a specific historical version of an entity
// directly from the 'entity_versions' table snapshot.
func (repository *PostgresEntityRepository) GetEntityByVersion(ctx context.Context, tx *sql.Tx, entityID string, version int32) (*entity.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.GetEntityByVersion")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("entity.version", fmt.Sprintf("%d", version))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.Entity, error) {
		// Step 1: Query the specific version snapshot.
		query := `
			SELECT entity_snapshot
			FROM entity_versions
			WHERE entity_id = $1 AND version = $2
		`
		row := sessionTx.QueryRowContext(spanContext, query, entityID, version)

		// Step 2: Scan the JSON snapshot string.
		var entitySnapshotJSON string
		if err := row.Scan(&entitySnapshotJSON); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				err := fmt.Errorf("entity version %d for ID '%s' not found", version, entityID)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
				return nil, err
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to scan entity snapshot for version %d, ID '%s'", version, entityID))
			return nil, fmt.Errorf("failed to scan entity snapshot for version %d, ID '%s': %w", version, entityID, err)
		}

		// Step 3: Unmarshal the JSON into an Entity object.
		entityRecord, err := workflowUtils.JSONToEntity(entitySnapshotJSON)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to unmarshal entity snapshot JSON for version %d, ID '%s'", version, entityID))
			return nil, fmt.Errorf("failed to unmarshal entity snapshot JSON for version %d, ID '%s': %w", version, entityID, err)
		}

		// Step 4: Return the unmarshalled entity.
		return entityRecord, nil
	})
}

// GetLatestActiveEntity retrieves the highest version of an entity whose status is active.
// It does so by querying the entity_versions table and filtering on the JSON snapshot field.
func (repository *PostgresEntityRepository) GetLatestActiveEntity(ctx context.Context, tx *sql.Tx, entityID string) (*entity.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.GetLatestActiveEntity")
	defer finishSpan()

	span.SetTag("entity.id", entityID)

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.Entity, error) {
		activeStatus := int32(entity.RecordStatus_RECORD_STATUS_ACTIVE) // typically 1

		query := `
			SELECT entity_snapshot
			FROM entity_versions
			WHERE entity_id = $1 
			  AND (entity_snapshot::jsonb->>'status')::int = $2
			ORDER BY version DESC
			LIMIT 1
		`
		row := sessionTx.QueryRowContext(spanContext, query, entityID, activeStatus)
		var entitySnapshotJSON string
		if err := row.Scan(&entitySnapshotJSON); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				err := fmt.Errorf("active entity with ID '%s' not found", entityID)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
				return nil, err
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query active entity snapshot")
			return nil, fmt.Errorf("failed to query active entity snapshot: %w", err)
		}
		activeEntity, err := workflowUtils.JSONToEntity(entitySnapshotJSON)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to unmarshal active entity JSON for ID '%s'", entityID))
			return nil, fmt.Errorf("failed to unmarshal active entity JSON for ID '%s': %w", entityID, err)
		}
		return activeEntity, nil
	})
}

// -------------------------------
// Entity Schema Operations
// -------------------------------

// CreateEntitySchema inserts a new schema record into 'entity_schemas' and its initial version history.
func (repository *PostgresEntityRepository) CreateEntitySchema(ctx context.Context, tx *sql.Tx, schemaToCreate *entity.EntitySchema) (*entity.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.CreateEntitySchema")
	defer finishSpan()

	span.SetTag("entity_schema.name", schemaToCreate.Name)
	span.SetTag("entity_schema.type", schemaToCreate.EntityType.String())

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.EntitySchema, error) {
		// Step 1: Prepare schema record.
		if schemaToCreate.Id == "" {
			schemaToCreate.Id = uuid.New().String()
		}
		orgID := cmncontext.GetOrgId(spanContext)
		currentTime := time.Now()
		schemaToCreate.CreateTime = currentTime.Format(time.RFC3339Nano)
		schemaToCreate.UpdateTime = currentTime.Format(time.RFC3339Nano)
		schemaToCreate.CreatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx) // TODO: Handle error if necessary
		schemaToCreate.UpdatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx) // TODO: Handle error if necessary
		if schemaToCreate.Version == 0 {
			schemaToCreate.Version = 1
		}
		schemaDefinitionJSON, err := workflowUtils.StructToJSON(schemaToCreate.SchemaDefinition)

		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal schema definition to JSON")
			return nil, fmt.Errorf("failed to marshal schema definition to JSON: %w", err)
		}

		// Step 2: Insert into 'entity_schemas'.
		insertSchemaQuery := `
			INSERT INTO entity_schemas (
				id, org_id, name, description, schema_definition, create_time, update_time,
				created_by, updated_by, version, entity_type, status, tags
			)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		`
		preparedStatement, err := sessionTx.PrepareContext(spanContext, insertSchemaQuery)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to prepare create schema statement")
			return nil, fmt.Errorf("failed to prepare create schema statement: %w", err)
		}
		defer preparedStatement.Close()
		_, err = preparedStatement.ExecContext(spanContext,
			schemaToCreate.Id,
			orgID,
			schemaToCreate.Name,
			schemaToCreate.Description,
			schemaDefinitionJSON,
			currentTime,
			currentTime,
			schemaToCreate.CreatedBy,
			schemaToCreate.UpdatedBy,
			schemaToCreate.Version,
			int32(schemaToCreate.EntityType),
			int32(schemaToCreate.Status),
			pq.Array(schemaToCreate.Tags),
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute create schema statement")
			return nil, fmt.Errorf("failed to execute create schema statement: %w", err)
		}

		// Step 3: Insert initial version history.
		schemaVersionSnapshotJSON, err := workflowUtils.EntitySchemaToJSON(schemaToCreate)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal schema snapshot for version history")
			return nil, fmt.Errorf("failed to marshal schema snapshot for version history: %w", err)
		}
		insertVersionQuery := `
			INSERT INTO entity_schema_versions (schema_id, version, schema_snapshot, modified_by, modified_time, change_comment, org_id)
			VALUES ($1, $2, $3, $4, $5, 'Created', $6)
		`
		// Use CreatedBy for the initial version's modified_by.
		_, err = sessionTx.ExecContext(spanContext, insertVersionQuery, schemaToCreate.Id, schemaToCreate.Version, schemaVersionSnapshotJSON, schemaToCreate.CreatedBy, currentTime.Format(time.RFC3339Nano), orgID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert initial schema version history")
			return nil, fmt.Errorf("failed to insert initial schema version history: %w", err)
		}

		// Step 4: Return the created schema.
		return repository.GetLatestEntitySchema(spanContext, sessionTx, schemaToCreate.Id)
	})
}

// GetLatestEntitySchema retrieves the most recent version of a schema from 'entity_schemas'.
func (repository *PostgresEntityRepository) GetLatestEntitySchema(ctx context.Context, tx *sql.Tx, schemaID string) (*entity.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.GetLatestEntitySchema")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaID)

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.EntitySchema, error) {
		// Step 1: Query the latest schema.
		query := `
			SELECT id, org_id, name, description, schema_definition, create_time, update_time,
				   created_by, updated_by, version, entity_type, status, tags, resource_type
			FROM entity_schemas
			WHERE id = $1
		`
		row := sessionTx.QueryRowContext(spanContext, query, schemaID)

		// Step 2: Scan the result.
		var fetchedSchema entity.EntitySchema
		var orgID int32
		var schemaDefinitionJSON string
		var entityTypeValue, statusValue int32
		var tags []string
		var createTime, updateTime time.Time // Scan time directly

		scanErr := row.Scan(
			&fetchedSchema.Id,
			&orgID,
			&fetchedSchema.Name,
			&fetchedSchema.Description,
			&schemaDefinitionJSON,
			&createTime,
			&updateTime,
			&fetchedSchema.CreatedBy,
			&fetchedSchema.UpdatedBy,
			&fetchedSchema.Version,
			&entityTypeValue,
			&statusValue,
			pq.Array(&tags),
			&fetchedSchema.ResourceType,
		)
		if scanErr != nil {
			if errors.Is(scanErr, sql.ErrNoRows) {
				err := fmt.Errorf("entity schema with ID '%s' not found", schemaID)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
				return nil, err
			}
			herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan entity schema row")
			return nil, fmt.Errorf("failed to scan entity schema row: %w", scanErr)
		}

		// Step 3: Post-process scanned data.
		fetchedSchema.CreateTime = createTime.Format(time.RFC3339Nano) // Format time to string
		fetchedSchema.UpdateTime = updateTime.Format(time.RFC3339Nano)
		fetchedSchema.EntityType = entity.EntityType(entityTypeValue)
		fetchedSchema.Status = entity.RecordStatus(statusValue)
		fetchedSchema.OrgId = orgID
		fetchedSchema.Tags = tags

		// Convert the schema definition JSON to a protobuf Struct.
		structDef, err := workflowUtils.JSONToStruct(schemaDefinitionJSON)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to unmarshal schema definition JSON for ID '%s'", schemaID))
			return nil, fmt.Errorf("failed to unmarshal schema definition JSON for ID '%s': %w", schemaID, err)
		}
		fetchedSchema.SchemaDefinition = structDef

		// Step 4: Return the schema.
		return &fetchedSchema, nil
	})
}

// GetLatestActiveEntitySchema retrieves the highest version of an entity schema where the status is active.
// It queries the entity_schema_versions table using the JSON snapshot.
func (repository *PostgresEntityRepository) GetLatestActiveEntitySchema(ctx context.Context, tx *sql.Tx, schemaID string) (*entity.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.GetLatestActiveEntitySchema")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaID)

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.EntitySchema, error) {
		activeStatus := int32(entity.RecordStatus_RECORD_STATUS_ACTIVE) // active status (1)
		query := `
				SELECT schema_snapshot
				FROM entity_schema_versions
				WHERE schema_id = $1
				  AND (schema_snapshot::jsonb->>'status')::int = $2
				ORDER BY version DESC
				LIMIT 1
			`
		row := sessionTx.QueryRowContext(spanContext, query, schemaID, activeStatus)
		var schemaSnapshotJSON string
		if err := row.Scan(&schemaSnapshotJSON); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				err := fmt.Errorf("active entity schema with ID '%s' not found", schemaID)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
				return nil, err
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query active schema snapshot")
			return nil, fmt.Errorf("failed to query active schema snapshot: %w", err)
		}
		activeSchema, err := workflowUtils.JSONToEntitySchema(schemaSnapshotJSON)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to unmarshal active schema JSON for ID '%s'", schemaID))
			return nil, fmt.Errorf("failed to unmarshal active schema JSON for ID '%s': %w", schemaID, err)
		}
		return activeSchema, nil
	})
}

// GetEntitySchemaByVersion retrieves a specific historical version of a schema from 'entity_schema_versions'.
func (repository *PostgresEntityRepository) GetEntitySchemaByVersion(ctx context.Context, tx *sql.Tx, schemaID string, version int32) (*entity.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.GetEntitySchemaByVersion")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaID)
	span.SetTag("entity_schema.version", fmt.Sprintf("%d", version))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.EntitySchema, error) {
		// Step 1: Query the specific version snapshot.
		query := `
			SELECT schema_snapshot
				FROM entity_schema_versions
				WHERE schema_id = $1 AND version = $2
		`
		row := sessionTx.QueryRowContext(spanContext, query, schemaID, version)

		// Step 2: Scan the JSON snapshot.
		var schemaSnapshotJSON string
		if err := row.Scan(&schemaSnapshotJSON); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				err := fmt.Errorf("entity schema version %d for ID '%s' not found", version, schemaID)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
				return nil, err
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to scan schema snapshot for version %d, ID '%s'", version, schemaID))
			return nil, fmt.Errorf("failed to scan schema snapshot for version %d, ID '%s': %w", version, schemaID, err)
		}

		// Step 3: Unmarshal JSON to schema object.
		schemaRecord, err := workflowUtils.JSONToEntitySchema(schemaSnapshotJSON)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to unmarshal schema snapshot JSON for version %d, ID '%s'", version, schemaID))
			return nil, fmt.Errorf("failed to unmarshal schema snapshot JSON for version %d, ID '%s': %w", version, schemaID, err)
		}

		// Step 4: Return the schema.
		return schemaRecord, nil
	})
}

// UpdateEntitySchema updates an existing schema using optimistic locking and adds a version history record.
func (repository *PostgresEntityRepository) UpdateEntitySchema(ctx context.Context, tx *sql.Tx, schemaToUpdate *entity.EntitySchema) (*entity.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.UpdateEntitySchema")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaToUpdate.Id)
	span.SetTag("entity_schema.name", schemaToUpdate.Name)

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.EntitySchema, error) {
		// Step 1: Get existing schema for optimistic lock version.
		existingSchema, err := repository.GetLatestEntitySchema(spanContext, sessionTx, schemaToUpdate.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get existing schema for update (ID: %s)", schemaToUpdate.Id))
			return nil, fmt.Errorf("failed to get existing schema for update (ID: %s): %w", schemaToUpdate.Id, err)
		}

		// Step 2: Prepare updated schema record.
		updatedVersion := existingSchema.Version + 1
		schemaToUpdate.Version = updatedVersion
		currentTime := time.Now()
		schemaToUpdate.UpdateTime = currentTime.Format(time.RFC3339Nano)
		schemaToUpdate.CreateTime = existingSchema.CreateTime // Preserve original creation info.
		schemaToUpdate.CreatedBy = existingSchema.CreatedBy
		schemaToUpdate.UpdatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx) // TODO: Handle error if necessary
		schemaDefinitionJSON, err := workflowUtils.StructToJSON(schemaToUpdate.SchemaDefinition)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal updated schema definition to JSON")
			return nil, fmt.Errorf("failed to marshal updated schema definition to JSON: %w", err)
		}

		// Step 3: Execute UPDATE with optimistic lock.
		updateQuery := `
			UPDATE entity_schemas
			SET name = $1, description = $2, schema_definition = $3, update_time = $4,
				updated_by = $5, version = $6, entity_type = $7, status = $8,
				tags = $9
			WHERE id = $10 AND version = $11 -- Optimistic lock
		`
		preparedStatement, err := sessionTx.PrepareContext(spanContext, updateQuery)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to prepare update schema statement")
			return nil, fmt.Errorf("failed to prepare update schema statement: %w", err)
		}
		defer preparedStatement.Close()
		result, err := preparedStatement.ExecContext(spanContext,
			schemaToUpdate.Name, schemaToUpdate.Description, schemaDefinitionJSON, currentTime,
			schemaToUpdate.UpdatedBy, schemaToUpdate.Version, int32(schemaToUpdate.EntityType),
			int32(schemaToUpdate.Status), pq.Array(schemaToUpdate.Tags),
			schemaToUpdate.Id, existingSchema.Version, // WHERE id = ? AND version = ?
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute update schema statement")
			return nil, fmt.Errorf("failed to execute update schema statement: %w", err)
		}

		// Step 4: Check optimistic lock result.
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to check rows affected after schema update")
			return nil, fmt.Errorf("failed to check rows affected after schema update: %w", err)
		}
		if rowsAffected == 0 {
			err := fmt.Errorf("update conflict: entity schema with ID '%s' was modified concurrently (expected version %d)", schemaToUpdate.Id, existingSchema.Version)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
			return nil, err
		}

		// Step 5: Insert new version history record.
		orgID := cmncontext.GetOrgId(spanContext)
		schemaVersionSnapshotJSON, err := workflowUtils.EntitySchemaToJSON(schemaToUpdate)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal schema snapshot for update version history")
			return nil, fmt.Errorf("failed to marshal schema snapshot for update version history: %w", err)
		}
		insertVersionQuery := `
			INSERT INTO entity_schema_versions (schema_id, version, schema_snapshot, modified_by, modified_time, change_comment, org_id)
			VALUES ($1, $2, $3, $4, $5, 'Updated', $6)
		`
		_, err = sessionTx.ExecContext(spanContext, insertVersionQuery,
			schemaToUpdate.Id, schemaToUpdate.Version, schemaVersionSnapshotJSON,
			schemaToUpdate.UpdatedBy, schemaToUpdate.UpdateTime, orgID,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert schema version history after update")
			return nil, fmt.Errorf("failed to insert schema version history after update: %w", err)
		}

		// Step 6: Return the updated schema.
		return repository.GetLatestEntitySchema(spanContext, sessionTx, schemaToUpdate.Id)
	})
}

// DeleteAllVersionsOfEntitySchema permanently deletes a schema and all its version history.
func (repository *PostgresEntityRepository) DeleteAllVersionsOfEntitySchema(ctx context.Context, tx *sql.Tx, schemaID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.DeleteAllVersionsOfEntitySchema")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaID)

	return database.WithSessionErr(repository.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		// Step 1: Delete version history.
		_, err := sessionTx.ExecContext(spanContext, "DELETE FROM entity_schema_versions WHERE schema_id = $1", schemaID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete schema versions for ID '%s'", schemaID))
			return fmt.Errorf("failed to delete schema versions for ID '%s': %w", schemaID, err)
		}

		// Step 2: Delete the main schema record.
		result, err := sessionTx.ExecContext(spanContext, "DELETE FROM entity_schemas WHERE id = $1", schemaID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete schema record for ID '%s'", schemaID))
			return fmt.Errorf("failed to delete schema record for ID '%s': %w", schemaID, err)
		}

		// Step 3: Verify deletion.
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to check rows affected after deleting schema ID '%s'", schemaID))
			return fmt.Errorf("failed to check rows affected after deleting schema ID '%s': %w", schemaID, err)
		}
		if rowsAffected == 0 {
			err := fmt.Errorf("entity schema with ID '%s' not found for deletion", schemaID)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return err
		}

		// Step 4: Success.
		return nil
	})
}

// DeleteSpecificVersionOfEntitySchema deletes a single version from 'entity_schema_versions'.
func (repository *PostgresEntityRepository) DeleteSpecificVersionOfEntitySchema(ctx context.Context, tx *sql.Tx, schemaID string, version int32) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.DeleteSpecificVersionOfEntitySchema")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaID)
	span.SetTag("entity_schema.version", fmt.Sprintf("%d", version))

	return database.WithSessionErr(repository.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		// Step 1: Execute delete for the specific version.
		result, err := sessionTx.ExecContext(spanContext, "DELETE FROM entity_schema_versions WHERE schema_id = $1 AND version = $2", schemaID, version)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete schema version %d for ID '%s'", version, schemaID))
			return fmt.Errorf("failed to delete schema version %d for ID '%s': %w", version, schemaID, err)
		}

		// Step 2: Verify deletion.
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to check rows affected after deleting schema version %d for ID '%s'", version, schemaID))
			return fmt.Errorf("failed to check rows affected after deleting schema version %d for ID '%s': %w", version, schemaID, err)
		}
		if rowsAffected == 0 {
			err := fmt.Errorf("entity schema version %d for ID '%s' not found for deletion", version, schemaID)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return err
		}

		// Step 3: Success.
		return nil
	})
}

// ListLatestEntitySchemas retrieves a paginated list of the latest schemas, optionally filtered by type.
func (repository *PostgresEntityRepository) ListLatestEntitySchemas(ctx context.Context, tx *sql.Tx, pageSize int, pageToken string, entityTypeFilter entity.EntityType) (*ListEntitySchemasResult, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.ListLatestEntitySchemas")
	defer finishSpan()

	span.SetTag("entity_schema.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("entity_schema.page_token", pageToken)
	span.SetTag("entity_schema.type_filter", entityTypeFilter.String())

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*ListEntitySchemasResult, error) {
		// Step 1: Determine offset from page token.
		offset := 0
		if pageToken != "" {
			if parsedOffset, err := strconv.Atoi(pageToken); err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			} // Ignore invalid tokens, start from beginning.
		}

		// Step 2: Build query with optional filter.
		baseQuery := `
			SELECT id, org_id, name, description, schema_definition, create_time, update_time,
				   created_by, updated_by, version, entity_type, status, tags, resource_type
			FROM entity_schemas
		`
		var conditions []string
		var args []interface{}
		argCounter := 1
		if entityTypeFilter != entity.EntityType_ENTITY_TYPE_UNSPECIFIED {
			conditions = append(conditions, fmt.Sprintf("entity_type = $%d", argCounter))
			args = append(args, int32(entityTypeFilter))
			argCounter++
		}

		query := baseQuery
		if len(conditions) > 0 {
			query += " WHERE " + conditions[0]
		}
		query += fmt.Sprintf(" ORDER BY create_time DESC, id DESC LIMIT $%d OFFSET $%d", argCounter, argCounter+1)
		args = append(args, pageSize, offset)

		// Step 3: Execute query.
		rows, err := sessionTx.QueryContext(spanContext, query, args...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query list entity schemas")
			return nil, fmt.Errorf("failed to query list entity schemas: %w", err)
		}
		defer rows.Close()

		// Step 4: Iterate and scan results.
		var schemas []*entity.EntitySchema
		for rows.Next() {
			var currentSchema entity.EntitySchema
			var orgID int32
			var schemaDefinitionJSON string
			var entityTypeValue, statusValue int32
			var tags []string
			var createTime, updateTime time.Time

			scanErr := rows.Scan(
				&currentSchema.Id,
				&orgID,
				&currentSchema.Name,
				&currentSchema.Description,
				&schemaDefinitionJSON,
				&createTime,
				&updateTime,
				&currentSchema.CreatedBy,
				&currentSchema.UpdatedBy,
				&currentSchema.Version,
				&entityTypeValue,
				&statusValue,
				pq.Array(&tags),
				&currentSchema.ResourceType,
			)
			if scanErr != nil {
				herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan entity schema row during list")
				return nil, fmt.Errorf("failed to scan entity schema row during list: %w", scanErr)
			}

			// Step 5: Post-process scanned data.
			currentSchema.CreateTime = createTime.Format(time.RFC3339Nano)
			currentSchema.UpdateTime = updateTime.Format(time.RFC3339Nano)
			currentSchema.EntityType = entity.EntityType(entityTypeValue)
			currentSchema.Status = entity.RecordStatus(statusValue)
			currentSchema.OrgId = orgID
			currentSchema.Tags = tags
			structDef, err := workflowUtils.JSONToStruct(schemaDefinitionJSON)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to unmarshal schema definition JSON for ID '%s' in list", currentSchema.Id))
				return nil, fmt.Errorf("failed to unmarshal schema definition JSON for ID '%s' in list: %w", currentSchema.Id, err)
			}
			currentSchema.SchemaDefinition = structDef

			schemas = append(schemas, &currentSchema)
		}
		if err = rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Error iterating entity schema list results")
			return nil, fmt.Errorf("error iterating entity schema list results: %w", err)
		}

		// Step 6: Determine next page token.
		var nextPageToken string
		if len(schemas) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		// Step 7: Return results.
		return &ListEntitySchemasResult{
			Schemas:   schemas,
			PageToken: nextPageToken,
		}, nil
	})
}

// ListAllVersionsOfEntitySchema retrieves all historical versions of a schema.
func (repository *PostgresEntityRepository) ListAllVersionsOfEntitySchema(ctx context.Context, tx *sql.Tx, schemaID string) ([]*entity.EntitySchemaVersion, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.ListAllVersionsOfEntitySchema")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaID)

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) ([]*entity.EntitySchemaVersion, error) {
		// Step 1: Query versions table.
		query := `
			SELECT version, schema_snapshot, modified_by, modified_time, change_comment, org_id
			FROM entity_schema_versions
			WHERE schema_id = $1
			ORDER BY version DESC
		`
		rows, err := sessionTx.QueryContext(spanContext, query, schemaID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to query schema versions for ID '%s'", schemaID))
			return nil, fmt.Errorf("failed to query schema versions for ID '%s': %w", schemaID, err)
		}
		defer rows.Close()

		// Step 2: Iterate and build version list.
		var schemaVersions []*entity.EntitySchemaVersion
		for rows.Next() {
			var versionNumber int32
			var schemaSnapshotJSON string
			var modifiedBy, modifiedTimeStr, changeComment string
			var orgID string

			if err := rows.Scan(&versionNumber, &schemaSnapshotJSON, &modifiedBy, &modifiedTimeStr, &changeComment, &orgID); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to scan schema version row for ID '%s'", schemaID))
				return nil, fmt.Errorf("failed to scan schema version row for ID '%s': %w", schemaID, err)
			}

			// Step 3: Unmarshal snapshot.
			snapshotSchema, err := workflowUtils.JSONToEntitySchema(schemaSnapshotJSON)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to unmarshal schema snapshot JSON for version %d of ID '%s'", versionNumber, schemaID))
				return nil, fmt.Errorf("failed to unmarshal schema snapshot JSON for version %d of ID '%s': %w", versionNumber, schemaID, err)
			}

			// Step 4: Append to list.
			schemaVersions = append(schemaVersions, &entity.EntitySchemaVersion{
				SchemaId:       schemaID,
				Version:        versionNumber,
				SchemaSnapshot: snapshotSchema,
				ModifiedBy:     modifiedBy,
				ModifiedTime:   modifiedTimeStr,
				ChangeComment:  changeComment,
			})
		}
		if err = rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Error iterating schema version results for ID '%s'", schemaID))
			return nil, fmt.Errorf("error iterating schema version results for ID '%s': %w", schemaID, err)
		}

		// Step 5: Return list.
		return schemaVersions, nil
	})
}

// BulkCreateEntitySchemas creates multiple schemas using optimized bulk INSERT operations.
// This implementation uses multi-row INSERT statements to minimize database round-trips.
func (repository *PostgresEntityRepository) BulkCreateEntitySchemas(ctx context.Context, tx *sql.Tx, schemasToCreate []*entity.EntitySchema) ([]*entity.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.BulkCreateEntitySchemas")
	defer finishSpan()

	span.SetTag("entity_schema.count", fmt.Sprintf("%d", len(schemasToCreate)))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) ([]*entity.EntitySchema, error) {
		if len(schemasToCreate) == 0 {
			return []*entity.EntitySchema{}, nil
		}

		// For very large batches, chunk them to prevent memory/parameter limit issues
		const maxBatchSize = 100
		if len(schemasToCreate) > maxBatchSize {
			var allCreatedSchemas []*entity.EntitySchema
			for i := 0; i < len(schemasToCreate); i += maxBatchSize {
				end := i + maxBatchSize
				if end > len(schemasToCreate) {
					end = len(schemasToCreate)
				}
				batchSchemas, err := repository.BulkCreateEntitySchemas(spanContext, sessionTx, schemasToCreate[i:end])
				if err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create schema batch starting at index %d", i))
					return nil, fmt.Errorf("failed to create schema batch starting at index %d: %w", i, err)
				}
				allCreatedSchemas = append(allCreatedSchemas, batchSchemas...)
			}
			return allCreatedSchemas, nil
		}

		// Step 1: Prepare schemas and collect data for bulk operations
		orgID := cmncontext.GetOrgId(spanContext)
		currentTime := time.Now()

		var schemaIDs []string
		var schemaValues []interface{}
		var versionValues []interface{}
		schemaPlaceholders := make([]string, 0, len(schemasToCreate))
		versionPlaceholders := make([]string, 0, len(schemasToCreate))

		for i, schemaToCreate := range schemasToCreate {
			// Generate ID if not provided
			if schemaToCreate.Id == "" {
				schemaToCreate.Id = uuid.New().String()
			}

			// Set timestamps and initial version
			schemaToCreate.CreateTime = currentTime.Format(time.RFC3339Nano)
			schemaToCreate.UpdateTime = currentTime.Format(time.RFC3339Nano)
			schemaToCreate.CreatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
			schemaToCreate.UpdatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
			if schemaToCreate.Version == 0 {
				schemaToCreate.Version = 1
			}

			// Convert protobuf Struct data to JSON
			schemaDefinitionJSON, err := workflowUtils.StructToJSON(schemaToCreate.SchemaDefinition)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to marshal schema definition to JSON for schema %d", i))
				return nil, fmt.Errorf("failed to marshal schema definition to JSON for schema %d: %w", i, err)
			}

			schemaIDs = append(schemaIDs, schemaToCreate.Id)

			// Build schema INSERT values
			baseIndex := i * 13
			schemaPlaceholders = append(schemaPlaceholders, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
				baseIndex+1, baseIndex+2, baseIndex+3, baseIndex+4, baseIndex+5, baseIndex+6, baseIndex+7,
				baseIndex+8, baseIndex+9, baseIndex+10, baseIndex+11, baseIndex+12, baseIndex+13))

			schemaValues = append(schemaValues,
				schemaToCreate.Id,
				orgID,
				schemaToCreate.Name,
				schemaToCreate.Description,
				schemaDefinitionJSON,
				currentTime,
				currentTime,
				schemaToCreate.CreatedBy,
				schemaToCreate.UpdatedBy,
				schemaToCreate.Version,
				int32(schemaToCreate.EntityType),
				int32(schemaToCreate.Status),
				pq.Array(schemaToCreate.Tags),
			)

			// Build version history INSERT values
			schemaVersionSnapshotJSON, err := workflowUtils.EntitySchemaToJSON(schemaToCreate)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to marshal schema snapshot for version history %d", i))
				return nil, fmt.Errorf("failed to marshal schema snapshot for version history %d: %w", i, err)
			}

			versionBaseIndex := i * 7
			versionPlaceholders = append(versionPlaceholders, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d)",
				versionBaseIndex+1, versionBaseIndex+2, versionBaseIndex+3, versionBaseIndex+4, versionBaseIndex+5, versionBaseIndex+6, versionBaseIndex+7))

			versionValues = append(versionValues,
				schemaToCreate.Id,
				schemaToCreate.Version,
				schemaVersionSnapshotJSON,
				schemaToCreate.CreatedBy,
				currentTime.Format(time.RFC3339Nano),
				"Created",
				orgID,
			)
		}

		// Step 2: Execute bulk INSERT for entity_schemas table
		var schemaQueryBuilder strings.Builder
		schemaQueryBuilder.WriteString(`
			INSERT INTO entity_schemas (
				id, org_id, name, description, schema_definition, create_time, update_time,
				created_by, updated_by, version, entity_type, status, tags
			) VALUES `)
		schemaQueryBuilder.WriteString(strings.Join(schemaPlaceholders, ", "))
		schemaInsertQuery := schemaQueryBuilder.String()

		_, err := sessionTx.ExecContext(spanContext, schemaInsertQuery, schemaValues...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk insert entity schemas")
			return nil, fmt.Errorf("failed to bulk insert entity schemas: %w", err)
		}

		// Step 3: Execute bulk INSERT for entity_schema_versions table
		var schemaVersionQueryBuilder strings.Builder
		schemaVersionQueryBuilder.WriteString(`
			INSERT INTO entity_schema_versions (
				schema_id, version, schema_snapshot, modified_by, modified_time, change_comment, org_id
			) VALUES `)
		schemaVersionQueryBuilder.WriteString(strings.Join(versionPlaceholders, ", "))
		versionInsertQuery := schemaVersionQueryBuilder.String()

		_, err = sessionTx.ExecContext(spanContext, versionInsertQuery, versionValues...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk insert entity schema versions")
			return nil, fmt.Errorf("failed to bulk insert entity schema versions: %w", err)
		}

		// Step 4: Batch retrieve the created schemas using optimized batch query
		return repository.batchGetLatestEntitySchemas(spanContext, sessionTx, schemaIDs)
	})
}

// BulkUpdateEntitySchemas updates multiple schemas using optimized batch operations where possible.
// Note: Due to optimistic locking requirements, this still requires individual updates,
// but uses batch retrieval for the final result to minimize database round-trips.
func (repository *PostgresEntityRepository) BulkUpdateEntitySchemas(ctx context.Context, tx *sql.Tx, schemasToUpdate []*entity.EntitySchema) ([]*entity.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.BulkUpdateEntitySchemas")
	defer finishSpan()

	span.SetTag("entity_schema.count", fmt.Sprintf("%d", len(schemasToUpdate)))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) ([]*entity.EntitySchema, error) {
		if len(schemasToUpdate) == 0 {
			return []*entity.EntitySchema{}, nil
		}

		// For very large batches, chunk them to prevent memory/parameter limit issues
		const maxBatchSize = 50 // Smaller batch size for updates due to complexity
		if len(schemasToUpdate) > maxBatchSize {
			var allUpdatedSchemas []*entity.EntitySchema
			for i := 0; i < len(schemasToUpdate); i += maxBatchSize {
				end := i + maxBatchSize
				if end > len(schemasToUpdate) {
					end = len(schemasToUpdate)
				}
				batchSchemas, err := repository.BulkUpdateEntitySchemas(spanContext, sessionTx, schemasToUpdate[i:end])
				if err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update schema batch starting at index %d", i))
					return nil, fmt.Errorf("failed to update schema batch starting at index %d: %w", i, err)
				}
				allUpdatedSchemas = append(allUpdatedSchemas, batchSchemas...)
			}
			return allUpdatedSchemas, nil
		}

		// Step 1: Collect schema IDs for batch retrieval at the end
		var schemaIDs []string

		// Step 2: Perform individual updates (required due to optimistic locking)
		// Note: This is still N operations, but we optimize the final retrieval
		for i, schemaToUpdate := range schemasToUpdate {
			// Get existing schema for optimistic lock version
			existingSchema, err := repository.GetLatestEntitySchema(spanContext, sessionTx, schemaToUpdate.Id)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get existing schema for update at index %d (ID: %s)", i, schemaToUpdate.Id))
				return nil, fmt.Errorf("failed to get existing schema for update at index %d (ID: %s): %w", i, schemaToUpdate.Id, err)
			}

			// Prepare updated schema record
			updatedVersion := existingSchema.Version + 1
			schemaToUpdate.Version = updatedVersion
			currentTime := time.Now()
			schemaToUpdate.UpdateTime = currentTime.Format(time.RFC3339Nano)
			schemaToUpdate.CreateTime = existingSchema.CreateTime
			schemaToUpdate.CreatedBy = existingSchema.CreatedBy
			schemaToUpdate.UpdatedBy, _ = workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)

			schemaDefinitionJSON, err := workflowUtils.StructToJSON(schemaToUpdate.SchemaDefinition)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Failed to marshal updated schema definition to JSON at index %d", i))
				return nil, fmt.Errorf("failed to marshal updated schema definition to JSON at index %d: %w", i, err)
			}

			// Execute UPDATE with optimistic lock
			updateQuery := `
				UPDATE entity_schemas
				SET name = $1, description = $2, schema_definition = $3, update_time = $4,
					updated_by = $5, version = $6, entity_type = $7, status = $8,
					tags = $9
				WHERE id = $10 AND version = $11
			`
			result, err := sessionTx.ExecContext(spanContext, updateQuery,
				schemaToUpdate.Name, schemaToUpdate.Description, schemaDefinitionJSON, currentTime,
				schemaToUpdate.UpdatedBy, schemaToUpdate.Version, int32(schemaToUpdate.EntityType),
				int32(schemaToUpdate.Status), pq.Array(schemaToUpdate.Tags),
				schemaToUpdate.Id, existingSchema.Version,
			)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to execute update schema statement at index %d", i))
				return nil, fmt.Errorf("failed to execute update schema statement at index %d: %w", i, err)
			}

			// Check optimistic lock result
			rowsAffected, err := result.RowsAffected()
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to check rows affected after schema update at index %d", i))
				return nil, fmt.Errorf("failed to check rows affected after schema update at index %d: %w", i, err)
			}
			if rowsAffected == 0 {
				err := fmt.Errorf("update conflict: entity schema with ID '%s' was modified concurrently (expected version %d)", schemaToUpdate.Id, existingSchema.Version)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
				return nil, err
			}

			// Insert new version history record
			orgID := cmncontext.GetOrgId(spanContext)
			schemaVersionSnapshotJSON, err := workflowUtils.EntitySchemaToJSON(schemaToUpdate)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to marshal schema snapshot for update version history at index %d", i))
				return nil, fmt.Errorf("failed to marshal schema snapshot for update version history at index %d: %w", i, err)
			}
			insertVersionQuery := `
				INSERT INTO entity_schema_versions (schema_id, version, schema_snapshot, modified_by, modified_time, change_comment, org_id)
				VALUES ($1, $2, $3, $4, $5, 'Updated', $6)
			`
			_, err = sessionTx.ExecContext(spanContext, insertVersionQuery,
				schemaToUpdate.Id, schemaToUpdate.Version, schemaVersionSnapshotJSON,
				schemaToUpdate.UpdatedBy, schemaToUpdate.UpdateTime, orgID,
			)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to insert schema version history after update at index %d", i))
				return nil, fmt.Errorf("failed to insert schema version history after update at index %d: %w", i, err)
			}

			schemaIDs = append(schemaIDs, schemaToUpdate.Id)
		}

		// Step 3: Batch retrieve all updated schemas in a single query
		return repository.batchGetLatestEntitySchemas(spanContext, sessionTx, schemaIDs)
	})
}

// BulkDeleteEntitySchemas deletes multiple schemas using optimized batch DELETE operations.
// This implementation uses WHERE id = ANY($1) pattern to minimize database round-trips.
func (repository *PostgresEntityRepository) BulkDeleteEntitySchemas(ctx context.Context, tx *sql.Tx, schemaIDsToDelete []string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.BulkDeleteEntitySchemas")
	defer finishSpan()

	span.SetTag("entity_schema.count", fmt.Sprintf("%d", len(schemaIDsToDelete)))

	return database.WithSessionErr(repository.database, spanContext, tx, func(sessionTx *sql.Tx) error {
		if len(schemaIDsToDelete) == 0 {
			return nil
		}

		// For very large batches, chunk them to prevent parameter limit issues
		const maxBatchSize = 1000
		if len(schemaIDsToDelete) > maxBatchSize {
			for i := 0; i < len(schemaIDsToDelete); i += maxBatchSize {
				end := i + maxBatchSize
				if end > len(schemaIDsToDelete) {
					end = len(schemaIDsToDelete)
				}
				err := repository.BulkDeleteEntitySchemas(spanContext, sessionTx, schemaIDsToDelete[i:end])
				if err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete schema batch starting at index %d", i))
					return fmt.Errorf("failed to delete schema batch starting at index %d: %w", i, err)
				}
			}
			return nil
		}

		// Step 1: Delete all schema version history records first (safer order)
		// Use batch DELETE with ANY() for efficient bulk deletion
		_, err := sessionTx.ExecContext(spanContext,
			"DELETE FROM entity_schema_versions WHERE schema_id = ANY($1)",
			pq.Array(schemaIDsToDelete))
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk delete entity schema versions")
			return fmt.Errorf("failed to bulk delete entity schema versions: %w", err)
		}

		// Step 2: Delete the main schema records
		// Use batch DELETE with ANY() for efficient bulk deletion
		result, err := sessionTx.ExecContext(spanContext,
			"DELETE FROM entity_schemas WHERE id = ANY($1)",
			pq.Array(schemaIDsToDelete))
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk delete entity schemas")
			return fmt.Errorf("failed to bulk delete entity schemas: %w", err)
		}

		// Step 3: Verify that at least some schemas were deleted
		// Note: We don't require all schemas to exist, as some might have been deleted already
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to check rows affected after bulk schema deletion")
			return fmt.Errorf("failed to check rows affected after bulk schema deletion: %w", err)
		}

		// If no rows were affected, it means none of the specified schemas existed
		if rowsAffected == 0 {
			err := fmt.Errorf("no entity schemas found for deletion from the provided %d schema IDs", len(schemaIDsToDelete))
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return err
		}

		return nil // Bulk deletion successful
	})
}

// RestoreEntitySchemaVersion reverts a schema to a previous version's snapshot, creating a new version record.
func (repository *PostgresEntityRepository) RestoreEntitySchemaVersion(ctx context.Context, tx *sql.Tx, schemaID string, targetVersion int32) (*entity.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.RestoreEntitySchemaVersion")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaID)
	span.SetTag("entity_schema.target_version", fmt.Sprintf("%d", targetVersion))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (*entity.EntitySchema, error) {
		// Step 1: List all versions to find target and latest version number.
		allVersions, err := repository.ListAllVersionsOfEntitySchema(spanContext, sessionTx, schemaID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to list schema versions for restore (ID: %s)", schemaID))
			return nil, fmt.Errorf("failed to list schema versions for restore (ID: %s): %w", schemaID, err)
		}
		if len(allVersions) == 0 {
			err := fmt.Errorf("cannot restore: no versions found for schema ID '%s'", schemaID)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return nil, err
		}

		// Step 2: Find the target snapshot.
		var targetVersionSnapshot *entity.EntitySchema
		for _, historicalVersion := range allVersions {
			if historicalVersion.Version == targetVersion {
				targetVersionSnapshot = proto.Clone(historicalVersion.SchemaSnapshot).(*entity.EntitySchema)
				break
			}
		}
		if targetVersionSnapshot == nil {
			err := fmt.Errorf("specified schema version %d not found for ID '%s'", targetVersion, schemaID)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return nil, err
		}

		// Step 3: Prepare the restored schema record.
		restoredSchema := targetVersionSnapshot // Already cloned.
		restoredSchema.Id = schemaID
		latestVersion := allVersions[0].Version
		nextVersion := latestVersion + 1
		restoredSchema.Version = nextVersion
		restoreTime := time.Now()
		restoredSchema.UpdateTime = restoreTime.Format(time.RFC3339Nano)
		restoredBy, _ := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx) // TODO: handle the error if necessary
		restoredSchemaDefinitionJSON, err := workflowUtils.StructToJSON(restoredSchema.SchemaDefinition)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to marshal restored schema definition to JSON")
			return nil, fmt.Errorf("failed to marshal restored schema definition to JSON: %w", err)
		}

		// Step 4: Update the main 'entity_schemas' table with optimistic locking.
		// First, get the current version for optimistic locking
		existingSchema, err := repository.GetLatestEntitySchema(spanContext, sessionTx, schemaID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get current schema state for restore (ID: %s)", schemaID))
			return nil, fmt.Errorf("failed to get current schema state for restore (ID: %s): %w", schemaID, err)
		}

		updateQuery := `
			UPDATE entity_schemas
			SET name = $1, description = $2, schema_definition = $3, update_time = $4,
				updated_by = $5, version = $6, entity_type = $7, status = $8,
				tags = $9
			WHERE id = $10 AND version = $11 -- Optimistic lock condition
		`
		result, err := sessionTx.ExecContext(spanContext, updateQuery,
			restoredSchema.Name, restoredSchema.Description, restoredSchemaDefinitionJSON, restoreTime,
			restoredBy,
			restoredSchema.Version, int32(restoredSchema.EntityType), int32(restoredSchema.Status),
			pq.Array(restoredSchema.Tags),
			schemaID,
			existingSchema.Version, // Add current version for optimistic locking
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update schema table during restore")
			return nil, fmt.Errorf("failed to update schema table during restore: %w", err)
		}

		// Check if the optimistic lock condition was met
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to check rows affected after schema restore")
			return nil, fmt.Errorf("failed to check rows affected after schema restore: %w", err)
		}
		if rowsAffected == 0 {
			err := fmt.Errorf("restore conflict: schema with ID '%s' was modified concurrently (expected version %d)",
				schemaID, existingSchema.Version)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
			return nil, err
		}

		// Step 5: Insert new version history record for the restore.
		orgID := cmncontext.GetOrgId(spanContext)
		restoredSchemaSnapshotJSON, err := workflowUtils.EntitySchemaToJSON(restoredSchema)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to marshal restored schema snapshot for version history")
			return nil, fmt.Errorf("failed to marshal restored schema snapshot for version history: %w", err)
		}
		insertVersionQuery := `
			INSERT INTO entity_schema_versions (schema_id, version, schema_snapshot, modified_by, modified_time, change_comment, org_id)
			VALUES ($1, $2, $3, $4, $5, $6, $7)
		`
		changeComment := fmt.Sprintf("Restored to version %d", targetVersion)
		_, err = sessionTx.ExecContext(spanContext, insertVersionQuery,
			schemaID, restoredSchema.Version, restoredSchemaSnapshotJSON,
			restoredBy,
			restoredSchema.UpdateTime, changeComment, orgID,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert restore schema version history")
			return nil, fmt.Errorf("failed to insert restore schema version history: %w", err)
		}

		// Step 6: Return the newly restored schema state.
		return repository.GetLatestEntitySchema(spanContext, sessionTx, schemaID)
	})
}

// DiffEntitySchemaVersions calculates the textual difference between two historical versions of an entity schema.
func (repository *PostgresEntityRepository) DiffEntitySchemaVersions(ctx context.Context, tx *sql.Tx, schemaID string, version1 int32, version2 int32) (string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.DiffEntitySchemaVersions")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaID)
	span.SetTag("entity_schema.version1", fmt.Sprintf("%d", version1))
	span.SetTag("entity_schema.version2", fmt.Sprintf("%d", version2))

	return database.WithSession(repository.database, spanContext, tx, func(sessionTx *sql.Tx) (string, error) {
		// Retrieve the two versions of the entity schema using the transaction from WithSession.
		schema1, err := repository.GetEntitySchemaByVersion(spanContext, sessionTx, schemaID, version1)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to retrieve schema version %d", version1))
			return "", fmt.Errorf("failed to retrieve schema version %d: %w", version1, err)
		}
		schema2, err := repository.GetEntitySchemaByVersion(spanContext, sessionTx, schemaID, version2)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to retrieve schema version %d", version2))
			return "", fmt.Errorf("failed to retrieve schema version %d: %w", version2, err)
		}

		// Marshal the schema snapshots into pretty-printed JSON.
		// Note: Using the full schema object from the snapshot for diffing.
		json1, err := json.MarshalIndent(schema1, "", "  ")
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to marshal schema version %d", version1))
			return "", fmt.Errorf("failed to marshal schema version %d: %w", version1, err)
		}
		json2, err := json.MarshalIndent(schema2, "", "  ")
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to marshal schema version %d", version2))
			return "", fmt.Errorf("failed to marshal schema version %d: %w", version2, err)
		}

		// Use diffmatchpatch to generate a diff.
		dmp := diffmatchpatch.New()
		diffs := dmp.DiffMain(string(json1), string(json2), false)
		diffText := dmp.DiffPrettyText(diffs)

		return diffText, nil
	})
}

// CheckEntitySchemaPermissions verifies user permissions for actions on schemas.
// NOTE: Placeholder implementation, always returns true.
func (repository *PostgresEntityRepository) CheckEntitySchemaPermissions(ctx context.Context, tx *sql.Tx, schemaID string, userID string, action string) (bool, error) {
	_, span, finishSpan := herosentry.StartSpan(ctx, "EntityRepository.CheckEntitySchemaPermissions")
	defer finishSpan()

	span.SetTag("entity_schema.id", schemaID)
	span.SetTag("user.id", userID)
	span.SetTag("permission.action", action)

	// TODO: Implement actual permission checking logic for schemas.
	fmt.Printf("Placeholder: Checking permission for user '%s' action '%s' on schema '%s'\n", userID, action, schemaID)
	return true, nil // Stub: Always allow.
}

// SearchEntities executes a comprehensive multi-criteria search against the entities dataset.
// This function leverages optimized database indexes (GIN, JSONB, BRIN, trigram) for efficient querying
// across both top-level entity fields and nested reference data.
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// SEARCH CAPABILITIES
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// 🔍 TEXT SEARCH (ILIKE pattern matching using GIN trigram indexes):
//   - id                          — Entity ID partial matching (entities.id)
//   - data                        — JSONB content pattern search (entities.data)
//   - tags                        — Entity tags array search (entities.tags)
//   - reference_display_name      — Entity reference display names (entity_references.display_name)
//   - reference_relation_type     — Entity reference relation types (entity_references.relation_type)
//
// 🎯 EXACT FILTERS (using btree indexes):
//   - status                      — Entity status enum values (RecordStatus)
//   - entity_types                — Entity type enum values (EntityType)
//   - created_by                  — Asset IDs of entity creators
//   - updated_by                  — Asset IDs of entity updaters
//   - schema_ids                  — Associated schema IDs array
//   - org_ids                     — Organization IDs array (in addition to context-based org filtering)
//   - tags                        — Entity tags array exact match (uses @> operator)
//
// 🔗 REFERENCE FILTERS (using entity_references table):
//   - reference_ids               — Referenced entity IDs within entity_references
//   - reference_types             — Reference types (PERSON, VEHICLE, PROPERTY, etc.)
//
// 📅 DATE RANGE FILTERS (using BRIN indexes for time-series data):
//   - create_time                 — Entity creation timestamp range
//   - update_time                 — Last modification timestamp range
//
// 📄 ADVANCED QUERY OPTIONS:
//   - query                       — Global search term across all/specified fields
//   - search_fields               — Limit global search to specific fields only
//   - field_queries               — Target different search terms to specific fields
//   - order_by                    — Sort by CREATED_AT, UPDATED_AT, STATUS, or RELEVANCE
//   - ascending                   — Sort direction (default: DESC)
//   - page_size                   — Results per page (max: 1000, default: 50)
//   - page_token                  — Pagination offset token
//
// 🔦 RESULT HIGHLIGHTING:
//   - Generates contextual text fragments showing matched search terms
//   - Up to 60-character context windows around matches
//   - Supports highlighting across all searchable text fields
//   - Deduplicates fragments to prevent redundancy
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// DATABASE SCHEMA INTEGRATION
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// Tables used:
//   - entities                    — Main entity data (id, schema_id, data, status, timestamps, etc.)
//   - entity_references           — Entity reference relationships (JOIN when reference search needed)
//
// Key indexes leveraged:
//   - idx_entities_org_id         — Organization filtering
//   - idx_entities_status         — Status filtering
//   - idx_entities_entity_type    — Entity type filtering
//   - idx_entities_created_by     — Creator filtering
//   - idx_entities_updated_by     — Updater filtering
//   - idx_entities_schema_id      — Schema ID filtering
//   - idx_entities_id_trgm        — Entity ID trigram search (GIN)
//   - gin_entities_data           — Entity data JSONB pattern search (GIN)
//   - gin_entities_tags           — Entity tags array search (GIN)
//   - brin_entities_create_time   — Creation time range queries (BRIN)
//   - brin_entities_update_time   — Update time range queries (BRIN)
//   - idx_entity_references_*     — Reference table indexes for JOIN operations
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// EXECUTION WORKFLOW
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// Step 1: Parameter validation and normalization (page size, offset parsing)
// Step 2: Organization context extraction and validation
// Step 3: Build base query structure (SELECT, FROM, potential JOINs with entity_references)
// Step 4: Apply entity-level filters (status, types, creators, dates, tags)
// Step 5: Apply reference-level filters (requires JOIN to entity_references)
// Step 6: Apply text search conditions (global query + field-specific queries)
// Step 7: Construct ORDER BY clause based on sort preferences
// Step 8: Execute main query to retrieve entities with pagination
// Step 9: Execute count query for pagination metadata
// Step 10: Generate search result highlights for matched terms
// Step 11: Return formatted response with entities, pagination, and highlights
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// SAMPLE QUERY STRUCTURE
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
//	SELECT DISTINCT e.id, e.org_id, e.schema_id, e.schema_version, e.data,
//	       e.create_time, e.update_time, e.entity_type, e.created_by, e.updated_by,
//	       e.version, e.status, e.tags, e.resource_type
//	FROM entities e
//	LEFT JOIN entity_references er ON e.id = er.entity_id
//	WHERE e.org_id = $1                                                          -- Organization filter
//	  AND e.status = ANY($2)                                                     -- Status filter
//	  AND e.entity_type = ANY($3)                                               -- Entity type filter
//	  AND e.create_time >= $4 AND e.create_time <= $5                          -- Date range filter
//	  AND e.tags @> $6                                                          -- Tags contains filter
//	  AND (e.id ILIKE $7 OR e.data::text ILIKE $7 OR                          -- Global text search
//	       array_to_string(e.tags, ' ') ILIKE $7 OR
//	       er.display_name ILIKE $7 OR er.relation_type ILIKE $7)
//	  AND e.data::text ILIKE $8                                                 -- Field-specific search
//	  AND er.ref_id = ANY($9)                                                   -- Reference ID filter
//	ORDER BY e.create_time DESC, e.id ASC                                       -- Consistent ordering
//	LIMIT $10 OFFSET $11                                                        -- Pagination
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// SUPPORTED SEARCH FIELDS
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// The following fields support partial/fuzzy matching via ILIKE and trigram indexes:
//   - "id"                        — Entity identifier partial matching
//   - "data"                      — JSONB content pattern search
//   - "tags"                      — Entity tags array search
//   - "reference_display_name"    — Reference display name search (requires JOIN)
//   - "reference_relation_type"   — Reference relation type search (requires JOIN)
//
// Fields that require exact match (not included in text search):
//   - status, entity_types, org_ids, created_by, updated_by, schema_ids, reference_ids, reference_types
//   - create_time, update_time (use range filters instead)
func (repository *PostgresEntityRepository) SearchEntities(requestContext context.Context, databaseTransaction *sql.Tx, searchRequest *entity.SearchEntitiesRequest) (*entity.SearchEntitiesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(requestContext, "EntityRepository.SearchEntities")
	defer finishSpan()

	span.SetTag("search.query", searchRequest.Query)
	span.SetTag("search.page_size", fmt.Sprintf("%d", searchRequest.PageSize))
	if searchRequest.PageToken != "" {
		span.SetTag("search.page_token", searchRequest.PageToken)
	}

	return database.WithSession(repository.database, spanContext, databaseTransaction, func(sessionTransaction *sql.Tx) (*entity.SearchEntitiesResponse, error) {
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 1: REQUEST PARAMETER VALIDATION & NORMALIZATION
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Validate and set page size (min: 1, max: 1000, default: 50)
		resultPageSize := int(searchRequest.PageSize)
		if resultPageSize <= 0 {
			resultPageSize = 50 // Default page size for optimal performance
		}
		if resultPageSize > 1000 {
			resultPageSize = 1000 // Maximum page size to prevent memory issues
		}

		// Parse pagination offset from page token (numeric offset as string)
		paginationOffset := 0
		if searchRequest.PageToken != "" {
			if parsedOffset, parseError := strconv.Atoi(searchRequest.PageToken); parseError == nil && parsedOffset >= 0 {
				paginationOffset = parsedOffset
			}
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 2: ORGANIZATION CONTEXT EXTRACTION & VALIDATION
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Get organization ID from context (required for all entity operations)
		organizationID := cmncontext.GetOrgId(spanContext)

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 3: BUILD BASE QUERY STRUCTURE & DETERMINE JOIN REQUIREMENTS
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Build the search query with conditional JOIN to entity_references
		var searchTermsMap map[string][]string
		baseQueryString, whereConditionsList, queryArguments, _ := repository.buildEntitySearchQuery(searchRequest, organizationID, &searchTermsMap) //nolint:unparam

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 4: CONSTRUCT ORDER BY CLAUSE BASED ON SORT PREFERENCE
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Add ORDER BY clause with secondary sort by ID for consistency
		orderByClause := repository.buildEntityOrderByClause(searchRequest.OrderBy, searchRequest.Ascending)

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 5: ASSEMBLE FINAL QUERY WITH PAGINATION
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Build final query with WHERE conditions, ORDER BY, and pagination
		finalQueryString := baseQueryString
		if len(whereConditionsList) > 0 {
			finalQueryString += " WHERE " + strings.Join(whereConditionsList, " AND ")
		}
		finalQueryString += orderByClause
		finalQueryString += fmt.Sprintf(" LIMIT $%d OFFSET $%d", len(queryArguments)+1, len(queryArguments)+2)
		queryArguments = append(queryArguments, resultPageSize, paginationOffset)

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 6: EXECUTE MAIN QUERY TO RETRIEVE ENTITIES
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Execute the main query to get paginated entity results
		queryResultRows, queryExecutionError := sessionTransaction.QueryContext(spanContext, finalQueryString, queryArguments...)
		if queryExecutionError != nil {
			herosentry.CaptureException(spanContext, queryExecutionError, herosentry.ErrorTypeDatabase, "Failed to execute entity search query")
			return nil, fmt.Errorf("failed to execute entity search query: %w", queryExecutionError)
		}
		defer queryResultRows.Close()

		// Scan results into entity objects
		var foundEntities []*entity.Entity
		for queryResultRows.Next() {
			entityRecord, scanError := repository.scanEntitySearchRow(queryResultRows)
			if scanError != nil {
				herosentry.CaptureException(spanContext, scanError, herosentry.ErrorTypeDatabase, "Failed to scan entity search result")
				return nil, fmt.Errorf("failed to scan entity search result: %w", scanError)
			}
			foundEntities = append(foundEntities, entityRecord)
		}
		if rowIterationError := queryResultRows.Err(); rowIterationError != nil {
			herosentry.CaptureException(spanContext, rowIterationError, herosentry.ErrorTypeDatabase, "Error iterating entity search results")
			return nil, fmt.Errorf("error iterating entity search results: %w", rowIterationError)
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 7: EXECUTE COUNT QUERY FOR PAGINATION METADATA
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Get total count (separate query without pagination parameters)
		totalResultCount, countQueryError := repository.getEntitySearchTotalCount(spanContext, sessionTransaction, baseQueryString, whereConditionsList, queryArguments[:len(queryArguments)-2]) // Remove pagination args
		if countQueryError != nil {
			herosentry.CaptureException(spanContext, countQueryError, herosentry.ErrorTypeDatabase, "Failed to get total search count")
			return nil, fmt.Errorf("failed to get total search count: %w", countQueryError)
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 8: GENERATE SEARCH RESULT HIGHLIGHTS
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Generate contextual highlight fragments for matched search terms
		searchHighlights := repository.generateEntitySearchHighlights(foundEntities, searchTermsMap)

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 9: DETERMINE NEXT PAGE TOKEN & RETURN RESPONSE
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Calculate next page token for pagination
		var nextPageToken string
		if len(foundEntities) == resultPageSize {
			nextPageToken = strconv.Itoa(paginationOffset + resultPageSize)
		}

		return &entity.SearchEntitiesResponse{
			Entities:      foundEntities,
			NextPageToken: nextPageToken,
			Highlights:    searchHighlights,
			TotalResults:  repository.safeIntToInt32(totalResultCount),
		}, nil
	})
}

// buildEntitySearchQuery constructs the base SQL query with WHERE conditions and arguments.
// This method handles the complex logic of building dynamic SQL based on the search request parameters.
//
// Parameters:
//   - searchRequest: The search request containing all filter and search criteria
//   - organizationID: Organization ID for access control filtering
//   - searchTermsMap: Output parameter to track search terms for highlighting
//
// Returns:
//   - baseQueryString: The base SQL SELECT statement with optional JOINs
//   - whereConditionsList: Array of WHERE clause conditions to be joined with AND
//   - queryArguments: Parameterized query arguments for SQL injection prevention
//   - shouldJoinReferences: Boolean indicating if entity_references table JOIN is needed
//
// The method intelligently determines when to JOIN with entity_references based on:
//   - Reference ID/type filters in the request
//   - Global search targeting reference fields
//   - Field-specific queries targeting reference fields
func (repository *PostgresEntityRepository) buildEntitySearchQuery(
	searchRequest *entity.SearchEntitiesRequest,
	organizationID int32,
	searchTermsMap *map[string][]string,
) (baseQueryString string, whereConditionsList []string, queryArguments []interface{}, shouldJoinReferences bool) {

	// Allowed fields for partial-match (ILIKE) search in SearchEntities.
	// Only fields that are matched with ILIKE (including trigram search) should be here.
	// Fields that require exact match (e.g., enums, ids, etc.) should NOT be in this list.
	var allowedSearchFieldsMap = map[string]bool{
		FieldID:                    true, // entities.id ILIKE (trigram index)
		FieldData:                  true, // entities.data::text ILIKE (pattern search)
		FieldTags:                  true, // array_to_string(entities.tags, ' ') ILIKE
		FieldReferenceDisplayName:  true, // entity_references.display_name ILIKE (trigram index)
		FieldReferenceRelationType: true, // entity_references.relation_type ILIKE (trigram index)
	}

	// Track search terms for highlighting functionality
	*searchTermsMap = make(map[string][]string)

	// ═══════════════════════════════════════════════════════════════════════════════════════════
	// DETERMINE JOIN REQUIREMENTS & BUILD BASE QUERY
	// ═══════════════════════════════════════════════════════════════════════════════════════════
	// Decide if we need to join with entity_references table based on search criteria
	shouldJoinReferences = len(searchRequest.ReferenceIds) > 0 || len(searchRequest.ReferenceTypes) > 0 ||
		repository.needsReferenceFieldSearch(searchRequest)

	// Build base query with conditional JOIN to entity_references
	if shouldJoinReferences {
		// Use DISTINCT to avoid duplicate rows when JOINing with entity_references
		baseQueryString = `
			SELECT DISTINCT e.id, e.org_id, e.schema_id, e.schema_version, e.data, 
				   e.create_time, e.update_time, e.entity_type, e.created_by, e.updated_by, 
				   e.version, e.status, e.tags, e.resource_type
			FROM entities e
			LEFT JOIN entity_references er ON e.id = er.entity_id`
	} else {
		// Simple query without JOIN when reference search is not needed
		baseQueryString = `
			SELECT e.id, e.org_id, e.schema_id, e.schema_version, e.data, 
				   e.create_time, e.update_time, e.entity_type, e.created_by, e.updated_by, 
				   e.version, e.status, e.tags, e.resource_type
			FROM entities e`
	}

	argumentCounter := 1

	// ═══════════════════════════════════════════════════════════════════════════════════════════
	// APPLY ENTITY-LEVEL FILTERS (entities table columns)
	// ═══════════════════════════════════════════════════════════════════════════════════════════
	// These filters operate directly on the entities table and use btree/BRIN indexes

	// 🏢 Organization filter (always required for access control)
	whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.org_id = $%d", argumentCounter))
	queryArguments = append(queryArguments, organizationID)
	argumentCounter++

	// 🎯 Status filters: Filter by entity status enum values (uses idx_entities_status)
	if len(searchRequest.Status) > 0 {
		statusValuesList := make([]interface{}, len(searchRequest.Status))
		for statusIndex, statusValue := range searchRequest.Status {
			statusValuesList[statusIndex] = int32(statusValue)
		}
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.status = ANY($%d)", argumentCounter))
		queryArguments = append(queryArguments, pq.Array(statusValuesList))
		argumentCounter++
	} else if !searchRequest.IncludeArchivedEntities {
		// Default behavior: exclude archived entities unless explicitly requested via includeArchivedEntities flag
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.status != $%d", argumentCounter))
		queryArguments = append(queryArguments, int32(entity.RecordStatus_RECORD_STATUS_ARCHIVED))
		argumentCounter++
	}
	// If includeArchivedEntities is true, no status filter is applied (includes all statuses)

	// 🏷️ Entity type filters: Filter by EntityType enum values (uses idx_entities_entity_type)
	if len(searchRequest.EntityTypes) > 0 {
		entityTypeValuesList := make([]interface{}, len(searchRequest.EntityTypes))
		for typeIndex, entityTypeValue := range searchRequest.EntityTypes {
			entityTypeValuesList[typeIndex] = int32(entityTypeValue)
		}
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.entity_type = ANY($%d)", argumentCounter))
		queryArguments = append(queryArguments, pq.Array(entityTypeValuesList))
		argumentCounter++
	}

	// 👤 Created by filters: Filter by creator asset IDs (uses idx_entities_created_by)
	if len(searchRequest.CreatedBy) > 0 {
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.created_by = ANY($%d)", argumentCounter))
		queryArguments = append(queryArguments, pq.Array(searchRequest.CreatedBy))
		argumentCounter++
	}

	// ✏️ Updated by filters: Filter by updater asset IDs (uses idx_entities_updated_by)
	if len(searchRequest.UpdatedBy) > 0 {
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.updated_by = ANY($%d)", argumentCounter))
		queryArguments = append(queryArguments, pq.Array(searchRequest.UpdatedBy))
		argumentCounter++
	}

	// 📋 Schema ID filters: Filter by entity schema IDs (uses idx_entities_schema_id)
	if len(searchRequest.SchemaIds) > 0 {
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.schema_id = ANY($%d)", argumentCounter))
		queryArguments = append(queryArguments, pq.Array(searchRequest.SchemaIds))
		argumentCounter++
	}

	// 🏢 Additional org ID filters (in addition to the required context-based org filter)
	if len(searchRequest.OrgIds) > 0 {
		organizationValuesList := make([]interface{}, len(searchRequest.OrgIds))
		for orgIndex, requestOrgID := range searchRequest.OrgIds {
			organizationValuesList[orgIndex] = requestOrgID
		}
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.org_id = ANY($%d)", argumentCounter))
		queryArguments = append(queryArguments, pq.Array(organizationValuesList))
		argumentCounter++
	}

	// 🏷️ Tags filters: Filter by exact tag matches using array contains operator (uses gin_entities_tags)
	if len(searchRequest.Tags) > 0 {
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.tags @> $%d", argumentCounter))
		queryArguments = append(queryArguments, pq.Array(searchRequest.Tags))
		argumentCounter++
	}

	// ═══════════════════════════════════════════════════════════════════════════════════════════
	// APPLY REFERENCE-LEVEL FILTERS (entity_references table columns)
	// ═══════════════════════════════════════════════════════════════════════════════════════════
	// These filters require JOIN with entity_references table

	// 🔗 Reference ID filters: Filter by referenced entity IDs
	if len(searchRequest.ReferenceIds) > 0 {
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("er.ref_id = ANY($%d)", argumentCounter))
		queryArguments = append(queryArguments, pq.Array(searchRequest.ReferenceIds))
		argumentCounter++
	}

	// 📝 Reference type filters: Filter by reference types (PERSON, VEHICLE, etc.)
	if len(searchRequest.ReferenceTypes) > 0 {
		whereConditionsList = append(whereConditionsList, fmt.Sprintf("er.ref_type = ANY($%d)", argumentCounter))
		queryArguments = append(queryArguments, pq.Array(searchRequest.ReferenceTypes))
		argumentCounter++
	}

	// ═══════════════════════════════════════════════════════════════════════════════════════════
	// APPLY DATE RANGE FILTERS (uses BRIN indexes for efficient time-series queries)
	// ═══════════════════════════════════════════════════════════════════════════════════════════

	// 📅 Create time filters: Filter by entity creation timestamp range
	if searchRequest.CreateTime != nil {
		if searchRequest.CreateTime.From != "" {
			whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.create_time >= $%d", argumentCounter))
			queryArguments = append(queryArguments, searchRequest.CreateTime.From)
			argumentCounter++
		}
		if searchRequest.CreateTime.To != "" {
			whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.create_time <= $%d", argumentCounter))
			queryArguments = append(queryArguments, searchRequest.CreateTime.To)
			argumentCounter++
		}
	}

	// 🔄 Update time filters: Filter by entity update timestamp range
	if searchRequest.UpdateTime != nil {
		if searchRequest.UpdateTime.From != "" {
			whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.update_time >= $%d", argumentCounter))
			queryArguments = append(queryArguments, searchRequest.UpdateTime.From)
			argumentCounter++
		}
		if searchRequest.UpdateTime.To != "" {
			whereConditionsList = append(whereConditionsList, fmt.Sprintf("e.update_time <= $%d", argumentCounter))
			queryArguments = append(queryArguments, searchRequest.UpdateTime.To)
			argumentCounter++
		}
	}

	// ═══════════════════════════════════════════════════════════════════════════════════════════
	// APPLY TEXT SEARCH CONDITIONS (ILIKE + full-text search)
	// ═══════════════════════════════════════════════════════════════════════════════════════════

	// 🔍 Free-text search: Build complex OR conditions across multiple fields
	if searchRequest.Query != "" {
		// Validate search fields against whitelist
		var validatedSearchFieldsList []string
		if len(searchRequest.SearchFields) > 0 {
			for _, searchField := range searchRequest.SearchFields {
				if allowedSearchFieldsMap[searchField] {
					validatedSearchFieldsList = append(validatedSearchFieldsList, searchField)
				}
			}
		} else {
			// Default to all allowed fields if none specified
			for allowedField := range allowedSearchFieldsMap {
				validatedSearchFieldsList = append(validatedSearchFieldsList, allowedField)
			}
		}

		searchConditionString, searchConditionArguments := repository.buildEntityTextSearchCondition(searchRequest.Query, validatedSearchFieldsList, argumentCounter, shouldJoinReferences)
		if searchConditionString != "" {
			whereConditionsList = append(whereConditionsList, searchConditionString)
			queryArguments = append(queryArguments, searchConditionArguments...)
			argumentCounter += len(searchConditionArguments)

			// Track search terms for highlighting functionality
			(*searchTermsMap)["general"] = []string{searchRequest.Query}
		}
	}

	// 🎯 Field-specific queries: Allow targeting specific fields with different search terms
	for _, fieldQueryRequest := range searchRequest.FieldQueries {
		// Skip invalid fields that are not in the allowed list
		if fieldQueryRequest.Query == "" || !allowedSearchFieldsMap[fieldQueryRequest.Field] {
			continue
		}

		fieldConditionString, fieldConditionArguments := repository.buildEntityFieldSearchCondition(fieldQueryRequest, argumentCounter, shouldJoinReferences)
		if fieldConditionString != "" {
			whereConditionsList = append(whereConditionsList, fieldConditionString)
			queryArguments = append(queryArguments, fieldConditionArguments...)
			argumentCounter += len(fieldConditionArguments)

			// Track field-specific search terms for highlighting
			if (*searchTermsMap)[fieldQueryRequest.Field] == nil {
				(*searchTermsMap)[fieldQueryRequest.Field] = []string{}
			}
			(*searchTermsMap)[fieldQueryRequest.Field] = append((*searchTermsMap)[fieldQueryRequest.Field], fieldQueryRequest.Query)
		}
	}

	return baseQueryString, whereConditionsList, queryArguments, shouldJoinReferences
}

// needsReferenceFieldSearch determines if the search requires joining with entity_references table.
// This optimization prevents unnecessary JOINs when reference search is not needed.
//
// Returns true if:
//   - Global query targets reference fields specifically
//   - Global query with no specified fields (searches all fields including references)
//   - Any field-specific query targets reference fields
func (repository *PostgresEntityRepository) needsReferenceFieldSearch(searchRequest *entity.SearchEntitiesRequest) bool {
	// Check if general query targets reference fields specifically
	if searchRequest.Query != "" {
		for _, searchField := range searchRequest.SearchFields {
			if searchField == FieldReferenceDisplayName || searchField == FieldReferenceRelationType {
				return true
			}
		}
		// If no search_fields specified, query searches all fields including references
		if len(searchRequest.SearchFields) == 0 {
			return true
		}
	}

	// Check field-specific queries for reference field targeting
	for _, fieldQueryRequest := range searchRequest.FieldQueries {
		if fieldQueryRequest.Field == FieldReferenceDisplayName || fieldQueryRequest.Field == FieldReferenceRelationType {
			return true
		}
	}

	return false
}

// buildEntityTextSearchCondition constructs search conditions for free-text queries.
// This method builds complex OR conditions across multiple searchable fields using ILIKE pattern matching
// and PostgreSQL full-text search capabilities.
//
// Search strategies used:
//   - Trigram ILIKE for fuzzy matching on ID field
//   - Full-text search + ILIKE for JSONB data content
//   - Array-to-string conversion for tags search
//   - Direct ILIKE on reference fields when JOIN is available
//
// Parameters:
//   - searchQuery: The search term to match
//   - searchFieldsList: Specific fields to search (empty = search all fields)
//   - startArgumentIndex: Starting parameter index for SQL placeholders
//   - shouldJoinReferences: Whether entity_references table is available for JOIN
//
// Returns:
//   - conditionString: SQL WHERE condition string with OR logic
//   - conditionArguments: Parameterized arguments for the condition
func (repository *PostgresEntityRepository) buildEntityTextSearchCondition(
	searchQuery string,
	searchFieldsList []string,
	startArgumentIndex int,
	shouldJoinReferences bool,
) (conditionString string, conditionArguments []interface{}) {

	if searchQuery == "" {
		return "", nil
	}

	argumentCounter := startArgumentIndex
	var searchConditionsList []string

	// If no specific fields are provided, search all available fields
	if len(searchFieldsList) == 0 {
		searchFieldsList = []string{FieldID, FieldData, FieldTags, FieldReferenceDisplayName, FieldReferenceRelationType}
	}

	for _, searchField := range searchFieldsList {
		switch searchField {
		case FieldID:
			// Trigram search on entity ID using idx_entities_id_trgm
			searchConditionsList = append(searchConditionsList, fmt.Sprintf("e.id ILIKE $%d", argumentCounter))
			conditionArguments = append(conditionArguments, "%"+searchQuery+"%")
			argumentCounter++

		case FieldData:
			// JSONB content search using ILIKE pattern matching (gin_entities_data index provides optimization)
			searchConditionsList = append(searchConditionsList, fmt.Sprintf("e.data::text ILIKE $%d", argumentCounter))
			conditionArguments = append(conditionArguments, "%"+searchQuery+"%")
			argumentCounter++

		case FieldTags:
			// Convert tags array to searchable string using gin_entities_tags
			searchConditionsList = append(searchConditionsList, fmt.Sprintf("array_to_string(e.tags, ' ') ILIKE $%d", argumentCounter))
			conditionArguments = append(conditionArguments, "%"+searchQuery+"%")
			argumentCounter++

		case FieldReferenceDisplayName:
			if shouldJoinReferences {
				// Search reference display names using trigram index
				searchConditionsList = append(searchConditionsList, fmt.Sprintf("er.display_name ILIKE $%d", argumentCounter))
				conditionArguments = append(conditionArguments, "%"+searchQuery+"%")
				argumentCounter++
			}

		case FieldReferenceRelationType:
			if shouldJoinReferences {
				// Search reference relation types using trigram index
				searchConditionsList = append(searchConditionsList, fmt.Sprintf("er.relation_type ILIKE $%d", argumentCounter))
				conditionArguments = append(conditionArguments, "%"+searchQuery+"%")
				argumentCounter++
			}
		}
	}

	if len(searchConditionsList) > 0 {
		return "(" + strings.Join(searchConditionsList, " OR ") + ")", conditionArguments
	}

	return "", nil
}

// buildEntityFieldSearchCondition constructs search conditions for field-specific queries.
// This allows users to target specific fields with different search terms for more precise searching.
//
// Each field uses optimized search strategies:
//   - ID: Trigram ILIKE for partial matching
//   - Data: JSONB ILIKE pattern matching (uses gin_entities_data index)
//   - Tags: Array-to-string conversion for tag content search
//   - Reference fields: Direct ILIKE when JOIN is available
func (repository *PostgresEntityRepository) buildEntityFieldSearchCondition(
	fieldQueryRequest *entity.FieldQuery,
	startArgumentIndex int,
	shouldJoinReferences bool,
) (conditionString string, conditionArguments []interface{}) {

	if fieldQueryRequest.Query == "" {
		return "", nil
	}

	argumentCounter := startArgumentIndex

	switch fieldQueryRequest.Field {
	case FieldID:
		return fmt.Sprintf("e.id ILIKE $%d", argumentCounter), []interface{}{"%" + fieldQueryRequest.Query + "%"}

	case FieldData:
		return fmt.Sprintf("e.data::text ILIKE $%d", argumentCounter), []interface{}{"%" + fieldQueryRequest.Query + "%"}

	case FieldTags:
		return fmt.Sprintf("array_to_string(e.tags, ' ') ILIKE $%d", argumentCounter), []interface{}{"%" + fieldQueryRequest.Query + "%"}

	case FieldReferenceDisplayName:
		if shouldJoinReferences {
			return fmt.Sprintf("er.display_name ILIKE $%d", argumentCounter), []interface{}{"%" + fieldQueryRequest.Query + "%"}
		}

	case FieldReferenceRelationType:
		if shouldJoinReferences {
			return fmt.Sprintf("er.relation_type ILIKE $%d", argumentCounter), []interface{}{"%" + fieldQueryRequest.Query + "%"}
		}
	}

	return "", nil
}

// buildEntityOrderByClause constructs the ORDER BY clause based on the search request.
// Includes secondary sort by ID for consistent pagination across multiple requests.
//
// Supported ordering options:
//   - CREATED_AT: Sort by entity creation time
//   - UPDATED_AT: Sort by entity update time
//   - STATUS: Sort by entity status value
//   - RELEVANCE: Fallback to creation time (full-text relevance scoring not implemented)
//
// All orderings include secondary sort by entity ID for deterministic pagination.
func (repository *PostgresEntityRepository) buildEntityOrderByClause(orderByOption entity.SearchOrderBy, isAscending bool) string {
	sortDirection := "DESC"
	if isAscending {
		sortDirection = "ASC"
	}

	switch orderByOption {
	case entity.SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT:
		return fmt.Sprintf(" ORDER BY e.create_time %s, e.id %s", sortDirection, sortDirection)
	case entity.SearchOrderBy_SEARCH_ORDER_BY_UPDATED_AT:
		return fmt.Sprintf(" ORDER BY e.update_time %s, e.id %s", sortDirection, sortDirection)
	case entity.SearchOrderBy_SEARCH_ORDER_BY_STATUS:
		return fmt.Sprintf(" ORDER BY e.status %s, e.create_time %s, e.id %s", sortDirection, sortDirection, sortDirection)
	case entity.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE:
		fallthrough
	default:
		// For relevance, use create_time as fallback since full-text ranking is not implemented
		return fmt.Sprintf(" ORDER BY e.create_time %s, e.id %s", sortDirection, sortDirection)
	}
}

// scanEntitySearchRow scans a database row into an Entity object for search results.
// Handles the mapping from SQL result columns to protobuf Entity fields with proper type conversions.
//
// Key transformations:
//   - Time values: PostgreSQL timestamps → RFC3339Nano formatted strings
//   - Enums: Integer values → protobuf enum types
//   - Nullable fields: SQL NULL handling for optional schema_id
//   - JSONB data: JSON string → protobuf Struct conversion
//   - Arrays: PostgreSQL arrays → Go slices
func (repository *PostgresEntityRepository) scanEntitySearchRow(databaseRow *sql.Rows) (*entity.Entity, error) {
	var entityRecord entity.Entity
	var organizationID int32
	var entityDataJSON string
	var entityTypeValue, statusValue int32
	var entityTags []string
	var nullableSchemaID sql.NullString
	var createTime, updateTime time.Time

	scanError := databaseRow.Scan(
		&entityRecord.Id,
		&organizationID,
		&nullableSchemaID,
		&entityRecord.SchemaVersion,
		&entityDataJSON,
		&createTime,
		&updateTime,
		&entityTypeValue,
		&entityRecord.CreatedBy,
		&entityRecord.UpdatedBy,
		&entityRecord.Version,
		&statusValue,
		pq.Array(&entityTags),
		&entityRecord.ResourceType,
	)

	if scanError != nil {
		return nil, scanError
	}

	// Post-process scanned data with proper type conversions
	entityRecord.CreateTime = createTime.Format(time.RFC3339Nano)
	entityRecord.UpdateTime = updateTime.Format(time.RFC3339Nano)
	entityRecord.EntityType = entity.EntityType(entityTypeValue)
	entityRecord.Status = entity.RecordStatus(statusValue)
	entityRecord.Tags = entityTags
	entityRecord.OrgId = organizationID

	// Handle nullable schema ID field
	if nullableSchemaID.Valid {
		entityRecord.SchemaId = nullableSchemaID.String
	} else {
		entityRecord.SchemaId = ""
	}

	// Convert JSON data to protobuf Struct using utility function
	structData, jsonConversionError := workflowUtils.JSONToStruct(entityDataJSON)
	if jsonConversionError != nil {
		herosentry.CaptureException(context.Background(), jsonConversionError, herosentry.ErrorTypeInternal)
		return nil, fmt.Errorf("failed to unmarshal entity data JSON for search result: %w", jsonConversionError)
	}
	entityRecord.Data = structData

	return &entityRecord, nil
}

// getEntitySearchTotalCount executes a COUNT query to get total search results without pagination.
// This provides the total_results field for pagination metadata in the search response.
//
// The method:
//  1. Converts the main SELECT query to a COUNT query
//  2. Handles both DISTINCT and non-DISTINCT cases
//  3. Executes with the same WHERE conditions but no pagination
//  4. Returns the total count for UI pagination displays
func (repository *PostgresEntityRepository) getEntitySearchTotalCount(
	requestContext context.Context,
	databaseTransaction *sql.Tx,
	baseQueryString string,
	whereConditionsList []string,
	queryArguments []interface{},
) (int, error) {

	// Build count query by replacing SELECT clause with COUNT
	// Use regex to handle both DISTINCT and non-DISTINCT cases with varying whitespace
	var countQueryString string

	// Remove extra whitespace and normalize the query
	normalizedQueryString := strings.Join(strings.Fields(baseQueryString), " ")

	// Check if we have DISTINCT and replace accordingly
	if strings.Contains(strings.ToUpper(normalizedQueryString), "SELECT DISTINCT") {
		// For DISTINCT queries, count distinct entity IDs
		countQueryString = "SELECT COUNT(DISTINCT e.id) FROM entities e"
		if strings.Contains(normalizedQueryString, "LEFT JOIN entity_references er ON e.id = er.entity_id") {
			countQueryString += " LEFT JOIN entity_references er ON e.id = er.entity_id"
		}
	} else {
		// For non-DISTINCT queries, simple count
		countQueryString = "SELECT COUNT(e.id) FROM entities e"
	}

	// Add WHERE conditions if any exist
	if len(whereConditionsList) > 0 {
		countQueryString += " WHERE " + strings.Join(whereConditionsList, " AND ")
	}

	// Execute count query
	var totalResultCount int
	countExecutionError := databaseTransaction.QueryRowContext(requestContext, countQueryString, queryArguments...).Scan(&totalResultCount)
	if countExecutionError != nil {
		return 0, fmt.Errorf("failed to get total count: %w", countExecutionError)
	}

	return totalResultCount, nil
}

// generateEntitySearchHighlights generates contextual highlight fragments for search results.
// Creates highlighted text snippets showing where search terms matched in the entity data.
//
// Highlighting features:
//   - Searches for matches across entity ID, data, and tags fields
//   - Creates 60-character context windows around matches
//   - Adds ellipsis for truncated content
//   - Deduplicates identical fragments
//   - Maps highlights by entity ID for easy lookup
//
// The highlighting helps users understand why specific entities matched their search query.
func (repository *PostgresEntityRepository) generateEntitySearchHighlights(
	foundEntities []*entity.Entity,
	searchTermsMap map[string][]string,
) map[string]*entity.HighlightResult {

	highlightsMap := make(map[string]*entity.HighlightResult)

	for _, entityRecord := range foundEntities {
		var highlightFragmentsList []string
		var highlightFieldName string

		// Check for matches in different fields based on search terms
		for searchField, searchTermsList := range searchTermsMap {
			for _, searchTerm := range searchTermsList {
				// Check ID field for both general and id-specific searches
				if searchField == "general" || searchField == "id" {
					if repository.containsTermIgnoreCase(entityRecord.Id, searchTerm) {
						highlightFragment := repository.createHighlightFragment(entityRecord.Id, searchTerm)
						if highlightFragment != "" {
							highlightFragmentsList = append(highlightFragmentsList, highlightFragment)
							highlightFieldName = "id"
						}
					}
				}

				// Check data field for both general and data-specific searches
				if searchField == "general" || searchField == "data" {
					if entityRecord.Data != nil {
						entityDataJSON, _ := workflowUtils.StructToJSON(entityRecord.Data)
						if repository.containsTermIgnoreCase(entityDataJSON, searchTerm) {
							highlightFragment := repository.createHighlightFragment(entityDataJSON, searchTerm)
							if highlightFragment != "" {
								highlightFragmentsList = append(highlightFragmentsList, highlightFragment)
								highlightFieldName = "data"
							}
						}
					}
				}

				// Check tags field for both general and tags-specific searches
				if searchField == "general" || searchField == "tags" {
					tagsTextContent := strings.Join(entityRecord.Tags, " ")
					if repository.containsTermIgnoreCase(tagsTextContent, searchTerm) {
						highlightFragment := repository.createHighlightFragment(tagsTextContent, searchTerm)
						if highlightFragment != "" {
							highlightFragmentsList = append(highlightFragmentsList, highlightFragment)
							highlightFieldName = "tags"
						}
					}
				}
			}
		}

		// Add highlight result if we found any matches
		if len(highlightFragmentsList) > 0 {
			highlightsMap[entityRecord.Id] = &entity.HighlightResult{
				Field:     highlightFieldName,
				Fragments: repository.deduplicateFragments(highlightFragmentsList),
			}
		}
	}

	return highlightsMap
}

// containsTermIgnoreCase performs case-insensitive substring matching.
// Used to determine if a field contains a search term for highlighting purposes.
func (repository *PostgresEntityRepository) containsTermIgnoreCase(textContent, searchTerm string) bool {
	return strings.Contains(strings.ToLower(textContent), strings.ToLower(searchTerm))
}

// createHighlightFragment creates a contextual text fragment around a matched search term.
// Includes up to 30 characters before and after the match for context.
// Adds ellipsis when content is truncated from either end.
//
// Parameters:
//   - textContent: The full text containing the match
//   - searchTerm: The search term that was matched
//
// Returns:
//   - A formatted fragment with context around the match, or empty string if no match found
func (repository *PostgresEntityRepository) createHighlightFragment(textContent, searchTerm string) string {
	lowerTextContent := strings.ToLower(textContent)
	lowerSearchTerm := strings.ToLower(searchTerm)

	matchIndex := strings.Index(lowerTextContent, lowerSearchTerm)
	if matchIndex == -1 {
		return ""
	}

	// Create fragment with context around the match (±30 characters)
	fragmentStartIndex := repository.maxInt(0, matchIndex-30)
	fragmentEndIndex := repository.minInt(len(textContent), matchIndex+len(searchTerm)+30)

	highlightFragment := textContent[fragmentStartIndex:fragmentEndIndex]

	// Add ellipsis if we truncated content
	if fragmentStartIndex > 0 {
		highlightFragment = "…" + highlightFragment
	}
	if fragmentEndIndex < len(textContent) {
		highlightFragment += "…"
	}

	return highlightFragment
}

// deduplicateFragments removes duplicate highlight fragments to prevent redundancy.
// Maintains insertion order while removing duplicates.
func (repository *PostgresEntityRepository) deduplicateFragments(fragmentsList []string) []string {
	seenFragmentsMap := make(map[string]bool)
	var deduplicatedFragments []string

	for _, highlightFragment := range fragmentsList {
		if !seenFragmentsMap[highlightFragment] {
			seenFragmentsMap[highlightFragment] = true
			deduplicatedFragments = append(deduplicatedFragments, highlightFragment)
		}
	}

	return deduplicatedFragments
}

// Helper utility functions for integer operations
func (repository *PostgresEntityRepository) minInt(firstValue, secondValue int) int {
	if firstValue < secondValue {
		return firstValue
	}
	return secondValue
}

func (repository *PostgresEntityRepository) maxInt(firstValue, secondValue int) int {
	if firstValue > secondValue {
		return firstValue
	}
	return secondValue
}

func (repository *PostgresEntityRepository) safeIntToInt32(value int) int32 {
	if value > 2147483647 {
		return 2147483647
	}
	if value < 0 {
		return 0
	}
	// Use explicit conversion with bounds checking
	result := int32(value) // nolint:gosec // Safe conversion after bounds check
	return result
}

// batchGetLatestEntitySchemas retrieves multiple entity schemas by their IDs in a single operation.
// This function follows the same pattern as BatchGetLatestEntities to avoid N+1 query issues.
func (repository *PostgresEntityRepository) batchGetLatestEntitySchemas(
	ctx context.Context,
	tx *sql.Tx,
	schemaIDs []string,
) ([]*entity.EntitySchema, error) {
	return database.WithSession(repository.database, ctx, tx, func(sessionTx *sql.Tx) ([]*entity.EntitySchema, error) {
		const maxBatchSize = 500

		// Early return for empty input
		if len(schemaIDs) == 0 {
			return []*entity.EntitySchema{}, nil
		}

		// Chunk very large lists
		if len(schemaIDs) > maxBatchSize {
			var allSchemas []*entity.EntitySchema
			for startIndex := 0; startIndex < len(schemaIDs); startIndex += maxBatchSize {
				endIndex := startIndex + maxBatchSize
				if endIndex > len(schemaIDs) {
					endIndex = len(schemaIDs)
				}
				batchSchemas, err := repository.batchGetLatestEntitySchemas(ctx, sessionTx, schemaIDs[startIndex:endIndex])
				if err != nil {
					return nil, err
				}
				allSchemas = append(allSchemas, batchSchemas...)
			}
			return allSchemas, nil
		}

		// Prepare result slice with nil placeholders
		resultSchemas := make([]*entity.EntitySchema, len(schemaIDs))

		// The query uses a CTE (Common Table Expression) to:
		// 1. Expand the Go slice (text[]) into rows with ordinality
		// 2. Join this CTE against the "entity_schemas" table on matching IDs
		// 3. Order by "ord" to return rows in the same order as the input slice
		const queryText = `
			WITH input_list AS (
				SELECT id, ord
				FROM unnest($1::text[]) WITH ORDINALITY AS t(id, ord)
			)
			SELECT
				input_list.ord,
				s.id,
				s.org_id,
				s.name,
				s.description,
				s.schema_definition,
				s.create_time,
				s.update_time,
				s.created_by,
				s.updated_by,
				s.version,
				s.entity_type,
				s.status,
				s.tags,
				s.resource_type
			FROM input_list
			JOIN entity_schemas AS s
			ON s.id = input_list.id
			ORDER BY input_list.ord;
		`
		rows, queryErr := sessionTx.QueryContext(ctx, queryText, pq.Array(schemaIDs))
		if queryErr != nil {
			herosentry.CaptureException(ctx, queryErr, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("failed to execute batch schema query: %w", queryErr)
		}
		defer rows.Close()

		for rows.Next() {
			var ordinalIndex int
			var schemaRecord entity.EntitySchema
			var organizationID int32
			var rawSchemaDefinitionJSON string
			var entityTypeInt, statusInt int32
			var tagList []string
			var createTimeValue, updateTimeValue time.Time

			if scanErr := rows.Scan(
				&ordinalIndex,
				&schemaRecord.Id,
				&organizationID,
				&schemaRecord.Name,
				&schemaRecord.Description,
				&rawSchemaDefinitionJSON,
				&createTimeValue,
				&updateTimeValue,
				&schemaRecord.CreatedBy,
				&schemaRecord.UpdatedBy,
				&schemaRecord.Version,
				&entityTypeInt,
				&statusInt,
				pq.Array(&tagList),
				&schemaRecord.ResourceType,
			); scanErr != nil {
				herosentry.CaptureException(ctx, scanErr, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("failed to scan entity schema row: %w", scanErr)
			}

			// Populate metadata fields
			schemaRecord.OrgId = organizationID
			schemaRecord.CreateTime = createTimeValue.Format(time.RFC3339Nano)
			schemaRecord.UpdateTime = updateTimeValue.Format(time.RFC3339Nano)
			schemaRecord.EntityType = entity.EntityType(entityTypeInt)
			schemaRecord.Status = entity.RecordStatus(statusInt)
			schemaRecord.Tags = tagList

			// Unmarshal JSON into protobuf Struct
			structDef, unmarshalErr := workflowUtils.JSONToStruct(rawSchemaDefinitionJSON)
			if unmarshalErr != nil {
				herosentry.CaptureException(ctx, unmarshalErr, herosentry.ErrorTypeInternal)
				return nil, fmt.Errorf("failed to unmarshal schema definition for ID '%s': %w", schemaRecord.Id, unmarshalErr)
			}
			schemaRecord.SchemaDefinition = structDef

			// Place into the correct slot (ord is 1-based)
			resultSchemas[ordinalIndex-1] = &schemaRecord
		}
		if iterationErr := rows.Err(); iterationErr != nil {
			herosentry.CaptureException(ctx, iterationErr, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("error iterating entity schema rows: %w", iterationErr)
		}

		return resultSchemas, nil
	})
}

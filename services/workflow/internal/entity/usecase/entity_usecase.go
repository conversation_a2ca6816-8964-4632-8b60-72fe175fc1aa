package usecase

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog" // Import slog for logging potential rollback errors

	"common/herosentry"
	entitypb "proto/hero/entity/v1"
	repo "workflow/internal/entity/data"
)

// defaultPageSize defines the number of items to return per page when
// a specific page size is not provided or is invalid (<= 0).
const defaultPageSize = 100

// EntityUseCase provides methods for coordinating business logic related to
// entities and their schemas. It uses repositories to interact with the
// underlying data storage and manages database transactions.
type EntityUseCase struct {
	database   *sql.DB
	entityRepo repo.EntityRepository // Repository for entity data operations
}

// NewEntityUseCase constructs a new instance of EntityUseCase.
// It requires a database connection and repositories for entities and schemas.
// If the provided *sql.DB is nil, it attempts to initialize an in-memory SQLite
// database for testing or development purposes.
func NewEntityUseCase(
	database *sql.DB, // Renamed db to database for clarity
	entityRepo repo.EntityRepository,
) (*EntityUseCase, error) {
	if database == nil {
		return nil, errors.New("database is nil: cannot initialize EntityUseCase")
	}
	// Basic check to ensure repositories are provided
	if entityRepo == nil {
		err := fmt.Errorf("entity repositories must not be nil")
		herosentry.CaptureException(context.Background(), err, herosentry.ErrorTypeValidation)
		return nil, err
	}
	return &EntityUseCase{
		database:   database,
		entityRepo: entityRepo,
	}, nil
}

// executeInTx is a helper function to wrap database operations within a transaction.
// It begins a transaction, executes the provided function `operationFunc`, and commits the transaction.
// If `operationFunc` returns an error or the commit fails, it rolls back the transaction.
// The error from rollback is logged but not returned, as the original error is usually more important.
func (entityUsecase *EntityUseCase) executeInTx(ctx context.Context, operationFunc func(tx *sql.Tx) error) error { // Renamed fn to operationFunc
	tx, err := entityUsecase.database.BeginTx(ctx, nil)
	if err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction")
		slog.ErrorContext(ctx, "Failed to begin transaction", slog.Any("error", err))
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	// Defer rollback in case of panic or error within operationFunc
	defer func() {
		if panicValue := recover(); panicValue != nil { // Renamed p to panicValue
			slog.ErrorContext(ctx, "Panic recovered during transaction, rolling back", slog.Any("panic", panicValue))
			if rollbackErr := tx.Rollback(); rollbackErr != nil { // Renamed rbErr to rollbackErr
				slog.ErrorContext(ctx, "Failed to rollback transaction after panic", slog.Any("rollback_error", rollbackErr))
			}
			// Re-panic after rollback attempt
			panic(panicValue)
		}
	}()

	if err = operationFunc(tx); err != nil {
		slog.WarnContext(ctx, "Error during transaction execution, rolling back", slog.Any("error", err))
		if rollbackErr := tx.Rollback(); rollbackErr != nil { // Renamed rbErr to rollbackErr
			// Log rollback error but return the original error from operationFunc
			slog.ErrorContext(ctx, "Failed to rollback transaction", slog.Any("rollback_error", rollbackErr), slog.Any("original_error", err))
		}
		return err // Return the original error
	}

	if err = tx.Commit(); err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase, "Failed to commit transaction")
		slog.ErrorContext(ctx, "Failed to commit transaction", slog.Any("error", err))
		// No need to explicitly rollback here, Commit failure implies rollback or unusable connection state.
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// --------------------
// Entity RPC Use‑Cases
// --------------------

// CreateEntity handles the creation of a single new entity.
// It ensures the operation is performed within a database transaction.
func (entityUsecase *EntityUseCase) CreateEntity(
	ctx context.Context,
	entityToCreate *entitypb.Entity,
) (*entitypb.Entity, error) {
	// Start span for business logic
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.CreateEntity")
	defer finishSpan()

	// Add business context
	span.SetTag("entity.type", entityToCreate.EntityType.String())
	span.SetTag("entity.org_id", fmt.Sprintf("%d", entityToCreate.OrgId))
	if entityToCreate.SchemaId != "" {
		span.SetTag("entity.schema_id", entityToCreate.SchemaId)
	}

	var createdEntity *entitypb.Entity
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		var transactionErr error // Renamed txErr to transactionErr
		createdEntity, transactionErr = entityUsecase.entityRepo.CreateEntity(spanContext, tx, entityToCreate)
		return transactionErr // Return error from repository operation
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to create entity")
		// Error is already logged by executeInTx if it originated there
		return nil, fmt.Errorf("CreateEntity failed: %w", err)
	}

	// Track created entity ID
	if createdEntity != nil {
		span.SetTag("entity.id", createdEntity.Id)
	}

	return createdEntity, nil
}

// GetLatestEntity retrieves the most recent version of an entity by its ID.
// This operation does not require a transaction as it's read-only.
func (entityUsecase *EntityUseCase) GetLatestEntity(
	ctx context.Context,
	entityID string,
) (*entitypb.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.GetLatestEntity")
	defer finishSpan()

	span.SetTag("entity.id", entityID)

	// Pass nil for the transaction argument as this is a read-only operation outside a tx.
	entity, err := entityUsecase.entityRepo.GetLatestEntity(spanContext, nil, entityID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get entity %s", entityID))
		// Consider logging the error here as well
		return nil, fmt.Errorf("GetLatestEntity failed for ID %s: %w", entityID, err)
	}

	// Add retrieved entity details
	if entity != nil {
		span.SetTag("entity.type", entity.EntityType.String())
		span.SetTag("entity.status", entity.Status.String())
		span.SetTag("entity.version", fmt.Sprintf("%d", entity.Version))
	}

	return entity, nil
}

// BatchGetLatestEntities retrieves multiple entities by their IDs in a single operation.
// This operation does not require a transaction as it's read-only.
func (entityUsecase *EntityUseCase) BatchGetLatestEntities(
	ctx context.Context,
	entityIDs []string,
) ([]*entitypb.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.BatchGetLatestEntities")
	defer finishSpan()

	span.SetTag("entity.ids_count", fmt.Sprintf("%d", len(entityIDs)))

	// Pass nil for the transaction argument as this is a read-only operation.
	entities, err := entityUsecase.entityRepo.BatchGetLatestEntities(spanContext, nil, entityIDs)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to batch get entities")
		return nil, fmt.Errorf("BatchGetLatestEntities failed: %w", err)
	}
	if entities == nil {
		// Ensure we always return non-nil slices/maps if no error occurred
		return []*entitypb.Entity{}, nil
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(entities)))

	return entities, nil
}

// GetEntityByVersion retrieves a specific historical version of an entity using its ID and version number.
// This operation does not require a transaction as it's read-only.
func (entityUsecase *EntityUseCase) GetEntityByVersion(
	ctx context.Context,
	entityID string,
	version int32,
) (*entitypb.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.GetEntityByVersion")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("entity.version", fmt.Sprintf("%d", version))

	// Pass nil for the transaction argument.
	entity, err := entityUsecase.entityRepo.GetEntityByVersion(spanContext, nil, entityID, int32(version))
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get entity %s version %d", entityID, version))
		return nil, fmt.Errorf("GetEntityByVersion failed for ID %s, Version %d: %w", entityID, version, err)
	}

	// Add retrieved entity details
	if entity != nil {
		span.SetTag("entity.type", entity.EntityType.String())
		span.SetTag("entity.status", entity.Status.String())
	}

	return entity, nil
}

// GetLatestActiveEntity retrieves the most recent version of an entity that has an 'Active' status.
// This operation does not require a transaction as it's read-only.
func (entityUsecase *EntityUseCase) GetLatestActiveEntity(
	ctx context.Context,
	entityID string,
) (*entitypb.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.GetLatestActiveEntity")
	defer finishSpan()

	span.SetTag("entity.id", entityID)

	// Pass nil for the transaction argument.
	entity, err := entityUsecase.entityRepo.GetLatestActiveEntity(spanContext, nil, entityID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get latest active entity %s", entityID))
		return nil, fmt.Errorf("GetLatestActiveEntity failed for ID %s: %w", entityID, err)
	}

	// Add retrieved entity details
	if entity != nil {
		span.SetTag("entity.type", entity.EntityType.String())
		span.SetTag("entity.status", entity.Status.String())
		span.SetTag("entity.version", fmt.Sprintf("%d", entity.Version))
	}

	return entity, nil
}

// UpdateEntity applies updates to an existing entity based on the provided request.
// This method performs a "patch" update: only fields present and non-default in
// the request's `Entity` field will overwrite the corresponding fields in the
// latest version of the target entity. The entire update occurs within a transaction.
func (entityUsecase *EntityUseCase) UpdateEntity(
	ctx context.Context,
	updateReq *entitypb.UpdateEntityRequest,
) (*entitypb.Entity, error) {
	// Start span for business logic
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.UpdateEntity")
	defer finishSpan()

	if updateReq == nil || updateReq.Entity == nil || updateReq.Entity.Id == "" {
		err := fmt.Errorf("invalid UpdateEntityRequest: request, entity, and entity ID must be provided")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation, "Invalid update request")
		return nil, err
	}

	span.SetTag("entity.id", updateReq.Entity.Id)
	if updateReq.Entity.EntityType != entitypb.EntityType_ENTITY_TYPE_UNSPECIFIED {
		span.SetTag("entity.type", updateReq.Entity.EntityType.String())
	}
	if updateReq.Entity.Status != entitypb.RecordStatus_RECORD_STATUS_UNSPECIFIED {
		span.SetTag("entity.status", updateReq.Entity.Status.String())
	}

	var updatedEntity *entitypb.Entity
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		// 1. Fetch the latest existing version within the transaction
		existingEntity, transactionErr := entityUsecase.entityRepo.GetLatestEntity(spanContext, tx, updateReq.Entity.Id) // Renamed txErr to transactionErr
		if transactionErr != nil {
			err := fmt.Errorf("failed to get existing entity %s for update: %w", updateReq.Entity.Id, transactionErr)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		// 2. Apply patch updates from the request
		// Only update fields if they are provided (non-default value) in the request.
		patchData := updateReq.Entity
		if patchData.OrgId != 0 {
			err := fmt.Errorf("not allowed to update OrgId for entity %s", patchData.Id)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return err
		}
		if patchData.SchemaId != "" {
			existingEntity.SchemaId = patchData.SchemaId
		}
		// Note: Version alway starts from 1
		if patchData.SchemaVersion != 0 {
			existingEntity.SchemaVersion = patchData.SchemaVersion
		}
		if patchData.Data != nil { // Assumes Data is a pointer type (e.g., *structpb.Struct)
			existingEntity.Data = patchData.Data
		}
		if patchData.References != nil { // Assumes References is a pointer or slice type
			existingEntity.References = patchData.References
		}
		if patchData.EntityType != entitypb.EntityType_ENTITY_TYPE_UNSPECIFIED {
			existingEntity.EntityType = patchData.EntityType
		}
		if patchData.Status != entitypb.RecordStatus_RECORD_STATUS_UNSPECIFIED {
			existingEntity.Status = patchData.Status
		}
		if patchData.UpdatedBy != "" {
			existingEntity.UpdatedBy = patchData.UpdatedBy
		}
		if len(patchData.Tags) > 0 { // Overwrites existing tags completely if provided
			existingEntity.Tags = patchData.Tags
		}

		// 3. Persist the updated entity
		updatedEntity, transactionErr = entityUsecase.entityRepo.UpdateEntity(spanContext, tx, existingEntity) // Renamed txErr to transactionErr
		if transactionErr != nil {
			err := fmt.Errorf("failed to persist updated entity %s: %w", existingEntity.Id, transactionErr)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}
		return nil // Success within the transaction function
	})

	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update entity %s", updateReq.Entity.Id))
		return nil, fmt.Errorf("UpdateEntity failed for ID %s: %w", updateReq.Entity.Id, err)
	}

	// Track updated entity version
	if updatedEntity != nil {
		span.SetTag("entity.updated_version", fmt.Sprintf("%d", updatedEntity.Version))
	}

	return updatedEntity, nil
}

// DeleteAllVersionsOfEntity permanently removes all historical versions of an entity by its ID.
// This operation is performed within a transaction.
func (entityUsecase *EntityUseCase) DeleteAllVersionsOfEntity(
	ctx context.Context,
	entityID string,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.DeleteAllVersionsOfEntity")
	defer finishSpan()
	span.SetTag("entity.id", entityID)

	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		return entityUsecase.entityRepo.DeleteAllVersionsOfEntity(spanContext, tx, entityID)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete entity")
		return fmt.Errorf("DeleteAllVersionsOfEntity failed for ID %s: %w", entityID, err)
	}
	return nil
}

// DeleteSpecificVersionOfEntity permanently removes a single historical version of an entity.
// This operation is performed within a transaction.
func (entityUsecase *EntityUseCase) DeleteSpecificVersionOfEntity(
	ctx context.Context,
	entityID string,
	version int32,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.DeleteSpecificVersionOfEntity")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("entity.version", fmt.Sprintf("%d", version))

	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		return entityUsecase.entityRepo.DeleteSpecificVersionOfEntity(spanContext, tx, entityID, int32(version))
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete entity %s version %d", entityID, version))
		return fmt.Errorf("DeleteSpecificVersionOfEntity failed for ID %s, Version %d: %w", entityID, version, err)
	}
	return nil
}

// ListLatestEntities retrieves a paginated list of the latest versions of entities.
// It supports filtering by entity type. If the provided pageSize is non-positive,
// it defaults to `defaultPageSize`.
// This operation does not require a transaction as it's read-only.
func (entityUsecase *EntityUseCase) ListLatestEntities(
	ctx context.Context,
	pageSize int32,
	pageToken string,
	entityTypeFilter entitypb.EntityType,
) ([]*entitypb.Entity, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.ListLatestEntities")
	defer finishSpan()

	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", effectivePageSize))
	span.SetTag("filter.entity_type", entityTypeFilter.String())

	// Pass nil for the transaction argument.
	listResult, err := entityUsecase.entityRepo.ListLatestEntities(spanContext, nil, int(effectivePageSize), pageToken, entityTypeFilter)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list entities")
		return nil, "", fmt.Errorf("ListLatestEntities failed: %w", err)
	}
	if listResult == nil {
		// Ensure we always return non-nil slices/maps if no error occurred
		return []*entitypb.Entity{}, "", nil
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(listResult.Entities)))

	return listResult.Entities, listResult.PageToken, nil
}

// ListAllVersionsOfEntity retrieves all historical versions (snapshots) of a specific entity by its ID.
// This operation does not require a transaction as it's read-only.
func (entityUsecase *EntityUseCase) ListAllVersionsOfEntity(
	ctx context.Context,
	entityID string,
) ([]*entitypb.EntityVersion, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.ListAllVersionsOfEntity")
	defer finishSpan()

	span.SetTag("entity.id", entityID)

	// Pass nil for the transaction argument.
	versions, err := entityUsecase.entityRepo.ListAllVersionsOfEntity(spanContext, nil, entityID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to list all versions for entity %s", entityID))
		return nil, fmt.Errorf("ListAllVersionsOfEntity failed for ID %s: %w", entityID, err)
	}
	if versions == nil {
		// Ensure non-nil slice return on success
		return []*entitypb.EntityVersion{}, nil
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(versions)))

	return versions, nil
}

// BulkCreateEntities creates multiple entities within a single database transaction.
// If any entity creation fails, the entire transaction is rolled back.
func (entityUsecase *EntityUseCase) BulkCreateEntities(
	ctx context.Context,
	entitiesToCreate []*entitypb.Entity,
) ([]*entitypb.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.BulkCreateEntities")
	defer finishSpan()

	span.SetTag("entities.count", fmt.Sprintf("%d", len(entitiesToCreate)))

	if len(entitiesToCreate) == 0 {
		return []*entitypb.Entity{}, nil // Nothing to create
	}

	var createdEntities []*entitypb.Entity
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		var transactionErr error // Renamed txErr to transactionErr
		createdEntities, transactionErr = entityUsecase.entityRepo.BulkCreateEntities(spanContext, tx, entitiesToCreate)
		return transactionErr
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk create entities")
		return nil, fmt.Errorf("BulkCreateEntities failed: %w", err)
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(createdEntities)))

	return createdEntities, nil
}

// BulkUpdateEntities updates multiple entities within a single database transaction.
// Note: This typically implies replacing the entire entity data for each item,
// not a patch operation unless the underlying repository implements patching.
// If any entity update fails, the entire transaction is rolled back.
func (entityUsecase *EntityUseCase) BulkUpdateEntities(
	ctx context.Context,
	entitiesToUpdate []*entitypb.Entity,
) ([]*entitypb.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.BulkUpdateEntities")
	defer finishSpan()

	span.SetTag("entities.count", fmt.Sprintf("%d", len(entitiesToUpdate)))

	if len(entitiesToUpdate) == 0 {
		return []*entitypb.Entity{}, nil // Nothing to update
	}

	var updatedEntities []*entitypb.Entity
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		var transactionErr error // Renamed txErr to transactionErr
		// Assuming BulkUpdateEntities replaces entities or handles updates internally
		updatedEntities, transactionErr = entityUsecase.entityRepo.BulkUpdateEntities(spanContext, tx, entitiesToUpdate)
		return transactionErr
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk update entities")
		return nil, fmt.Errorf("BulkUpdateEntities failed: %w", err)
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(updatedEntities)))

	return updatedEntities, nil
}

// BulkDeleteEntities deletes multiple entities (all versions) by their IDs within a single transaction.
// If any deletion fails, the entire transaction is rolled back.
func (entityUsecase *EntityUseCase) BulkDeleteEntities(
	ctx context.Context,
	entityIDs []string,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.BulkDeleteEntities")
	defer finishSpan()

	span.SetTag("entities.count", fmt.Sprintf("%d", len(entityIDs)))

	if len(entityIDs) == 0 {
		return nil // Nothing to delete
	}

	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		return entityUsecase.entityRepo.BulkDeleteEntities(spanContext, tx, entityIDs)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk delete entities")
		return fmt.Errorf("BulkDeleteEntities failed: %w", err)
	}
	return nil
}

// RestoreEntityVersion reverts the state of an entity to a specific historical version.
// This typically involves creating a new version whose content matches the specified historical version.
// The operation is performed within a transaction.
func (entityUsecase *EntityUseCase) RestoreEntityVersion(
	ctx context.Context,
	entityID string,
	version int32,
) (*entitypb.Entity, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.RestoreEntityVersion")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("entity.restore_version", fmt.Sprintf("%d", version))

	var restoredEntity *entitypb.Entity
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		var transactionErr error // Renamed txErr to transactionErr
		restoredEntity, transactionErr = entityUsecase.entityRepo.RestoreEntityVersion(spanContext, tx, entityID, int32(version))
		return transactionErr
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to restore entity %s to version %d", entityID, version))
		return nil, fmt.Errorf("RestoreEntityVersion failed for ID %s, Version %d: %w", entityID, version, err)
	}

	// Track restored entity version
	if restoredEntity != nil {
		span.SetTag("entity.new_version", fmt.Sprintf("%d", restoredEntity.Version))
	}

	return restoredEntity, nil
}

// DiffEntityVersions computes the difference between two historical versions of an entity.
// The format of the returned diff string depends on the repository implementation (e.g., JSON diff, textual diff).
// This operation does not require a transaction as it's read-only.
func (entityUsecase *EntityUseCase) DiffEntityVersions(
	ctx context.Context,
	entityID string,
	version1, version2 int32,
) (string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.DiffEntityVersions")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("entity.version1", fmt.Sprintf("%d", version1))
	span.SetTag("entity.version2", fmt.Sprintf("%d", version2))

	// Pass nil for the transaction argument.
	diffResult, err := entityUsecase.entityRepo.DiffEntityVersions(spanContext, nil, entityID, int32(version1), int32(version2)) // Renamed diff to diffResult
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to diff entity %s versions %d and %d", entityID, version1, version2))
		return "", fmt.Errorf("DiffEntityVersions failed for ID %s between versions %d and %d: %w", entityID, version1, version2, err)
	}
	return diffResult, nil
}

// CheckEntityPermissions verifies if a user has permission to perform a specific action on an entity.
// The permission logic is delegated to the repository, potentially involving ruleset evaluation.
// This operation does not require a transaction as it's read-only.
func (entityUsecase *EntityUseCase) CheckEntityPermissions(
	ctx context.Context,
	entityID, userID, action string,
) (bool, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.CheckEntityPermissions")
	defer finishSpan()

	span.SetTag("entity.id", entityID)
	span.SetTag("user.id", userID)
	span.SetTag("permission.action", action)

	// Pass nil for the transaction argument.
	allowed, err := entityUsecase.entityRepo.CheckEntityPermissions(spanContext, nil, entityID, userID, action)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to check permissions for entity %s", entityID))
		return false, fmt.Errorf("CheckEntityPermissions failed for ID %s, User %s, Action %s: %w", entityID, userID, action, err)
	}

	span.SetTag("permission.allowed", fmt.Sprintf("%t", allowed))

	return allowed, nil
}

// ---------------------------
// EntitySchema RPC Use‑Cases
// ---------------------------
// Note: Schema operations mirror entity operations closely. Comments below highlight differences or key aspects.

// CreateEntitySchema handles the creation of a single new entity schema.
// It ensures the operation is performed within a database transaction.
func (entityUsecase *EntityUseCase) CreateEntitySchema(
	ctx context.Context,
	schemaToCreate *entitypb.EntitySchema,
) (*entitypb.EntitySchema, error) {
	// Start span for business logic
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.CreateEntitySchema")
	defer finishSpan()

	// Add business context
	span.SetTag("schema.name", schemaToCreate.Name)
	span.SetTag("schema.entity_type", schemaToCreate.EntityType.String())
	span.SetTag("schema.org_id", fmt.Sprintf("%d", schemaToCreate.OrgId))

	var createdSchema *entitypb.EntitySchema
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		var transactionErr error // Renamed txErr to transactionErr
		createdSchema, transactionErr = entityUsecase.entityRepo.CreateEntitySchema(spanContext, tx, schemaToCreate)
		return transactionErr
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to create entity schema")
		return nil, fmt.Errorf("CreateEntitySchema failed: %w", err)
	}

	// Track created schema ID
	if createdSchema != nil {
		span.SetTag("schema.id", createdSchema.Id)
	}

	return createdSchema, nil
}

// GetLatestEntitySchema retrieves the most recent version of an entity schema by its ID.
// Read-only, no transaction needed.
func (entityUsecase *EntityUseCase) GetLatestEntitySchema(
	ctx context.Context,
	schemaID string,
) (*entitypb.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.GetLatestEntitySchema")
	defer finishSpan()

	span.SetTag("schema.id", schemaID)

	schema, err := entityUsecase.entityRepo.GetLatestEntitySchema(spanContext, nil, schemaID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get entity schema %s", schemaID))
		return nil, fmt.Errorf("GetLatestEntitySchema failed for ID %s: %w", schemaID, err)
	}

	// Add retrieved schema details
	if schema != nil {
		span.SetTag("schema.name", schema.Name)
		span.SetTag("schema.entity_type", schema.EntityType.String())
		span.SetTag("schema.status", schema.Status.String())
		span.SetTag("schema.version", fmt.Sprintf("%d", schema.Version))
	}

	return schema, nil
}

// GetLatestActiveEntitySchema retrieves the most recent version of a schema that has an 'Active' status.
// Read-only, no transaction needed.
func (entityUsecase *EntityUseCase) GetLatestActiveEntitySchema(
	ctx context.Context,
	schemaID string,
) (*entitypb.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.GetLatestActiveEntitySchema")
	defer finishSpan()

	span.SetTag("schema.id", schemaID)

	schema, err := entityUsecase.entityRepo.GetLatestActiveEntitySchema(spanContext, nil, schemaID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get latest active entity schema %s", schemaID))
		return nil, fmt.Errorf("GetLatestActiveEntitySchema failed for ID %s: %w", schemaID, err)
	}

	// Add retrieved schema details
	if schema != nil {
		span.SetTag("schema.name", schema.Name)
		span.SetTag("schema.entity_type", schema.EntityType.String())
		span.SetTag("schema.status", schema.Status.String())
		span.SetTag("schema.version", fmt.Sprintf("%d", schema.Version))
	}

	return schema, nil
}

// GetEntitySchemaByVersion retrieves a specific historical version of an entity schema.
// Read-only, no transaction needed.
func (entityUsecase *EntityUseCase) GetEntitySchemaByVersion(
	ctx context.Context,
	schemaID string,
	version int32,
) (*entitypb.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.GetEntitySchemaByVersion")
	defer finishSpan()

	span.SetTag("schema.id", schemaID)
	span.SetTag("schema.version", fmt.Sprintf("%d", version))

	schema, err := entityUsecase.entityRepo.GetEntitySchemaByVersion(spanContext, nil, schemaID, int32(version))
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get entity schema %s version %d", schemaID, version))
		return nil, fmt.Errorf("GetEntitySchemaByVersion failed for ID %s, Version %d: %w", schemaID, version, err)
	}

	// Add retrieved schema details
	if schema != nil {
		span.SetTag("schema.name", schema.Name)
		span.SetTag("schema.entity_type", schema.EntityType.String())
		span.SetTag("schema.status", schema.Status.String())
	}

	return schema, nil
}

// UpdateEntitySchema applies updates to an existing entity schema based on the provided request.
// Similar to UpdateEntity, this performs a "patch" update within a transaction.
// Only fields present and non-default in the request's `Schema` field will overwrite existing values.
func (entityUsecase *EntityUseCase) UpdateEntitySchema(
	ctx context.Context,
	updateReq *entitypb.UpdateEntitySchemaRequest,
) (*entitypb.EntitySchema, error) {
	// Start span for business logic
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.UpdateEntitySchema")
	defer finishSpan()

	// Assuming updateReq.Schema.Id is the correct path to the ID.
	if updateReq == nil || updateReq.Schema == nil || updateReq.Schema.Id == "" {
		err := fmt.Errorf("invalid UpdateEntitySchemaRequest: request, schema, and schema ID must be provided")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation, "Invalid update schema request")
		return nil, err
	}

	span.SetTag("schema.id", updateReq.Schema.Id)
	if updateReq.Schema.EntityType != entitypb.EntityType_ENTITY_TYPE_UNSPECIFIED {
		span.SetTag("schema.entity_type", updateReq.Schema.EntityType.String())
	}
	if updateReq.Schema.Status != entitypb.RecordStatus_RECORD_STATUS_UNSPECIFIED {
		span.SetTag("schema.status", updateReq.Schema.Status.String())
	}

	var updatedSchema *entitypb.EntitySchema
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		// 1. Fetch the latest existing version
		existingSchema, transactionErr := entityUsecase.entityRepo.GetLatestEntitySchema(spanContext, tx, updateReq.Schema.Id) // Renamed txErr to transactionErr
		if transactionErr != nil {
			err := fmt.Errorf("failed to get existing schema %s for update: %w", updateReq.Schema.Id, transactionErr)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		// 2. Apply patch updates
		schemaUpdateData := updateReq.Schema
		if schemaUpdateData.OrgId != 0 {
			err := fmt.Errorf("not allowed to update OrgId for schema %s", schemaUpdateData.Id)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return err
		}
		if schemaUpdateData.Name != "" {
			existingSchema.Name = schemaUpdateData.Name
		}
		if schemaUpdateData.Description != "" {
			existingSchema.Description = schemaUpdateData.Description
		}
		if schemaUpdateData.SchemaDefinition != nil {
			existingSchema.SchemaDefinition = schemaUpdateData.SchemaDefinition
		}
		if schemaUpdateData.EntityType != entitypb.EntityType_ENTITY_TYPE_UNSPECIFIED {
			existingSchema.EntityType = schemaUpdateData.EntityType
		}
		// Assuming the Status field exists on EntitySchema and uses RecordStatus enum
		if schemaUpdateData.Status != entitypb.RecordStatus_RECORD_STATUS_UNSPECIFIED {
			existingSchema.Status = schemaUpdateData.Status
		}
		if schemaUpdateData.UpdatedBy != "" {
			existingSchema.UpdatedBy = schemaUpdateData.UpdatedBy
		}
		if len(schemaUpdateData.Tags) > 0 { // Overwrites existing tags
			existingSchema.Tags = schemaUpdateData.Tags
		}

		// 3. Persist the updated schema
		updatedSchema, transactionErr = entityUsecase.entityRepo.UpdateEntitySchema(spanContext, tx, existingSchema) // Renamed txErr to transactionErr
		if transactionErr != nil {
			err := fmt.Errorf("failed to persist updated schema %s: %w", existingSchema.Id, transactionErr)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}
		return nil // Success
	})

	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update entity schema %s", updateReq.Schema.Id))
		return nil, fmt.Errorf("UpdateEntitySchema failed for ID %s: %w", updateReq.Schema.Id, err)
	}

	// Track updated schema version
	if updatedSchema != nil {
		span.SetTag("schema.updated_version", fmt.Sprintf("%d", updatedSchema.Version))
	}

	return updatedSchema, nil
}

// DeleteAllVersionsOfEntitySchema permanently removes all historical versions of a schema.
// Performed within a transaction.
func (entityUsecase *EntityUseCase) DeleteAllVersionsOfEntitySchema(
	ctx context.Context,
	schemaID string,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.DeleteAllVersionsOfEntitySchema")
	defer finishSpan()
	span.SetTag("schema.id", schemaID)

	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		return entityUsecase.entityRepo.DeleteAllVersionsOfEntitySchema(spanContext, tx, schemaID)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete entity schema")
		return fmt.Errorf("DeleteAllVersionsOfEntitySchema failed for ID %s: %w", schemaID, err)
	}
	return nil
}

// DeleteSpecificVersionOfEntitySchema permanently removes a single historical version of a schema.
// Performed within a transaction.
func (entityUsecase *EntityUseCase) DeleteSpecificVersionOfEntitySchema(
	ctx context.Context,
	schemaID string,
	version int32,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.DeleteSpecificVersionOfEntitySchema")
	defer finishSpan()

	span.SetTag("schema.id", schemaID)
	span.SetTag("schema.version", fmt.Sprintf("%d", version))

	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		return entityUsecase.entityRepo.DeleteSpecificVersionOfEntitySchema(spanContext, tx, schemaID, int32(version))
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete entity schema %s version %d", schemaID, version))
		return fmt.Errorf("DeleteSpecificVersionOfEntitySchema failed for ID %s, Version %d: %w", schemaID, version, err)
	}
	return nil
}

// ListLatestEntitySchemas retrieves a paginated list of the latest versions of schemas.
// Supports filtering by entity type and uses `defaultPageSize` if pageSize is invalid.
// Read-only, no transaction needed.
func (entityUsecase *EntityUseCase) ListLatestEntitySchemas(
	ctx context.Context,
	pageSize int32,
	pageToken string,
	entityTypeFilter entitypb.EntityType,
) ([]*entitypb.EntitySchema, string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.ListLatestEntitySchemas")
	defer finishSpan()

	effectivePageSize := pageSize
	if effectivePageSize <= 0 {
		effectivePageSize = defaultPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", effectivePageSize))
	span.SetTag("filter.entity_type", entityTypeFilter.String())

	listResult, err := entityUsecase.entityRepo.ListLatestEntitySchemas(spanContext, nil, int(effectivePageSize), pageToken, entityTypeFilter)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list entity schemas")
		return nil, "", fmt.Errorf("ListLatestEntitySchemas failed: %w", err)
	}
	if listResult == nil {
		return []*entitypb.EntitySchema{}, "", nil
	}
	// Ensure non-nil slice return on success
	schemas := listResult.Schemas
	if schemas == nil {
		schemas = []*entitypb.EntitySchema{}
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(schemas)))

	return schemas, listResult.PageToken, nil
}

// ListAllVersionsOfEntitySchema retrieves all historical versions of a specific schema.
// Read-only, no transaction needed.
func (entityUsecase *EntityUseCase) ListAllVersionsOfEntitySchema(
	ctx context.Context,
	schemaID string,
) ([]*entitypb.EntitySchemaVersion, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.ListAllVersionsOfEntitySchema")
	defer finishSpan()

	span.SetTag("schema.id", schemaID)

	versions, err := entityUsecase.entityRepo.ListAllVersionsOfEntitySchema(spanContext, nil, schemaID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to list all versions for entity schema %s", schemaID))
		return nil, fmt.Errorf("ListAllVersionsOfEntitySchema failed for ID %s: %w", schemaID, err)
	}
	if versions == nil {
		return []*entitypb.EntitySchemaVersion{}, nil
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(versions)))

	return versions, nil
}

// BulkCreateEntitySchemas creates multiple schemas within a single transaction.
func (entityUsecase *EntityUseCase) BulkCreateEntitySchemas(
	ctx context.Context,
	schemasToCreate []*entitypb.EntitySchema,
) ([]*entitypb.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.BulkCreateEntitySchemas")
	defer finishSpan()

	span.SetTag("schemas.count", fmt.Sprintf("%d", len(schemasToCreate)))

	if len(schemasToCreate) == 0 {
		return []*entitypb.EntitySchema{}, nil
	}

	var createdSchemas []*entitypb.EntitySchema
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		var transactionErr error // Renamed txErr to transactionErr
		createdSchemas, transactionErr = entityUsecase.entityRepo.BulkCreateEntitySchemas(spanContext, tx, schemasToCreate)
		return transactionErr
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk create entity schemas")
		return nil, fmt.Errorf("BulkCreateEntitySchemas failed: %w", err)
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(createdSchemas)))

	return createdSchemas, nil
}

// BulkUpdateEntitySchemas updates multiple schemas within a single transaction.
// Assumes replacement semantics unless the repository implements patching.
func (entityUsecase *EntityUseCase) BulkUpdateEntitySchemas(
	ctx context.Context,
	schemasToUpdate []*entitypb.EntitySchema,
) ([]*entitypb.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.BulkUpdateEntitySchemas")
	defer finishSpan()

	span.SetTag("schemas.count", fmt.Sprintf("%d", len(schemasToUpdate)))

	if len(schemasToUpdate) == 0 {
		return []*entitypb.EntitySchema{}, nil
	}

	var updatedSchemas []*entitypb.EntitySchema
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		var transactionErr error // Renamed txErr to transactionErr
		updatedSchemas, transactionErr = entityUsecase.entityRepo.BulkUpdateEntitySchemas(spanContext, tx, schemasToUpdate)
		return transactionErr
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk update entity schemas")
		return nil, fmt.Errorf("BulkUpdateEntitySchemas failed: %w", err)
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(updatedSchemas)))

	return updatedSchemas, nil
}

// BulkDeleteEntitySchemas deletes multiple schemas (all versions) by ID within a single transaction.
func (entityUsecase *EntityUseCase) BulkDeleteEntitySchemas(
	ctx context.Context,
	schemaIDs []string,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.BulkDeleteEntitySchemas")
	defer finishSpan()

	span.SetTag("schemas.count", fmt.Sprintf("%d", len(schemaIDs)))

	if len(schemaIDs) == 0 {
		return nil
	}

	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		return entityUsecase.entityRepo.BulkDeleteEntitySchemas(spanContext, tx, schemaIDs)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to bulk delete entity schemas")
		return fmt.Errorf("BulkDeleteEntitySchemas failed: %w", err)
	}
	return nil
}

// RestoreEntitySchemaVersion reverts a schema to a specific historical version.
// Creates a new version matching the content of the specified historical version.
// Performed within a transaction.
func (entityUsecase *EntityUseCase) RestoreEntitySchemaVersion(
	ctx context.Context,
	schemaID string,
	version int32,
) (*entitypb.EntitySchema, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.RestoreEntitySchemaVersion")
	defer finishSpan()

	span.SetTag("schema.id", schemaID)
	span.SetTag("schema.restore_version", fmt.Sprintf("%d", version))

	var restoredSchema *entitypb.EntitySchema
	err := entityUsecase.executeInTx(spanContext, func(tx *sql.Tx) error {
		var transactionErr error // Renamed txErr to transactionErr
		restoredSchema, transactionErr = entityUsecase.entityRepo.RestoreEntitySchemaVersion(spanContext, tx, schemaID, int32(version))
		return transactionErr
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to restore entity schema %s to version %d", schemaID, version))
		return nil, fmt.Errorf("RestoreEntitySchemaVersion failed for ID %s, Version %d: %w", schemaID, version, err)
	}

	// Track restored schema version
	if restoredSchema != nil {
		span.SetTag("schema.new_version", fmt.Sprintf("%d", restoredSchema.Version))
	}

	return restoredSchema, nil
}

// DiffEntitySchemaVersions computes the difference between two historical schema versions.
// Read-only, no transaction needed.
func (entityUsecase *EntityUseCase) DiffEntitySchemaVersions(
	ctx context.Context,
	schemaID string,
	version1, version2 int32,
) (string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.DiffEntitySchemaVersions")
	defer finishSpan()

	span.SetTag("schema.id", schemaID)
	span.SetTag("schema.version1", fmt.Sprintf("%d", version1))
	span.SetTag("schema.version2", fmt.Sprintf("%d", version2))

	diffResult, err := entityUsecase.entityRepo.DiffEntitySchemaVersions(spanContext, nil, schemaID, int32(version1), int32(version2)) // Renamed diff to diffResult
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to diff entity schema %s versions %d and %d", schemaID, version1, version2))
		return "", fmt.Errorf("DiffEntitySchemaVersions failed for ID %s between versions %d and %d: %w", schemaID, version1, version2, err)
	}
	return diffResult, nil
}

// CheckEntitySchemaPermissions verifies if a user can perform an action on a schema.
// Read-only, no transaction needed.
func (entityUsecase *EntityUseCase) CheckEntitySchemaPermissions(
	ctx context.Context,
	schemaID, userID, action string,
) (bool, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.CheckEntitySchemaPermissions")
	defer finishSpan()

	span.SetTag("schema.id", schemaID)
	span.SetTag("user.id", userID)
	span.SetTag("permission.action", action)

	allowed, err := entityUsecase.entityRepo.CheckEntitySchemaPermissions(spanContext, nil, schemaID, userID, action)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to check permissions for entity schema %s", schemaID))
		return false, fmt.Errorf("CheckEntitySchemaPermissions failed for ID %s, User %s, Action %s: %w", schemaID, userID, action, err)
	}

	span.SetTag("permission.allowed", fmt.Sprintf("%t", allowed))

	return allowed, nil
}

// ---------------------------
// Entity Search Use‑Cases
// ---------------------------

// SearchEntities performs a comprehensive search across entities with various filters and search options.
// This operation does not require a transaction as it's read-only.
func (entityUsecase *EntityUseCase) SearchEntities(
	ctx context.Context,
	searchRequest *entitypb.SearchEntitiesRequest,
) (*entitypb.SearchEntitiesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "EntityUseCase.SearchEntities")
	defer finishSpan()

	if searchRequest.PageSize <= 0 {
		searchRequest.PageSize = defaultPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", searchRequest.PageSize))
	if searchRequest.Query != "" {
		span.SetTag("search.query", searchRequest.Query)
	}
	if searchRequest.EntityTypes != nil {
		span.SetTag("filter.entity_type_count", fmt.Sprintf("%d", len(searchRequest.EntityTypes)))
	}

	// Pass nil for the transaction argument as this is a read-only operation.
	searchResponse, err := entityUsecase.entityRepo.SearchEntities(spanContext, nil, searchRequest)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to search entities")
		return nil, fmt.Errorf("SearchEntities failed: %w", err)
	}

	if searchResponse != nil && searchResponse.Entities != nil {
		span.SetTag("result.count", fmt.Sprintf("%d", len(searchResponse.Entities)))
	}

	return searchResponse, nil
}

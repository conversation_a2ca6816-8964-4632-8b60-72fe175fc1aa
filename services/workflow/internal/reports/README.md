## Report Module

In our system, a **Report** captures structured and unstructured findings following an incident or situation. Reports consist of multiple sections (narrative, entity lists, incident details, offenses, media attachments), global comments, a multi‑level review workflow, versioned snapshots, and **object relations** that connect entities, people, and events within the report context. The Report Module is responsible for creating, retrieving, updating, deleting reports; managing comments; orchestrating review rounds; handling arbitrary JSON metadata; fetching historical versions; **managing media file attachments via the filerepository service**; and **managing semantic relationships between report objects**.

Reports are tightly integrated with the Situations, Assets, and Entity modules. They help to:
- **Collect Findings:** Organize narrative, entities, incident snapshots, and media evidence.  
- **Review Workflow:** Support multi‑level review rounds with approvals and change requests.  
- **Collaborative Commentary:** Enable global and section‑specific comments.  
- **Audit Trail & Versioning:** Maintain timestamps and monotonic version counter; allow historical snapshots.
- **Media Management:** Attach and organize files (images, videos, documents) with metadata via filerepository integration. *(Note: Media content is not yet searchable)*
- **Object Relations:** Create semantic connections between any objects within a report (people, places, entities, incidents, media files, etc.).

### Review Workflow Features

The review workflow supports several advanced features:

#### Preferred Reviewer Selection
- Authors can preview eligible reviewers before submitting using `GetEligibleReviewers`
- When submitting for review, authors can specify a `preferred_reviewer_asset_id`
- The system validates that the preferred reviewer is eligible for the review level
- If the preferred reviewer is not eligible, the system falls back to automatic selection

#### Reviewer Assignment Logic
When assigning reviewers, the system follows this priority:
1. **Preferred Reviewer** - If specified and eligible, assigns the preferred reviewer
2. **Previous Reviewer** - For re-submissions at the same level, tries to assign the same reviewer
3. **Random Selection** - Randomly selects from eligible reviewers based on workflow configuration

#### Re-submission After Changes
When a reviewer requests changes:
- The reviewer can specify `sendToLevel` and `sendToAssetId` for routing
- Authors can make changes and re-submit with a preferred reviewer
- The system maintains review history and tracks all rounds

---

# Data Model Reference

## Enums

### ReportStatus

| Name                                | Value | Description                           |
|-------------------------------------|-------|---------------------------------------|
| REPORT_STATUS_UNSPECIFIED           | 0     | Default unset state.                  |
| REPORT_STATUS_ASSIGNED              | 1     | Task assigned to author.              |
| REPORT_STATUS_IN_PROGRESS           | 2     | Author is writing the report.         |
| REPORT_STATUS_SUBMITTED_FOR_REVIEW  | 3     | Submitted and awaiting review.        |
| REPORT_STATUS_UNDER_REVIEW          | 4     | Reviewer is actively reviewing.       |
| REPORT_STATUS_CHANGES_REQUESTED     | 5     | Reviewer requested changes.           |
| REPORT_STATUS_IN_REWORK             | 6     | Author reworking changes.             |
| REPORT_STATUS_APPROVED              | 7     | Report approved and finalized.        |
| REPORT_STATUS_REJECTED              | 8     | Report permanently rejected.          |
| REPORT_STATUS_CANCELLED             | 9     | Report process cancelled.             |

### ReportType

| Name                                | Value | Description                           |
|-------------------------------------|-------|---------------------------------------|
| REPORT_TYPE_UNSPECIFIED             | 0     | Default unset type.                   |
| REPORT_TYPE_INCIDENT_PRIMARY        | 1     | Primary incident report for an incident.|
| REPORT_TYPE_INCIDENT_SUPPLEMENTAL   | 2     | Supplemental incident report for an incident.|

### SectionType

| Name                              | Value | Description                                        |
|-----------------------------------|-------|----------------------------------------------------|
| SECTION_TYPE_UNSPECIFIED          | 0     | Default unset type                                 |
| SECTION_TYPE_NARRATIVE            | 1     | Free-form rich text (e.g., HTML/Markdown)          |
| SECTION_TYPE_INCIDENT_DETAILS     | 3     | Structured incident summary                        |
| SECTION_TYPE_ENTITY_LIST_PEOPLE   | 4     | List of references to people entities              |
| SECTION_TYPE_ENTITY_LIST_VEHICLE  | 5     | List of references to vehicle entities             |
| SECTION_TYPE_ENTITY_LIST_PROPERTIES| 6    | List of references to property entities            |
| SECTION_TYPE_OFFENSE              | 7     | Structured multiple offense information with metadata |
| SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS | 8 | List of references to organization entities        |
| SECTION_TYPE_ARREST              | 9     | Structured arrest information with metadata        |
| SECTION_TYPE_MEDIA               | 10    | Media attachments and file references with metadata |

### ReviewStatus

| Name                             | Value | Description              |
|----------------------------------|-------|--------------------------|
| REVIEW_STATUS_UNSPECIFIED        | 0     | Default unset.           |
| REVIEW_STATUS_AWAITING_ACTION    | 1     | Awaiting reviewer action.|
| REVIEW_STATUS_APPROVED           | 2     | Approved.                |
| REVIEW_STATUS_CHANGES_REQUESTED  | 3     | Changes requested.       |

---

## Messages

### ObjectReference

Flexible reference to any object within a report that can participate in relations. Uses string-based types for maximum flexibility without requiring protobuf changes.

**Validation:** At least one of `report_scoped_id`, `global_id`, or `external_id` MUST be set.

**ID Types:**
- `report_scoped_id`: IDs unique within this report (e.g., responder.id, reporting_person.id, section content IDs)
- `global_id`: System-wide unique IDs (e.g., asset_id, entity_id, case_id, situation_id)
- `external_id`: IDs from external systems or custom references

**Common object_type values:**
- `"entity"` — Reference to an entity (person, vehicle, property) - use global_id
- `"section"` — Reference to a report section - use global_id (section.id)
- `"offense"` — Reference to an offense within a section - use report_scoped_id
- `"incident_details"` — Reference to incident details within a section - use report_scoped_id
- `"narrative"` — Reference to narrative content within a section - use report_scoped_id
- `"comment"` — Reference to a comment - use global_id (comment.id)
- `"responder"` — Reference to a specific responder in incident details - use report_scoped_id
- `"reporting_person"` — Reference to the reporting person in incident details - use report_scoped_id
- `"asset"` — Reference to an asset - use global_id (asset_id)
- `"case"` — Reference to a case - use global_id (case_id)
- `"situation"` — Reference to a situation - use global_id (situation_id)
- `"media_content"` — Reference to media content within a section - use report_scoped_id
- `"file_reference"` — Reference to a file reference within media content - use report_scoped_id
- `"custom_*"` — Any custom object type

| Field            | Type                   | Description                                 |
|------------------|------------------------|---------------------------------------------|
| object_type      | string                 | Type of the referenced object (REQUIRED).  |
| report_scoped_id | string                 | ID unique within this report.              |
| global_id        | string                 | System-wide unique ID.                     |
| external_id      | string                 | External system ID or custom reference.    |
| section_id       | string                 | If object is within a section, the section ID for context. |
| display_name     | string                 | Human-readable name for the referenced object. |
| metadata         | google.protobuf.Struct | Additional metadata about the reference.   |

### Relation

Establishes a semantic connection between any two objects within a report. Uses string-based relation types for maximum flexibility. Relationships are bidirectional - both objects have equal weight.

**Validation:**
- `object_a` and `object_b` MUST be valid ObjectReference instances
- `relation_type` MUST be non-empty
- `object_a` and `object_b` SHOULD NOT reference the same object (no self-relations)

**Common relation_type values (contextualized for college campus incidents):**
- `"associated_with"` — General association between objects
- `"connected_to"` — Direct connection between objects
- `"related_to"` — General relation between objects
- `"involved_in"` — Both objects involved in same incident
- `"occurred_with"` — Objects occurred together in incident
- `"located_at"` — Object/person located at facility/room/building
- `"part_of"` — Object is part of another (e.g., room part of building)
- `"witnessed_by"` — Incident/event witnessed by person
- `"reported_by"` — Incident reported by person/student/staff
- `"responded_to_by"` — Incident responded to by officer/staff/security
- `"victim_of"` — Person was victim of incident/violation
- `"perpetrator_of"` — Person was perpetrator of incident/violation
- `"suspect_in"` — Person is suspect in incident
- `"complainant_in"` — Person filed complaint in incident
- `"violates_policy"` — Incident violates specific campus policy
- `"assigned_to"` — Case/incident assigned to officer/staff
- `"escalated_to"` — Incident escalated to higher authority
- `"follows_up_on"` — Report follows up on previous incident
- `"references"` — Object references another object
- `"occurred_in"` — Incident occurred in specific location/timeframe
- `"involves_substance"` — Incident involves drugs/alcohol
- `"involves_property"` — Incident involves campus property/equipment
- `"custom_*"` — Any custom relation type

| Field              | Type                   | Description                                 |
|--------------------|------------------------|---------------------------------------------|
| id                 | string                 | Unique relation identifier (auto-generated).|
| report_id          | string                 | The report this relation belongs to (REQUIRED). |
| object_a           | ObjectReference        | First object in the bidirectional relationship (REQUIRED). |
| object_b           | ObjectReference        | Second object in the bidirectional relationship (REQUIRED). |
| relation_type      | string                 | Semantic meaning of the relationship (REQUIRED). |
| description        | string                 | Optional human-readable description.        |
| metadata           | google.protobuf.Struct | Flexible JSON for additional relationship data. |
| created_at         | string                 | ISO8601 timestamp when relation was created. |
| updated_at         | string                 | ISO8601 timestamp for last relation update. |
| created_by_asset_id| string                 | Asset ID who created this relation (REQUIRED). |

### Comment

| Field              | Type    | Description                                 |
|--------------------|---------|---------------------------------------------|
| id                 | string  | Unique comment identifier.                  |
| reportId           | string  | ID of the report.                           |
| sectionId          | string  | *(Optional)* Section ID.                    |
| replyToCommentId   | string  | *(Optional)* Parent comment ID.             |
| authorAssetId      | string  | Asset ID of the comment author.             |
| displayName        | string  | *(Optional)* Display name of the author.    |
| text               | string  | Comment text content.                       |
| createdAt          | string  | Creation timestamp (ISO 8601 string).       |
| resolved           | bool    | Whether this comment has been resolved.     |
| resolvedAt         | string  | *(Optional)* Resolution timestamp.          |
| resolvedByAssetId  | string  | *(Optional)* Asset ID who resolved it.      |
| updatedAt          | string  | Last update timestamp (ISO 8601).           |
| resourceType       | string  | Constant "COMMENT".                         |

### NarrativeContent

| Field     | Type   | Description         |
|-----------|--------|---------------------|
| id        | string | Unique identifier for this narrative content. |
| richText  | string | The rich‑text blob. |

### OffenseContent

Section containing multiple individual offenses with structured data and metadata.

| Field        | Type                   | Description                          |
|--------------|------------------------|--------------------------------------|
| id           | string                 | Unique identifier for this offense content. |
| offense_type | string                 | Type or classification of offense.   |
| data         | google.protobuf.Struct | Flexible JSON structure for additional data. |
| schema       | google.protobuf.Struct | Schema for the offense content.     |

### EntityListContent

| Field      | Type                              | Description           |
|------------|-----------------------------------|-----------------------|
| id         | string                            | Unique identifier for this entity list content. |
| title      | string                            | Section title.        |
| entityRefs | repeated hero.entity.v1.Reference | Entity references.    |

### MediaContent

Section containing media files with structured metadata and links to the filerepository service.

| Field      | Type                              | Description                          |
|------------|-----------------------------------|--------------------------------------|
| id         | string                            | Unique identifier for this media content. |
| title      | string                            | Section title/header.                |
| file_refs  | repeated FileReference            | Array of file references.            |
| metadata   | google.protobuf.Struct            | Section-level metadata.              |

### FileReference

Individual file reference with metadata linking to the filerepository service.

| Field         | Type                   | Description                                 |
|---------------|------------------------|---------------------------------------------|
| id            | string                 | Unique identifier for this file reference. |
| file_id       | string                 | File ID in the filerepository service.     |
| caption       | string                 | Optional description/caption.               |
| display_name  | string                 | Optional display name for the file.        |
| display_order | int32                  | UI ordering (lower values first).          |
| file_category | string                 | File category (image/video/audio/document/other). |
| metadata      | google.protobuf.Struct | Additional JSON metadata for the file.     |

### Reference

| Field       | Type   | Description                                               |
|-------------|--------|-----------------------------------------------------------|
| id          | string | Unique identifier for the referenced object.              |
| type        | string | Type of the referenced object (e.g., "entity", "report"). |
| version     | int32  | Version of the referenced object.                         |
| displayName | string | *(Optional)* Display name for the reference.              |

### IncidentDetailsContent

Enhanced incident details with comprehensive location information and involved parties.

| Field                           | Type                              | Description                         |
|---------------------------------|-----------------------------------|-------------------------------------|
| id                              | string                            | Unique identifier for this incident details content. |
| initialType                     | hero.situations.v2.SituationType | Original situation type.            |
| incident_start_time             | string                            | Incident start time (ISO 8601).     |
| incident_end_time               | string                            | Incident end time (ISO 8601).       |
| reported_time                   | string                            | ISO8601 reported time.              |
| incident_location_clery_type    | string                            | CLERY location type (Optional).     |
| incident_location_street_address| string                           | Street address.                     |
| incident_location_unit_info     | string                            | Apt/Suite/Unit # (Optional).        |
| incident_location_type          | string                            | Location type (e.g., Dorm, Building). |
| incident_location_common_name   | string                            | Common name for the location (Optional). |
| incident_location_city          | string                            | City.                               |
| incident_location_state         | string                            | State.                              |
| incident_location_zip_code      | string                            | Zip code.                           |
| incident_location_country       | string                            | Country.                            |
| incident_location_latitude      | double                            | Latitude coordinate.                |
| incident_location_longitude     | double                            | Longitude coordinate.               |
| responders                      | repeated IncidentResponder        | Involved responders list.           |
| reporting_person                | ReportingPerson                   | Who reported the incident.          |
| finalType                       | hero.situations.v2.SituationType | Final situation type.               |
| involved_agencies               | repeated InvolvedAgency           | Involved agencies list.             |
| description                     | string                            | Description of the incident.        |

#### IncidentResponder

| Field        | Type   | Description                |
|--------------|--------|----------------------------|
| id           | string | Report-scoped unique identifier for this responder. |
| assetId      | string | ID of the responder asset. |
| displayName  | string | Display name of responder. |
| role         | string | Role/title of responder.   |

#### ReportingPerson

| Field        | Type   | Description                      |
|--------------|--------|----------------------------------|
| id           | string | Report-scoped unique identifier for this reporting person. |
| assetId      | string | ID of the reporting asset.       |
| firstName    | string | Reporter first name.             |
| middleName   | string | Reporter middle name (optional). |
| lastName     | string | Reporter last name.              |
| phoneNumber  | string | Reporter contact number.         |
| reporterRole | string | *(Optional)* Role of the reporter. |

#### InvolvedAgency

| Field                         | Type   | Description                    |
|-------------------------------|--------|--------------------------------|
| id                            | string | Unique identifier for the content. |
| agency_name                   | string | Name of the agency.            |
| incident_reference_number     | string | Agency incident reference number. |

### ReportSection

| Field    | Type                | Description                                         |
|----------|---------------------|-----------------------------------------------------|
| id       | string              | Unique section ID.                                  |
| type     | SectionType         | Section type.                                       |
| content  | oneof               | Section content (narrative, entityList, incidentDetails, offenseList, mediaList). |
| comments | repeated Comment    | Section‑specific comments.                          |
| createdAt| string             | Creation timestamp (ISO 8601).                      |
| updatedAt| string             | Last update timestamp (ISO 8601).                   |
| reportId | string             | Parent report ID.                                   |

### ReviewRound

| Field            | Type          | Description                                |
|------------------|---------------|--------------------------------------------|
| id               | string        | Unique review round ID.                    |
| reportId         | string        | Parent report ID.                          |
| reviewerAssetId  | string        | Assigned reviewer asset ID.                |
| level            | int32         | Review hierarchy level.                    |
| status           | ReviewStatus  | Current status.                            |
| sentToLevel      | int32         | Next routing level (0=author).             |
| sentToAssetId    | string        | Specific asset for routing.                |
| requestedAt      | string        | Assignment timestamp (ISO 8601).           |
| resolvedAt       | string        | Resolution timestamp (ISO 8601).           |
| roundNote        | string        | Reviewer note.                             |
| snapshotVersion  | int32         | Report.version at round start.             |
| dueAt            | string        | *(Optional)* Due date (ISO 8601).          |
| createByAssetId  | string        | Asset ID of the user who created the round.|
| noteForReviewer  | string        | Optional instructions/note.                |

### Report

| Field               | Type                 | Description                                                |
|---------------------|----------------------|------------------------------------------------------------|
| id                  | string               | Unique report ID.                                          |
| orgId               | int32                | Owning organization ID.                                    |
| authorAssetId       | string               | Asset ID of author.                                        |
| title               | string               | Report title.                                              |
| sections            | repeated ReportSection | List of sections.                                          |
| status              | ReportStatus         | Report status.                                             |
| reviewRounds        | repeated ReviewRound | Review history.                                            |
| relations           | repeated Relation    | Object relationships within the report.                    |
| comments            | repeated Comment     | Global comments.                                           |
| assignedAt          | string               | Assignment timestamp (ISO 8601).                          |
| updatedAt           | string               | Last update timestamp (ISO 8601).                         |
| completedAt         | string               | Completion timestamp (ISO 8601).                          |
| resourceType        | string               | Fixed value `"REPORT"`.                                   |
| additionalInfoJson  | google.protobuf.Struct | Arbitrary JSON metadata.                                  |
| version             | int32                | Monotonic version counter.                                |
| situationId         | string               | *(Optional)* ID of the situation this report is based on. |
| caseId              | string               | *(Optional)* ID of the case this report is associated with.|
| watcherAssetIds     | repeated string      | Asset IDs of watchers.                                     |
| createdAt           | string               | Creation timestamp (ISO 8601).                            |
| createdByAssetId    | string               | Asset ID of the user who created the report.              |
| reportType          | ReportType           | Type of the report.                                        |

### ReportSnapshot

| Field      | Type    | Description                                    |
|------------|---------|------------------------------------------------|
| reportId   | string  | Parent report ID.                              |
| version    | int32   | Version number.                                |
| report     | Report  | Full embedded report state at that version.    |
| createdAt  | string  | Snapshot creation timestamp (ISO 8601).        |

---

## Overview of Report object

```json
{
  "id": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
  "orgId": 42,
  "authorAssetId": "asset_ad3f4c71",
  "title": "Structure-Fire Incident – 123 Main St",
  "reportType": "REPORT_TYPE_INCIDENT_PRIMARY",

  /* --------------------------------------------------------------------- */
  "sections": [
    /* ---------- Section 1 : Narrative ---------------------------------- */
    {
      "id": "section_a9af36cb",
      "type": "SECTION_TYPE_NARRATIVE",
      "narrative": {
        "id": "narrative_01",
        "richText": "<p>Arrived on scene at 22:14 hrs; fire showing from second-floor windows.</p>"
      },
      "comments": [
        {
          "id": "comment_sec_01",
          "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
          "sectionId": "section_a9af36cb",
          "replyToCommentId": "",
          "authorAssetId": "asset_cf12b0e4",
          "displayName": "Lt. Reynolds",
          "text": "Please clarify ventilation tactics in paragraph 3.",
          "createdAt": "2025-04-23T09:17:00Z",
          "resolved": false,
          "resolvedAt": "",
          "resolvedByAssetId": "",
          "updatedAt": "2025-04-23T09:17:00Z",
          "resourceType": "COMMENT"
        }
      ],
      "createdAt": "2025-04-23T09:15:35Z",
      "updatedAt": "2025-04-23T09:18:02Z",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b"
    },

    /* ---------- Section 2 : Entity List (People) ----------------------- */
    {
      "id": "section_c6e57d28",
      "type": "SECTION_TYPE_ENTITY_LIST_PEOPLE",
      "entityList": {
        "id": "entity_list_01",
        "title": "Crew Members on Scene",
        "entityRefs": [
          {
            "entityId": "asset_engine51",
            "entityType": "VEHICLE",
            "displayName": "Engine 51"
          },
          {
            "entityId": "asset_truck21",
            "entityType": "VEHICLE",
            "displayName": "Truck 21"
          }
        ]
      },
      "comments": [],
      "createdAt": "2025-04-23T09:19:11Z",
      "updatedAt": "2025-04-23T09:19:11Z",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b"
    },

    /* ---------- Section 3 : Incident Details -------------------------- */
    {
      "id": "section_e67d9af3",
      "type": "SECTION_TYPE_INCIDENT_DETAILS",
      "incidentDetails": {
        "id": "incident_details_01",
        "initialType": "SITUATION_TYPE_FIRE",
        "incident_start_time": "2025-04-22T22:00:00Z",
        "incident_end_time": "2025-04-22T22:30:00Z",
        "reported_time": "2025-04-22T22:05:00Z",
        "incident_location_street_address": "123 Main St",
        "incident_location_city": "Springfield",
        "incident_location_state": "OR",
        "incident_location_zip_code": "97477",
        "incident_location_type": "Residential Building",
        "responders": [
          {
            "id": "responder_01",
            "assetId": "asset_engine51",
            "displayName": "Engine 51",
            "role": "Primary Engine"
          },
          {
            "id": "responder_02",
            "assetId": "asset_truck21",
            "displayName": "Truck 21",
            "role": "Ventilation Truck"
          }
        ],
        "reporting_person": {
          "id": "reporting_person_01",
          "assetId": "asset_citizen8899",
          "firstName": "Jamie",
          "middleName": "",
          "lastName": "Nguyen",
          "phoneNumber": "******-555-0099",
          "reporterRole": "Witness"
        },
        "finalType": "SITUATION_TYPE_FIRE",
        "involved_agencies": [
          {
            "id": "agency_01",
            "agency_name": "Springfield Fire Department",
            "incident_reference_number": "SFD-2025-001234"
          }
        ],
        "description": "The quick brown fox jumped over the lazy dog. ".
      },
      "comments": [],
      "createdAt": "2025-04-23T09:20:27Z",
      "updatedAt": "2025-04-23T09:20:27Z",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b"
    },

    /* ---------- Section 4 : Offense ------------------------------------ */
    {
      "id": "section_f78e8bf4",
      "type": "SECTION_TYPE_OFFENSE",
      "offenseList": {
        "id": "offense_content_01",
        "offenses": [
          {
            "id": "offense-01-1",
            "offense_type": "Property Damage",
            "data": {
              "estimated_damage": "$15000",
              "property_affected": "Second floor bedroom",
              "severity": "Major"
            },
            "schema": {
              "version": "1.0",
              "fields": ["estimated_damage", "property_affected", "severity"]
            }
          },
          {
            "id": "offense-01-2", 
            "offense_type": "Fire Code Violation",
            "data": {
              "violation_code": "FC-12.3",
              "description": "Blocked fire exit",
              "citation_required": true
            },
            "schema": {
              "version": "1.0",
              "fields": ["violation_code", "description", "citation_required"]
            }
          }
        ],
        "metadata": {
          "total_offenses": 2,
          "primary_classification": "Property Damage",
          "investigation_required": true
        }
      },
      "comments": [],
      "createdAt": "2025-04-23T09:21:15Z",
      "updatedAt": "2025-04-23T09:21:15Z",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b"
    },

    /* ---------- Section 5 : Arrest ------------------------------------ */
    {
      "id": "section_a7b2c9d3",
      "type": "SECTION_TYPE_ARREST",
      "arrestList": {
        "id": "arrest_content_01",
        "arrests": [
          {
            "id": "arrest-01-1",
            "arrest_type": "Criminal",
            "data": {
              "arrest_date": "2025-04-23T08:15:00Z",
              "arresting_officer": "John Smith",
              "charges": ["Burglary", "Property Damage"],
              "bail_amount": "$25000",
              "booking_number": "BK-2025-0423-001"
            },
            "schema": {
              "version": "1.0",
              "fields": ["arrest_date", "arresting_officer", "charges", "bail_amount", "booking_number"]
            }
          }
        ],
        "metadata": {
          "total_arrests": 1,
          "primary_charge": "Burglary",
          "case_number": "CR-2025-0423-001"
        }
      },
      "comments": [],
      "createdAt": "2025-04-23T09:21:15Z",
      "updatedAt": "2025-04-23T09:21:15Z",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b"
    },

    /* ---------- Section 6 : Media ------------------------------------ */
    {
      "id": "section_m8c4e1f7",
      "type": "SECTION_TYPE_MEDIA",
      "mediaList": {
        "id": "media_content_01",
        "title": "Scene Documentation",
        "fileRefs": [
          {
            "id": "file_ref_01",
            "fileId": "file_abc123def456",
            "caption": "Front entrance showing forced entry damage",
            "displayName": "front_entrance.jpg",
            "displayOrder": 1,
            "fileCategory": "image",
            "metadata": {
              "timestamp": "2025-04-23T08:00:00Z",
              "camera_model": "Canon EOS R5",
              "location": "Main entrance",
              "evidence_tag": "E-2025-001"
            }
          },
          {
            "id": "file_ref_02",
            "fileId": "file_def789ghi012",
            "caption": "Security camera footage from 07:45-08:15",
            "displayName": "security_footage.mp4",
            "displayOrder": 2,
            "fileCategory": "video",
            "metadata": {
              "duration_seconds": 1800,
              "resolution": "1920x1080",
              "frame_rate": 30,
              "evidence_tag": "E-2025-002"
            }
          }
        ],
        "metadata": {
          "total_files": 2,
          "evidence_chain": "Chain of custody maintained by Officer Smith",
          "review_status": "Approved for inclusion"
        }
      },
      "comments": [],
      "createdAt": "2025-04-23T09:22:30Z",
      "updatedAt": "2025-04-23T09:22:30Z",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b"
    }
  ],

  /* --------------------------------------------------------------------- */
  "status": "REPORT_STATUS_IN_PROGRESS",

  "reviewRounds": [
    {
      "id": "review_round_01",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "reviewerAssetId": "asset_captain01",
      "level": 1,
      "status": "REVIEW_STATUS_AWAITING_ACTION",
      "sentToLevel": 0,
      "sentToAssetId": "",
      "requestedAt": "2025-04-23T09:25:00Z",
      "resolvedAt": "",
      "roundNote": "",
      "snapshotVersion": 1,
      "dueAt": "2025-04-24T09:00:00Z",
      "createByAssetId": "asset_ad3f4c71",
      "noteForReviewer": "Focus on NFPA-compliance wording, please."
    }
  ],

  /* --------------------------------------------------------------------- */
  "relations": [
    {
      "id": "relation_01",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "object_a": {
        "object_type": "responder",
        "report_scoped_id": "responder_01",
        "section_id": "section_e67d9af3",
        "display_name": "Engine 51"
      },
      "object_b": {
        "object_type": "entity",
        "global_id": "asset_engine51",
        "display_name": "Engine 51"
      },
      "relation_type": "responded_to_by",
      "description": "Engine 51 responded to this fire incident",
      "created_at": "2025-04-23T09:22:00Z",
      "updated_at": "2025-04-23T09:22:00Z",
      "created_by_asset_id": "asset_ad3f4c71"
    },
    {
      "id": "relation_02",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "object_a": {
        "object_type": "reporting_person",
        "report_scoped_id": "reporting_person_01",
        "section_id": "section_e67d9af3",
        "display_name": "Jamie Nguyen"
      },
      "object_b": {
        "object_type": "incident_details",
        "report_scoped_id": "incident_details_01",
        "section_id": "section_e67d9af3",
        "display_name": "Fire Incident"
      },
      "relation_type": "witnessed_by",
      "description": "Jamie Nguyen witnessed the fire incident",
      "created_at": "2025-04-23T09:22:30Z",
      "updated_at": "2025-04-23T09:22:30Z",
      "created_by_asset_id": "asset_ad3f4c71"
    },
    {
      "id": "relation_03",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "object_a": {
        "object_type": "file_reference",
        "report_scoped_id": "file_ref_01",
        "section_id": "section_m8c4e1f7",
        "display_name": "front_entrance.jpg"
      },
      "object_b": {
        "object_type": "incident_details",
        "report_scoped_id": "incident_details_01",
        "section_id": "section_e67d9af3",
        "display_name": "Fire Incident"
      },
      "relation_type": "documents_evidence_of",
      "description": "Photo documents physical evidence from the fire incident",
      "created_at": "2025-04-23T09:23:00Z",
      "updated_at": "2025-04-23T09:23:00Z",
      "created_by_asset_id": "asset_ad3f4c71"
    }
  ],

  /* --------------- Global (report-level) comments ---------------------- */
  "comments": [
    {
      "id": "comment_global_02",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "sectionId": "",
      "replyToCommentId": "",
      "authorAssetId": "asset_divchief07",
      "displayName": "Division Chief Mills",
      "text": "Make sure the final narrative references the new SOP 13-B.",
      "createdAt": "2025-04-23T09:30:10Z",
      "resolved": false,
      "resolvedAt": "",
      "resolvedByAssetId": "",
      "updatedAt": "2025-04-23T09:30:10Z",
      "resourceType": "COMMENT"
    }
  ],

  /* --------------------------------------------------------------------- */
  "assignedAt": "2025-04-22T22:05:00Z",
  "updatedAt": "2025-04-23T09:30:10Z",
  "completedAt": "",
  "resourceType": "REPORT",

  "additionalInfoJson": {
    "severity": "High",
    "weather": {
      "temperatureF": 55,
      "windMph": 4
    },
    "tags": ["Structure Fire", "Residential", "Night"]
  },

  "version": 2,
  "situationId": "situation_7b3c9e2e",
  "caseId": "case_f2b71d90",
  "watcherAssetIds": [
    "asset_training_officer01",
    "asset_firemarshal03"
  ],
  "createdAt": "2025-04-22T22:04:45Z",
  "createdByAssetId": "asset_ad3f4c71"
}
```

---

## Overview of Endpoints

1. [CreateReport](#1-createreport)
2. [GetReport](#2-getreport)
3. [UpdateReport](#3-updatereport)
4. [ListReports](#4-listreports)
5. [BatchGetReports](#5-batchgetreports)
6. [DeleteReport](#6-deletereport)
7. [CreateReportSection](#7-createreportsection)
8. [GetReportSection](#8-getreportsection)
9. [UpdateReportSection](#9-updatereportsection)
10. [DeleteReportSection](#10-deletereportsection)
11. [ListReportSections](#11-listreportsections)
12. [AddComment](#12-addcomment)
13. [UpdateComment](#13-updatecomment)
14. [DeleteComment](#14-deletecomment)
15. [GetComments](#15-getcomments)
16. [ResolveComment](#16-resolvecomment)
17. [SubmitForReview](#17-submitforreview)
18. [GetEligibleReviewers](#18-geteligiblereviewers)
19. [AddReviewRound](#19-addreviewround)
20. [GetReviewRound](#20-getreviewround)
21. [UpdateReviewRound](#21-updatereviewround)
22. [DeleteReviewRound](#22-deletereviewround)
23. [ApproveReviewRound](#23-approvereviewround)
24. [RequestChanges](#24-requestchanges)
25. [UpdateAdditionalInfoJson](#25-updateadditionalinfojson)
26. [GetAdditionalInfo](#26-getadditionalinfo)
27. [GetReportVersion](#27-getreportversion)
28. [ListReportVersions](#28-listreportversions)
29. [ListReportsBySituationId](#29-listreportsbysituationid)
30. [ListReportsByCaseId](#30-listreportsbycaseid)
31. [ListReviewRoundsForReport](#31-listreviewroundsforreport)
32. [SearchReports](#32-searchreports)
33. [CreateRelation](#33-createrelation)
34. [GetRelation](#34-getrelation)
35. [UpdateRelation](#35-updaterelation)
36. [DeleteRelation](#36-deleterelation)
37. [ListRelations](#37-listrelations)

---

### 1. CreateReport

**Method:** `CreateReport`  
**Route:** `POST /hero.reports.v2.ReportService/CreateReport`

#### Request

**CreateReportRequest:**

| Field  | Type   | Description                                                |
|--------|--------|------------------------------------------------------------|
| report | Report | Report object to create. **Omit:** `id`, timestamps, `version`. |

**Sample Request (JSON):**
```json
{
  "report": {
    "title": "Incident Alpha Analysis",
    "sections": [],
    "additionalInfoJson": {}
  }
}
```

#### Response

**CreateReportResponse:**

| Field  | Type   | Description                                                          |
|--------|--------|----------------------------------------------------------------------|
| report | Report | Created report with generated `id`, `assignedAt`, `updatedAt`, `version=1`, etc. |

**Sample Response (JSON):**
```json
{
    "report": {
        "id": "ae93af54-ced8-41a7-a9a7-828f22087af1",
        "orgId": 1,
        "authorAssetId": "cognito:88714360-20f1-706d-ac7b-df9b540bd0b6",
        "title": "Incident Alpha Analysis",
        "assignedAt": "2025-04-23T18:27:12.410737463Z",
        "updatedAt": "2025-04-23T18:27:12.410737463Z",
        "resourceType": "REPORT",
        "additionalInfoJson": {},
        "version": 1,
        "createdAt": "2025-04-23T18:27:12.410737463Z"
    }
}
```

---

### 2. GetReport

**Method:** `GetReport`  
**Route:** `POST /hero.reports.v2.ReportService/GetReport`

#### Request

**GetReportRequest:**

| Field | Type   | Description                   |
|-------|--------|-------------------------------|
| id    | string | Unique report identifier.     |

**Sample Request (JSON):**
```json
{
  "id": "ae93af54-ced8-41a7-a9a7-828f22087af1"
}
```

#### Response

Returns the latest `Report` object.

**Sample Response (JSON):**
```json
{
    "id": "ae93af54-ced8-41a7-a9a7-828f22087af1",
    "orgId": 1,
    "authorAssetId": "cognito:88714360-20f1-706d-ac7b-df9b540bd0b6",
    "title": "Incident Figma Analysis",
    "sections": [
        {
            "id": "0a3b0773-3704-432a-b392-44880faeef21",
            "type": "SECTION_TYPE_NARRATIVE",
            "narrative": {
                "richText": "<p>New findings...</p>"
            },
            "createdAt": "2025-04-23T20:02:43.221403Z",
            "updatedAt": "2025-04-23T20:02:43.221403Z",
            "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1"
        },
        {
            "id": "7e900328-be4f-4334-aa05-30cd79e05d0c",
            "type": "SECTION_TYPE_NARRATIVE",
            "narrative": {
                "richText": "<p>Updated findings...</p>"
            },
            "createdAt": "2025-04-23T19:33:27.75145Z",
            "updatedAt": "2025-04-23T20:01:32.347759Z",
            "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1"
        }
    ],
    "status": "REPORT_STATUS_IN_PROGRESS",
    "assignedAt": "2025-04-23T18:27:12.410737Z",
    "updatedAt": "2025-04-23T18:48:39.637721Z",
    "resourceType": "REPORT",
    "additionalInfoJson": {},
    "version": 7,
    "createdAt": "2025-04-23T18:27:12.410737Z"
}
```

---

### 3. UpdateReport

**Method:** `UpdateReport`  
**Route:** `POST /hero.reports.v2.ReportService/UpdateReport`

#### Request

**UpdateReportRequest:**

| Field  | Type   | Description                                                |
|--------|--------|------------------------------------------------------------|
| report | Report | Report object with the fields to update. **Must include** `id`. Only non-empty/non-default values will be updated. |

**Updatable Fields:**
- `title` - Report title
- `status` - Report status (use specific enum values, not UNSPECIFIED)
- `author_asset_id` - Asset ID of the report author
- `situation_id` - Link to situation entity
- `case_id` - Link to case entity  
- `watcher_asset_ids` - Array of asset IDs subscribed for updates
- `additional_info_json` - JSON metadata (recursively merged with existing data)

**Auto-managed Fields:**
- `updated_at` - Automatically set to current timestamp
- `completed_at` - Automatically set based on status transitions
- `version` - Automatically incremented for optimistic locking

**Field Update Behavior:**
- The usecase fetches the existing report and merges only provided fields
- Empty/default values in the request are ignored (existing values preserved)
- `additional_info_json` is recursively merged with existing JSON data
- `watcher_asset_ids` array replaces the existing array if provided

**Sample Request (JSON):**
```json
{
    "report": {
        "id": "ae93af54-ced8-41a7-a9a7-828f22087af1",
        "title": "Updated Incident Analysis Report",
        "status": "REPORT_STATUS_IN_PROGRESS",
        "author_asset_id": "asset-456",
        "case_id": "case-12345",
        "additional_info_json": {
            "priority": "high",
            "tags": ["urgent", "follow-up"]
        }
    }
}
```

#### Response

Returns the complete updated `Report` object with `version` incremented and `updated_at` refreshed.

**Sample Response (JSON):**
```json
{
    "id": "ae93af54-ced8-41a7-a9a7-828f22087af1",
    "title": "Updated Incident Analysis Report",
    "status": "REPORT_STATUS_IN_PROGRESS",
    "author_asset_id": "asset-456",
    "case_id": "case-12345",
    "assignedAt": "2025-04-23T18:27:12.410737Z",
    "updatedAt": "2025-04-23T18:44:23.099114135Z",
    "resourceType": "REPORT",
    "additional_info_json": {
        "priority": "high",
        "tags": ["urgent", "follow-up"]
    },
    "version": 6,
    "createdAt": "2025-04-23T18:27:12.410737Z"
}
```

---

### 4. ListReports

**Method:** `ListReports`  
**Route:** `POST /hero.reports.v2.ReportService/ListReports`

#### Request

**ListReportsRequest:**

| Field     | Type         | Description                                 |
|-----------|--------------|---------------------------------------------|
| pageSize  | int32        | Maximum number of reports to return.        |
| pageToken | string       | Pagination token for a specific page.       |
| status    | ReportStatus | *(Optional)* Filter by report status.       |
| orgId     | int32        | *(Optional)* Filter by organization ID.     |

**Sample Request (JSON):**
```json
{
  "pageSize": 2,
  "pageToken": "",
  "status": "REPORT_STATUS_IN_PROGRESS"
}
```

#### Response

**ListReportsResponse:**

| Field           | Type               | Description                      |
|-----------------|--------------------|----------------------------------|
| reports         | repeated Report   | List of `Report` objects.        |
| nextPageToken   | string             | Token for the next page, if any. |

**Sample Response (JSON):**
```json
{
    "reports": [
        {
            "id": "63daec8e-957f-4863-818e-3785292bf48a",
            "orgId": 1,
            "authorAssetId": "cognito:88714360-20f1-706d-ac7b-df9b540bd0b6",
            "title": "Incident Alpha Analysis",
            "status": "REPORT_STATUS_IN_PROGRESS",
            "assignedAt": "2025-04-23T18:50:06.982683Z",
            "updatedAt": "2025-04-23T18:50:06.982683Z",
            "resourceType": "REPORT",
            "additionalInfoJson": {},
            "version": 1,
            "createdAt": "2025-04-23T18:50:06.982683Z"
        },
        {
            "id": "ae93af54-ced8-41a7-a9a7-828f22087af1",
            "orgId": 1,
            "authorAssetId": "cognito:88714360-20f1-706d-ac7b-df9b540bd0b6",
            "title": "Incident Figma Analysis",
            "status": "REPORT_STATUS_IN_PROGRESS",
            "assignedAt": "2025-04-23T18:27:12.410737Z",
            "updatedAt": "2025-04-23T18:48:39.637721Z",
            "resourceType": "REPORT",
            "additionalInfoJson": {},
            "version": 7,
            "createdAt": "2025-04-23T18:27:12.410737Z"
        }
    ],
    "nextPageToken": "2"
}
```

---

### 5. BatchGetReports

**Method:** `BatchGetReports`  
**Route:** `POST /hero.reports.v2.ReportService/BatchGetReports`

#### Request

**BatchGetReportsRequest:**

| Field      | Type            | Description                               |
|------------|-----------------|-------------------------------------------|
| reportIds  | repeated string | List of report IDs to fetch (max ~100).   |

**Sample Request (JSON):**
```json
{
  "reportIds": ["63daec8e-957f-4863-818e-3785292bf48a", "ae93af54-ced8-41a7-a9a7-828f22087af1"]
}
```

#### Response

**BatchGetReportsResponse:**

| Field   | Type            | Description                               |
|---------|-----------------|-------------------------------------------|
| reports | repeated Report | List of found reports (in request order). |

**Sample Response (JSON):**
```json
{
    "reports": [
        {
            "id": "63daec8e-957f-4863-818e-3785292bf48a",
            "orgId": 1,
            "authorAssetId": "cognito:88714360-20f1-706d-ac7b-df9b540bd0b6",
            "title": "Incident Alpha Analysis",
            "status": "REPORT_STATUS_IN_PROGRESS",
            "assignedAt": "2025-04-23T18:50:06.982683Z",
            "updatedAt": "2025-04-23T18:50:06.982683Z",
            "resourceType": "REPORT",
            "additionalInfoJson": {},
            "version": 1,
            "createdAt": "2025-04-23T18:50:06.982683Z"
        },
        {
            "id": "ae93af54-ced8-41a7-a9a7-828f22087af1",
            "orgId": 1,
            "authorAssetId": "cognito:88714360-20f1-706d-ac7b-df9b540bd0b6",
            "title": "Incident Figma Analysis",
            "status": "REPORT_STATUS_IN_PROGRESS",
            "assignedAt": "2025-04-23T18:27:12.410737Z",
            "updatedAt": "2025-04-23T18:48:39.637721Z",
            "resourceType": "REPORT",
            "additionalInfoJson": {},
            "version": 7,
            "createdAt": "2025-04-23T18:27:12.410737Z"
        }
    ]
}
```

---

### 6. DeleteReport

**Method:** `DeleteReport`  
**Route:** `POST /hero.reports.v2.ReportService/DeleteReport`

#### Request

**DeleteReportRequest:**

| Field | Type   | Description               |
|-------|--------|---------------------------|
| id    | string | Unique report identifier. |

**Sample Request (JSON):**
```json
{ "id": "63daec8e-957f-4863-818e-3785292bf48a" }
```

#### Response

Returns `google.protobuf.Empty`.

**Sample Response (JSON):**
```json
{}
```

---

### 7. CreateReportSection

**Method:** `CreateReportSection`  
**Route:** `POST /hero.reports.v2.ReportService/CreateReportSection`

#### Request

**CreateReportSectionRequest:**

| Field    | Type          | Description                    |
|----------|---------------|--------------------------------|
| reportId | string        | Parent report ID.              |
| section  | ReportSection | Section object to create.      |

**Sample Request (JSON):**
```json
{
  "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1",
  "section": {
    "type": "SECTION_TYPE_NARRATIVE",
    "narrative": {
      "richText": "<p>Initial findings...</p>"
    }
  }
}
```

**Sample Media Section Request (JSON):**
```json
{
  "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1",
  "section": {
    "type": "SECTION_TYPE_MEDIA",
    "mediaList": {
      "title": "Evidence Photos",
      "fileRefs": [
        {
          "fileId": "file_abc123def456",
          "caption": "Scene photo showing entry point",
          "displayName": "scene_photo_1.jpg",
          "displayOrder": 1,
          "fileCategory": "image",
          "metadata": {
            "timestamp": "2025-04-23T08:00:00Z",
            "evidence_tag": "E-2025-001"
          }
        }
      ],
      "metadata": {
        "photographer": "Officer Johnson",
        "location": "Main entrance"
      }
    }
  }
}
```

#### Response

Returns the created `ReportSection` with generated ID and timestamps.

**Sample Response (JSON):**
```json
{
    "id": "7e900328-be4f-4334-aa05-30cd79e05d0c",
    "type": "SECTION_TYPE_NARRATIVE",
    "narrative": {
        "richText": "<p>Initial findings...</p>"
    },
    "createdAt": "2025-04-23T19:33:27.751450341Z",
    "updatedAt": "2025-04-23T19:33:27.751450341Z"
}

```
 
---

### 8. GetReportSection

**Method:** `GetReportSection`  
**Route:** `POST /hero.reports.v2.ReportService/GetReportSection`

#### Request

**GetReportSectionRequest:**

| Field     | Type   | Description        |
|-----------|--------|--------------------|
| reportId  | string | Parent report ID.  |
| sectionId | string | Target section ID. |

**Sample Request (JSON):**
```json
{
  "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1",
  "sectionId": "7e900328-be4f-4334-aa05-30cd79e05d0c"
}
```
#### Response

Returns the requested `ReportSection` including its comments.

**Sample Response (JSON):**
```json
{
    "id": "7e900328-be4f-4334-aa05-30cd79e05d0c",
    "type": "SECTION_TYPE_NARRATIVE",
    "narrative": {
        "richText": "<p>Updated findings...</p>"
    },
    "createdAt": "2025-04-23T19:33:27.75145Z",
    "updatedAt": "2025-04-23T20:01:32.347759Z",
    "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1"
}

```

---

### 9. UpdateReportSection

**Method:** `UpdateReportSection`  
**Route:** `POST /hero.reports.v2.ReportService/UpdateReportSection`

#### Request

**UpdateReportSectionRequest:**

| Field    | Type          | Description                    |
|----------|---------------|--------------------------------|
| reportId | string        | Parent report ID.              |
| section  | ReportSection | Section with updates.          |

**Sample Request (JSON):**
```json
{
  "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1",
  "section": {
    "id": "7e900328-be4f-4334-aa05-30cd79e05d0c",
    "type": "SECTION_TYPE_NARRATIVE",
    "narrative": {
      "richText": "<p>Updated findings...</p>"
    }
  }
}
```

#### Response

Returns the updated `ReportSection`.

**Sample Response (JSON):**
```json
{
    "id": "7e900328-be4f-4334-aa05-30cd79e05d0c",
    "type": "SECTION_TYPE_NARRATIVE",
    "narrative": {
        "richText": "<p>Updated findings...</p>"
    },
    "createdAt": "2025-04-23T19:33:27.75145Z",
    "updatedAt": "2025-04-23T20:01:32.347759302Z"
} 
```

---

### 10. DeleteReportSection

**Method:** `DeleteReportSection`  
**Route:** `POST /hero.reports.v2.ReportService/DeleteReportSection`

#### Request

**DeleteReportSectionRequest:**

| Field     | Type   | Description        |
|-----------|--------|--------------------|
| reportId  | string | Parent report ID.  |
| sectionId | string | Target section ID. |

**Sample Request (JSON):**
```json
{
  "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1",
  "sectionId": "7e900328-be4f-4334-aa05-30cd79e05d0c"
}
```

#### Response

Returns `google.protobuf.Empty`.

**Sample Response (JSON):**
```json
{}
```
---

### 11. ListReportSections

**Method:** `ListReportSections`  
**Route:** `POST /hero.reports.v2.ReportService/ListReportSections`

#### Request

**ListReportSectionsRequest:**

| Field    | Type   | Description       |
|----------|--------|-------------------|
| reportId | string | Parent report ID. |

**Sample Request (JSON):**
```json
{ "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1" }
```

#### Response

**ListReportSectionsResponse:**

| Field    | Type                    | Description              |
|----------|-------------------------|--------------------------|
| sections | repeated ReportSection | List of report sections. |

**Sample Response (JSON):**
```json
{
    "sections": [
        {
            "id": "0a3b0773-3704-432a-b392-44880faeef21",
            "type": "SECTION_TYPE_NARRATIVE",
            "narrative": {
                "richText": "<p>New findings...</p>"
            },
            "createdAt": "2025-04-23T20:02:43.221403Z",
            "updatedAt": "2025-04-23T20:02:43.221403Z",
            "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1"
        },
        {
            "id": "7e900328-be4f-4334-aa05-30cd79e05d0c",
            "type": "SECTION_TYPE_NARRATIVE",
            "narrative": {
                "richText": "<p>Updated findings...</p>"
            },
            "createdAt": "2025-04-23T19:33:27.75145Z",
            "updatedAt": "2025-04-23T20:01:32.347759Z",
            "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1"
        }
    ]
}
```

---

### 12. AddComment

**Method:** `AddComment`  
**Route:** `POST /hero.reports.v2.ReportService/AddComment`

#### Request

**AddCommentRequest:**

| Field   | Type    | Description                    |
|---------|---------|--------------------------------|
| comment | Comment | Full comment object to create. |

**Sample Request (JSON):**
```json
{
  "comment": {
    "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1",
    "sectionId": "0a3b0773-3704-432a-b392-44880faeef21",
    "text": "Please clarify the ventilation tactics in paragraph 3.",
  }
}
```

#### Response

Returns the created `Comment`.

**Sample Response (JSON):**
```json
{
    "id": "comment_56ea1142-5d68-4f92-af26-3d57828bed6f",
    "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
    "sectionId": "section_0a3b0773-3704-432a-b392-44880faeef21",
    "replyToCommentId": "comment_88c4ad46-d2b0-4b1d-b5f8-65a99e7a54eb",
    "authorAssetId": "cognito:88714360-20f1-706d-ac7b-df9b540bd0b6",
    "displayName": "Lt. Reynolds",
    "text": "Please clarify the ventilation tactics in paragraph 3.",
    "createdAt": "2025-04-23T20:27:56.721821257Z",
    "resolved": false,
    "resolvedAt": "",
    "resolvedByAssetId": "",
    "updatedAt": "2025-04-23T20:27:56.721821257Z",
    "resourceType": "COMMENT"
}
```

---

### 13. UpdateComment

**Note**: This has some issue. DON'T USE IT NOW.

**Method:** `UpdateComment`  
**Route:** `POST /hero.reports.v2.ReportService/UpdateComment`

#### Request

**UpdateCommentRequest:**

| Field   | Type    | Description                                                              |
|---------|---------|--------------------------------------------------------------------------|
| comment | Comment | Full comment object with updates. **Must include** `id` and `updatedAt`. |

**Sample Request (JSON):**
```json
{
  "comment": {
    "id": "comment-123",
    "text": "Actually, I have a question.",
    "updatedAt": "2025-04-21T15:45:00Z"
  }
}
```

#### Response

Returns the updated `Comment`.

**Sample Response (JSON):**
```json
{
  "id": "comment-123",
  "reportId": "generated-uuid",
  "authorAssetId": "asset-789",
  "text": "Actually, I have a question.",
  "createdAt": "2025-04-21T15:45:00Z",
  "updatedAt": "2025-04-21T15:50:00Z",
  "resolved": false,
  "resourceType": "COMMENT"
}
```

---

### 14. DeleteComment

**Method:** `DeleteComment`  
**Route:** `POST /hero.reports.v2.ReportService/DeleteComment`

#### Request

**DeleteCommentRequest:**

| Field      | Type   | Description           |
|------------|--------|-----------------------|
| commentId  | string | ID of comment to delete. |

**Sample Request (JSON):**
```json
{ "commentId": "56ea1142-5d68-4f92-af26-3d57828bed6f" }
```

#### Response

Returns `google.protobuf.Empty`.

**Sample Response (JSON):**
```json
{}
```

---

### 15. GetComments

**Method:** `GetComments`  
**Route:** `POST /hero.reports.v2.ReportService/GetComments`

#### Request

**GetCommentsRequest:**

| Field      | Type   | Description                               |
|------------|--------|-------------------------------------------|
| reportId   | string | Report ID.                                |
| sectionId  | string | *(Optional)* Section filter.              |
| pageSize   | int32  | *(Optional)* Max comments per page.       |
| pageToken  | string | *(Optional)* Opaque pagination cursor.    |

**Sample Request (JSON):**
```json
{
  "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1"
}
```

#### Response

**GetCommentsResponse:**

| Field           | Type              | Description                      |
|-----------------|-------------------|----------------------------------|
| comments        | repeated Comment | List of comments.                |
| nextPageToken   | string            | Token for the next page, if any. |

**Sample Response (JSON):**
```json
{
    "comments": [
        {
            "id": "88c4ad46-d2b0-4b1d-b5f8-65a99e7a54eb",
            "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1",
            "sectionId": "0a3b0773-3704-432a-b392-44880faeef21",
            "authorAssetId": "cognito:88714360-20f1-706d-ac7b-df9b540bd0b6",
            "text": "Looks good to me!",
            "createdAt": "2025-04-23T20:40:05.064186Z",
            "updatedAt": "2025-04-23T20:40:05.064186Z",
            "resourceType": "COMMENT"
        }
    ]
}
```

---

### 16. ResolveComment

**Method:** `ResolveComment`  
**Route:** `POST /hero.reports.v2.ReportService/ResolveComment`

#### Request

**ResolveCommentRequest:**

| Field             | Type   | Description                    |
|-------------------|--------|--------------------------------|
| commentId         | string | ID of the comment to resolve.  |

**Sample Request (JSON):**
```json
{
  "commentId": "88c4ad46-d2b0-4b1d-b5f8-65a99e7a54eb"
}
```

#### Response

Returns the resolved `Comment`.
```json
{
    "id": "88c4ad46-d2b0-4b1d-b5f8-65a99e7a54eb",
    "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1",
    "sectionId": "0a3b0773-3704-432a-b392-44880faeef21",
    "authorAssetId": "cognito:88714360-20f1-706d-ac7b-df9b540bd0b6",
    "createdAt": "2025-04-23T20:40:05.064186Z",
    "resolved": true,
    "resolvedAt": "2025-04-23T21:06:18.323866Z",
    "resolvedByAssetId": "asset-456",
    "updatedAt": "2025-04-23T21:06:18.323866Z",
    "resourceType": "COMMENT"
}
```

---

### 17. SubmitForReview

**Method:** `SubmitForReview`  
**Route:** `POST /hero.reports.v2.ReportService/SubmitForReview`

Submits a report for review, transitioning it to the review workflow. Supports optional preferred reviewer selection.

#### Request

**SubmitForReviewRequest:**

| Field                      | Type   | Description                                           |
|----------------------------|--------|-------------------------------------------------------|
| reportId                   | string | Report ID.                                            |
| note                       | string | *(Optional)* Note for the reviewer.                  |
| preferred_reviewer_asset_id| string | *(Optional)* Asset ID of preferred reviewer to assign.|

**Sample Request (JSON):**
```json
{ 
  "reportId": "generated-uuid", 
  "note": "Please review content.",
  "preferred_reviewer_asset_id": "asset_supervisor_123"
}
```

**Sample Request without preferred reviewer (JSON):**
```json
{ 
  "reportId": "generated-uuid", 
  "note": "Please review content." 
}
```

#### Response

Returns updated `Report` with new review round. The assigned reviewer will be either:
- The preferred reviewer (if specified and eligible)
- A previously assigned reviewer at the same level (if re-submitting)
- A randomly selected eligible reviewer

**Sample Response (JSON):**
```json
{
  "id": "generated-uuid",
  "status": "REPORT_STATUS_SUBMITTED_FOR_REVIEW",
  "reviewRounds": [
    {
      "id": "round-1",
      "reportId": "generated-uuid",
      "reviewerAssetId": "asset_supervisor_123",
      "level": 1,
      "status": "REVIEW_STATUS_AWAITING_ACTION",
      "requestedAt": "2025-04-21T16:00:00Z",
      "noteForReviewer": "Please review content.",
      "snapshotVersion": 4
    }
  ],
  "updatedAt": "2025-04-21T16:00:00Z",
  "version": 5
}
```

---

### 18. GetEligibleReviewers

**Method:** `GetEligibleReviewers`  
**Route:** `POST /hero.reports.v2.ReportService/GetEligibleReviewers`

Retrieves a list of eligible reviewers for a report based on the workflow configuration and current review state. This allows users to preview available reviewers before submitting for review.

#### Request

**GetEligibleReviewersRequest:**

| Field     | Type   | Description                        |
|-----------|--------|-------------------------------------|
| report_id | string | Report ID to get reviewers for.    |

**Sample Request (JSON):**
```json
{ 
  "report_id": "report_48d9bd9e0bc24288a1d94fa44ff5018b" 
}
```

#### Response

**GetEligibleReviewersResponse:**

| Field      | Type                         | Description                                        |
|------------|------------------------------|-----------------------------------------------------|
| reviewers  | repeated EligibleReviewer    | List of eligible reviewers with their workload.    |
| next_level | int32                        | The review level these reviewers are for.          |
| level_name | string                       | Human-readable name of the review level.           |

**EligibleReviewer:**

| Field               | Type   | Description                                         |
|---------------------|--------|-----------------------------------------------------|
| asset_id            | string | Asset ID of the eligible reviewer.                 |
| name                | string | Display name of the reviewer.                      |
| asset_type          | string | Type of asset (e.g., "ASSET_TYPE_SUPERVISOR").     |
| active_review_count | int32  | Number of reviews currently assigned to reviewer.  |
| email               | string | Contact email of the reviewer (if available).      |
| role                | string | Role/title of the reviewer (if available).         |

**Sample Response (JSON):**
```json
{
  "reviewers": [
    {
      "asset_id": "asset_supervisor_123",
      "name": "John Smith",
      "asset_type": "ASSET_TYPE_SUPERVISOR",
      "active_review_count": 3,
      "email": "<EMAIL>",
      "role": ""
    },
    {
      "asset_id": "asset_supervisor_456",
      "name": "Jane Doe",
      "asset_type": "ASSET_TYPE_SUPERVISOR",
      "active_review_count": 1,
      "email": "<EMAIL>",
      "role": ""
    }
  ],
  "next_level": 1,
  "level_name": "Review Report"
}
```

**Usage Notes:**
- The list excludes the report author and any deactivated assets
- `active_review_count` helps identify reviewer workload for load balancing
- The `next_level` indicates which review level will be assigned (based on workflow configuration)
- Call this endpoint before `SubmitForReview` to allow users to select a preferred reviewer

---

### 19. AddReviewRound

**Method:** `AddReviewRound`  
**Route:** `POST /hero.reports.v2.ReportService/AddReviewRound`

#### Request

**AddReviewRoundRequest:**

| Field        | Type        | Description                               |
|--------------|-------------|-------------------------------------------|
| review_round | ReviewRound | The review round object to create.        |

**Sample Request (JSON):**
```json
{
  "review_round": {
    "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
    "reviewerAssetId": "asset_reviewer_123",
    "level": 1,
    "noteForReviewer": "First pass review"
  }
}
```

#### Response

Returns the newly created `ReviewRound`.

**Sample Response (JSON):**
```json
{
  "id": "review_round_uuid_123",
  "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
  "reviewerAssetId": "asset_reviewer_123",
  "level": 1,
  "status": "REVIEW_STATUS_AWAITING_ACTION",
  "requestedAt": "2025-05-01T10:00:00Z",
  "snapshotVersion": 5,
  "createByAssetId": "asset_submitter_456",
  "noteForReviewer": "First pass review"
}
```

---

### 19. GetReviewRound

**Method:** `GetReviewRound`  
**Route:** `POST /hero.reports.v2.ReportService/GetReviewRound`

#### Request

**GetReviewRoundRequest:**

| Field           | Type   | Description             |
| reviewRoundId | string | ID of the review round. |

**Sample Request (JSON):**
```json
{
  "reviewRoundId": "review_round_uuid_123"
}
```

#### Response

Returns the requested `ReviewRound`.

**Sample Response (JSON):**
```json
{
  "id": "review_round_uuid_123",
  "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
  "reviewerAssetId": "asset_reviewer_123",
  "level": 1,
  "status": "REVIEW_STATUS_AWAITING_ACTION",
  "requestedAt": "2025-05-01T10:00:00Z",
  "snapshotVersion": 5,
  "createByAssetId": "asset_submitter_456",
  "noteForReviewer": "First pass review"
}
```

---

### 20. UpdateReviewRound

**Method:** `UpdateReviewRound`  
**Route:** `POST /hero.reports.v2.ReportService/UpdateReviewRound`

#### Request

**UpdateReviewRoundRequest:**

| Field        | Type        | Description                                  |
|--------------|-------------|----------------------------------------------|
| review_round | ReviewRound | The review round object with updates. Must include `id`. |

**Sample Request (JSON):**
```json
{
  "review_round": {
    "id": "review_round_uuid_123",
    "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
    "reviewerAssetId": "asset_reviewer_123",
    "level": 1,
    "status": "REVIEW_STATUS_AWAITING_ACTION",
    "noteForReviewer": "Updated instructions for review."
  }
}
```

#### Response

Returns the updated `ReviewRound`.

**Sample Response (JSON):**
```json
{
  "id": "review_round_uuid_123",
  "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
  "reviewerAssetId": "asset_reviewer_123",
  "level": 1,
  "status": "REVIEW_STATUS_AWAITING_ACTION",
  "requestedAt": "2025-05-01T10:00:00Z",
  "snapshotVersion": 5,
  "createByAssetId": "asset_submitter_456",
  "noteForReviewer": "Updated instructions for review."
}
```

---

### 21. DeleteReviewRound

**Method:** `DeleteReviewRound`  
**Route:** `POST /hero.reports.v2.ReportService/DeleteReviewRound`

#### Request

**DeleteReviewRoundRequest:**

| Field           | Type   | Description                  |
|-----------------|--------|------------------------------|
| review_round_id | string | ID of the review round to delete. |

**Sample Request (JSON):**
```json
{
  "reviewRoundId": "review_round_uuid_123"
}
```

#### Response

Returns `google.protobuf.Empty`.

**Sample Response (JSON):**
```json
{}
```

---

### 22. ApproveReviewRound

**Method:** `ApproveReviewRound`  
**Route:** `POST /hero.reports.v2.ReportService/ApproveReviewRound`

#### Request

**ApproveReviewRoundRequest:**

| Field             | Type   | Description                    |
|-------------------|--------|--------------------------------|
| reviewRoundId   | string | Review round ID.              |
| note              | string | *(Optional)* Approval note.   |

**Sample Request (JSON):**
```json
{
  "reviewRoundId": "round-1",
  "note": "Looks good, approved."
}
```

#### Response

Returns the updated `ReviewRound`.

**Sample Response (JSON):**
```json
{
  "id": "round-1",
  "reportId": "generated-uuid",
  "reviewerAssetId": "asset-reviewer",
  "level": 1,
  "status": "REVIEW_STATUS_APPROVED",
  "requestedAt": "2025-04-21T16:10:00Z",
  "resolvedAt": "2025-04-21T16:30:00Z",
  "roundNote": "Looks good, approved.",
  "snapshotVersion": 5
}
```

---

### 23. RequestChanges

**Method:** `RequestChanges`  
**Route:** `POST /hero.reports.v2.ReportService/RequestChanges`

#### Request

**RequestChangesRequest:**

| Field           | Type   | Description                             |
|-----------------|--------|-----------------------------------------|
| reviewRoundId   | string | Review round ID.                        |
| note            | string | Change request notes.                   |
| sendToLevel     | int32  | Next routing level (0=author).          |
| sendToAssetId   | string | Specific asset for routing.             |

**Sample Request (JSON):**
```json
{
  "reviewRoundId": "round-1",
  "note": "Please clarify section 2.",
  "sendToLevel": 0
}
```

```

#### Response

Returns the updated `ReviewRound`.

---

### 24. UpdateAdditionalInfoJson

**Method:** `UpdateAdditionalInfoJson`  
**Route:** `POST /hero.reports.v2.ReportService/UpdateAdditionalInfoJson`

#### Request

**UpdateAdditionalInfoJsonRequest:**

| Field                | Type   | Description                         |
|----------------------|--------|-------------------------------------|
| reportId             | string | Report ID to update.               |
| additionalInfoJson   | string | JSON metadata blob to merge.       |

**Sample Request (JSON):**
```json
{
  "reportId": "generated-uuid",
  "additionalInfoJson": "{\"key\":\"value\"}"
}
```

#### Response

**UpdateAdditionalInfoJsonResponse:**

| Field                | Type   | Description                          |
|----------------------|--------|--------------------------------------|
| reportId             | string | Identifier of the updated report.    |
| additionalInfoJson   | string | Updated JSON metadata blob.          |

---

### 25. GetAdditionalInfo

**Method:** `GetAdditionalInfo`  
**Route:** `POST /hero.reports.v2.ReportService/GetAdditionalInfo`

#### Request

**GetAdditionalInfoRequest:**

| Field     | Type   | Description   |
|-----------|--------|---------------|
| reportId  | string | Report ID.    |

**Sample Request (JSON):**
```json
{ "reportId": "generated-uuid" }
```

#### Response

**GetAdditionalInfoResponse:**

| Field                | Type   | Description               |
|----------------------|--------|---------------------------|
| reportId             | string | Identifier of the report. |
| additionalInfoJson   | string | Current JSON metadata.    |

---

### 26. GetReportVersion

**Method:** `GetReportVersion`  
**Route:** `POST /hero.reports.v2.ReportService/GetReportVersion`

#### Request

**GetReportVersionRequest:**

| Field     | Type   | Description            |
|-----------|--------|------------------------|
| reportId  | string | Report ID.             |
| version   | int32  | Desired version number.|

**Sample Request (JSON):**
```json
{ "reportId": "generated-uuid", "version": 3 }
```

#### Response

Returns the `ReportSnapshot` for that version.

---

### 27. ListReportVersions

**Method:** `ListReportVersions`  
**Route:** `POST /hero.reports.v2.ReportService/ListReportVersions`

#### Request

**ListReportVersionsRequest:**

| Field     | Type   | Description   |
|-----------|--------|---------------|
| reportId  | string | Report ID.    |

**Sample Request (JSON):**
```json
{ "reportId": "generated-uuid" }
```

#### Response

**ListReportVersionsResponse:**

| Field     | Type          | Description                   |
|-----------|---------------|-------------------------------|
| versions  | repeated int32| List of available version numbers. |

**Sample Response (JSON):**
```json
{
  "versions": [1, 2, 3, 4, 5]
}
```

---

### 28. ListReportsBySituationId

**Method:** `ListReportsBySituationId`  
**Route:** `POST /hero.reports.v2.ReportService/ListReportsBySituationId`

#### Request

**ListReportsBySituationIdRequest:**

| Field        | Type   | Description                              |
|--------------|--------|------------------------------------------|
| situationId  | string | Target Situation ID to filter by.        |
| pageSize     | int32  | *(Optional)* Maximum items per page.     |
| pageToken    | string | *(Optional)* Cursor for pagination.      |

**Sample Request (JSON):**
```json
{
  "situationId": "situation_7b3c9e2e",
  "pageToken": ""
}
```

#### Response

**ListReportsResponse:**

| Field         | Type             | Description                          |
|---------------|------------------|--------------------------------------|
| reports       | repeated Report  | Page of reports matching the filter. |
| nextPageToken | string           | Token for next page, if any.         |

**Sample Response (JSON):**
```json
{
  "reports": [
    {
      "id": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "orgId": 42,
      "authorAssetId": "asset_ad3f4c71",
      "title": "Structure-Fire Incident – 123 Main St",
      "status": "REPORT_STATUS_IN_PROGRESS",
      "assignedAt": "2025-04-22T22:05:00Z",
      "updatedAt": "2025-04-23T09:30:10Z",
      "resourceType": "REPORT",
      "additionalInfoJson": {},
      "version": 2,
      "createdAt": "2025-04-22T22:04:45Z"
    }
  ],
  "nextPageToken": "2"
}
```

---

### 29. ListReportsByCaseId

**Method:** `ListReportsByCaseId`  
**Route:** `POST /hero.reports.v2.ReportService/ListReportsByCaseId`

#### Request

**ListReportsByCaseIdRequest:**

| Field     | Type   | Description                              |
|-----------|--------|------------------------------------------|
| caseId    | string | Target Case ID to filter by.            |
| pageSize  | int32  | *(Optional)* Maximum items per page.     |
| pageToken | string | *(Optional)* Cursor for pagination.      |

**Sample Request (JSON):**
```json
{
  "caseId": "case_f2b71d90",
  "pageSize": 2,
  "pageToken": ""
}
```

#### Response

**ListReportsResponse:**

| Field         | Type             | Description                          |
|---------------|------------------|--------------------------------------|
| reports       | repeated Report  | Page of reports matching the filter. |
| nextPageToken | string           | Token for next page, if any.         |

**Sample Response (JSON):**
```json
{
  "reports": [
    {
      "id": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "orgId": 42,
      "authorAssetId": "asset_ad3f4c71",
      "title": "Structure-Fire Incident – 123 Main St",
      "status": "REPORT_STATUS_IN_PROGRESS",
      "assignedAt": "2025-04-22T22:05:00Z",
      "updatedAt": "2025-04-23T09:30:10Z",
      "resourceType": "REPORT",
      "additionalInfoJson": {},
      "version": 2,
      "createdAt": "2025-04-22T22:04:45Z"
    }
  ],
  "nextPageToken": "2"
}
```

---

### 30. ListReviewRoundsForReport

**Method:** `ListReviewRoundsForReport`  
**Route:** `POST /hero.reports.v2.ReportService/ListReviewRoundsForReport`

#### Request

**ListReviewRoundsForReportRequest:**

| Field     | Type   | Description                         |
|-----------|--------|-------------------------------------|
| reportId  | string | Target report ID.                   |
| pageSize  | int32  | *(Optional)* Max items per page.    |
| pageToken | string | *(Optional)* Cursor for pagination. |

**Sample Request (JSON):**
```json
{
  "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
  "pageSize": 10
}
```

#### Response

**ListReviewRoundsForReportResponse:**

| Field           | Type                  | Description                      |
|-----------------|-----------------------|----------------------------------|
| reviewRounds    | repeated ReviewRound | Page of review rounds.           |
| nextPageToken   | string                | Token for the next page, if any. |

**Sample Response (JSON):**
```json
{
  "reviewRounds": [
    {
      "id": "review_round_01",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "reviewerAssetId": "asset_captain01",
      "level": 1,
      "status": "REVIEW_STATUS_AWAITING_ACTION",
      "sentToLevel": 0,
      "sentToAssetId": "",
      "requestedAt": "2025-04-23T09:25:00Z",
      "resolvedAt": "",
      "roundNote": "",
      "snapshotVersion": 1,
      "dueAt": "2025-04-24T09:00:00Z",
      "createByAssetId": "asset_ad3f4c71",
      "noteForReviewer": "Focus on NFPA-compliance wording, please."
    }
  ],
  "nextPageToken": ""
}
```

---

### 31. SearchReports

**Method:** `SearchReports`  
**Route:** `POST /hero.reports.v2.ReportService/SearchReports`

The SearchReports endpoint provides comprehensive multi-criteria search capabilities across the entire reports dataset. It supports text search, exact filtering, date range queries, and section-specific content filtering with intelligent query optimization and result highlighting.

#### Key Capabilities

🔍 **Text Search**: ILIKE pattern matching across 10+ fields using GIN trigram indexes  
🎯 **Exact Filters**: Status, ID-based, and enum filtering with optimized B-tree indexes  
📅 **Date Ranges**: Time-series queries with BRIN indexes for efficient range scans  
🗂️ **JSONB Search**: Deep section content search with path operators and array functions  
🔦 **Highlighting**: Contextual text fragments showing matched search terms  
📄 **Pagination**: Efficient offset-based pagination with total result counts  

#### Request

**SearchReportsRequest:**

| Field                | Type                              | Description                                                |
|----------------------|-----------------------------------|------------------------------------------------------------|
| **Text Search Parameters** |                               |                                                            |
| query                | string                            | *(Optional)* Global search term across all/specified fields. Supports partial matching with ILIKE. |
| searchFields         | repeated string                   | *(Optional)* Limit global search to specific fields. See [Searchable Fields](#searchable-fields) for valid values. |
| fieldQueries         | repeated FieldQuery               | *(Optional)* Target different search terms to specific fields. Allows field-specific queries. |
| **Report-Level Filters** |                               |                                                            |
| status               | repeated ReportStatus             | *(Optional)* Filter by report status values. Supports multiple statuses with OR logic. |
| reportTypes          | repeated ReportType               | *(Optional)* Filter by report type values (PRIMARY, SUPPLEMENTAL). Supports multiple types with OR logic. |
| situationIds         | repeated string                   | *(Optional)* Filter by associated situation IDs. Reports linked to specified situations. |
| caseIds              | repeated string                   | *(Optional)* Filter by associated case IDs. Reports linked to specified cases. |
| createdByAssetIds    | repeated string                   | *(Optional)* Filter by report creator asset IDs. Reports created by specified assets. |
| **Date Range Filters** |                               |                                                            |
| createdAt            | DateRange                         | *(Optional)* Filter by creation date range. Uses BRIN indexes for efficient queries. |
| updatedAt            | DateRange                         | *(Optional)* Filter by last update date range. Tracks modification timestamps. |
| assignedAt           | DateRange                         | *(Optional)* Filter by assignment date range. When reports were assigned to authors. |
| completedAt          | DateRange                         | *(Optional)* Filter by completion date range. When reports reached terminal status. |
| **Section-Specific Filters** |                               |                                                            |
| entityListRefIds     | repeated string                   | *(Optional)* Filter by entity reference IDs in entity list sections. Uses JSONB array search. |
| referenceType        | string                            | *(Optional)* Filter by entity reference type (PERSON, VEHICLE, PROPERTY). |
| incidentStartTime    | DateRange                         | *(Optional)* Filter by incident start time range from incident details sections. |
| incidentEndTime      | DateRange                         | *(Optional)* Filter by incident end time range from incident details sections. |
| initialTypes         | repeated hero.situations.v2.SituationType | *(Optional)* Filter by initial situation types in incident details. |
| finalTypes           | repeated hero.situations.v2.SituationType | *(Optional)* Filter by final situation types in incident details. |
| responderAssetIds    | repeated string                   | *(Optional)* Filter by responder asset IDs in incident details. |
| responderRoles       | repeated string                   | *(Optional)* Filter by responder roles in incident details. |
| **Offense Filters**  |                               |                                                            |
| offenseTypes         | repeated string                   | *(Optional)* Filter by offense types in offense sections. Exact match across all offenses within sections. |
| offenseIds           | repeated string                   | *(Optional)* Filter by specific offense IDs in offense sections. Exact match for individual offense identifiers. |
| **Relation Filters** |                               |                                                            |
| relationTypes        | repeated string                   | *(Optional)* Filter by relation types. Reports containing relations of specified types. |
| relationCreatedByAssetIds | repeated string               | *(Optional)* Filter by relation creator asset IDs. Reports with relations created by specified assets. |
| relationInvolvedObjectTypes | repeated string              | *(Optional)* Filter by object types involved in relations. Reports with relations involving specified object types. |
| relationInvolvedReportScopedIds | repeated string          | *(Optional)* Filter by report-scoped IDs involved in relations. Reports with relations involving specified IDs. |
| relationInvolvedGlobalIds | repeated string                | *(Optional)* Filter by global IDs involved in relations. Reports with relations involving specified global IDs. |
| relationInvolvedExternalIds | repeated string              | *(Optional)* Filter by external IDs involved in relations. Reports with relations involving specified external IDs. |
| **Sorting and Pagination** |                               |                                                            |
| orderBy              | SearchOrderBy                     | *(Optional)* Sort field (default: CREATED_AT). See [Sort Options](#sort-options). |
| ascending            | bool                              | *(Optional)* Sort direction (default: false = DESC). True for ascending order. |
| pageSize             | int32                             | *(Optional)* Results per page (min: 1, max: 100, default: 20). |
| pageToken            | string                            | *(Optional)* Pagination offset token from previous response. |

#### Helper Types

**FieldQuery:**

Allows targeting specific fields with different search terms, enabling complex queries like searching for "fire" in titles and "Main Street" in locations simultaneously.

| Field | Type   | Description                   |
|-------|--------|-------------------------------|
| field | string | Target field name. Must be one of the [searchable fields](#searchable-fields). |
| query | string | Search term for this field. Supports partial matching with ILIKE. |

**DateRange:**

Supports precise time-based filtering with optional start and end boundaries. Both fields are optional, allowing for open-ended ranges.

| Field | Type   | Description                      |
|-------|--------|----------------------------------|
| from  | string | Start date (ISO 8601 format). Reports created/updated on or after this time. |
| to    | string | End date (ISO 8601 format). Reports created/updated on or before this time. |

#### Sort Options

**SearchOrderBy Enum:**

| Value                          | Description                     | Index Used | Performance Notes |
|--------------------------------|---------------------------------|------------|-------------------|
| SEARCH_ORDER_BY_CREATED_AT     | Sort by creation timestamp.     | BRIN | Best for time-series queries |
| SEARCH_ORDER_BY_UPDATED_AT     | Sort by last update timestamp.  | BRIN | Good for recent activity |
| SEARCH_ORDER_BY_STATUS         | Sort by report status.          | B-tree | Fast for status grouping |
| SEARCH_ORDER_BY_RELEVANCE      | Sort by search relevance.       | N/A | Falls back to CREATED_AT DESC |

#### Searchable Fields

The following fields support text search with ILIKE pattern matching and trigram indexing:

**Report-Level Fields:**
- `title` - Report title field
- `id` - Report ID (partial matching supported)

**Section Content Fields:**
- `narrative` - Rich text content from narrative sections
- `entity_list_title` - Titles from entity list sections
- `incident_location` - Location information from incident details (searches across street address, city, state, common name, location type, unit info, zip code, and country)
- `reference_display_name` - Display names of entity references
- `responder_display_name` - Display names of incident responders
- `reporting_person_name` - Names of reporting persons (searches across firstName, middleName, lastName)
- `reporting_person_phone_number` - Phone numbers from incident details
- `reporting_person_role` - Roles of reporting persons
- `responder_role` - Roles of incident responders
- `offense_type` - Offense type/classification from offense sections (searches across all offenses within offense sections)
- `agency_name` - Names of involved agencies from incident details
- `agency_reference` - Incident reference numbers from involved agencies


**Relation Fields:**
- `relation_description` - Description text from report relations
- `relation_object_name` - Display names of objects in relations (searches both object_a and object_b display names)

**Field Selection Guidelines:**
- Use `searchFields` to limit global search scope for better performance
- Use `fieldQueries` for targeting specific fields with different terms
- Combine both approaches for complex search scenarios

#### Sample Requests

**Basic Text Search:**
```json
{
  "query": "fire incident",
  "pageSize": 10
}
```

**Advanced Multi-Criteria Search:**
```json
{
  "query": "fire incident",
  "searchFields": ["title", "narrative", "incident_location"],
  "status": ["REPORT_STATUS_IN_PROGRESS", "REPORT_STATUS_SUBMITTED_FOR_REVIEW"],
  "createdAt": {
    "from": "2025-01-01T00:00:00Z",
    "to": "2025-12-31T23:59:59Z"
  },
  "fieldQueries": [
    {
      "field": "incident_location",
      "query": "Main Street"
    },
    {
      "field": "responder_display_name",
      "query": "Engine 51"
    }
  ],
  "initialTypes": ["SITUATION_TYPE_FIRE", "SITUATION_TYPE_MEDICAL_EMERGENCY"],
  "orderBy": "SEARCH_ORDER_BY_UPDATED_AT",
  "ascending": false,
  "pageSize": 20,
  "pageToken": ""
}
```

**Entity-Focused Search:**
```json
{
  "entityListRefIds": ["asset_engine51", "asset_truck21"],
  "referenceType": "VEHICLE",
  "responderAssetIds": ["asset_captain01"],
  "orderBy": "SEARCH_ORDER_BY_CREATED_AT",
  "pageSize": 50
}
```

**Date Range and Status Search:**
```json
{
  "status": ["REPORT_STATUS_APPROVED"],
  "completedAt": {
    "from": "2025-01-01T00:00:00Z",
    "to": "2025-01-31T23:59:59Z"
  },
  "situationIds": ["situation_123", "situation_456"],
  "orderBy": "SEARCH_ORDER_BY_STATUS",
  "ascending": true,
  "pageSize": 25
}
```

**Relation-Based Search:**
```json
{
  "relationTypes": ["responded_to_by", "involved_in"],
  "relationInvolvedObjectTypes": ["entity", "responder"],
  "relationInvolvedGlobalIds": ["asset_engine51", "entity_vehicle_123"],
  "fieldQueries": [
    {
      "field": "relation_description",
      "query": "fire response"
    }
  ],
  "orderBy": "SEARCH_ORDER_BY_CREATED_AT",
  "pageSize": 30
}
```

**Offense-Based Search:**
```json
{
  "offenseTypes": ["Theft", "Vandalism", "Trespassing"],
  "offenseIds": ["offense-123-1", "offense-456-2"],
  "fieldQueries": [
    {
      "field": "offense_type",
      "query": "minor"
    }
  ],
  "status": ["REPORT_STATUS_APPROVED"],
  "orderBy": "SEARCH_ORDER_BY_UPDATED_AT",
  "pageSize": 25
}
```

**Complex Multi-Filter Search:**
```json
{
  "query": "emergency response",
  "searchFields": ["title", "narrative", "relation_description"],
  "status": ["REPORT_STATUS_IN_PROGRESS", "REPORT_STATUS_APPROVED"],
  "reportTypes": ["REPORT_TYPE_INCIDENT_PRIMARY"],
  "createdAt": {
    "from": "2025-01-01T00:00:00Z"
  },
  "relationTypes": ["responded_to_by"],
  "responderAssetIds": ["asset_captain01", "asset_engine51"],
  "initialTypes": ["SITUATION_TYPE_FIRE"],
  "orderBy": "SEARCH_ORDER_BY_RELEVANCE",
  "pageSize": 20
}
```

#### Response

**SearchReportsResponse:**

| Field         | Type                               | Description                                    |
|---------------|------------------------------------|------------------------------------------------|
| reports       | repeated Report                    | List of matching reports with full object details. Sorted according to `orderBy` parameter. |
| nextPageToken | string                             | Token for next page, if available. Empty string indicates no more results. |
| highlights    | map<string, HighlightResult>       | Search result highlights by report ID. Contains contextual text fragments. |
| totalResults  | int32                              | Total number of matching reports across all pages. Use for pagination UI. |

**HighlightResult:**

| Field     | Type            | Description                                  |
|-----------|-----------------|----------------------------------------------|
| field     | string          | Field name where matches were found. One of the searchable fields. |
| fragments | repeated string | Text fragments with highlighted matches. Up to 3 fragments per report, 40-character context windows. |

#### Response Characteristics

**Performance Metrics:**
- Typical response time: 50-200ms for most queries
- Maximum results per page: 100 reports
- Highlighting processing: ~5-10ms additional overhead
- Total results calculation: Uses optimized COUNT query

**Highlighting Behavior:**
- Fragments show 40 characters of context around matches
- Maximum 3 fragments per report for readability
- Ellipsis (`...`) indicates truncated content
- Field priority: First match field becomes the highlight field

**Result Ordering:**
- Secondary sort by `id` ASC ensures consistent pagination
- RELEVANCE ordering falls back to CREATED_AT DESC
- NULL values in sort fields are ordered last

**Sample Response (JSON):**
```json
{
  "reports": [
    {
      "id": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "orgId": 42,
      "authorAssetId": "asset_ad3f4c71",
      "title": "Structure Fire Incident – 123 Main St",
      "sections": [
        {
          "id": "section_a9af36cb",
          "type": "SECTION_TYPE_NARRATIVE",
          "narrative": {
            "richText": "<p>Arrived on scene at 22:14 hrs; fire showing from second-floor windows.</p>"
          },
          "createdAt": "2025-04-23T09:15:35Z",
          "updatedAt": "2025-04-23T09:18:02Z",
          "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b"
        }
      ],
      "status": "REPORT_STATUS_IN_PROGRESS",
      "assignedAt": "2025-04-22T22:05:00Z",
      "updatedAt": "2025-04-23T09:30:10Z",
      "resourceType": "REPORT",
      "version": 2,
      "createdAt": "2025-04-22T22:04:45Z"
    }
  ],
  "nextPageToken": "20",
  "highlights": {
    "report_48d9bd9e0bc24288a1d94fa44ff5018b": {
      "field": "title",
      "fragments": [
        "Structure **Fire Incident** – 123 Main St"
      ]
    }
  },
  "totalResults": 156
}
```

---

### 32. CreateRelation

**Method:** `CreateRelation`  
**Route:** `POST /hero.reports.v2.ReportService/CreateRelation`

#### Request

**CreateRelationRequest:**

| Field        | Type        | Description                               |
|--------------|-------------|-------------------------------------------|
| relation     | Relation    | The relation object to create.            |

**Sample Request (JSON):**
```json
{
  "relation": {
    "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
    "objectA": {
      "objectType": "responder",
      "reportScopedId": "responder_01",
      "sectionId": "section_e67d9af3",
      "displayName": "Engine 51"
    },
    "objectB": {
      "objectType": "entity",
      "globalId": "asset_engine51",
      "displayName": "Engine 51"
    },
    "relationType": "responded_to_by",
    "description": "Engine 51 responded to this fire incident"
  }
}
```

#### Response

Returns the newly created `Relation`.

**Sample Response (JSON):**
```json
{
  "id": "relation_uuid_123",
  "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
  "objectA": {
    "objectType": "responder",
    "reportScopedId": "responder_01",
    "sectionId": "section_e67d9af3",
    "displayName": "Engine 51"
  },
  "objectB": {
    "objectType": "entity",
    "globalId": "asset_engine51",
    "displayName": "Engine 51"
  },
  "relationType": "responded_to_by",
  "description": "Engine 51 responded to this fire incident",
  "createdAt": "2025-05-01T10:00:00Z",
  "updatedAt": "2025-05-01T10:00:00Z",
  "createdByAssetId": "asset_submitter_456"
}
```

---

### 33. GetRelation

**Method:** `GetRelation`  
**Route:** `POST /hero.reports.v2.ReportService/GetRelation`

#### Request

**GetRelationRequest:**

| Field      | Type   | Description        |
|------------|--------|--------------------|
| relationId | string | ID of the relation. |

**Sample Request (JSON):**
```json
{
  "relationId": "relation_uuid_123"
}
```

#### Response

Returns the requested `Relation`.

**Sample Response (JSON):**
```json
{
  "id": "relation_uuid_123",
  "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
  "objectA": {
    "objectType": "responder",
    "reportScopedId": "responder_01",
    "sectionId": "section_e67d9af3",
    "displayName": "Engine 51"
  },
  "objectB": {
    "objectType": "entity",
    "globalId": "asset_engine51",
    "displayName": "Engine 51"
  },
  "relationType": "responded_to_by",
  "description": "Engine 51 responded to this fire incident",
  "createdAt": "2025-05-01T10:00:00Z",
  "updatedAt": "2025-05-01T10:00:00Z",
  "createdByAssetId": "asset_submitter_456"
}
```

---

### 34. UpdateRelation

**Method:** `UpdateRelation`  
**Route:** `POST /hero.reports.v2.ReportService/UpdateRelation`

#### Request

**UpdateRelationRequest:**

| Field        | Type        | Description                               |
|--------------|-------------|-------------------------------------------|
| relation     | Relation    | The relation object with updates.         |

**Sample Request (JSON):**
```json
{
  "relation": {
    "id": "relation_uuid_123",
    "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
    "objectA": {
      "objectType": "responder",
      "reportScopedId": "responder_01",
      "sectionId": "section_e67d9af3",
      "displayName": "Engine 51"
    },
    "objectB": {
      "objectType": "entity",
      "globalId": "asset_engine51",
      "displayName": "Engine 51"
    },
    "relationType": "responded_to_by",
    "description": "Updated description",
    "updatedAt": "2025-05-01T10:00:00Z"
  }
}
```

#### Response

Returns the updated `Relation`.

**Sample Response (JSON):**
```json
{
  "id": "relation_uuid_123",
  "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
  "objectA": {
    "objectType": "responder",
    "reportScopedId": "responder_01",
    "sectionId": "section_e67d9af3",
    "displayName": "Engine 51"
  },
  "objectB": {
    "objectType": "entity",
    "globalId": "asset_engine51",
    "displayName": "Engine 51"
  },
  "relationType": "responded_to_by",
  "description": "Updated description",
  "createdAt": "2025-05-01T10:00:00Z",
  "updatedAt": "2025-05-01T10:00:00Z",
  "createdByAssetId": "asset_submitter_456"
}
```

---

### 35. DeleteRelation

**Method:** `DeleteRelation`  
**Route:** `POST /hero.reports.v2.ReportService/DeleteRelation`

#### Request

**DeleteRelationRequest:**

| Field      | Type   | Description        |
|------------|--------|--------------------|
| relationId | string | ID of the relation. |

**Sample Request (JSON):**
```json
{
  "relationId": "relation_uuid_123"
}
```

#### Response

Returns `google.protobuf.Empty`.

**Sample Response (JSON):**
```json
{}
```

---

### 36. ListRelations

**Method:** `ListRelations`  
**Route:** `POST /hero.reports.v2.ReportService/ListRelations`

#### Request

**ListRelationsRequest:**

| Field     | Type   | Description       |
|-----------|--------|-------------------|
| reportId  | string | Parent report ID. |

**Sample Request (JSON):**
```json
{ "reportId": "ae93af54-ced8-41a7-a9a7-828f22087af1" }
```

#### Response

**ListRelationsResponse:**

| Field     | Type                    | Description              |
|-----------|-------------------------|--------------------------|
| relations | repeated Relation        | List of relations.       |
| nextPageToken | string                | Token for the next page, if any. |

**Sample Response (JSON):**
```json
{
  "relations": [
    {
      "id": "relation_uuid_123",
      "reportId": "report_48d9bd9e0bc24288a1d94fa44ff5018b",
      "objectA": {
        "objectType": "responder",
        "reportScopedId": "responder_01",
        "sectionId": "section_e67d9af3",
        "displayName": "Engine 51"
      },
      "objectB": {
        "objectType": "entity",
        "globalId": "asset_engine51",
        "displayName": "Engine 51"
      },
      "relationType": "responded_to_by",
      "description": "Engine 51 responded to this fire incident",
      "createdAt": "2025-05-01T10:00:00Z",
      "updatedAt": "2025-05-01T10:00:00Z",
      "createdByAssetId": "asset_submitter_456"
    }
  ],
  "nextPageToken": ""
}
```

---

# Testing Reports

This section covers how to test the Reports module using the provided test automation scripts and test suites, including comprehensive testing of the new Relations functionality.


## Test Directory Structure

The Reports module includes a comprehensive test suite located at `services/workflow/test/reports/` with the following components:

```
services/workflow/test/reports/
├── run_tests.sh                    # Main test runner script
├── token.txt                       # Authentication token (user-provided)
├── test_utils.go                   # Shared testing utilities
├── reports_api_sanity_test.go      # Core CRUD functionality tests
├── reports_search_api_test.go      # Comprehensive search tests
├── populate_reports_test.go        # Test data generation
├── clean_reports_test.go           # Test data cleanup
└── created_report_ids.txt          # Generated report IDs for cleanup
```

## Test Script Overview

The Reports module includes a comprehensive test automation script at `services/workflow/test/reports/run_tests.sh` that simplifies running different test suites against the Reports API.

### Prerequisites

Before running tests, ensure you have:

1. **Authentication Token**: A valid Cognito access token saved in `services/workflow/test/reports/token.txt`
2. **Go Environment**: Go installed and configured 
3. **Network Access**: Connectivity to the target API service (default: `http://localhost:9086`)
4. **Test Dependencies**: All required Go modules installed
5. **Performance Tests**: Adequate system resources (CPU, memory) and 600-second timeout for comprehensive testing

### Script Usage

```bash
# Basic usage
./run_tests.sh [test_type] [options]

# Available test types:
#   sanity               - Run core CRUD functionality tests
#   performance          - Run basic performance tests (~3-8 min)
#   search               - Run comprehensive search tests including performance validation
#   search-fast          - Run 4 comprehensive search tests (~15 sec)
#   search-performance   - Run only search performance tests (~90 sec)
#   scalability          - Run scalability stress tests with 100,000+ reports (~45-60 min)
#   populate             - Generate test data (50 reports with full content)
#   cleanup              - Clean up test data from database
#   all                  - Run sanity, performance, and search test suites (default, excludes scalability)

# Available options:
#   nocache  - Bypass Go test cache (-count=1)
```

### Test Types

#### 1. Sanity Tests (`TestSanity_*`)
**Purpose**: Verify core CRUD operations and basic functionality with comprehensive workflow testing
**Coverage**: 
- **Report Lifecycle**: Creation, retrieval, updating, deletion with proper field validation
- **Asset Integration**: Creates test author and supervisor assets for complete workflow
- **Situation Integration**: Creates test situations and links them to reports
- **Section Management**: Create, update, delete report sections (narrative, entity lists, incident details)
- **Comment Operations**: Add, update, resolve comments with author attribution
- **Review Workflow**: Review round management with supervisor assignments
- **Field Validation**: Verifies all report fields (OrgId, AuthorAssetId, SituationId, etc.)
- **Error Handling**: Tests validation failures and constraint violations
- **Resource Cleanup**: Comprehensive cleanup of all created test data
- **Integration Testing**: End-to-end workflow from asset creation to report completion

**Example Usage:**
```bash
./run_tests.sh sanity
```

**Sample Output:**
```
Running sanity tests...
=== RUN   TestSanity_ReportService
=== RUN   TestSanity_ReportService/Step_0:_Creating_Test_Author_Asset
=== RUN   TestSanity_ReportService/Step_1:_Creating_Test_Situation
=== RUN   TestSanity_ReportService/Step_2:_Creating_Test_Report
=== RUN   TestSanity_ReportService/Step_3:_Verifying_Report_Fields
--- PASS: TestSanity_ReportService (2.45s)
PASS
```

#### 2. Performance Tests (`TestReports_BasicPerformance`)
**Purpose**: Validate system performance under realistic load and ensure latency requirements are met
**Coverage**:
- **Test Data Volume**: Creates 100 reports + 10 assets + 3 supervisors + 5 situations for comprehensive testing
- **CRUD Operations**: GetReport, ListReports, UpdateReport performance validation
- **Search Performance**: Text search, status filters, report type filters, situation ID filters, complex combined filters
- **Batch Operations**: BatchGetReports performance testing
- **Section Operations**: CreateReportSection, GetReportSection, ListReportSections performance
- **Comment Operations**: AddComment, GetComments performance testing
- **Relations Management**: CreateRelation, ListRelations performance validation
- **Filtered Listing**: Various filtering scenarios and pagination performance
- **JSON Metadata**: UpdateAdditionalInfoJson, GetAdditionalInfo performance
- **Latency Thresholds**: 200ms for individual operations, 2000ms for bulk operations
- **Performance Metrics**: Detailed reporting with min/max/average latencies and success rates
- **Runtime**: 3-8 minutes including comprehensive cleanup

**Example Usage:**
```bash
./run_tests.sh performance
```

**Sample Output:**
```
Running basic performance tests...
🚀 Starting Reports Basic Performance Test with 100 reports, 10 assets, 3 supervisors, 5 situations
📋 Step 1: Creating performance test assets...
✅ Created 10 assets in 2.3s (avg: 230ms per asset)
📋 Step 1.5: Creating performance test supervisor assets...
✅ Created 3 supervisors in 890ms (avg: 296ms per supervisor)
📋 Step 2: Creating performance test situations...
✅ Created 5 situations in 1.8s (avg: 360ms per situation)
📋 Step 3: Creating performance test reports...
✅ Created 100 reports in 45.6s (avg: 456ms per report)
🔥 Running Performance Tests...
✅ GetReport average latency: 89ms (success rate: 10/10)
✅ ListReports latency: 145ms (returned 25 reports)
✅ UpdateReport average latency: 178ms (success rate: 5/5)
✅ SearchReports_BasicTextSearch latency: 123ms (returned 12 reports)
✅ BatchGetReports latency: 156ms (returned 10 reports)
✅ CreateReportSection latency: 134ms
✅ AddComment latency: 98ms
✅ CreateRelation latency: 167ms
🧹 Starting cleanup...
✅ Cleanup completed

================================================================================
📊 PERFORMANCE SUMMARY
================================================================================
Overall Success Rate: 47/47 (100.0%)

✅ GetReport                          | Count:  10 | Avg:     89ms | Min:     67ms | Max:    134ms
✅ ListReports                        | Count:   1 | Avg:    145ms | Min:    145ms | Max:    145ms
✅ UpdateReport                       | Count:   5 | Avg:    178ms | Min:    134ms | Max:    223ms
✅ SearchReports_BasicTextSearch      | Count:   1 | Avg:    123ms | Min:    123ms | Max:    123ms
✅ BatchGetReports                    | Count:   1 | Avg:    156ms | Min:    156ms | Max:    156ms
✅ CreateReportSection                | Count:   5 | Avg:    134ms | Min:    112ms | Max:    167ms
✅ AddComment                         | Count:   3 | Avg:     98ms | Min:     78ms | Max:    123ms
✅ CreateRelation                     | Count:   3 | Avg:    167ms | Min:    145ms | Max:    189ms
================================================================================
Latency Threshold: 200ms
================================================================================
--- PASS: TestReports_BasicPerformance (312.45s)
PASS
```

**Performance Validation Categories:**
- **Core CRUD Operations**: Basic report operations with individual latency validation
- **Search Performance**: Multiple search scenarios including text, filters, and combinations
- **Bulk Operations**: Batch retrieval and processing performance
- **Section Management**: Report section CRUD operations performance
- **Comment System**: Comment creation and retrieval performance
- **Relations System**: Object relationship creation and listing performance
- **Filtered Operations**: Various listing and filtering scenarios
- **Metadata Operations**: JSON metadata update and retrieval performance

#### 3. Search Performance Tests (`TestSearch_ReportsPerformance`)
**Purpose**: Validate database optimization and search query performance under realistic load
**Coverage**:
- **Test Data Volume**: Creates 100 reports with comprehensive sections, relations, and diverse content for search optimization testing
- **Index Validation**: Tests effectiveness of database indexes (B-tree, GIN, BRIN, trigram indexes)
- **Query Performance Categories**:
  - **Basic Search**: Covering indexes validation (< 150ms threshold)
  - **Status Filters**: Enum index performance (< 150ms threshold)
  - **Report Type Filters**: Enum index optimization (< 150ms threshold)  
  - **Date Range Queries**: BRIN index effectiveness (< 500ms threshold)
  - **Relation Type Filters**: GIN index performance (< 500ms threshold)
  - **Relation ID Parameters**: New relation search capabilities with multiple ID types
  - **Multi-Filter Combinations**: Composite index utilization (< 500ms threshold)
  - **Field Queries**: Full-text search optimization (< 500ms threshold)
  - **Ordering Performance**: Various sort operations (< 500ms threshold)
  - **Large Pagination**: High-volume result handling (< 2000ms threshold)
- **Advanced Relation Search**: Tests new relation search parameters including report-scoped IDs, global IDs, and external IDs
- **Performance Thresholds**: 150ms for fast queries, 500ms for medium complexity, 2000ms for heavy operations
- **Runtime**: ~5 minutes including comprehensive cleanup

**Example Usage:**
```bash
./run_tests.sh search-performance
```

**Sample Output:**
```
🚀 PERFORMANCE TESTING - Database Optimization Validation
📊 Step 1: Creating 100 reports for performance testing...
   Created 25/100 reports...
   Created 50/100 reports...
   Created 75/100 reports...
   Created 100/100 reports...
✅ Performance test data created: 100 reports, 100 relations
⏳ Waiting for database indexing optimization...
📊 Step 2: Running performance validation tests...
✅ Basic search performance: 89ms (threshold: 150ms)
✅ Status filter performance: 67ms (threshold: 150ms)  
✅ Report type filter performance: 78ms (threshold: 150ms)
✅ Date range filter performance: 234ms (threshold: 500ms)
✅ Relation types filter performance: 289ms (threshold: 500ms)
📊 Step 3: Testing new relation ID parameters...
✅ Relation report scoped IDs performance: 312ms (threshold: 500ms)
✅ Relation global IDs performance: 298ms (threshold: 500ms)
✅ Relation external IDs performance: 276ms (threshold: 500ms)
📊 Step 4: Testing complex query performance...
✅ Multi-filter query performance: 345ms (threshold: 500ms)
✅ Field queries performance: 423ms (threshold: 500ms)
📊 Step 5: Testing ordering and pagination performance...
✅ Ordering by CreatedAt performance: 234ms (threshold: 500ms)
✅ Ordering by UpdatedAt performance: 198ms (threshold: 500ms)
✅ Ordering by Status performance: 156ms (threshold: 500ms)
✅ Large pagination performance: 1.2s (threshold: 2s)
🧹 Cleaning up performance test data...
✅ Performance test cleanup completed
🎯 Performance testing completed!
--- PASS: TestSearch_ReportsPerformance (289.45s)
PASS
```

**Database Optimization Focus:**
- **Covering Indexes**: Validates queries use indexes without table scans
- **Enum Indexes**: Tests B-tree index effectiveness for status and type filters
- **Time-Series Indexes**: BRIN index performance for date range queries
- **Full-Text Search**: Trigram index optimization for text searches
- **Composite Indexes**: Multi-column index utilization for complex queries
- **Relation Indexes**: GIN index performance for array and JSONB operations
- **Connection Pooling**: Database connection efficiency under load

#### 4. Search Tests (`TestSearch_*`)
**Purpose**: Validate comprehensive search functionality with complete API parameter coverage
**Coverage**:
- **Complete Parameter Coverage**: ALL 27 SearchReportsRequest parameters tested individually
- **Field Query Coverage**: All 17 supported field types (title, narrative, incident_location, etc.)
- **Status Filtering**: ALL ReportStatus enum values including rare edge cases
- **Date Range Queries**: created_at, updated_at, assigned_at, completed_at, incident_start_time, incident_end_time
- **Entity Filtering**: entity_list_ref_ids, reference_type, responder_asset_ids, responder_roles
- **Relation Filtering**: relation_types, relation_created_by_asset_ids, relation object types and IDs
- **Advanced Relation Search**: report-scoped IDs, global IDs, external IDs
- **Ordering & Pagination**: ALL SearchOrderBy values, page_size, page_token handling
- **Edge Cases**: Empty database, non-existent IDs, invalid date ranges, special characters
- **Deterministic Testing**: Precisely controlled test data with exact expected result counts
- **Multi-Criteria Combinations**: Complex filter combinations and field-specific queries

**Test Functions:**
1. `TestSearch_Reports` - Original comprehensive search validation
2. `TestSearch_ValidationEnhancements_*` - Deterministic validation with exact result counts
3. `TestSearch_ComprehensiveParameterValidation` - ALL 27 parameters tested systematically
4. `TestSearch_MissingParametersAndEdgeCases` - Edge cases and rare scenarios

**Example Usage:**
```bash
# Run all search tests (comprehensive)
./run_tests.sh search

# Run quick search validation (4 core tests)
./run_tests.sh search-fast
```

**Sample Output:**
```
Running search tests...
=== RUN   TestSearch_Reports
=== RUN   TestSearch_Reports/TextSearch_BasicQuery
=== RUN   TestSearch_Reports/TextSearch_FieldSpecificQueries
=== RUN   TestSearch_Reports/ExactMatch_StatusAndDateFilters
=== RUN   TestSearch_Reports/SectionContent_NarrativeAndEntitySearch
=== RUN   TestSearch_Reports/Pagination_ConsistentOrderingAndHighlights
--- PASS: TestSearch_Reports (45.67s)
=== RUN   TestSearch_ValidationEnhancements_VerifyCorrectFiltering
--- PASS: TestSearch_ValidationEnhancements_VerifyCorrectFiltering (12.34s)
=== RUN   TestSearch_ComprehensiveParameterValidation
--- PASS: TestSearch_ComprehensiveParameterValidation (89.45s)
=== RUN   TestSearch_MissingParametersAndEdgeCases
--- PASS: TestSearch_MissingParametersAndEdgeCases (23.67s)
PASS
```

**Search-Fast Option:**
The `search-fast` option runs only the 4 most comprehensive search tests:
- `TestSearch_Reports` (original comprehensive test)
- `TestSearch_ValidationEnhancements_*` (deterministic validation)
- `TestSearch_ComprehensiveParameterValidation` (all parameters)
- `TestSearch_MissingParametersAndEdgeCases` (edge cases)

This provides quick validation of core search functionality in ~15 seconds.

#### 5. Populate Tests (`TestPopulateReports`)
**Purpose**: Generate comprehensive test data for manual testing and development
**Coverage**:
- **Test Data Volume**: Creates 50 realistic reports + 10 assets + 15 situations + 8 cases
- **Comprehensive Section Types**: ALL section types including narrative, entity lists, incident details, offense, arrest, media
- **Entity Lists**: People, vehicles, properties, organizations with realistic references
- **Incident Details**: Complete location data, reporting persons, responders, timestamps
- **Offense Sections**: Multiple offense types with structured data and metadata
- **Arrest Sections**: Arrest information with charges, booking details, and court data
- **Media Sections**: File references with categories, captions, and metadata
- **Object Relations**: Creates semantic relationships between all report components
- **Comments & Reviews**: Adds realistic comments and review rounds to reports
- **Realistic Data**: Uses extensive data pools for names, locations, agencies, vehicles, properties
- **ID Persistence**: Saves all created IDs to files for cleanup (`created_report_ids.txt`, `created_asset_ids.txt`, `created_situation_ids.txt`)
- **Diverse Content**: Varied report titles, statuses, types, timestamps for comprehensive testing
- **Full Workflow**: Tests complete report lifecycle from creation to review

**Example Usage:**
```bash
./run_tests.sh populate
```

**Sample Output:**
```
Running populate script to create test reports...
=== RUN   TestPopulateReports
    Created 10 test assets for authors and reviewers
    Created 15 test situations
    Created 8 test cases
    Creating report 1/50: Security Incident Report (INCIDENT_PRIMARY)
    - Generated narrative content with ID: narrative_abc123
    - Generated incident details with ID: incident_details_def456
    - Created 3 responders with IDs: responder_01, responder_02, responder_03
    - Created 5 relations between objects
    Creating report 10/50: Medical Emergency Response (INCIDENT_SUPPLEMENTAL)
    - Generated entity list with ID: entity_list_ghi789
    - Created 2 relations to reference primary incident
    ...
    Creating report 50/50: Perimeter Breach Report (INCIDENT_PRIMARY)
    Saved 50 report IDs to created_report_ids.txt
    Total relations created: 247
    Total content objects with IDs: 150
--- PASS: TestPopulateReports (120.34s)
PASS
```

#### 6. Cleanup Tests (`TestCleanupAllReports`)
**Purpose**: Remove all test data from the database with comprehensive cleanup
**Coverage**:
- **Reports Cleanup**: Deletes reports using IDs from `created_report_ids.txt` or lists all reports
- **Assets Cleanup**: Removes test assets using IDs from `created_asset_ids.txt`
- **Situations Cleanup**: Deletes test situations using IDs from `created_situation_ids.txt`
- **Fallback Strategy**: Lists and deletes all data if ID files are not available
- **Progress Reporting**: Shows cleanup progress with batch processing
- **File Management**: Removes ID files after successful cleanup
- **Error Handling**: Continues cleanup even if individual deletions fail
- **Rate Limiting**: Includes delays to avoid overwhelming the server
- **Comprehensive Scope**: Cleans up all test data types created by populate tests

**Example Usage:**
```bash
./run_tests.sh cleanup
```

**Sample Output:**
```
Running cleanup script to delete test reports...
=== RUN   TestCleanupAllReports
    Found 50 report IDs in created_report_ids.txt
    Deleted 10 reports...
    Deleted 20 reports...
    ...
    Deleted 50/50 reports from created_report_ids.txt
    Removed created_report_ids.txt file
--- PASS: TestCleanupAllReports (15.23s)
PASS
```

#### 7. Scalability Tests (`TestReportsSearchScalability`)
**Purpose**: Validate system performance under extreme load with large datasets
**Coverage**:
- **Target Scale**: Creates 100,000+ reports, assets, situations, and relations using parallel goroutines
- **Parallel Creation**: Up to 30 concurrent workers with semaphore control and progress tracking
- **Creation Rate Monitoring**: Real-time progress reporting with ETA calculation and throughput metrics
- **Query Performance Testing**: Validates search response times with 100K+ record datasets
- **Memory Usage Monitoring**: Tracks memory consumption during complex operations (< 100MB target)
- **Concurrent Query Testing**: 10+ simultaneous queries with performance validation
- **Connection Pooling**: Tests 100+ concurrent database connections under load
- **Index Effectiveness**: Validates various query types with high cardinality data
- **Performance Categories**:
  - **Basic Search Scalability**: Query performance with massive datasets (< 2s threshold)
  - **Memory Usage Validation**: Memory consumption during large result processing
  - **Concurrent Queries**: Multiple simultaneous operations (< 2s average)
  - **Connection Pooling**: Database connection efficiency under extreme load
  - **Index Effectiveness**: Query optimization with high-volume data
- **Comprehensive Cleanup**: Parallel cleanup of all test data with progress monitoring
- **Minimum Success Threshold**: Requires 5,000+ successful reports to proceed with performance testing

**Example Usage:**
```bash
./run_tests.sh scalability
```

**Sample Output:**
```
🔥 SCALABILITY STRESS TESTING - High Volume Validation
📊 Step 1: Creating 100000 reports with parallel processing...
   Progress: 2000/100000 reports created (45.2 reports/sec, ETA: 36m)
   Progress: 4000/100000 reports created (47.8 reports/sec, ETA: 33m)
   ...
✅ Test data created: 100000 reports, 100000 assets, 100000 situations, 100000 relations
📊 Creation time: 45m12s (36.9 reports/sec)
📊 Step 2: Testing query performance with 100000 reports...
✅ Basic search performance with 100000 reports: 1.2s (threshold: 2s)
✅ Memory usage acceptable: 85MB
✅ Concurrent queries performance: avg 1.8s (threshold: 2s)
🧹 Step 3: Cleaning up stress test data (100000 reports)...
✅ Cleanup completed successfully in 8m45s
🎯 Scalability testing completed in 58m32s!
--- PASS: TestReportsSearchScalability (3512.45s)
```

**Important Notes:**
- ⚠️ **DO NOT run on production systems**
- Requires `ENABLE_STRESS_TESTS=true` environment variable
- Expected runtime: 45-60 minutes
- Will heavily load database and system resources
- Creates temporary data that is automatically cleaned up
- Minimum 5,000 successful reports required to proceed with testing

**Performance Validation:**
- **Creation Rate**: Tests parallel report creation (target: 35+ reports/sec)
- **Query Performance**: Validates search response times under load (thresholds: 500ms-2s)
- **Memory Management**: Monitors memory usage during large queries (< 100MB)
- **Concurrency**: Tests 10+ simultaneous queries (target: < 2s average)
- **Connection Pooling**: Validates 100+ concurrent database connections
- **Index Effectiveness**: Tests various query types with large datasets (100K+ records)

**Technical Implementation:**
- **Parallel Creation**: Uses up to 30 concurrent goroutines with semaphore control
- **Progress Tracking**: Real-time progress reporting with ETA calculation and creation rate monitoring
- **Resource Management**: Controlled concurrency to prevent system overload
- **Error Handling**: Comprehensive error collection and reporting with non-blocking channel operations
- **Cleanup Guarantee**: Parallel cleanup ensures test data removal
- **Deadlock Prevention**: Non-blocking channel sends prevent goroutine deadlocks
- **Timeout Protection**: Multiple timeout layers (60m context, 50m completion, 15m cleanup)

#### 6. All Tests (Default)

### Authentication Setup

#### 1. Obtain Access Token
Get a valid Cognito access token using your preferred method:
- Browser developer tools after login
- Postman authentication flow
- CLI authentication tools

#### 2. Save Token to File
Create the token file with your access token:

```bash
# Navigate to test directory
cd services/workflow/test/reports/

# Save token (replace with your actual token)
echo "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." > token.txt

# Verify token file
cat token.txt
```

⚠️ **Security Note**: Never commit `token.txt` to version control. The file is in `.gitignore`.

### Advanced Usage

#### Bypass Test Cache
When debugging or making rapid code changes:

```bash
./run_tests.sh search nocache
./run_tests.sh all nocache
```

#### Running Specific Test Functions
For debugging individual test cases:

```bash
# Run only search tests
go test -v -run "^TestSearch_" ./test/reports

# Run specific test pattern
go test -v -run "TestSearch_Reports/TextSearch" ./test/reports

# Run with cache bypass
go test -v -run "^TestSanity_" -count=1 ./test/reports
```

#### Environment Variables
The script automatically sets required environment variables:

```bash
export COGNITO_ACCESS_TOKEN="$(cat token.txt)"
```

You can also override the service URL:

```bash
export SERVICE_URL="https://api.example.com"  # Override default localhost:9086
```

### Test Data Management

#### Creating Test Data for Development
Generate comprehensive test data for manual testing:

```bash
# Create 50 realistic reports with full content
./run_tests.sh populate
```

This creates:
- **Test Assets**: Authors, reviewers, and responders with realistic names
- **Test Situations**: Various incident types linked to reports  
- **Test Cases**: Case IDs for report association
- **Complete Reports**: With sections, comments, and review rounds
- **ID Tracking**: Saves report IDs to `created_report_ids.txt` for cleanup

#### Data Cleanup
Clean up test data after development or testing:

```bash
# Clean up using saved IDs (recommended)
./run_tests.sh cleanup

# Or run the test directly
go test -v -run "^TestCleanupAllReports$" ./test/reports
```

**Cleanup Behavior:**
- **With ID File**: Deletes only reports listed in `created_report_ids.txt`
- **Without ID File**: Lists all reports and deletes them (use with caution)
- **Error Handling**: Continues cleanup even if some deletions fail
- **File Management**: Removes `created_report_ids.txt` after successful cleanup

### Test Utilities

The test suite includes shared utilities in `test_utils.go`:

#### Key Functions:
- **`AddAuthHeader()`**: Adds authentication to HTTP clients
- **`getAccessToken()`**: Reads token from `token.txt` file  
- **`SaveReportID()`**: Tracks created report IDs for cleanup
- **Service URL Configuration**: Defaults to `http://localhost:9086`

#### Usage in Custom Tests:
```go
// Setup authenticated client
httpClient := http.DefaultClient
AddAuthHeader(httpClient)

// Create service client
reportsClient := reportsConnect.NewReportServiceClient(httpClient, ServiceURL)

// Track created reports for cleanup
SaveReportID(createdReportID)
```

### Troubleshooting

#### Common Issues

**1. Token Authentication Failures**
```
Error: No token found in 'token.txt'
```
**Solution**: Ensure `token.txt` exists and contains a valid access token without extra whitespace

**2. Network Connectivity Issues**
```
Error: connection refused
```
**Solution**: 
- Verify service is running on `localhost:9086`
- Update `ServiceURL` constant in `test_utils.go` if needed
- Check firewall and network settings

**3. Test Timeouts**
```
Error: test timed out after 10m0s
```
**Solution**: 
- Check service performance and database connectivity
- Reduce test data size for populate tests
- Increase timeout in test context if needed

**4. Permission Errors**
```
Error: access denied or insufficient permissions
```
**Solution**: 
- Verify token has required permissions for test operations
- Check organization ID access rights
- Ensure token hasn't expired

**5. Database State Issues**
```
Error: duplicate key value or constraint violations
```
**Solution**:
- Run cleanup tests to clear previous test data
- Check for orphaned test records
- Verify test isolation between runs

**6. Performance Test Failures**
```
Error: operation exceeded latency threshold: 350ms > 200ms
```
**Solution**:
- Ensure adequate system resources (CPU, memory)
- Check for concurrent database operations
- Verify database indexes are properly created
- Run tests in a consistent environment

**7. Performance Test Timeouts**
```
Error: test timeout after 600s
```
**Solution**:
- Reduce test data volume if needed (modify constants in performance test)
- Check database performance and connectivity
- Ensure no resource contention during tests
- Monitor system resources during test execution

#### Debug Mode
Enable verbose output for troubleshooting:

```bash
# Run with verbose Go test output
go test -v -run "TestSearch_" ./test/reports

# Add timing information
time ./run_tests.sh search

# Check specific operations
go test -v -run "TestPopulateReports" ./test/reports
```

### Performance Testing

The test suite includes performance validation:

```bash
# Monitor test performance
time ./run_tests.sh search

# Check populate performance (large data creation)
time ./run_tests.sh populate

# Check cleanup performance
time ./run_tests.sh cleanup
```

**Expected Performance Benchmarks:**
- **Basic CRUD operations**: < 200ms per operation (performance test threshold)
- **GetReport operations**: < 200ms response time (individual report retrieval)
- **ListReports operations**: < 200ms response time (paginated listing)
- **UpdateReport operations**: < 200ms response time (individual report updates)
- **Search operations**: < 200ms response time (text search, filters, combinations)
- **BatchGetReports**: < 200ms response time (batch retrieval operations)
- **Section operations**: < 200ms response time (create, get, list sections)
- **Comment operations**: < 200ms response time (add, get comments)
- **Relations operations**: < 200ms response time (create, list relations)
- **JSON metadata operations**: < 200ms response time (update, get additional info)
- **Performance test suite**: 3-8 minutes total time (100 reports + comprehensive testing)
- **Search performance fast queries**: < 150ms response time (basic search, status/type filters)
- **Search performance medium queries**: < 500ms response time (date ranges, relations, multi-filters)
- **Search performance heavy queries**: < 2000ms response time (large pagination, complex operations)
- **Search performance test suite**: ~5 minutes total time (100 reports + database optimization validation)
- **Search comprehensive test suite**: ~3 minutes total time (all search functionality)
- **Search fast test suite**: ~15 seconds total time (4 core search tests)
- **Populate 50 reports**: < 2 minutes total time
- **Cleanup operations**: < 30 seconds for 50 reports
- **Scalability test creation**: 35+ reports/sec parallel creation rate
- **Scalability test queries**: < 2s response time with 100,000+ reports
- **Scalability memory usage**: < 100MB for complex queries
- **Scalability concurrent queries**: < 2s average for 10+ simultaneous requests
- **Scalability test total time**: 45-60 minutes for complete test suite

### Test Development Guidelines

#### Writing New Tests
1. **Use existing patterns**: Follow the structure in `reports_api_sanity_test.go`
2. **Include cleanup**: Always clean up created test data
3. **Add authentication**: Use `AddAuthHeader()` for all HTTP clients
4. **Track resources**: Use `SaveReportID()` for created reports
5. **Validate thoroughly**: Check all response fields and side effects

#### Test Naming Conventions
- **Sanity Tests**: `TestSanity_[Feature]`
- **Search Tests**: `TestSearch_[SearchType]`
- **Utility Tests**: `TestPopulateReports`, `TestCleanupAllReports`

#### Best Practices

1. **Token Management**: Use short-lived tokens and rotate regularly
2. **Test Isolation**: Ensure tests don't interfere with each other
3. **Resource Cleanup**: Always clean up test data, even on test failures
4. **Performance Monitoring**: Track test execution times for regressions
5. **Environment Separation**: Use dedicated test environments when possible
6. **Error Handling**: Include proper error checking and meaningful error messages
7. **Documentation**: Comment complex test logic and expected behaviors

---

# Search Functionality

This section provides a comprehensive technical deep-dive into the SearchReports functionality, covering its architecture, implementation patterns, performance optimization strategies, and operational considerations. Whether you're debugging search queries, optimizing performance, or extending search capabilities, this guide provides the foundational knowledge you need.

## Architecture Overview

The SearchReports functionality is built on a sophisticated multi-layered architecture that leverages PostgreSQL's advanced search capabilities, combining them with intelligent application-level query optimization and result enhancement:

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                             │
│  Frontend Apps │ API Clients │ Search UIs │ Analytics Tools     │
└─────────────────────────────┬───────────────────────────────────┘
                              │ gRPC/HTTP Requests
┌─────────────────────────────┴───────────────────────────────────┐
│                      gRPC Service Layer                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Request         │ │ Response        │ │ Error           │    │
│  │ Validation      │ │ Formatting      │ │ Handling        │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Parameter       │ │ Result          │ │ Pagination      │    │
│  │ Normalization   │ │ Highlighting    │ │ Management      │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────┬───────────────────────────────────┘
                              │ Repository Interface
┌─────────────────────────────┴───────────────────────────────────┐
│                    Repository Layer                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Query           │ │ Transaction     │ │ Result          │    │
│  │ Construction    │ │ Management      │ │ Mapping         │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Parameter       │ │ Filter          │ │ Session         │    │
│  │ Binding         │ │ Application     │ │ Handling        │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────┬───────────────────────────────────┘
                              │ SQL Queries
┌─────────────────────────────┴───────────────────────────────────┐
│                     Database Layer (PostgreSQL)                │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Query Engine    │ │ Index           │ │ JSONB           │    │
│  │ & Planner       │ │ Utilization     │ │ Operations      │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Full-text       │ │ Transaction     │ │ Constraint      │    │
│  │ Search          │ │ Isolation       │ │ Enforcement     │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────────────────────────────────────────┘
```

### Core Components

#### 1. SearchReports Service Method
- **Purpose**: Primary entry point for all search operations
- **Responsibilities**: Request validation, parameter normalization, response assembly
- **Key Features**: Multi-criteria filtering, intelligent query routing, error boundary

#### 2. Dynamic Query Builder
- **Purpose**: Constructs optimized SQL queries based on search criteria
- **Capabilities**: 
  - Conditional JOIN logic (only joins `report_sections` when needed)
  - Parameterized query construction for SQL injection prevention
  - Filter prioritization for optimal query plans
  - Smart index hint generation

#### 3. Multi-Modal Filter Engine
- **Text Search**: ILIKE pattern matching with trigram indexes
- **Exact Filters**: Status, ID, and enum matching with B-tree indexes
- **Date Range Filters**: Time-series queries optimized with BRIN indexes
- **Section-Specific Filters**: JSONB path operations with specialized indexes

#### 4. Intelligent Indexing Strategy
- **Primary Indexes (B-tree)**: Fast exact matches and range queries
- **Trigram Indexes (GIN)**: Efficient partial text matching for ILIKE operations
- **JSONB Indexes**: Path-specific and general content indexing
- **Time-Series Indexes (BRIN)**: Minimal overhead for timestamp ranges

#### 5. Advanced Highlighting Engine
- **Context Window Generation**: 40-character windows around matches
- **Multi-Field Priority**: Intelligent field selection for highlights
- **Performance Optimization**: Limited fragment generation (max 3 per report)
- **Format-Aware Processing**: Handles HTML and plain text content

#### 6. Efficient Pagination System
- **Offset-Based Navigation**: Simple page jumping with LIMIT/OFFSET
- **Total Count Optimization**: Separate optimized COUNT queries
- **Consistent Ordering**: Secondary sort by ID for deterministic results
- **Token-Based State**: Stateless pagination with offset tokens

## Implementation Details

### Query Construction Pipeline

The search implementation follows a systematic 11-step approach to query construction, ensuring optimal performance and maintainable code:

```
1. Request Parameters Intake
       ↓
2. Parameter Validation & Normalization
       ↓
3. Filter Categorization & Prioritization
       ↓
4. JOIN Strategy Determination
       ↓
5. WHERE Clause Construction
       ↓
6. ORDER BY Clause Assembly
       ↓
7. Pagination Parameter Addition
       ↓
8. COUNT Query Generation
       ↓
9. Main Query Execution
       ↓
10. Result Enhancement (Highlighting)
       ↓
11. Response Assembly & Return
```

#### Step-by-Step Breakdown

**Steps 1-3: Input Processing**
- Validate required fields and parameter ranges
- Normalize empty strings to nil, apply defaults
- Categorize filters by type (text/exact/date/section-specific)
- Prioritize filters by selectivity for optimal query plans

**Steps 4-7: Query Construction**
- Determine if `report_sections` JOIN is needed (only when section filters present)
- Build parameterized WHERE conditions with proper operator precedence
- Construct ORDER BY with secondary sort for consistency
- Add LIMIT/OFFSET parameters for pagination

**Steps 8-11: Execution & Enhancement**
- Execute optimized COUNT query for total results
- Execute main query for paginated report IDs
- Fetch complete report objects via `GetReport()`
- Generate contextual highlights and assemble final response

### Filter Types and Implementation Patterns

The search implementation handles four distinct types of filters, each optimized for different data patterns and query performance characteristics:

#### 1. Text Search Filters (ILIKE + GIN Trigram)

**Implementation Pattern:**
```go
// Global search across multiple fields
if req.Query != "" {
    var searchConditions []string
    for _, field := range searchableFields {
        switch field {
        case "title":
            searchConditions = append(searchConditions,
                fmt.Sprintf("r.title ILIKE $%d", parameterPosition))
            queryParameters = append(queryParameters, "%"+req.Query+"%")
            parameterPosition++
        case "narrative":
            requiresSectionJoin = true
            searchConditions = append(searchConditions,
                fmt.Sprintf("(s.type = 1 AND s.content #>> '{narrative,richText}' ILIKE $%d)", parameterPosition))
            queryParameters = append(queryParameters, "%"+req.Query+"%")
            parameterPosition++
        }
    }
    whereClauseConditions = append(whereClauseConditions,
        fmt.Sprintf("(%s)", strings.Join(searchConditions, " OR ")))
}
```

**Optimizations:**
- Uses GIN trigram indexes for fast ILIKE operations
- Conditional JOIN logic to avoid unnecessary table scans
- Parameter binding to prevent SQL injection

#### 2. Exact Value Filters (B-tree Indexes)

**Implementation Pattern:**
```go
// Status filter with multiple values
if len(req.Status) > 0 {
    statusPlaceholders := make([]string, len(req.Status))
    for i, status := range req.Status {
        statusPlaceholders[i] = fmt.Sprintf("$%d", parameterPosition)
        queryParameters = append(queryParameters, int32(status))
        parameterPosition++
    }
    whereClauseConditions = append(whereClauseConditions,
        fmt.Sprintf("r.status IN (%s)", strings.Join(statusPlaceholders, ",")))
}
```

**Optimizations:**
- Uses B-tree indexes for fast equality and IN operations
- Bulk parameter handling for multiple values
- Enum conversion for type safety

#### 3. Date Range Filters (BRIN Indexes)

**Implementation Pattern:**
```go
// Date range with optional boundaries
if req.CreatedAt != nil {
    if req.CreatedAt.From != "" {
        whereClauseConditions = append(whereClauseConditions,
            fmt.Sprintf("r.created_at >= $%d", parameterPosition))
        queryParameters = append(queryParameters, req.CreatedAt.From)
        parameterPosition++
    }
    if req.CreatedAt.To != "" {
        whereClauseConditions = append(whereClauseConditions,
            fmt.Sprintf("r.created_at <= $%d", parameterPosition))
        queryParameters = append(queryParameters, req.CreatedAt.To)
        parameterPosition++
    }
}
```

**Optimizations:**
- Uses BRIN indexes for efficient range scans on time-series data
- Supports open-ended ranges (from-only or to-only)
- ISO 8601 timestamp handling

#### 4. JSONB Content Filters (Specialized Indexes)

**Implementation Pattern:**
```go
// Array search with EXISTS subqueries
if len(req.ResponderAssetIds) > 0 {
    requiresSectionJoin = true
    var responderConditions []string
    for _, assetID := range req.ResponderAssetIds {
        responderConditions = append(responderConditions,
            fmt.Sprintf("EXISTS (SELECT 1 FROM jsonb_array_elements(s.content #> '{incidentDetails,responders}') as resp WHERE resp->>'assetId' = $%d)", parameterPosition))
        queryParameters = append(queryParameters, assetID)
        parameterPosition++
    }
    whereClauseConditions = append(whereClauseConditions, strings.Join(responderConditions, " OR "))
}
```

**Optimizations:**
- Uses GIN indexes on JSONB content for path operations
- EXISTS subqueries to avoid result multiplication
- Path-specific operators for efficient nested access

#### 5. Relation Filters (Advanced Semantic Search)

**Implementation Pattern:**
```go
// Relation type filtering
if len(req.RelationTypes) > 0 {
    requiresRelationJoin = true
    relationTypePlaceholders := make([]string, len(req.RelationTypes))
    for i, relationType := range req.RelationTypes {
        relationTypePlaceholders[i] = fmt.Sprintf("$%d", parameterPosition)
        queryParameters = append(queryParameters, relationType)
        parameterPosition++
    }
    whereClauseConditions = append(whereClauseConditions,
        fmt.Sprintf("relations.relation_type IN (%s)", strings.Join(relationTypePlaceholders, ",")))
}

// Object involvement filtering (bidirectional)
if len(req.RelationInvolvedGlobalIds) > 0 {
    requiresRelationJoin = true
    var involvementConditions []string
    for _, globalId := range req.RelationInvolvedGlobalIds {
        involvementConditions = append(involvementConditions,
            fmt.Sprintf("(relations.object_a_global_id = $%d OR relations.object_b_global_id = $%d)", parameterPosition, parameterPosition+1))
        queryParameters = append(queryParameters, globalId, globalId)
        parameterPosition += 2
    }
    whereClauseConditions = append(whereClauseConditions, strings.Join(involvementConditions, " OR "))
}
```

**Key Features:**
- **Bidirectional Search**: Searches both object_a and object_b for involved objects
- **Multiple ID Types**: Supports global_id, report_scoped_id, and external_id
- **Relation Metadata**: Includes relation type, description, and creator filtering
- **Text Search Integration**: Supports text search on relation descriptions and object names

**Supported Relation Filters:**
- `relationTypes`: Filter by semantic relationship types (e.g., "responded_to_by", "involved_in")
- `relationCreatedByAssetIds`: Filter by who created the relationships
- `relationInvolvedObjectTypes`: Filter by types of objects involved in relationships
- `relationInvolvedGlobalIds`: Filter by specific entities involved in relationships
- `relationInvolvedReportScopedIds`: Filter by report-scoped objects involved in relationships
- `relationInvolvedExternalIds`: Filter by external system objects involved in relationships

**Optimizations:**
- Uses B-tree indexes for exact relation type matching
- Uses composite indexes for common query patterns (report_id + relation_type)
- Uses trigram indexes for text search on descriptions and object names
- Uses partial indexes to exclude NULL values for performance

**Processing Flow:**
```
JSONB Field Filtering
       ↓
ORDER BY Clause Assembly
       ↓
Pagination Parameter Addition
       ↓
Query Execution & Result Processing
```

### Filter Types and Implementation

#### 1. Text Search Filters (ILIKE-based)
- **Implementation**: Uses PostgreSQL's `ILIKE` operator with trigram indexes
- **Supported Fields**: title, id, narrative, entity_list_title, incident_location, reporting_person_name, reference_display_name, responder_display_name, reporting_person_phone_number, reporting_person_role
- **Query Pattern**: `field ILIKE '%search_term%'`
- **Index Type**: GIN trigram indexes (`gin_trgm_ops`)

#### 2. Exact Value Filters
- **Implementation**: Uses `IN` clauses for multiple values, `=` for single values
- **Supported Fields**: status, situation_ids, case_ids, created_by_asset_ids
- **Query Pattern**: `field IN ($1, $2, $3)` or `field = $1`
- **Index Type**: B-tree indexes

#### 3. Date Range Filters
- **Implementation**: Uses `>=` and `<=` operators for timestamp ranges
- **Supported Fields**: created_at, updated_at, assigned_at, completed_at
- **Query Pattern**: `field >= $1 AND field <= $2`
- **Index Type**: BRIN indexes for time-series data

#### 4. Section-Specific Filters (JSONB-based)
- **Implementation**: Uses JSONB path operators and array functions
- **Supported Operations**: 
  - Entity reference searches: `jsonb_array_elements()` with `EXISTS` subqueries
  - Nested field searches: `content #>> '{path,to,field}'`
  - Type filtering: `content #>> '{path}' = value`
- **Index Type**: GIN indexes on JSONB content

### Dynamic JOIN Logic

The system intelligently determines when to JOIN the `report_sections` table:

```sql
-- Base query (reports table only)
SELECT DISTINCT r.id, r.created_at 
FROM reports r
WHERE r.status = $1;

-- With section filters (requires JOIN)
SELECT DISTINCT r.id, r.created_at 
FROM reports r
JOIN report_sections s ON r.id = s.report_id
WHERE r.status = $1 
  AND s.content #>> '{narrative,richText}' ILIKE $2;
```

### Parameter Binding and SQL Injection Prevention

All user inputs are safely parameterized:

```go
// Safe parameter binding
whereClauseConditions = append(whereClauseConditions,
    fmt.Sprintf("r.title ILIKE $%d", parameterPosition))
queryParameters = append(queryParameters, "%"+searchTerm+"%")
parameterPosition++
```

## Example Generated SQL

### Basic Text Search
```sql
SELECT DISTINCT r.id, r.created_at
FROM reports r
WHERE r.title ILIKE $1
ORDER BY r.created_at DESC, r.id ASC
LIMIT $2 OFFSET $3;

-- Parameters: ["%fire incident%", 20, 0]
```

### Complex Multi-Filter Search
```sql
SELECT DISTINCT r.id, r.created_at
FROM reports r
JOIN report_sections s ON r.id = s.report_id
WHERE r.status IN ($1, $2)
  AND r.situation_id IN ($3, $4)
  AND r.created_at >= $5 AND r.created_at <= $6
  AND (r.title ILIKE $7 OR s.content #>> '{narrative,richText}' ILIKE $8)
  AND s.type = 3 
  AND s.content #>> '{incidentDetails,location}' ILIKE $9
  AND EXISTS (
    SELECT 1 FROM jsonb_array_elements(s.content #> '{incidentDetails,responders}') as resp 
    WHERE resp->>'assetId' = $10
  )
ORDER BY r.created_at DESC, r.id ASC
LIMIT $11 OFFSET $12;

-- Parameters: [1, 2, "sit-123", "sit-456", "2025-01-01T00:00:00Z", 
--             "2025-12-31T23:59:59Z", "%fire%", "%fire%", "%Main St%", 
--             "asset_123", 20, 0]
```

## Indexing Strategy

### Primary Indexes (B-tree)
```sql
-- Basic filtering and sorting
CREATE INDEX idx_reports_org_id      ON reports (org_id);
CREATE INDEX idx_reports_status      ON reports (status);
CREATE INDEX idx_reports_sit_id      ON reports (situation_id);
CREATE INDEX idx_reports_case_id     ON reports (case_id);
CREATE INDEX idx_reports_created_by  ON reports (created_by_asset_id);

-- Composite indexes for common query patterns
CREATE INDEX idx_reports_org_assigned_at ON reports (org_id, assigned_at DESC);
```

### Full-Text Search Indexes (GIN Trigram)
```sql
-- Text search optimization
CREATE INDEX idx_reports_id_trgm       ON reports USING gin (id gin_trgm_ops);
CREATE INDEX idx_reports_title_trgm    ON reports USING gin (lower(title) gin_trgm_ops);

-- Section content search
CREATE INDEX gin_report_sections_content ON report_sections USING gin (content);
CREATE INDEX idx_sec_narr_trgm ON report_sections 
  USING gin (lower(content #>> '{narrative,richText}') gin_trgm_ops);
CREATE INDEX idx_sec_list_title_trgm ON report_sections 
  USING gin (lower(content #>> '{entityList,title}') gin_trgm_ops);
```

### JSONB Indexes
```sql
-- Entity reference searches
CREATE INDEX idx_sec_entity_refs_jsonb ON report_sections 
  USING gin ((content #> '{entityList,entityRefs}'));
  
-- Incident details searches
CREATE INDEX idx_sec_responders_jsonb ON report_sections 
  USING gin ((content #> '{incidentDetails,responders}'));

-- Offense content searches
CREATE INDEX idx_sec_offenses_jsonb ON report_sections 
  USING gin ((content #> '{offenseList,offenses}'));
CREATE INDEX idx_sec_offenses_type_trgm ON report_sections 
  USING gin (lower(jsonb_array_elements(content #> '{offenseList,offenses}') ->> 'offense_type') gin_trgm_ops);
CREATE INDEX idx_sec_offense_metadata ON report_sections 
  USING gin ((content #> '{offenseList,metadata}'));
CREATE INDEX idx_sec_offenses_non_empty ON report_sections 
  (report_id) WHERE content #> '{offenseList,offenses}' IS NOT NULL;
```

### Relation Indexes
```sql
-- Basic relation filtering
CREATE INDEX idx_report_relations_report_id ON report_relations (report_id);
CREATE INDEX idx_report_relations_type ON report_relations (relation_type);
CREATE INDEX idx_report_relations_created_by ON report_relations (created_by_asset_id);

-- Object reference indexes
CREATE INDEX idx_report_relations_obj_a_type ON report_relations (object_a_type);
CREATE INDEX idx_report_relations_obj_b_type ON report_relations (object_b_type);
CREATE INDEX idx_report_relations_obj_a_global_id ON report_relations (object_a_global_id);
CREATE INDEX idx_report_relations_obj_b_global_id ON report_relations (object_b_global_id);

-- Text search on relation content
CREATE INDEX idx_relations_desc_trgm ON report_relations 
  USING gin (lower(description) gin_trgm_ops);
CREATE INDEX idx_relations_obj_a_name_trgm ON report_relations 
  USING gin (lower(object_a_display_name) gin_trgm_ops);
CREATE INDEX idx_relations_obj_b_name_trgm ON report_relations 
  USING gin (lower(object_b_display_name) gin_trgm_ops);

-- Composite indexes for common patterns
CREATE INDEX idx_report_relations_report_type_created ON report_relations 
  (report_id, relation_type, created_at DESC);
CREATE INDEX idx_report_relations_obj_type_global ON report_relations 
  (object_a_type, object_a_global_id, object_b_type, object_b_global_id);

-- Performance optimizations with partial indexes
CREATE INDEX idx_relations_obj_a_name_non_null ON report_relations (object_a_display_name) 
  WHERE object_a_display_name IS NOT NULL;
CREATE INDEX idx_relations_obj_b_name_non_null ON report_relations (object_b_display_name) 
  WHERE object_b_display_name IS NOT NULL;
```

### Time-Series Indexes (BRIN)
```sql
-- Efficient range queries on timestamp columns
CREATE INDEX brin_reports_created_at   ON reports USING brin (created_at);
CREATE INDEX brin_reports_updated_at   ON reports USING brin (updated_at);
CREATE INDEX brin_reports_completed_at ON reports USING brin (completed_at);
```

## Searchable Content

### Top-Level Report Fields
| Field | Type | Search Method | Index Type |
|-------|------|---------------|------------|
| id | String | Partial match (ILIKE) | GIN trigram |
| title | String | Partial match (ILIKE) | GIN trigram |
| status | Enum | Exact match (IN) | B-tree |
| situation_id | String | Exact match (IN) | B-tree |
| case_id | String | Exact match (IN) | B-tree |
| created_by_asset_id | String | Exact match (IN) | B-tree |
| created_at | Timestamp | Range queries (>=, <=) | BRIN |
| updated_at | Timestamp | Range queries (>=, <=) | BRIN |
| assigned_at | Timestamp | Range queries (>=, <=) | BRIN |
| completed_at | Timestamp | Range queries (>=, <=) | BRIN |

### Section-Specific Content
| Section Type | Field Path | Search Method | Index Type |
|--------------|------------|---------------|------------|
| NARRATIVE | `{narrative,richText}` | Partial match (ILIKE) | GIN trigram |
| ENTITY_LIST | `{entityList,title}` | Partial match (ILIKE) | GIN trigram |
| ENTITY_LIST | `{entityList,entityRefs}` | Array contains (EXISTS) | GIN JSONB |
| INCIDENT_DETAILS | `{incidentDetails,location}` | Partial match (ILIKE) | GIN trigram |
| INCIDENT_DETAILS | `{incidentDetails,initialType}` | Exact match (=) | B-tree expression |
| INCIDENT_DETAILS | `{incidentDetails,finalType}` | Exact match (=) | B-tree expression |
| INCIDENT_DETAILS | `{incidentDetails,responders}` | Array contains (EXISTS) | GIN JSONB |
| INCIDENT_DETAILS | `{incidentDetails,reportingPerson}` | Nested field search | GIN trigram |
| INCIDENT_DETAILS | `{incidentDetails,involvedAgencies}` | Array contains (EXISTS) | GIN JSONB |
| OFFENSE | `{offenseList,offenses}` | Array contains (EXISTS) | GIN JSONB |
| OFFENSE | `{offenseList,offenses}[].offense_type` | Partial match (ILIKE) | GIN trigram |
| OFFENSE | `{offenseList,metadata}` | JSONB field search | GIN JSONB |

### Relation Content
| Field | Type | Search Method | Index Type |
|-------|------|---------------|------------|
| relation_type | String | Exact match (IN) | B-tree |
| description | String | Partial match (ILIKE) | GIN trigram |
| object_a_display_name | String | Partial match (ILIKE) | GIN trigram |
| object_b_display_name | String | Partial match (ILIKE) | GIN trigram |
| object_a_type | String | Exact match (IN) | B-tree |
| object_b_type | String | Exact match (IN) | B-tree |
| object_a_global_id | String | Exact match (IN) | B-tree |
| object_b_global_id | String | Exact match (IN) | B-tree |
| object_a_report_scoped_id | String | Exact match (IN) | B-tree |
| object_b_report_scoped_id | String | Exact match (IN) | B-tree |
| object_a_external_id | String | Exact match (IN) | B-tree |
| object_b_external_id | String | Exact match (IN) | B-tree |
| created_by_asset_id | String | Exact match (IN) | B-tree |

## Architectural Decisions and Trade-offs

### Design Decisions

1. **ILIKE vs Full-Text Search (ts_vector)**
   - **Chosen**: ILIKE with trigram indexes
   - **Rationale**: Simpler implementation, better for exact phrase matching, easier debugging
   - **Trade-off**: Less sophisticated ranking, no stemming support

2. **Conditional JOINs vs Always JOIN**
   - **Chosen**: Conditional JOINs based on filter requirements
   - **Rationale**: Better performance for report-only queries
   - **Trade-off**: More complex query construction logic

3. **Parameterized Queries vs Query Building**
   - **Chosen**: Fully parameterized queries with dynamic construction
   - **Rationale**: Security (SQL injection prevention), query plan caching
   - **Trade-off**: More complex parameter management

4. **BRIN vs B-tree for Timestamps**
   - **Chosen**: BRIN indexes for timestamp columns
   - **Rationale**: Much smaller index size, optimized for time-series data
   - **Trade-off**: Less efficient for non-sequential access patterns

5. **Offset-based vs Cursor-based Pagination**
   - **Chosen**: Offset-based pagination with LIMIT/OFFSET
   - **Rationale**: Simpler implementation, supports jumping to arbitrary pages
   - **Trade-off**: Performance degradation for large offsets

### Performance Trade-offs

#### Advantages
- **Index Diversity**: Multiple index types optimized for different query patterns
- **Selective JOINs**: Avoids unnecessary table joins when not needed
- **Efficient Text Search**: Trigram indexes provide fast ILIKE operations
- **Time-Series Optimization**: BRIN indexes for efficient date range queries

#### Limitations
- **Complex Query Plans**: Multiple filter types can lead to suboptimal query plans
- **Index Maintenance**: Large number of indexes increases write overhead
- **Memory Usage**: GIN indexes can consume significant memory
- **Large Offset Performance**: OFFSET-based pagination becomes slow for large values

## Performance Considerations

### Query Performance Optimization

1. **Index Selection Priority**
   ```sql
   -- Most selective filters first
   WHERE r.org_id = $1           -- Most selective (B-tree)
     AND r.status IN ($2, $3)    -- Selective (B-tree)
     AND r.created_at >= $4      -- Range (BRIN)
     AND r.title ILIKE $5        -- Text search (GIN)
   ```

2. **JOIN Optimization**
   - Only JOIN `report_sections` when section-specific filters are present
   - Use `EXISTS` subqueries for array searches to avoid result multiplication
   - Ensure proper index coverage for JOIN conditions

3. **Memory Management**
   - Limit result set size (max 100 per page)
   - Use streaming for large result processing
   - Implement connection pooling for database access

### Monitoring and Metrics

#### Key Performance Indicators
- **Query Execution Time**: Track p95/p99 latencies
- **Index Usage**: Monitor index hit ratios and unused indexes
- **Memory Consumption**: Track working memory usage for complex queries
- **Cache Hit Rates**: Monitor query plan cache effectiveness

#### Performance Monitoring Queries
```sql
-- Query performance analysis
EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) 
SELECT DISTINCT r.id FROM reports r WHERE ...;

-- Index usage statistics
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('reports', 'report_sections');
```

## Future Improvement Opportunities

### Short-term Enhancements (Next 6 months)

1. **Media Search Implementation** 🎯
   - Add search support for media section titles, file captions, and display names
   - Create GIN trigram indexes for media content fields
   - Implement JSONB search for file metadata and categories
   - Add file category filtering (image/video/audio/document/other)

2. **Query Performance Optimization**
   - Implement query result caching for common search patterns
   - Add query plan analysis and automatic index recommendations
   - Optimize highlighting algorithm for better performance

3. **Search Feature Expansion**
   - Add fuzzy search capabilities for typo tolerance
   - Implement search query suggestions and auto-completion
   - Support for search within comment content

4. **API Improvements**
   - Add search result aggregations (counts by status, date ranges)
   - Implement saved search functionality
   - Support for search result export in various formats

### Medium-term Enhancements (6-12 months)

1. **Advanced Search Capabilities**
   - Implement full-text search with `ts_vector` for better ranking
   - Add geographical search for location-based queries
   - Support for complex boolean search expressions

2. **Performance Scaling**
   - Implement search result caching layer (Redis)
   - Add read replicas for search-heavy workloads
   - Implement partition pruning for time-based queries

3. **Analytics and Insights**
   - Search analytics dashboard for usage patterns
   - Performance monitoring and alerting
   - Search relevance feedback and tuning

### Long-term Vision (12+ months)

1. **Search Infrastructure Evolution**
   - Evaluate dedicated search engines (Elasticsearch, OpenSearch)
   - Implement real-time search index synchronization
   - Support for federated search across multiple data sources

2. **AI-Powered Search**
   - Natural language query processing
   - Semantic search capabilities
   - Automated content categorization and tagging

3. **Enterprise Features**
   - Advanced access control for search results
   - Search audit logging and compliance
   - Multi-tenant search isolation

## TLDR

**What**: Comprehensive search functionality for reports with text search, filtering, highlighting, and advanced relation-based semantic search capabilities.

**How**: PostgreSQL-based implementation using multiple index types (B-tree, GIN trigram, BRIN, JSONB) with dynamic query construction, conditional JOINs, and bidirectional relation filtering.

**Key Features**:
- 🔍 **Text Search**: ILIKE-based search across 15+ fields with trigram indexes
- 🎯 **Exact Filters**: Status, ID-based, and enum filtering with B-tree indexes  
- 📅 **Date Ranges**: Time-series queries optimized with BRIN indexes
- 🗂️ **JSONB Search**: Section content search with path operators and array functions
- 🔗 **Relation Search**: Advanced semantic search across object relationships with bidirectional filtering
- 🔦 **Highlighting**: Contextual text fragments showing matched terms
- 📄 **Pagination**: Offset-based pagination with total result counts

**Current Limitations**:
- ⚠️ **Media Content**: Media sections (files, captions, titles) are not yet searchable - requires future implementation

**Performance**:
- Supports 100+ concurrent search requests
- Sub-100ms response times for most queries
- Efficient memory usage with selective JOINs and conditional relation filtering
- Scales to millions of reports with proper indexing

**Trade-offs**:
- ✅ Security via parameterized queries
- ✅ Flexible filtering with good performance  
- ✅ Simple implementation and maintenance
- ✅ Advanced relation-based semantic search
- ❌ Limited search ranking sophistication
- ❌ Performance degradation for large pagination offsets
- ❌ High index maintenance overhead

**Perfect for**: Organizations needing robust report search with moderate scale requirements (< 10M reports) where implementation simplicity, security, and advanced relationship modeling are priorities over advanced search features.

---

## Search Best Practices and Troubleshooting

### Performance Optimization Guidelines

#### Query Performance Best Practices

1. **Filter Ordering for Optimal Performance**
   ```json
   // ✅ Good: Most selective filters first
   {
     "status": ["REPORT_STATUS_APPROVED"],           // High selectivity
     "createdByAssetIds": ["asset_123"],            // High selectivity  
     "createdAt": {"from": "2025-01-01T00:00:00Z"}, // Medium selectivity
     "query": "fire"                                // Low selectivity
   }
   
   // ❌ Avoid: Text search without constraining filters
   {
     "query": "the"  // Too common, will scan many rows
   }
   ```

2. **Efficient Field Selection**
   ```json
   // ✅ Good: Limit search fields for better performance
   {
     "query": "fire incident",
     "searchFields": ["title", "narrative"]  // Only search specific fields
   }
   
   // ❌ Avoid: Broad searches across all fields
   {
     "query": "fire incident"  // Searches all 10+ fields
   }
   ```

3. **Pagination Best Practices**
   ```json
   // ✅ Good: Reasonable page sizes
   {
     "pageSize": 20,  // Good default
     "pageToken": "40"
   }
   
   // ❌ Avoid: Large page sizes or deep pagination
   {
     "pageSize": 1000,  // Too large, causes memory issues
     "pageToken": "50000"  // Deep pagination is slow
   }
   ```

#### Index Utilization Guidelines

1. **Monitor Index Usage**
   ```sql
   -- Check index usage statistics
   SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read
   FROM pg_stat_user_indexes 
   WHERE tablename IN ('reports', 'report_sections')
   ORDER BY idx_scan DESC;
   ```

2. **Analyze Query Plans**
   ```sql
   -- Analyze search query performance
   EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
   SELECT DISTINCT r.id FROM reports r 
   WHERE r.title ILIKE '%fire%'
   ORDER BY r.created_at DESC LIMIT 20;
   ```

### Common Issues and Solutions

#### 1. Slow Query Performance

**Symptoms:**
- Response times > 500ms
- High CPU usage during searches
- Query timeouts

**Diagnosis:**
```sql
-- Check slow queries
SELECT query, mean_exec_time, calls
FROM pg_stat_statements 
WHERE query LIKE '%SearchReports%'
ORDER BY mean_exec_time DESC;
```

**Solutions:**
- Add missing indexes for frequently filtered fields
- Reduce search scope with more selective filters
- Consider materialized views for complex aggregations
- Implement query result caching

#### 2. Memory Usage Issues

**Symptoms:**
- Out of memory errors
- High memory consumption
- Connection pool exhaustion

**Solutions:**
```go
// Limit result set size
if req.PageSize > 100 {
    req.PageSize = 100  // Enforce maximum
}

// Use streaming for large result processing
// Implement connection pooling
```

#### 3. Inconsistent Search Results

**Symptoms:**
- Results appearing/disappearing between pages
- Duplicate results across pages
- Missing expected results

**Solutions:**
- Ensure consistent ordering with secondary sort by ID
- Use stable sort fields (avoid sorting by frequently updated fields)
- Implement proper transaction isolation

#### 4. Highlighting Performance Issues

**Symptoms:**
- Slow response times with highlighting enabled
- Memory spikes during highlight generation

**Solutions:**
```go
// Optimize highlighting
const maxFragments = 3
const contextWindow = 40

// Limit highlighting to first few matches
if len(highlights) >= maxFragments {
    break
}
```

### Monitoring and Observability

#### Key Metrics to Track

1. **Performance Metrics**
   - Query execution time (p50, p95, p99)
   - Total request volume and QPS
   - Error rates and timeout frequencies
   - Database connection pool utilization

2. **Business Metrics**
   - Search success rate (results > 0)
   - Popular search terms and filters
   - User search patterns and session flow
   - Feature adoption (field queries, highlighting usage)

#### Monitoring Queries

```sql
-- Search performance overview
SELECT 
    DATE_TRUNC('hour', created_at) as hour,
    COUNT(*) as search_count,
    AVG(response_time_ms) as avg_response_time,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY response_time_ms) as p95_response_time
FROM search_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY hour ORDER BY hour;

-- Index efficiency check
SELECT 
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    ROUND(idx_tup_read::numeric / NULLIF(idx_scan, 0), 2) as avg_tuples_per_scan
FROM pg_stat_user_indexes 
WHERE tablename = 'reports'
ORDER BY idx_scan DESC;
```

### Development and Testing Guidelines

#### Unit Testing Patterns

```go
// Test search functionality
func TestSearchReports_BasicTextSearch(t *testing.T) {
    req := &reports.SearchReportsRequest{
        Query:    "fire incident",
        PageSize: 10,
    }
    
    resp, err := repo.SearchReports(ctx, nil, req)
    assert.NoError(t, err)
    assert.NotEmpty(t, resp.Reports)
    assert.Contains(t, resp.Highlights, resp.Reports[0].Id)
}

// Test complex filtering
func TestSearchReports_MultiCriteriaFilter(t *testing.T) {
    req := &reports.SearchReportsRequest{
        Status: []reports.ReportStatus{reports.ReportStatus_REPORT_STATUS_APPROVED},
        CreatedAt: &reports.DateRange{
            From: "2025-01-01T00:00:00Z",
            To:   "2025-12-31T23:59:59Z",
        },
        FieldQueries: []*reports.FieldQuery{
            {Field: "title", Query: "fire"},
            {Field: "incident_location", Query: "Main St"},
        },
    }
    
    resp, err := repo.SearchReports(ctx, nil, req)
    assert.NoError(t, err)
    // Verify all returned reports meet the criteria
    for _, report := range resp.Reports {
        assert.Equal(t, reports.ReportStatus_REPORT_STATUS_APPROVED, report.Status)
        assert.Contains(t, strings.ToLower(report.Title), "fire")
    }
}
```

#### Integration Testing

```go
// Test with real database and indexes
func TestSearchReports_Integration(t *testing.T) {
    // Create test reports with known content
    // Execute search requests
    // Verify results match expectations
    // Check performance within acceptable bounds
}
```

### Future Enhancement Roadmap

#### Phase 1: Performance Optimization (Next 3 months)
- Implement query result caching with Redis
- Add database read replicas for search workloads
- Optimize highlighting algorithm performance
- Add comprehensive performance monitoring

#### Phase 2: Search Feature Expansion (3-6 months)
- Fuzzy search capabilities for typo tolerance
- Auto-complete and search suggestions
- Saved searches and search history
- Advanced boolean query support

#### Phase 3: Advanced Capabilities (6-12 months)
- Full-text search with ts_vector for better ranking
- Geographical search for location-based queries
- Machine learning-powered relevance scoring
- Real-time search result updates

---

# Report Reviewing Workflow

This document provides a comprehensive guide to the report reviewing workflow implementation, focusing on the interaction between reports, review rounds, and orders. It's designed to help developers understand the system's architecture, state management, and side effects.

## Overview

The report reviewing workflow is a sophisticated multi-level review process that manages the lifecycle of reports from creation to final approval. The system uses a combination of:
- Report statuses to track the overall state
- Review rounds to manage the review process
- Orders to handle task assignments and workflow progression

## Key Components

### Report Statuses
Each report goes through a series of status changes that reflect its current state in the workflow:

| Status | Description | Trigger | Side Effects |
|--------|-------------|---------|--------------|
| `REPORT_STATUS_ASSIGNED` | Initial state when report is created | Report creation | Creates `WRITE_REPORT` order |
| `REPORT_STATUS_IN_PROGRESS` | Author is actively writing | Order acknowledgment | None |
| `REPORT_STATUS_SUBMITTED_FOR_REVIEW` | Author has submitted for review | Author submission | Creates review round and order |
| `REPORT_STATUS_UNDER_REVIEW` | Report is being reviewed | Review round creation | None |
| `REPORT_STATUS_CHANGES_REQUESTED` | Reviewer requested changes | Review round changes | Creates `REVISE_REPORT` order |
| `REPORT_STATUS_IN_REWORK` | Author reworking changes | Order acknowledgment | None |
| `REPORT_STATUS_APPROVED` | Report fully approved | Final review approval | Completes all orders |
| `REPORT_STATUS_REJECTED` | Report permanently rejected | Manual rejection | Cancels all orders |
| `REPORT_STATUS_CANCELLED` | Report process cancelled | Manual cancellation | Cancels all orders |

### Review Round Statuses
Review rounds track the state of each review level:

| Status | Description | Next Actions |
|--------|-------------|--------------|
| `REVIEW_STATUS_AWAITING_ACTION` | Waiting for reviewer to take action | Reviewer can approve or request changes |
| `REVIEW_STATUS_APPROVED` | Review round is approved | Creates next level or finalizes report |
| `REVIEW_STATUS_CHANGES_REQUESTED` | Reviewer requested changes | Author must revise and resubmit |

### Order Types
Orders manage task assignments and workflow progression:

| Order Type | Assigned To | Purpose | Created When |
|------------|-------------|---------|--------------|
| `ORDER_TYPE_WRITE_REPORT` | Report author | Initial writing task | Report creation |
| `ORDER_TYPE_REVIEW_REPORT` | Reviewer | Review task | Report submission |
| `ORDER_TYPE_REVISE_REPORT` | Report author | Revision task | Changes requested |

## Detailed Workflow Stages

### 1. Report Creation and Initial Writing

**Purpose:**
- Initialize a new report and assign it to an author
- Set up the initial writing task
- Begin the report lifecycle

**Process Flow:**
1. **Report Creation**
   - Client requests report creation with basic metadata
   - System creates report in `ASSIGNED` state
   - `ReportSideEffect` triggers to create initial writing task
   - Creates `WRITE_REPORT` order for the author
   - All operations are atomic within a transaction

2. **Order Acknowledgment**
   - Author acknowledges the writing task
   - `OrderSideEffect` handles the acknowledgment
   - Updates report status to `IN_PROGRESS`
   - Author can now begin writing the report

```mermaid
sequenceDiagram
    participant Client
    participant System
    participant ReportSideEffect
    participant OrderSideEffect
    participant Database
    
    Client->>System: CreateReport request
    System->>Database: Begin Transaction
    System->>Database: Insert report (ASSIGNED)
    Database-->>System: Report created
    System->>ReportSideEffect: Trigger ASSIGNED status
    ReportSideEffect->>Database: Create WRITE_REPORT order
    Database-->>ReportSideEffect: Order created
    ReportSideEffect-->>System: Side effect completed
    System->>Database: Commit Transaction
    System-->>Client: Report created response
    
    Note over Client,Database: Author acknowledges order
    
    Client->>System: AcknowledgeOrder request
    System->>Database: Begin Transaction
    System->>OrderSideEffect: Trigger order acknowledgment
    OrderSideEffect->>Database: Update report status to IN_PROGRESS
    Database-->>OrderSideEffect: Status updated
    OrderSideEffect-->>System: Side effect completed
    System->>Database: Commit Transaction
    System-->>Client: Order acknowledged response
```

**Implementation Details:**
- **System Components:**
  - `ReportService`: Handles report creation and status updates
  - `ReportSideEffect`: Manages report status side effects
  - `OrderSideEffect`: Handles order-related side effects
  - `Database`: Manages data persistence

### 2. Submitting for Review

**Purpose:**
- Transition report from writing to review phase
- Initialize the review workflow
- Create first review round
- Assign initial reviewer

**Process Flow:**
1. **Review Submission**
   - Author submits report for review
   - System updates status to `SUBMITTED_FOR_REVIEW`
   - `ReportSideEffect` handles the submission:
     - Completes the `WRITE_REPORT` order
     - Creates first review round
     - Creates `REVIEW_REPORT` order
     - Updates status to `UNDER_REVIEW`
   - Reviewer is automatically assigned based on organization configuration

2. **Review Round Initialization**
   - Review round is created with `AWAITING_ACTION` status
   - Reviewer is notified of new review task
   - Report version is snapshotted for review
   - Review timeline is initialized

```mermaid
sequenceDiagram
    participant Client
    participant System
    participant ReportSideEffect
    participant OrderSideEffect
    participant Database
    
    Client->>System: SubmitForReview request
    System->>Database: Begin Transaction
    System->>Database: Update report status to SUBMITTED_FOR_REVIEW
    Database-->>System: Status updated
    System->>ReportSideEffect: Trigger SUBMITTED_FOR_REVIEW status
    ReportSideEffect->>Database: Complete WRITE_REPORT order
    Database-->>ReportSideEffect: Order completed
    ReportSideEffect->>Database: Create review round (level 1)
    Database-->>ReportSideEffect: Review round created
    ReportSideEffect->>Database: Create REVIEW_REPORT order
    Database-->>ReportSideEffect: Order created
    ReportSideEffect->>Database: Update status to UNDER_REVIEW
    Database-->>ReportSideEffect: Status updated
    ReportSideEffect-->>System: Side effect completed
    System->>Database: Commit Transaction
    System-->>Client: Review submission response
```

**Implementation Details:**
- **System Components:**
  - `ReportService`: Handles review submission
  - `ReportSideEffect`: Manages review-related side effects
  - `OrderSideEffect`: Handles order transitions
  - `ReviewRoundService`: Manages review round creation

### 3. Review Process

**Purpose:**
- Enable reviewers to evaluate report content
- Support multi-level review workflow
- Manage review decisions and feedback
- Track review history

**Process Flow:**

#### When Changes are Requested:
1. **Change Request**
   - Reviewer requests changes with feedback
   - System updates review round status to `CHANGES_REQUESTED`
   - `ReviewSideEffect` handles the change request:
     - Completes current `REVIEW_REPORT` order
     - Creates `REVISE_REPORT` order for author
     - Updates report status to `IN_REWORK`
   - Author is notified of requested changes

2. **Feedback Management**
   - Review comments are preserved
   - Change request details are recorded
   - Review round is marked as requiring revision
   - Author is assigned revision task

```mermaid
sequenceDiagram
    participant Client
    participant System
    participant ReviewSideEffect
    participant OrderSideEffect
    participant Database
    
    Client->>System: RequestChanges request
    System->>Database: Begin Transaction
    System->>Database: Update review round status to CHANGES_REQUESTED
    Database-->>System: Status updated
    System->>ReviewSideEffect: Trigger changes requested
    ReviewSideEffect->>Database: Complete REVIEW_REPORT order
    Database-->>ReviewSideEffect: Order completed
    ReviewSideEffect->>Database: Create REVISE_REPORT order
    Database-->>ReviewSideEffect: Order created
    ReviewSideEffect->>Database: Update report status to IN_REWORK
    Database-->>ReviewSideEffect: Status updated
    ReviewSideEffect-->>System: Side effect completed
    System->>Database: Commit Transaction
    System-->>Client: Changes requested response
```

#### When Review is Approved:
1. **Review Approval**
   - Reviewer approves the report
   - System updates review round status to `APPROVED`
   - `ReviewSideEffect` handles the approval:
     - Completes current `REVIEW_REPORT` order
     - If not last level:
       - Creates next review round
       - Creates new `REVIEW_REPORT` order
     - If last level:
       - Updates report status to `APPROVED`
       - Completes all remaining orders

2. **Workflow Progression**
   - Review history is updated
   - Next level is initialized if needed
   - Final approval triggers completion
   - All stakeholders are notified

```mermaid
sequenceDiagram
    participant Client
    participant System
    participant ReviewSideEffect
    participant OrderSideEffect
    participant Database
    
    Client->>System: ApproveReviewRound request
    System->>Database: Begin Transaction
    System->>Database: Update review round status to APPROVED
    Database-->>System: Status updated
    System->>ReviewSideEffect: Trigger review approved
    ReviewSideEffect->>Database: Complete REVIEW_REPORT order
    Database-->>ReviewSideEffect: Order completed
    
    alt Not last level
        ReviewSideEffect->>Database: Create next review round
        Database-->>ReviewSideEffect: Review round created
        ReviewSideEffect->>Database: Create REVIEW_REPORT order
        Database-->>ReviewSideEffect: Order created
    else Last level
        ReviewSideEffect->>Database: Update report status to APPROVED
        Database-->>ReviewSideEffect: Status updated
        ReviewSideEffect->>Database: Complete all remaining orders
        Database-->>ReviewSideEffect: Orders completed
    end
    
    ReviewSideEffect-->>System: Side effect completed
    System->>Database: Commit Transaction
    System-->>Client: Review approved response
```

**Implementation Details:**
- **System Components:**
  - `ReviewService`: Handles review decisions
  - `ReviewSideEffect`: Manages review-related side effects
  - `OrderSideEffect`: Handles order transitions
  - `WorkflowService`: Manages workflow progression

### 4. Revision Process

**Purpose:**
- Enable authors to address review feedback
- Track revision history
- Maintain review context
- Support iterative improvements

**Process Flow:**
1. **Revision Initiation**
   - Author acknowledges revision task
   - `OrderSideEffect` handles acknowledgment
   - Updates report status to `IN_REWORK`
   - Author can begin making changes

2. **Revision Submission**
   - Author submits revised report
   - System completes `REVISE_REPORT` order
   - `ReviewSideEffect` handles resubmission:
     - Creates new review round
     - Creates new `REVIEW_REPORT` order
     - Updates status to `UNDER_REVIEW`
   - Original reviewer is notified

3. **Version Management**
   - New version is created
   - Previous versions are preserved
   - Review history is maintained
   - Changes are tracked

```mermaid
sequenceDiagram
    participant Client
    participant System
    participant OrderSideEffect
    participant ReviewSideEffect
    participant Database
    
    Client->>System: AcknowledgeOrder request
    System->>Database: Begin Transaction
    System->>OrderSideEffect: Trigger order acknowledgment
    OrderSideEffect->>Database: Update report status to IN_REWORK
    Database-->>OrderSideEffect: Status updated
    OrderSideEffect-->>System: Side effect completed
    System->>Database: Commit Transaction
    System-->>Client: Order acknowledged response
    
    Note over Client,Database: Author submits revisions
    
    Client->>System: SubmitRevisions request
    System->>Database: Begin Transaction
    System->>Database: Complete REVISE_REPORT order
    Database-->>System: Order completed
    System->>ReviewSideEffect: Trigger revision submission
    ReviewSideEffect->>Database: Create new review round
    Database-->>ReviewSideEffect: Review round created
    ReviewSideEffect->>Database: Create REVIEW_REPORT order
    Database-->>ReviewSideEffect: Order created
    ReviewSideEffect->>Database: Update status to UNDER_REVIEW
    Database-->>ReviewSideEffect: Status updated
    ReviewSideEffect-->>System: Side effect completed
    System->>Database: Commit Transaction
    System-->>Client: Revisions submitted response
```

**Implementation Details:**
- **System Components:**
  - `OrderService`: Handles order management
  - `OrderSideEffect`: Manages order-related side effects
  - `ReviewSideEffect`: Handles review round creation
  - `ReportService`: Manages report status updates

## Review Level Configuration

The review workflow is configurable through the `ReviewReportWorkflow` configuration:

```json
{
  "levels": [
    {
      "level": 1,
      "requiredAssetTypes": ["SUPERVISOR"],
      "autoAssign": true
    },
    {
      "level": 2,
      "requiredAssetTypes": ["MANAGER"],
      "autoAssign": false
    }
  ]
}
```

**Configuration Options:**
- Number of review levels
- Required asset types for each level
- Auto-assignment settings
- Review level hierarchy
- Due date policies
- Notification settings

## Implementation Best Practices

1. **Transaction Management**
   - All status changes and order operations must be atomic
   - Use database transactions for consistency
   - Handle rollbacks appropriately

2. **Error Handling**
   - Validate state transitions
   - Handle concurrent modifications
   - Implement proper error recovery

3. **Performance Considerations**
   - Index frequently queried fields
   - Cache review workflow configurations
   - Optimize order queries

4. **Testing Guidelines**
   - Test all state transitions
   - Verify side effects
   - Check concurrent operations
   - Validate configuration changes

## Common Development Tasks

### Adding a New Review Level
1. Update organization configuration
2. Add new asset type requirements
3. Test workflow progression
4. Verify order creation

### Modifying Review Process
1. Update status transition logic
2. Modify side effect handlers
3. Update order management
4. Test all affected workflows

### Customizing Review Assignment
1. Modify auto-assignment logic
2. Update reviewer selection
3. Add custom assignment rules
4. Test assignment scenarios

## Troubleshooting Guide

### Common Issues:
1. **Order Creation Failures**
   - Check transaction logs
   - Verify side effect handlers
   - Validate state transitions

2. **Review Round Issues**
   - Check reviewer assignments
   - Verify level configuration
   - Review status transitions

3. **Status Update Problems**
   - Check concurrent modifications
   - Verify transaction isolation
   - Review side effect chain

## API Reference

Key endpoints for workflow management:

1. `SubmitForReview`: Initiates review process
2. `ApproveReviewRound`: Handles review approval
3. `RequestChanges`: Manages change requests
4. `UpdateReportStatus`: Updates report state
5. `CreateReviewRound`: Creates new review rounds

For detailed API documentation, see the [API Reference](#api-reference) section.

## Future Considerations

1. **Planned Enhancements**
   - Parallel review support
   - Custom review workflows
   - Enhanced notification system

2. **Known Limitations**
   - Single reviewer per level
   - Sequential review process
   - Limited customization options

3. **Migration Paths**
   - Version upgrade procedures
   - Configuration migration
   - Data migration strategies
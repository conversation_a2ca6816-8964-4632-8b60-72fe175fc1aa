package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"
	"common/database"
	"common/herosentry"
	workflowUtils "workflow/internal/common/utils"

	reports "proto/hero/reports/v2"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
)

// PostgresReportRepository provides CRUD over hero.reports.
type PostgresReportRepository struct {
	database *sql.DB
}

// NewPostgresReportRepository creates a new repository.
func NewPostgresReportRepository(databaseConnection *sql.DB) *PostgresReportRepository {
	return &PostgresReportRepository{database: databaseConnection}
}

// nullIfEmpty returns nil for empty strings (so SQL NULL), else inputString.
func nullIfEmpty(inputString string) interface{} {
	if inputString == "" {
		return nil
	}
	return inputString
}

// insertSnapshot fetches the full report by ID, marshals it, and inserts it into the report_snapshots table.
func (repository *PostgresReportRepository) insertSnapshot(
	context context.Context,
	transaction *sql.Tx,
	reportID string,
) error {
	// Fetch the full report object using GetReport
	report, err := repository.GetReport(context, transaction, reportID)
	if err != nil {
		// If GetReport fails (e.g., report not found after an update attempt), wrap the error.
		herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase, "Failed to get report for snapshotting")
		return fmt.Errorf("insertSnapshot: failed to get report %s for snapshotting: %w", reportID, err)
	}

	// Marshal the fetched report
	marshalOptions := protojson.MarshalOptions{EmitUnpopulated: true, UseEnumNumbers: true}
	snapshotJSON, marshalError := marshalOptions.Marshal(report)
	if marshalError != nil {
		herosentry.CaptureException(context, marshalError, herosentry.ErrorTypeInternal, "Failed to marshal report snapshot")
		return fmt.Errorf("insertSnapshot: marshal snapshot for report %s version %d: %w", report.Id, report.Version, marshalError)
	}

	const insertSnapshotSQL = `
		INSERT INTO report_snapshots (
		  report_id, version, report_snapshot, created_at
		) VALUES ($1,$2,$3,$4)
	`
	// Use the report's UpdatedAt time for the snapshot creation time if available, otherwise use Now()
	snapshotTime, parseErr := time.Parse(time.RFC3339Nano, report.UpdatedAt)
	if parseErr != nil {
		snapshotTime = time.Now() // Fallback if UpdatedAt is not parseable (should not happen in normal flow)
	}

	if _, execError := transaction.ExecContext(
		context,
		insertSnapshotSQL,
		report.Id,      // Use ID from the fetched report
		report.Version, // Use Version from the fetched report
		snapshotJSON,
		snapshotTime, // Use the determined snapshot time
	); execError != nil {
		herosentry.CaptureException(context, execError, herosentry.ErrorTypeDatabase, "Failed to insert report snapshot")
		return fmt.Errorf("insertSnapshot: insert snapshot for report %s version %d: %w", report.Id, report.Version, execError)
	}
	return nil
}

// CreateReport inserts a new report and its initial snapshot.
func (repository *PostgresReportRepository) CreateReport(
	context context.Context,
	transaction *sql.Tx,
	newReport *reports.Report,
) (*reports.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.CreateReport")
	defer finishSpan()

	span.SetTag("report.type", newReport.ReportType.String())
	span.SetTag("report.status", newReport.Status.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.Report, error) {
		// 1. Ensure report ID
		if newReport.Id == "" {
			newReport.Id = uuid.New().String()
		}

		// 2. Retrieve organization ID
		organizationID := cmncontext.GetOrgId(spanContext)
		newReport.OrgId = organizationID

		// Set created_by_asset_id from context
		createdByAssetID, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
		if err != nil {
			log.Printf("error: failed to get createdByAssetID from context: %v", err)
			createdByAssetID = SystemAssetID
		}

		// 4. Set resource type and initial version
		newReport.ResourceType = FixedResourceTypeReport
		if newReport.Version == 0 {
			newReport.Version = 1
		}

		// 5. Populate timestamps - store time.Time in DB, format for protobuf
		creationTime := time.Now().UTC() // Always use UTC for consistency
		timestampString := creationTime.Format(time.RFC3339Nano)

		// Set protobuf string fields
		newReport.AssignedAt = timestampString
		newReport.UpdatedAt = timestampString
		newReport.CreatedAt = timestampString
		if newReport.Status == reports.ReportStatus_REPORT_STATUS_APPROVED ||
			newReport.Status == reports.ReportStatus_REPORT_STATUS_REJECTED ||
			newReport.Status == reports.ReportStatus_REPORT_STATUS_CANCELLED {
			newReport.CompletedAt = timestampString
		}

		// 6. Marshal additional_info_json - defensive handling for protobuf Struct
		var additionalInfoJSON []byte
		if newReport.AdditionalInfoJson != nil {
			// Safely convert protobuf Struct to JSON with error handling
			additionalInfoMap := newReport.AdditionalInfoJson.AsMap()
			if additionalInfoMap == nil {
				// Handle case where Struct is not nil but AsMap() returns nil
				additionalInfoJSON = []byte("{}")
			} else {
				var marshalError error
				additionalInfoJSON, marshalError = json.Marshal(additionalInfoMap)
				if marshalError != nil {
					herosentry.CaptureException(spanContext, marshalError, herosentry.ErrorTypeInternal, "Failed to marshal additional_info_json")
					return nil, fmt.Errorf("CreateReport: failed to marshal additional_info_json: %w", marshalError)
				}
			}
		} else {
			additionalInfoJSON = []byte("{}") // Default to empty JSON object if nil
		}

		// 7. Insert top-level report row
		const insertReportSQL = `
			INSERT INTO reports (
			  id, org_id, author_asset_id, title, status,
			  assigned_at, updated_at, completed_at,
			  additional_info_json, version,
			  situation_id, case_id,
			  watcher_asset_ids, resource_type, created_at, created_by_asset_id, report_type
			) VALUES (
			  $1,$2,$3,$4,$5,
			  $6,$7,$8,
			  $9,$10,
			  $11,$12,
			  $13,$14,$15,$16,$17
			)
		`
		var completedParam interface{}
		if newReport.CompletedAt != "" {
			completedParam = creationTime
		}

		if _, execError := sessionTx.ExecContext(
			spanContext,
			insertReportSQL,
			newReport.Id,
			organizationID,
			nullIfEmpty(newReport.AuthorAssetId),
			newReport.Title,
			int32(newReport.Status),
			creationTime,
			creationTime,
			completedParam,
			additionalInfoJSON,
			newReport.Version,
			nullIfEmpty(newReport.SituationId),
			nullIfEmpty(newReport.CaseId),
			pq.Array(newReport.WatcherAssetIds),
			newReport.ResourceType,
			creationTime,
			createdByAssetID,
			int32(newReport.ReportType),
		); execError != nil {
			herosentry.CaptureException(spanContext, execError, herosentry.ErrorTypeDatabase, "Failed to insert report row")
			return nil, fmt.Errorf("CreateReport: failed to insert report row: %w", execError)
		}

		// 8. Insert sections
		const insertSectionSQL = `
			INSERT INTO report_sections (
			  id, report_id, type, content, created_at, updated_at
			) VALUES ($1,$2,$3,$4,$5,$6)
		`
		for _, section := range newReport.Sections {
			// Generate ID if empty
			if section.Id == "" {
				section.Id = uuid.New().String()
			}
			// marshal oneof content
			var contentJSON []byte
			marshalOptions := protojson.MarshalOptions{EmitUnpopulated: true, UseEnumNumbers: true}
			switch section.Type {
			case reports.SectionType_SECTION_TYPE_NARRATIVE:
				narrativeSection := section.GetNarrative()
				if narrativeSection == nil {
					narrativeSection = &reports.NarrativeContent{}
				}
				// Generate ID for narrative content if missing
				if narrativeSection.Id == "" {
					narrativeSection.Id = uuid.New().String()
				}
				contentJSON, _ = marshalOptions.Marshal(narrativeSection)
				contentJSON = []byte(fmt.Sprintf(`{"narrative":%s}`, contentJSON))

			case reports.SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE,
				reports.SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE,
				reports.SectionType_SECTION_TYPE_ENTITY_LIST_PROPERTIES,
				reports.SectionType_SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS:
				entityListSection := section.GetEntityList()
				if entityListSection == nil {
					entityListSection = &reports.EntityListContent{}
				}
				// Generate ID for entity list content if missing
				if entityListSection.Id == "" {
					entityListSection.Id = uuid.New().String()
				}
				contentJSON, _ = marshalOptions.Marshal(entityListSection)
				contentJSON = []byte(fmt.Sprintf(`{"entityList":%s}`, contentJSON))

			case reports.SectionType_SECTION_TYPE_PROPERTY:
				propertyListSection := section.GetPropertyList()
				if propertyListSection == nil {
					propertyListSection = &reports.PropertyListContent{}
				}
				// Generate ID for property list content if missing
				if propertyListSection.Id == "" {
					propertyListSection.Id = uuid.New().String()
				}
				contentJSON, _ = marshalOptions.Marshal(propertyListSection)
				contentJSON = []byte(fmt.Sprintf(`{"propertyList":%s}`, contentJSON))

			case reports.SectionType_SECTION_TYPE_ARREST:
				arrestSection := section.GetArrestList()
				if arrestSection == nil {
					arrestSection = &reports.ArrestContent{}
				}
				// Generate ID for arrest content if missing
				if arrestSection.Id == "" {
					arrestSection.Id = uuid.New().String()
				}
				contentJSON, _ = marshalOptions.Marshal(arrestSection)
				contentJSON = []byte(fmt.Sprintf(`{"arrestList":%s}`, contentJSON))

			case reports.SectionType_SECTION_TYPE_INCIDENT_DETAILS:
				incidentDetails := section.GetIncidentDetails()
				if incidentDetails == nil {
					incidentDetails = &reports.IncidentDetailsContent{}
				}
				// Generate ID for incident details content if missing
				if incidentDetails.Id == "" {
					incidentDetails.Id = uuid.New().String()
				}
				// Generate IDs for responders if missing
				for _, responder := range incidentDetails.Responders {
					if responder.Id == "" {
						responder.Id = uuid.New().String()
					}
				}
				// Generate ID for reporting person if missing
				if incidentDetails.ReportingPerson != nil && incidentDetails.ReportingPerson.Id == "" {
					incidentDetails.ReportingPerson.Id = uuid.New().String()
				}
				// Generate IDs for involved agencies if missing
				for _, agency := range incidentDetails.InvolvedAgencies {
					if agency.Id == "" {
						agency.Id = uuid.New().String()
					}
				}
				contentJSON, _ = marshalOptions.Marshal(incidentDetails)
				contentJSON = []byte(fmt.Sprintf(`{"incidentDetails":%s}`, contentJSON))

			case reports.SectionType_SECTION_TYPE_OFFENSE:
				offenseSection := section.GetOffenseList()
				if offenseSection == nil {
					offenseSection = &reports.OffenseContent{}
				}
				// Generate ID for offense content if missing
				if offenseSection.Id == "" {
					offenseSection.Id = uuid.New().String()
				}
				// Generate IDs for individual offenses if missing
				for _, offense := range offenseSection.Offenses {
					if offense.Id == "" {
						offense.Id = uuid.New().String()
					}
				}
				contentJSON, _ = marshalOptions.Marshal(offenseSection)
				contentJSON = []byte(fmt.Sprintf(`{"offenseList":%s}`, contentJSON))

			case reports.SectionType_SECTION_TYPE_MEDIA:
				mediaSection := section.GetMediaList()
				if mediaSection == nil {
					mediaSection = &reports.MediaContent{}
				}
				// Generate ID for media content if missing
				if mediaSection.Id == "" {
					mediaSection.Id = uuid.New().String()
				}
				// Generate IDs for file references if missing
				for _, fileRef := range mediaSection.FileRefs {
					if fileRef.Id == "" {
						fileRef.Id = uuid.New().String()
					}
				}
				contentJSON, _ = marshalOptions.Marshal(mediaSection)
				contentJSON = []byte(fmt.Sprintf(`{"mediaList":%s}`, contentJSON))

			default:
				contentJSON = []byte(`{}`)
			}

			if _, execError := sessionTx.ExecContext(
				spanContext,
				insertSectionSQL,
				section.Id,
				newReport.Id,
				int32(section.Type),
				contentJSON,
				creationTime,
				creationTime,
			); execError != nil {
				herosentry.CaptureException(spanContext, execError, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to insert section %q", section.Id))
				return nil, fmt.Errorf("CreateReport: insert section %q: %w", section.Id, execError)
			}

			// 9. Insert section-scoped comments
			const insertCommentSQL = `
				INSERT INTO report_comments (
				  id, report_id, section_id, reply_to_comment_id,
				  author_asset_id, text,
				  created_at, updated_at,
				  resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
				) VALUES (
				  $1,$2,$3,$4,
				  $5,$6,
				  $7,$8,
				  $9,$10,$11,$12,$13
				)
			`
			for _, comment := range section.Comments {
				if comment.Id == "" {
					comment.Id = uuid.New().String()
				}
				comment.ReportId = newReport.Id
				comment.SectionId = section.Id
				comment.CreatedAt = timestampString
				comment.UpdatedAt = timestampString
				comment.ResourceType = FixedResourceTypeComment

				var resolvedTimestamp interface{}
				if comment.Resolved && comment.ResolvedAt != "" {
					if parsedTime, parseError := time.Parse(time.RFC3339Nano, comment.ResolvedAt); parseError == nil {
						resolvedTimestamp = parsedTime
					}
				}

				if _, execError := sessionTx.ExecContext(
					spanContext,
					insertCommentSQL,
					comment.Id,
					comment.ReportId,
					comment.SectionId,
					nullIfEmpty(comment.ReplyToCommentId),
					nullIfEmpty(comment.AuthorAssetId),
					comment.Text,
					creationTime,
					creationTime,
					comment.Resolved,
					resolvedTimestamp,
					nullIfEmpty(comment.ResolvedByAssetId),
					comment.ResourceType,
					nullIfEmpty(comment.DisplayName),
				); execError != nil {
					herosentry.CaptureException(spanContext, execError, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to insert comment %s", comment.Id))
					return nil, fmt.Errorf("CreateReport: insert comment %q: %w", comment.Id, execError)
				}
			}
		}

		// 10. Insert global comments
		const insertGlobalCommentSQL = `
			INSERT INTO report_comments (
			  id, report_id, section_id, reply_to_comment_id,
			  author_asset_id, text,
			  created_at, updated_at,
			  resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
			) VALUES (
			  $1,$2,NULL,$3,
			  $4,$5,
			  $6,$7,
			  $8,$9,$10,$11,$12
			)
		`
		for _, comment := range newReport.Comments {
			if comment.Id == "" {
				comment.Id = uuid.New().String()
			}
			comment.ReportId = newReport.Id
			comment.CreatedAt = timestampString
			comment.UpdatedAt = timestampString
			comment.ResourceType = FixedResourceTypeComment

			var resolvedTimestamp interface{}
			if comment.Resolved && comment.ResolvedAt != "" {
				if parsedTime, parseError := time.Parse(time.RFC3339Nano, comment.ResolvedAt); parseError == nil {
					resolvedTimestamp = parsedTime
				}
			}

			if _, execError := sessionTx.ExecContext(
				spanContext,
				insertGlobalCommentSQL,
				comment.Id,
				comment.ReportId,
				nullIfEmpty(comment.ReplyToCommentId),
				nullIfEmpty(comment.AuthorAssetId),
				comment.Text,
				creationTime,
				creationTime,
				comment.Resolved,
				resolvedTimestamp,
				nullIfEmpty(comment.ResolvedByAssetId),
				comment.ResourceType,
				nullIfEmpty(comment.DisplayName),
			); execError != nil {
				herosentry.CaptureException(spanContext, execError, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to insert global comment %s", comment.Id))
				return nil, fmt.Errorf("CreateReport: insert global comment %q: %w", comment.Id, execError)
			}
		}

		// 11. Insert review rounds
		const insertReviewRoundSQL = `
			INSERT INTO report_review_rounds (
			  id, report_id,
			  reviewer_asset_id, level, status,
			  sent_to_level, sent_to_asset_id,
			  requested_at, resolved_at,
			  round_note, snapshot_version,
			  due_at, create_by_asset_id, note_for_reviewer
			) VALUES (
			  $1,$2,
			  $3,$4,$5,
			  $6,$7,
			  $8,$9,
			  $10,$11,
			  $12,$13,$14
			)
		`
		for _, reviewRound := range newReport.ReviewRounds {
			if reviewRound.Id == "" {
				reviewRound.Id = uuid.New().String()
			}
			requestedAtTime, _ := time.Parse(time.RFC3339Nano, reviewRound.RequestedAt)

			var resolvedTimestamp interface{}
			if reviewRound.ResolvedAt != "" {
				if parsedTime, parseError := time.Parse(time.RFC3339Nano, reviewRound.ResolvedAt); parseError == nil {
					resolvedTimestamp = parsedTime
				}
			}

			if _, execError := sessionTx.ExecContext(
				spanContext,
				insertReviewRoundSQL,
				reviewRound.Id,
				newReport.Id,
				nullIfEmpty(reviewRound.ReviewerAssetId),
				reviewRound.Level,
				int32(reviewRound.Status),
				reviewRound.SentToLevel,
				nullIfEmpty(reviewRound.SentToAssetId),
				requestedAtTime,
				resolvedTimestamp,
				nullIfEmpty(reviewRound.RoundNote),
				reviewRound.SnapshotVersion,
				nullIfEmpty(reviewRound.DueAt), // if empty string, returns nil
				nullIfEmpty(reviewRound.CreateByAssetId),
				nullIfEmpty(reviewRound.NoteForReviewer),
			); execError != nil {
				herosentry.CaptureException(spanContext, execError, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to insert review round %s", reviewRound.Id))
				return nil, fmt.Errorf("AddReviewRound: insert review round: %w", execError)
			}
		}

		// 12. Insert relations
		const insertRelationSQL = `
			INSERT INTO report_relations (
				id, report_id, relation_type, description,
				object_a_type, object_a_report_scoped_id, object_a_global_id, object_a_external_id, object_a_section_id, object_a_display_name, object_a_metadata,
				object_b_type, object_b_report_scoped_id, object_b_global_id, object_b_external_id, object_b_section_id, object_b_display_name, object_b_metadata,
				metadata, created_at, updated_at, created_by_asset_id
			) VALUES (
				$1, $2, $3, $4,
				$5, $6, $7, $8, $9, $10, $11,
				$12, $13, $14, $15, $16, $17, $18,
				$19, $20, $21, $22
			)
		`
		for _, relation := range newReport.Relations {
			// Generate ID if empty
			if relation.Id == "" {
				relation.Id = uuid.New().String()
			}

			// Set report ID and timestamps
			relation.ReportId = newReport.Id
			relation.CreatedAt = timestampString
			relation.UpdatedAt = timestampString
			relation.CreatedByAssetId = createdByAssetID

			// Marshal object metadata to JSON
			objectAMetadataJSON := []byte("{}")
			if relation.ObjectA != nil && relation.ObjectA.Metadata != nil {
				if metadataMap := relation.ObjectA.Metadata.AsMap(); metadataMap != nil {
					if jsonBytes, err := json.Marshal(metadataMap); err == nil {
						objectAMetadataJSON = jsonBytes
					}
				}
			}

			objectBMetadataJSON := []byte("{}")
			if relation.ObjectB != nil && relation.ObjectB.Metadata != nil {
				if metadataMap := relation.ObjectB.Metadata.AsMap(); metadataMap != nil {
					if jsonBytes, err := json.Marshal(metadataMap); err == nil {
						objectBMetadataJSON = jsonBytes
					}
				}
			}

			relationMetadataJSON := []byte("{}")
			if relation.Metadata != nil {
				if metadataMap := relation.Metadata.AsMap(); metadataMap != nil {
					if jsonBytes, err := json.Marshal(metadataMap); err == nil {
						relationMetadataJSON = jsonBytes
					}
				}
			}

			// Insert relation
			if _, execError := sessionTx.ExecContext(spanContext, insertRelationSQL,
				relation.Id,
				relation.ReportId,
				relation.RelationType,
				nullIfEmpty(relation.Description),
				nullIfEmpty(relation.ObjectA.ObjectType),
				nullIfEmpty(relation.ObjectA.ReportScopedId),
				nullIfEmpty(relation.ObjectA.GlobalId),
				nullIfEmpty(relation.ObjectA.ExternalId),
				nullIfEmpty(relation.ObjectA.SectionId),
				nullIfEmpty(relation.ObjectA.DisplayName),
				objectAMetadataJSON,
				nullIfEmpty(relation.ObjectB.ObjectType),
				nullIfEmpty(relation.ObjectB.ReportScopedId),
				nullIfEmpty(relation.ObjectB.GlobalId),
				nullIfEmpty(relation.ObjectB.ExternalId),
				nullIfEmpty(relation.ObjectB.SectionId),
				nullIfEmpty(relation.ObjectB.DisplayName),
				objectBMetadataJSON,
				relationMetadataJSON,
				creationTime,
				creationTime,
				createdByAssetID,
			); execError != nil {
				herosentry.CaptureException(spanContext, execError, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to insert relation %s", relation.Id))
				return nil, fmt.Errorf("CreateReport: insert relation %q: %w", relation.Id, execError)
			}
		}

		// 13. Insert initial snapshot
		if err := repository.insertSnapshot(spanContext, sessionTx, newReport.Id); err != nil { // Pass ID only
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert initial snapshot")
			return nil, fmt.Errorf("CreateReport: %w", err) // Error is already descriptive
		}

		return newReport, nil
	})
}

// GetReport retrieves a report by ID, including its sections, comments, and review rounds.
// GetReport retrieves a report with its sections, comments, and review rounds in batched fashion.
func (repository *PostgresReportRepository) GetReport(
	context context.Context,
	parentTx *sql.Tx,
	reportIdentifier string,
) (*reports.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.GetReport")
	defer finishSpan()

	span.SetTag("report.id", reportIdentifier)

	return database.WithSession(repository.database, spanContext, parentTx, func(sessionTx *sql.Tx) (*reports.Report, error) {
		//------------------------------------------------------------------
		// 1. Main report record (no cursor → nothing to close)
		//------------------------------------------------------------------
		const mainQuery = `
            SELECT
              id, org_id, author_asset_id, title, status,
              assigned_at, updated_at, completed_at,
              additional_info_json, version,
              situation_id, case_id,
              watcher_asset_ids, resource_type, created_at, created_by_asset_id, report_type
            FROM reports WHERE id = $1
        `

		report := &reports.Report{}
		var (
			authorAssetID                 sql.NullString
			completedAtTime               sql.NullTime
			situationIdentifier           sql.NullString
			caseIdentifier                sql.NullString
			additionalInfoJSON            []byte
			watcherAssetIDSlice           []string
			assignedAtTime, updatedAtTime time.Time
			createdAtTime                 time.Time
			createdByAssetID              sql.NullString
			reportTypeInt32               int32
		)

		if err := sessionTx.QueryRowContext(spanContext, mainQuery, reportIdentifier).Scan(
			&report.Id, &report.OrgId, &authorAssetID, &report.Title,
			(*int32)(&report.Status), &assignedAtTime, &updatedAtTime, &completedAtTime,
			&additionalInfoJSON, &report.Version,
			&situationIdentifier, &caseIdentifier,
			pq.Array(&watcherAssetIDSlice), &report.ResourceType, &createdAtTime,
			&createdByAssetID, &reportTypeInt32,
		); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, ErrReportNotFound
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan report row")
			return nil, fmt.Errorf("GetReport: %w", err)
		}

		report.AuthorAssetId = authorAssetID.String
		report.AssignedAt = assignedAtTime.Format(time.RFC3339Nano)
		report.UpdatedAt = updatedAtTime.Format(time.RFC3339Nano)
		report.CreatedAt = createdAtTime.Format(time.RFC3339Nano)
		report.ReportType = reports.ReportType(reportTypeInt32) // Add report type support
		if completedAtTime.Valid {
			report.CompletedAt = completedAtTime.Time.Format(time.RFC3339Nano)
		}
		if situationIdentifier.Valid {
			report.SituationId = situationIdentifier.String
		}
		if caseIdentifier.Valid {
			report.CaseId = caseIdentifier.String
		}
		report.WatcherAssetIds = watcherAssetIDSlice
		if createdByAssetID.Valid {
			report.CreatedByAssetId = createdByAssetID.String
		}

		var additionalInfo map[string]interface{}
		if err := json.Unmarshal(additionalInfoJSON, &additionalInfo); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to unmarshal additional_info_json")
			return nil, fmt.Errorf("GetReport: invalid additional_info_json: %w", err)
		}
		if structData, err := structpb.NewStruct(additionalInfo); err == nil {
			report.AdditionalInfoJson = structData
		} else {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to convert to structpb")
			return nil, fmt.Errorf("GetReport: struct conversion: %w", err)
		}

		//------------------------------------------------------------------
		// 2. Load every section in one shot
		//------------------------------------------------------------------
		const sectionsQuery = `
            SELECT id, report_id, type, content, created_at, updated_at
            FROM report_sections
            WHERE report_id = $1
            ORDER BY id ASC
        `
		sectionRows, err := sessionTx.QueryContext(spanContext, sectionsQuery, reportIdentifier)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to query sections for report %s", reportIdentifier))
			return nil, fmt.Errorf("GetReport: query sections: %w", err)
		}

		sectionMap := make(map[string]*reports.ReportSection) // id → ptr
		sectionIDs := make([]string, 0)

		for sectionRows.Next() {
			// ---- scan into local variables --------------------------------------
			var (
				dbSectionID        string
				dbReportID         string
				dbTypeInt32        int32
				sectionContentJSON []byte
				sectionCreatedTime time.Time
				sectionUpdatedTime time.Time
			)
			if err := sectionRows.Scan(
				&dbSectionID, &dbReportID, &dbTypeInt32,
				&sectionContentJSON, &sectionCreatedTime, &sectionUpdatedTime,
			); err != nil {
				sectionRows.Close()
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan section row")
				return nil, fmt.Errorf("GetReport: scan section: %w", err)
			}

			// ---- unmarshal JSON into a fresh ReportSection ----------------------
			section := &reports.ReportSection{}
			if err := protojson.Unmarshal(sectionContentJSON, section); err != nil {
				sectionRows.Close()
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to unmarshal section content")
				return nil, fmt.Errorf("GetReport: unmarshal section content: %w", err)
			}

			// ---- add the scalar / enum fields that were NOT in the JSON ---------
			section.Id = dbSectionID
			section.ReportId = dbReportID
			section.Type = reports.SectionType(dbTypeInt32)
			section.CreatedAt = sectionCreatedTime.Format(time.RFC3339Nano)
			section.UpdatedAt = sectionUpdatedTime.Format(time.RFC3339Nano)

			// ---- store -----------------------------------------------------------
			report.Sections = append(report.Sections, section)
			sectionMap[dbSectionID] = section
			sectionIDs = append(sectionIDs, dbSectionID)
		}

		if err := sectionRows.Err(); err != nil {
			sectionRows.Close()
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to iterate sections")
			return nil, fmt.Errorf("GetReport: iterate sections: %w", err)
		}
		sectionRows.Close()

		//------------------------------------------------------------------
		// 3. One query fetches **all** section-scoped comments
		//------------------------------------------------------------------
		if len(sectionIDs) > 0 {
			const allCommentsQuery = `
                SELECT
                  id, report_id, section_id, reply_to_comment_id,
                  author_asset_id, text,
                  created_at, updated_at,
                  resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
                FROM report_comments
                WHERE report_id = $1 AND section_id = ANY($2)
                ORDER BY created_at DESC
            `
			commentRows, err := sessionTx.QueryContext(spanContext, allCommentsQuery, reportIdentifier, pq.Array(sectionIDs))
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query section comments")
				return nil, fmt.Errorf("GetReport: query section comments: %w", err)
			}

			for commentRows.Next() {
				comment := &reports.Comment{}
				var (
					replyToCommentID     sql.NullString
					resolvedAtTime       sql.NullTime
					resolvedByAssetID    sql.NullString
					displayName          sql.NullString
					createdAtCommentTime time.Time
					updatedAtCommentTime time.Time
				)
				var sectionID string
				if err := commentRows.Scan(
					&comment.Id, &comment.ReportId, &sectionID,
					&replyToCommentID, &comment.AuthorAssetId, &comment.Text,
					&createdAtCommentTime, &updatedAtCommentTime,
					&comment.Resolved, &resolvedAtTime,
					&resolvedByAssetID, &comment.ResourceType, &displayName,
				); err != nil {
					commentRows.Close()
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan section comment")
					return nil, fmt.Errorf("GetReport: scan section comment: %w", err)
				}

				comment.SectionId = sectionID
				comment.CreatedAt = createdAtCommentTime.Format(time.RFC3339Nano)
				comment.UpdatedAt = updatedAtCommentTime.Format(time.RFC3339Nano)
				if replyToCommentID.Valid {
					comment.ReplyToCommentId = replyToCommentID.String
				}
				if resolvedByAssetID.Valid {
					comment.ResolvedByAssetId = resolvedByAssetID.String
				}
				if resolvedAtTime.Valid {
					comment.ResolvedAt = resolvedAtTime.Time.Format(time.RFC3339Nano)
				}
				if displayName.Valid {
					comment.DisplayName = displayName.String
				}

				// push into its owning section
				if owner, ok := sectionMap[sectionID]; ok {
					owner.Comments = append(owner.Comments, comment)
				}
			}
			if err := commentRows.Err(); err != nil {
				commentRows.Close()
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to iterate section comments")
				return nil, fmt.Errorf("GetReport: iterate section comments: %w", err)
			}
			commentRows.Close()
		}

		//------------------------------------------------------------------
		// 4. Global comments (single query, flat loop, immediate close)
		//------------------------------------------------------------------
		const globalCommentsQuery = `
            SELECT
              id, report_id, reply_to_comment_id, author_asset_id, text,
              created_at, updated_at,
              resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
            FROM report_comments
            WHERE report_id = $1 AND section_id IS NULL
            ORDER BY created_at DESC
        `
		globalRows, err := sessionTx.QueryContext(spanContext, globalCommentsQuery, reportIdentifier)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query global comments")
			return nil, fmt.Errorf("GetReport: query global comments: %w", err)
		}

		for globalRows.Next() {
			comment := &reports.Comment{}
			var (
				replyToCommentID     sql.NullString
				resolvedAtTime       sql.NullTime
				createdAtCommentTime time.Time
				updatedAtCommentTime time.Time
				displayName          sql.NullString
				resolvedByAssetID    sql.NullString
			)
			if err := globalRows.Scan(
				&comment.Id, &comment.ReportId,
				&replyToCommentID, &comment.AuthorAssetId, &comment.Text,
				&createdAtCommentTime, &updatedAtCommentTime,
				&comment.Resolved, &resolvedAtTime,
				&resolvedByAssetID, &comment.ResourceType, &displayName,
			); err != nil {
				globalRows.Close()
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan global comment")
				return nil, fmt.Errorf("GetReport: scan global comment: %w", err)
			}
			comment.CreatedAt = createdAtCommentTime.Format(time.RFC3339Nano)
			comment.UpdatedAt = updatedAtCommentTime.Format(time.RFC3339Nano)
			if replyToCommentID.Valid {
				comment.ReplyToCommentId = replyToCommentID.String
			}
			if resolvedAtTime.Valid {
				comment.ResolvedAt = resolvedAtTime.Time.Format(time.RFC3339Nano)
			}
			if resolvedByAssetID.Valid {
				comment.ResolvedByAssetId = resolvedByAssetID.String
			}
			if displayName.Valid {
				comment.DisplayName = displayName.String
			}
			report.Comments = append(report.Comments, comment)
		}
		if err := globalRows.Err(); err != nil {
			globalRows.Close()
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to iterate global comments")
			return nil, fmt.Errorf("GetReport: iterate global comments: %w", err)
		}
		globalRows.Close()

		//------------------------------------------------------------------
		// 5. Review rounds (single query, flat loop)
		//------------------------------------------------------------------
		const reviewRoundsQuery = `
            SELECT
              id, reviewer_asset_id, level, status,
              sent_to_level, sent_to_asset_id,
              requested_at, resolved_at,
              round_note, snapshot_version,
              due_at, create_by_asset_id, note_for_reviewer
            FROM report_review_rounds
            WHERE report_id = $1
            ORDER BY requested_at DESC
        `
		reviewRows, err := sessionTx.QueryContext(spanContext, reviewRoundsQuery, reportIdentifier)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query review rounds")
			return nil, fmt.Errorf("GetReport: query review rounds: %w", err)
		}

		for reviewRows.Next() {
			reviewRound := &reports.ReviewRound{}
			var (
				reviewStatusInt32 int32
				resolvedAtTime    sql.NullTime
				dueAtTime         sql.NullTime
				noteForReviewer   sql.NullString
				// Use NullString for potentially null string columns
				reviewerAssetID sql.NullString
				sentToAssetID   sql.NullString
				roundNote       sql.NullString
				createByAssetID sql.NullString
			)
			if err := reviewRows.Scan(
				&reviewRound.Id, &reviewerAssetID, &reviewRound.Level,
				&reviewStatusInt32, &reviewRound.SentToLevel, &sentToAssetID,
				&reviewRound.RequestedAt, &resolvedAtTime, &roundNote,
				&reviewRound.SnapshotVersion, &dueAtTime,
				&createByAssetID, &noteForReviewer,
			); err != nil {
				reviewRows.Close()
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan review round")
				return nil, fmt.Errorf("GetReport: scan review round: %w", err)
			}
			reviewRound.Status = reports.ReviewStatus(reviewStatusInt32)
			if reviewerAssetID.Valid {
				reviewRound.ReviewerAssetId = reviewerAssetID.String
			}
			if sentToAssetID.Valid {
				reviewRound.SentToAssetId = sentToAssetID.String
			}
			if resolvedAtTime.Valid {
				reviewRound.ResolvedAt = resolvedAtTime.Time.Format(time.RFC3339Nano)
			}
			if roundNote.Valid {
				reviewRound.RoundNote = roundNote.String
			}
			if dueAtTime.Valid {
				reviewRound.DueAt = dueAtTime.Time.Format(time.RFC3339Nano)
			}
			if createByAssetID.Valid {
				reviewRound.CreateByAssetId = createByAssetID.String
			}
			if noteForReviewer.Valid {
				reviewRound.NoteForReviewer = noteForReviewer.String
			}
			report.ReviewRounds = append(report.ReviewRounds, reviewRound)
		}
		if err := reviewRows.Err(); err != nil {
			reviewRows.Close()
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to iterate review rounds")
			return nil, fmt.Errorf("GetReport: iterate review rounds: %w", err)
		}
		reviewRows.Close()

		//------------------------------------------------------------------
		// 6. Load Relations for this report
		//------------------------------------------------------------------
		const relationsQuery = `
            SELECT
              id, report_id, relation_type, description,
              object_a_type, object_a_report_scoped_id, object_a_global_id, object_a_external_id, object_a_section_id, object_a_display_name, object_a_metadata,
              object_b_type, object_b_report_scoped_id, object_b_global_id, object_b_external_id, object_b_section_id, object_b_display_name, object_b_metadata,
              metadata, created_at, updated_at, created_by_asset_id
            FROM report_relations
            WHERE report_id = $1
            ORDER BY created_at DESC
        `
		relationsRows, err := sessionTx.QueryContext(spanContext, relationsQuery, reportIdentifier)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to query relations for report %s", reportIdentifier))
			return nil, fmt.Errorf("GetReport: query relations: %w", err)
		}

		for relationsRows.Next() {
			relation := &reports.Relation{}
			var (
				description      sql.NullString
				objectAMetadata  sql.NullString
				objectBMetadata  sql.NullString
				relationMetadata sql.NullString
				createdAt        time.Time
				updatedAt        time.Time
				createdByAssetID sql.NullString
				// Object A fields
				objectAType        sql.NullString
				objectAScopedID    sql.NullString
				objectAGlobalID    sql.NullString
				objectAExternalID  sql.NullString
				objectASectionID   sql.NullString
				objectADisplayName sql.NullString
				// Object B fields
				objectBType        sql.NullString
				objectBScopedID    sql.NullString
				objectBGlobalID    sql.NullString
				objectBExternalID  sql.NullString
				objectBSectionID   sql.NullString
				objectBDisplayName sql.NullString
			)

			if err := relationsRows.Scan(
				&relation.Id, &relation.ReportId, &relation.RelationType, &description,
				&objectAType, &objectAScopedID, &objectAGlobalID, &objectAExternalID, &objectASectionID, &objectADisplayName, &objectAMetadata,
				&objectBType, &objectBScopedID, &objectBGlobalID, &objectBExternalID, &objectBSectionID, &objectBDisplayName, &objectBMetadata,
				&relationMetadata, &createdAt, &updatedAt, &createdByAssetID,
			); err != nil {
				relationsRows.Close()
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan relation")
				return nil, fmt.Errorf("GetReport: scan relation: %w", err)
			}

			// Populate relation fields
			if description.Valid {
				relation.Description = description.String
			}
			relation.CreatedAt = createdAt.Format(time.RFC3339Nano)
			relation.UpdatedAt = updatedAt.Format(time.RFC3339Nano)
			if createdByAssetID.Valid {
				relation.CreatedByAssetId = createdByAssetID.String
			}

			// Populate object A
			relation.ObjectA = &reports.ObjectReference{}
			if objectAType.Valid {
				relation.ObjectA.ObjectType = objectAType.String
			}
			if objectAScopedID.Valid {
				relation.ObjectA.ReportScopedId = objectAScopedID.String
			}
			if objectAGlobalID.Valid {
				relation.ObjectA.GlobalId = objectAGlobalID.String
			}
			if objectAExternalID.Valid {
				relation.ObjectA.ExternalId = objectAExternalID.String
			}
			if objectASectionID.Valid {
				relation.ObjectA.SectionId = objectASectionID.String
			}
			if objectADisplayName.Valid {
				relation.ObjectA.DisplayName = objectADisplayName.String
			}

			// Populate object B
			relation.ObjectB = &reports.ObjectReference{}
			if objectBType.Valid {
				relation.ObjectB.ObjectType = objectBType.String
			}
			if objectBScopedID.Valid {
				relation.ObjectB.ReportScopedId = objectBScopedID.String
			}
			if objectBGlobalID.Valid {
				relation.ObjectB.GlobalId = objectBGlobalID.String
			}
			if objectBExternalID.Valid {
				relation.ObjectB.ExternalId = objectBExternalID.String
			}
			if objectBSectionID.Valid {
				relation.ObjectB.SectionId = objectBSectionID.String
			}
			if objectBDisplayName.Valid {
				relation.ObjectB.DisplayName = objectBDisplayName.String
			}

			// Parse JSON metadata if present
			if objectAMetadata.Valid && objectAMetadata.String != "" {
				var metadataMap map[string]interface{}
				if err := json.Unmarshal([]byte(objectAMetadata.String), &metadataMap); err == nil {
					if structData, err := structpb.NewStruct(metadataMap); err == nil {
						relation.ObjectA.Metadata = structData
					}
				}
			}

			if objectBMetadata.Valid && objectBMetadata.String != "" {
				var metadataMap map[string]interface{}
				if err := json.Unmarshal([]byte(objectBMetadata.String), &metadataMap); err == nil {
					if structData, err := structpb.NewStruct(metadataMap); err == nil {
						relation.ObjectB.Metadata = structData
					}
				}
			}

			if relationMetadata.Valid && relationMetadata.String != "" {
				var metadataMap map[string]interface{}
				if err := json.Unmarshal([]byte(relationMetadata.String), &metadataMap); err == nil {
					if structData, err := structpb.NewStruct(metadataMap); err == nil {
						relation.Metadata = structData
					}
				}
			}

			report.Relations = append(report.Relations, relation)
		}
		if err := relationsRows.Err(); err != nil {
			relationsRows.Close()
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("GetReport: iterate relations: %w", err)
		}
		relationsRows.Close()

		return report, nil
	})
}

// UpdateReport updates the top‐level fields of an existing report using optimistic locking,
// increments its version, writes a new snapshot, and returns the updated report.
func (repository *PostgresReportRepository) UpdateReport(
	context context.Context,
	transaction *sql.Tx,
	report *reports.Report,
) (*reports.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.UpdateReport")
	defer finishSpan()

	span.SetTag("report.id", report.Id)
	span.SetTag("report.status", report.Status.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.Report, error) {
		// 1. Load existing version & status
		const fetchSQL = `
            SELECT version, completed_at
            FROM reports
            WHERE id = $1
        `
		var oldVersion int32
		var oldCompletedAt sql.NullTime
		if err := sessionTx.QueryRowContext(spanContext, fetchSQL, report.Id).
			Scan(&oldVersion, &oldCompletedAt); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, ErrReportNotFound
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to fetch existing report")
			return nil, fmt.Errorf("UpdateReport: fetch existing: %w", err)
		}

		// 2. Prepare updated fields
		now := time.Now()
		nowStr := now.Format(time.RFC3339Nano)

		report.UpdatedAt = nowStr
		report.Version = oldVersion + 1

		// completed_at logic
		switch report.Status {
		case reports.ReportStatus_REPORT_STATUS_APPROVED,
			reports.ReportStatus_REPORT_STATUS_REJECTED,
			reports.ReportStatus_REPORT_STATUS_CANCELLED:
			if !oldCompletedAt.Valid {
				report.CompletedAt = nowStr
			}
		default:
			report.CompletedAt = ""
		}

		// marshal AdditionalInfoJson - defensive handling for protobuf Struct
		var infoJSON []byte
		if report.AdditionalInfoJson != nil {
			// Safely convert protobuf Struct to JSON with error handling
			additionalInfoMap := report.AdditionalInfoJson.AsMap()
			if additionalInfoMap == nil {
				// Handle case where Struct is not nil but AsMap() returns nil
				infoJSON = []byte("{}")
			} else {
				var marshalError error
				infoJSON, marshalError = json.Marshal(additionalInfoMap)
				if marshalError != nil {
					// Log error but don't fail the entire update - use empty JSON as fallback
					infoJSON = []byte("{}")
				}
			}
		} else {
			infoJSON = []byte(`{}`)
		}

		// 3. Update top-level row only
		const updateSQL = `
            UPDATE reports SET
              title                = $1,
              status               = $2,
              updated_at           = $3,
              completed_at         = $4,
              additional_info_json = $5,
              version              = $6,
              situation_id         = $7,
              case_id              = $8,
              watcher_asset_ids    = $9,
              author_asset_id      = $10,
              report_type          = $11
            WHERE id = $12 AND version = $13
        `

		var completedParam interface{}
		if report.CompletedAt != "" {
			completedParam = now
		}

		res, err := sessionTx.ExecContext(spanContext, updateSQL,
			report.Title,
			int32(report.Status),
			now,
			completedParam,
			infoJSON,
			report.Version,
			nullIfEmpty(report.SituationId),
			nullIfEmpty(report.CaseId),
			pq.Array(report.WatcherAssetIds),
			nullIfEmpty(report.AuthorAssetId),
			int32(report.ReportType), // Add report_type support
			report.Id,
			oldVersion,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("UpdateReport: exec top-level update: %w", err)
		}
		if n, _ := res.RowsAffected(); n == 0 {
			err := fmt.Errorf("UpdateReport: version conflict (expected %d)", oldVersion)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
			return nil, err
		}

		// 4. Insert new snapshot of the top-level state
		if err := repository.insertSnapshot(spanContext, sessionTx, report.Id); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("UpdateReport: %w", err)
		}
		updatedReport, err := repository.GetReport(spanContext, sessionTx, report.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("UpdateReport: failed to retrieve updated report: %w", err)
		}
		return updatedReport, nil
	})
}

// UpdateReportStatus updates only the status of a report, increments version, and creates a snapshot.
func (repository *PostgresReportRepository) UpdateReportStatus(
	context context.Context,
	transaction *sql.Tx,
	reportID string,
	newStatus reports.ReportStatus,
) (*reports.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.UpdateReportStatus")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	span.SetTag("report.new_status", newStatus.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.Report, error) {
		// 1. Get current report to check version and existing status
		currentReport, err := repository.GetReport(spanContext, sessionTx, reportID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get current report in UpdateReportStatus")
			return nil, fmt.Errorf("UpdateReportStatus: failed to get current report %s: %w", reportID, err)
		}

		// 2. Check if status is actually changing
		if currentReport.Status == newStatus {
			return currentReport, nil // No change needed
		}

		// 3. Prepare update fields
		now := time.Now()
		newVersion := currentReport.Version + 1
		var completedAtParam interface{}
		if newStatus == reports.ReportStatus_REPORT_STATUS_APPROVED ||
			newStatus == reports.ReportStatus_REPORT_STATUS_REJECTED ||
			newStatus == reports.ReportStatus_REPORT_STATUS_CANCELLED {
			completedAtParam = now
		} else if currentReport.CompletedAt != "" {
			completedAtParam = nil
		}

		// 4. Update the report status, updated_at, completed_at (if applicable), and version
		const updateStatusSQL = `
			UPDATE reports
			SET status = $1, updated_at = $2, completed_at = $3, version = $4
			WHERE id = $5 AND version = $6
		`
		result, err := sessionTx.ExecContext(spanContext, updateStatusSQL,
			int32(newStatus),
			now,
			completedAtParam,
			newVersion,
			reportID,
			currentReport.Version, // Optimistic locking
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute status update query")
			return nil, fmt.Errorf("UpdateReportStatus: failed to execute update for report %s: %w", reportID, err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected for status update")
			return nil, fmt.Errorf("UpdateReportStatus: failed to get rows affected for report %s: %w", reportID, err)
		}
		if rowsAffected == 0 {
			// Could be due to optimistic lock failure or report not found (already checked by GetReport)
			err := fmt.Errorf("UpdateReportStatus: optimistic lock failure or report %s not found (rows affected 0)", reportID)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
			return nil, err
		}

		// 5. Fetch the updated report data
		updatedReport, err := repository.GetReport(spanContext, sessionTx, reportID) // Re-fetch to get all fields correctly
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get updated report after status change")
			return nil, fmt.Errorf("UpdateReportStatus: failed to get updated report %s after status change: %w", reportID, err)
		}

		// 6. Create a new snapshot
		if err := repository.insertSnapshot(spanContext, sessionTx, reportID); err != nil { // Pass ID only
			// Fetch the version again just for the error message if snapshotting failed
			failedReport, getErr := repository.GetReport(spanContext, sessionTx, reportID)
			failedVersion := int32(0) // Default if fetch fails
			if getErr == nil {
				failedVersion = failedReport.Version
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to create snapshot after status update")
			return nil, fmt.Errorf("UpdateReportStatus: failed to create snapshot for report %s version %d: %w", reportID, failedVersion, err)
		}

		return updatedReport, nil
	})
}

// DeleteReport permanently deletes a report (and cascades to sections, comments, review rounds, snapshots).
func (repository *PostgresReportRepository) DeleteReport(
	context context.Context,
	transaction *sql.Tx,
	reportIdentifier string,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.DeleteReport")
	defer finishSpan()

	span.SetTag("report.id", reportIdentifier)

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		result, errorMessage := sessionTx.ExecContext(spanContext, `
            DELETE FROM reports
            WHERE id = $1
        `, reportIdentifier)
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to execute delete report query")
			return fmt.Errorf("DeleteReport: exec delete: %w", errorMessage)
		}
		rowsAffected, errorMessage := result.RowsAffected()
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to get rows affected for delete report")
			return fmt.Errorf("DeleteReport: rows affected: %w", errorMessage)
		}
		if rowsAffected == 0 {
			return ErrReportNotFound
		}
		return nil
	})
}

// ListReports returns a paginated list of fully populated reports, optionally filtered by status and org ID.
func (repository *PostgresReportRepository) ListReports(
	context context.Context,
	transaction *sql.Tx,
	limit int,
	offsetToken string,
	filterStatus reports.ReportStatus,
	filterOrgID int32,
) (*reports.ListReportsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.ListReports")
	defer finishSpan()

	span.SetTag("report.limit", fmt.Sprintf("%d", limit))
	span.SetTag("report.offset_token", offsetToken)
	span.SetTag("report.filter_status", filterStatus.String())
	span.SetTag("report.filter_org_id", fmt.Sprintf("%d", filterOrgID))

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ListReportsResponse, error) {
		// 1. Parse pageToken into offset
		queryOffset := 0
		if offsetToken != "" {
			if parsedOffset, errorMessage := strconv.Atoi(offsetToken); errorMessage == nil && parsedOffset >= 0 {
				queryOffset = parsedOffset
			}
		}

		// 2. Build WHERE clauses
		var (
			whereClauses   []string
			queryArguments []interface{}
			argIdx         = 1
		)
		if filterStatus != reports.ReportStatus_REPORT_STATUS_UNSPECIFIED {
			whereClauses = append(whereClauses, fmt.Sprintf("status = $%d", argIdx))
			queryArguments = append(queryArguments, int32(filterStatus))
			argIdx++
		}
		if filterOrgID != 0 {
			whereClauses = append(whereClauses, fmt.Sprintf("org_id = $%d", argIdx))
			queryArguments = append(queryArguments, filterOrgID)
			argIdx++
		}

		// 3. Select only IDs for pagination
		selectQuery := `
            SELECT id
            FROM reports
        `
		if len(whereClauses) > 0 {
			selectQuery += " WHERE " + strings.Join(whereClauses, " AND ")
		}
		selectQuery += fmt.Sprintf(" ORDER BY assigned_at DESC, id ASC LIMIT $%d OFFSET $%d",
			argIdx, argIdx+1)
		queryArguments = append(queryArguments, limit, queryOffset)

		rows, errorMessage := sessionTx.QueryContext(spanContext, selectQuery, queryArguments...)
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("ListReports: query failed: %w", errorMessage)
		}
		defer rows.Close()

		// 4. Collect IDs
		var ids []string
		for rows.Next() {
			var id string
			if errorMessage := rows.Scan(&id); errorMessage != nil {
				herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("ListReports: scan id: %w", errorMessage)
			}
			ids = append(ids, id)
		}
		if errorMessage := rows.Err(); errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("ListReports: iteration errorMessage: %w", errorMessage)
		}

		// 5. Batch load main report data and relationships
		var reportList []*reports.Report
		if len(ids) > 0 {
			// First load main report records
			mainQuery := `
				SELECT
				  id, org_id, author_asset_id, title, status,
				  assigned_at, updated_at, completed_at,
				  additional_info_json, version,
				  situation_id, case_id,
				  watcher_asset_ids, resource_type, created_at, created_by_asset_id, report_type
				FROM reports WHERE id = ANY($1)
				ORDER BY assigned_at DESC, id ASC
			`
			mainRows, err := sessionTx.QueryContext(spanContext, mainQuery, pq.Array(ids))
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query main reports in ListReports")
				return nil, fmt.Errorf("ListReports: query main reports: %w", err)
			}
			defer mainRows.Close()

			reportsMap := make(map[string]*reports.Report)
			for mainRows.Next() {
				report := &reports.Report{}
				var (
					authorAssetID                 sql.NullString
					completedAtTime               sql.NullTime
					situationIdentifier           sql.NullString
					caseIdentifier                sql.NullString
					additionalInfoJSON            []byte
					watcherAssetIDSlice           []string
					assignedAtTime, updatedAtTime time.Time
					createdAtTime                 time.Time
					createdByAssetID              sql.NullString
					reportTypeInt32               int32
				)

				if err := mainRows.Scan(
					&report.Id, &report.OrgId, &authorAssetID, &report.Title,
					(*int32)(&report.Status), &assignedAtTime, &updatedAtTime, &completedAtTime,
					&additionalInfoJSON, &report.Version,
					&situationIdentifier, &caseIdentifier,
					pq.Array(&watcherAssetIDSlice), &report.ResourceType, &createdAtTime,
					&createdByAssetID, &reportTypeInt32,
				); err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
					return nil, fmt.Errorf("ListReports: scan report: %w", err)
				}

				report.AuthorAssetId = authorAssetID.String
				report.AssignedAt = assignedAtTime.Format(time.RFC3339Nano)
				report.UpdatedAt = updatedAtTime.Format(time.RFC3339Nano)
				report.CreatedAt = createdAtTime.Format(time.RFC3339Nano)
				report.ReportType = reports.ReportType(reportTypeInt32)
				if completedAtTime.Valid {
					report.CompletedAt = completedAtTime.Time.Format(time.RFC3339Nano)
				}
				if situationIdentifier.Valid {
					report.SituationId = situationIdentifier.String
				}
				if caseIdentifier.Valid {
					report.CaseId = caseIdentifier.String
				}
				report.WatcherAssetIds = watcherAssetIDSlice
				if createdByAssetID.Valid {
					report.CreatedByAssetId = createdByAssetID.String
				}

				var additionalInfo map[string]interface{}
				if err := json.Unmarshal(additionalInfoJSON, &additionalInfo); err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
					return nil, fmt.Errorf("ListReports: invalid additional_info_json: %w", err)
				}
				if structData, err := structpb.NewStruct(additionalInfo); err == nil {
					report.AdditionalInfoJson = structData
				} else {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
					return nil, fmt.Errorf("ListReports: struct conversion: %w", err)
				}

				reportsMap[report.Id] = report
			}

			// Batch load all relationships
			sectionsMap, commentsMap, reviewRoundsMap, relationsMap, err := repository.batchLoadReportRelationships(spanContext, sessionTx, ids)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to load relationships in ListReports")
				return nil, fmt.Errorf("ListReports: loading relationships: %w", err)
			}

			// Assemble reports with their relationships in original order
			for _, id := range ids {
				if report, exists := reportsMap[id]; exists {
					report.Sections = sectionsMap[id]
					report.Comments = commentsMap[id]
					report.ReviewRounds = reviewRoundsMap[id]
					report.Relations = relationsMap[id]
					reportList = append(reportList, report)
				}
			}
		}

		// 6. Next page token
		nextPageToken := ""
		if len(ids) == limit {
			nextPageToken = strconv.Itoa(queryOffset + limit)
		}

		// 7. Return response
		return &reports.ListReportsResponse{
			Reports:       reportList,
			NextPageToken: nextPageToken,
		}, nil
	})
}

// BatchGetReports fetches multiple reports by ID (in request order), skipping any not found.
func (repository *PostgresReportRepository) BatchGetReports(
	context context.Context,
	transaction *sql.Tx,
	reportIdentifiers []string,
) ([]*reports.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.BatchGetReports")
	defer finishSpan()

	span.SetTag("report.batch_size", fmt.Sprintf("%d", len(reportIdentifiers)))

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) ([]*reports.Report, error) {
		if len(reportIdentifiers) == 0 {
			return []*reports.Report{}, nil
		}

		// First load main report records
		mainQuery := `
			SELECT
			  id, org_id, author_asset_id, title, status,
			  assigned_at, updated_at, completed_at,
			  additional_info_json, version,
			  situation_id, case_id,
			  watcher_asset_ids, resource_type, created_at, created_by_asset_id, report_type
			FROM reports WHERE id = ANY($1)
		`
		mainRows, err := sessionTx.QueryContext(spanContext, mainQuery, pq.Array(reportIdentifiers))
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query main reports in BatchGetReports")
			return nil, fmt.Errorf("BatchGetReports: query main reports: %w", err)
		}
		defer mainRows.Close()

		reportsMap := make(map[string]*reports.Report)
		for mainRows.Next() {
			report := &reports.Report{}
			var (
				authorAssetID                 sql.NullString
				completedAtTime               sql.NullTime
				situationIdentifier           sql.NullString
				caseIdentifier                sql.NullString
				additionalInfoJSON            []byte
				watcherAssetIDSlice           []string
				assignedAtTime, updatedAtTime time.Time
				createdAtTime                 time.Time
				createdByAssetID              sql.NullString
				reportTypeInt32               int32
			)

			if err := mainRows.Scan(
				&report.Id, &report.OrgId, &authorAssetID, &report.Title,
				(*int32)(&report.Status), &assignedAtTime, &updatedAtTime, &completedAtTime,
				&additionalInfoJSON, &report.Version,
				&situationIdentifier, &caseIdentifier,
				pq.Array(&watcherAssetIDSlice), &report.ResourceType, &createdAtTime,
				&createdByAssetID, &reportTypeInt32,
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("BatchGetReports: scan report: %w", err)
			}

			report.AuthorAssetId = authorAssetID.String
			report.AssignedAt = assignedAtTime.Format(time.RFC3339Nano)
			report.UpdatedAt = updatedAtTime.Format(time.RFC3339Nano)
			report.CreatedAt = createdAtTime.Format(time.RFC3339Nano)
			report.ReportType = reports.ReportType(reportTypeInt32)
			if completedAtTime.Valid {
				report.CompletedAt = completedAtTime.Time.Format(time.RFC3339Nano)
			}
			if situationIdentifier.Valid {
				report.SituationId = situationIdentifier.String
			}
			if caseIdentifier.Valid {
				report.CaseId = caseIdentifier.String
			}
			report.WatcherAssetIds = watcherAssetIDSlice
			if createdByAssetID.Valid {
				report.CreatedByAssetId = createdByAssetID.String
			}

			var additionalInfo map[string]interface{}
			if err := json.Unmarshal(additionalInfoJSON, &additionalInfo); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, fmt.Errorf("BatchGetReports: invalid additional_info_json: %w", err)
			}
			if structData, err := structpb.NewStruct(additionalInfo); err == nil {
				report.AdditionalInfoJson = structData
			} else {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, fmt.Errorf("BatchGetReports: struct conversion: %w", err)
			}

			reportsMap[report.Id] = report
		}

		// Get list of found report IDs for batch loading relationships
		var foundIds []string
		for _, id := range reportIdentifiers {
			if _, exists := reportsMap[id]; exists {
				foundIds = append(foundIds, id)
			}
		}

		// Batch load all relationships
		sectionsMap, commentsMap, reviewRoundsMap, relationsMap, err := repository.batchLoadReportRelationships(spanContext, sessionTx, foundIds)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to load relationships in BatchGetReports")
			return nil, fmt.Errorf("BatchGetReports: loading relationships: %w", err)
		}

		// Assemble reports with their relationships in original request order, skipping missing ones
		output := make([]*reports.Report, 0, len(reportIdentifiers))
		for _, id := range reportIdentifiers {
			if report, exists := reportsMap[id]; exists {
				report.Sections = sectionsMap[id]
				report.Comments = commentsMap[id]
				report.ReviewRounds = reviewRoundsMap[id]
				report.Relations = relationsMap[id]
				output = append(output, report)
			}
			// Skip missing reports (matches original behavior)
		}
		return output, nil
	})
}

// AddComment inserts a new comment on a report or section, returning the created Comment.
func (repository *PostgresReportRepository) AddComment(
	context context.Context,
	transaction *sql.Tx,
	request *reports.AddCommentRequest,
) (*reports.Comment, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.AddComment")
	defer finishSpan()

	span.SetTag("report.id", request.Comment.ReportId)
	span.SetTag("report.section_id", request.Comment.SectionId)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.Comment, error) {
		// Ensure the parent report exists
		var exists bool
		if errorMessage := sessionTx.QueryRowContext(spanContext, `
            SELECT true FROM reports WHERE id = $1
        `, request.Comment.ReportId).Scan(&exists); errorMessage != nil {
			if errors.Is(errorMessage, sql.ErrNoRows) {
				return nil, ErrReportNotFound
			}
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to verify report exists in AddComment")
			return nil, fmt.Errorf("AddComment: verifying report exists: %w", errorMessage)
		}

		// Prepare timestamps
		now := time.Now()
		timestamp := now.Format(time.RFC3339Nano)

		// Generate ID if not provided
		commentID := request.Comment.Id
		if commentID == "" {
			commentID = uuid.New().String()
		}

		// Determine author
		authorAssetID, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
		if err != nil {
			log.Printf("error: failed to get authorAssetID from context: %v", err)
			authorAssetID = SystemAssetID
		}

		if request.Comment.AuthorAssetId != "" {
			if authorAssetID != request.Comment.AuthorAssetId {
				log.Printf("warning: AddComment: author mismatch: %q != %q", authorAssetID, request.Comment.AuthorAssetId)
			}
		}

		newComment := &reports.Comment{
			Id:               commentID,
			ReportId:         request.Comment.ReportId,
			SectionId:        request.Comment.SectionId,
			ReplyToCommentId: request.Comment.ReplyToCommentId,
			AuthorAssetId:    authorAssetID,
			Text:             request.Comment.Text,
			CreatedAt:        timestamp,
			UpdatedAt:        timestamp,
			Resolved:         request.Comment.Resolved,
			ResourceType:     FixedResourceTypeComment,
			DisplayName:      request.Comment.DisplayName,
		}

		// Insert into database
		const insertQuery = `
            INSERT INTO report_comments (
              id, report_id, section_id, reply_to_comment_id,
              author_asset_id, text,
              created_at, updated_at,
              resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
            ) VALUES (
              $1,$2,$3,$4,
              $5,$6,
              $7,$8,
              $9,$10,$11,$12,$13
            )
        `
		if _, errorMessage := sessionTx.ExecContext(spanContext, insertQuery,
			newComment.Id,
			newComment.ReportId,
			nullIfEmpty(newComment.SectionId),
			nullIfEmpty(newComment.ReplyToCommentId),
			newComment.AuthorAssetId,
			newComment.Text,
			now,
			now,
			newComment.Resolved,
			nil, // resolved_at
			nil, // resolved_by_asset_id
			newComment.ResourceType,
			nullIfEmpty(newComment.DisplayName),
		); errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to insert comment in AddComment")
			return nil, fmt.Errorf("AddComment: exec insert: %w", errorMessage)
		}

		return newComment, nil
	})
}

// UpdateComment updates an existing comment. If inputComment.UpdatedAt is
// non-empty we do an optimistic-locking check; otherwise we update
// unconditionally.
func (repository *PostgresReportRepository) UpdateComment(
	context context.Context,
	transaction *sql.Tx,
	inputComment *reports.Comment,
) (*reports.Comment, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.UpdateComment")
	defer finishSpan()

	span.SetTag("comment.id", inputComment.Id)
	span.SetTag("report.id", inputComment.ReportId)
	span.SetTag("report.section_id", inputComment.SectionId)

	return database.WithSession(repository.database, spanContext, transaction,
		func(sessionTx *sql.Tx) (*reports.Comment, error) {

			//------------------------------------------------------------------
			// 1. Decide whether we're locking on updated_at
			//------------------------------------------------------------------
			var (
				useLock  bool
				lastSeen time.Time
			)
			if strings.TrimSpace(inputComment.UpdatedAt) != "" {
				var err error
				lastSeen, err = time.Parse(time.RFC3339Nano, inputComment.UpdatedAt)
				if err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
					return nil, fmt.Errorf(
						"UpdateComment: invalid updatedAt %q: %w", inputComment.UpdatedAt, err)
				}
				useLock = true
			}

			//------------------------------------------------------------------
			// 2. Prepare new-values
			//------------------------------------------------------------------
			now := time.Now()

			var resolvedAtParam, resolvedByParam interface{}
			if inputComment.Resolved {
				if ts, err := time.Parse(time.RFC3339Nano, inputComment.ResolvedAt); err == nil {
					resolvedAtParam = ts
				} else {
					resolvedAtParam = now
				}
				resolvedByParam = nullIfEmpty(inputComment.ResolvedByAssetId)
			}

			//------------------------------------------------------------------
			// 3. Build SQL + args (differs if we lock)
			//------------------------------------------------------------------
			var (
				sqlStmt string
				args    []interface{}
			)
			if useLock {
				sqlStmt = `
					UPDATE report_comments SET
					  text                 = $1,
					  updated_at           = $2,
					  resolved             = $3,
					  resolved_at          = $4,
					  resolved_by_asset_id = $5,
					  display_name         = $6
					WHERE id = $7 AND updated_at = $8`
				args = []interface{}{
					inputComment.Text,
					now,
					inputComment.Resolved,
					resolvedAtParam,
					resolvedByParam,
					nullIfEmpty(inputComment.DisplayName),
					inputComment.Id,
					lastSeen,
				}
			} else {
				sqlStmt = `
					UPDATE report_comments SET
					  text                 = $1,
					  updated_at           = $2,
					  resolved             = $3,
					  resolved_at          = $4,
					  resolved_by_asset_id = $5,
					  display_name         = $6
					WHERE id = $7`
				args = []interface{}{
					inputComment.Text,
					now,
					inputComment.Resolved,
					resolvedAtParam,
					resolvedByParam,
					nullIfEmpty(inputComment.DisplayName),
					inputComment.Id,
				}
			}

			//------------------------------------------------------------------
			// 4. Execute
			//------------------------------------------------------------------
			res, err := sessionTx.ExecContext(spanContext, sqlStmt, args...)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("UpdateComment: exec update: %w", err)
			}
			rows, _ := res.RowsAffected()
			if rows == 0 {
				return nil, ErrCommentNotFound // id not found OR optimistic-lock failed
			}

			//------------------------------------------------------------------
			// 5. Return the fresh row
			//------------------------------------------------------------------
			const fetchSQL = `
				SELECT
				  id, report_id, section_id, reply_to_comment_id,
				  author_asset_id, text,
				  created_at, updated_at,
				  resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
				FROM report_comments
				WHERE id = $1`
			updated := &reports.Comment{}
			var (
				sectionNull, replyNull, resolvedByNull, displayNameNull sql.NullString
				resAtNull                                               sql.NullTime
				upAtTime                                                time.Time
			)
			if err := sessionTx.QueryRowContext(spanContext, fetchSQL, inputComment.Id).Scan(
				&updated.Id,
				&updated.ReportId,
				&sectionNull,
				&replyNull,
				&updated.AuthorAssetId,
				&updated.Text,
				&updated.CreatedAt,
				&upAtTime,
				&updated.Resolved,
				&resAtNull,
				&resolvedByNull,
				&updated.ResourceType,
				&displayNameNull,
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("UpdateComment: fetch updated comment: %w", err)
			}
			if sectionNull.Valid {
				updated.SectionId = sectionNull.String
			}
			if replyNull.Valid {
				updated.ReplyToCommentId = replyNull.String
			}
			updated.UpdatedAt = upAtTime.Format(time.RFC3339Nano)
			if resAtNull.Valid {
				updated.ResolvedAt = resAtNull.Time.Format(time.RFC3339Nano)
			}
			if displayNameNull.Valid {
				updated.DisplayName = displayNameNull.String
			}

			return updated, nil
		})
}

// DeleteComment permanently deletes a comment, returning ErrCommentNotFound if missing.
func (repository *PostgresReportRepository) DeleteComment(
	context context.Context,
	transaction *sql.Tx,
	commentIdentifier string,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.DeleteComment")
	defer finishSpan()

	span.SetTag("comment.id", commentIdentifier)

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		result, errorMessage := sessionTx.ExecContext(spanContext, `
            DELETE FROM report_comments
            WHERE id = $1
        `, commentIdentifier)
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("DeleteComment: exec delete: %w", errorMessage)
		}
		rowsAffected, errorMessage := result.RowsAffected()
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("DeleteComment: rows affected: %w", errorMessage)
		}
		if rowsAffected == 0 {
			return ErrCommentNotFound
		}
		return nil
	})
}

// GetComments retrieves comments for a report (and optional section) with pagination.
func (repository *PostgresReportRepository) GetComments(
	context context.Context,
	transaction *sql.Tx,
	reportIdentifier, sectionIdentifier string,
	limit int,
	offsetToken string,
) (*reports.GetCommentsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.GetComments")
	defer finishSpan()

	span.SetTag("report.id", reportIdentifier)
	span.SetTag("report.section_id", sectionIdentifier)
	span.SetTag("pagination.limit", fmt.Sprintf("%d", limit))
	span.SetTag("pagination.offset_token", offsetToken)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.GetCommentsResponse, error) {
		fmt.Printf("reportIdentifier: %s\nsectionIdentifier: %s\nlimit: %d\noffsetToken: %s\n", reportIdentifier, sectionIdentifier, limit, offsetToken)

		// 1. Parse pageToken into offset
		if limit <= 0 {
			limit = 50
		}
		queryOffset := 0
		if offsetToken != "" {
			if parsedOffset, err := strconv.Atoi(offsetToken); err == nil && parsedOffset >= 0 {
				queryOffset = parsedOffset
			}
		}

		// 2. Prepare query and args
		var (
			rows     *sql.Rows
			queryErr error
		)
		if sectionIdentifier == "" {
			const q = `
                SELECT
                  id, report_id, section_id, reply_to_comment_id,
                  author_asset_id, text,
                  created_at, updated_at,
                  resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
                FROM report_comments
                WHERE report_id = $1
                ORDER BY created_at DESC, id ASC
                LIMIT $2 OFFSET $3
            `
			rows, queryErr = sessionTx.QueryContext(spanContext, q, reportIdentifier, limit, queryOffset)
		} else {
			const q = `
                SELECT
                  id, report_id, section_id, reply_to_comment_id,
                  author_asset_id, text,
                  created_at, updated_at,
                  resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
                FROM report_comments
                WHERE report_id = $1 AND section_id = $2
                ORDER BY created_at DESC, id ASC
                LIMIT $3 OFFSET $4
            `
			rows, queryErr = sessionTx.QueryContext(spanContext, q, reportIdentifier, sectionIdentifier, limit, queryOffset)
		}
		if queryErr != nil {
			herosentry.CaptureException(spanContext, queryErr, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("GetComments: query failed: %w", queryErr)
		}
		defer rows.Close()

		// 3. Scan rows
		var commentList []*reports.Comment
		for rows.Next() {
			comment := &reports.Comment{}
			var (
				sectionNull       sql.NullString
				replyToCommentID  sql.NullString
				createdAtTime     time.Time
				updatedAtTime     time.Time
				resolvedAtTime    sql.NullTime
				resolvedByAssetID sql.NullString
				displayName       sql.NullString
			)

			if err := rows.Scan(
				&comment.Id,
				&comment.ReportId,
				&sectionNull,
				&replyToCommentID,
				&comment.AuthorAssetId,
				&comment.Text,
				&createdAtTime,
				&updatedAtTime,
				&comment.Resolved,
				&resolvedAtTime,
				&resolvedByAssetID,
				&comment.ResourceType,
				&displayName,
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("GetComments: scan failed: %w", err)
			}

			if sectionNull.Valid {
				comment.SectionId = sectionNull.String
			}
			if replyToCommentID.Valid {
				comment.ReplyToCommentId = replyToCommentID.String
			}
			if resolvedByAssetID.Valid {
				comment.ResolvedByAssetId = resolvedByAssetID.String
			}
			if displayName.Valid {
				comment.DisplayName = displayName.String
			}

			comment.CreatedAt = createdAtTime.Format(time.RFC3339Nano)
			comment.UpdatedAt = updatedAtTime.Format(time.RFC3339Nano)
			if resolvedAtTime.Valid {
				comment.ResolvedAt = resolvedAtTime.Time.Format(time.RFC3339Nano)
			}

			commentList = append(commentList, comment)
		}
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("GetComments: rows iteration error: %w", err)
		}

		// 4. Compute next page token
		nextPageToken := ""
		if len(commentList) == limit {
			nextPageToken = strconv.Itoa(queryOffset + limit)
		}

		// 5. Return response
		return &reports.GetCommentsResponse{
			Comments:      commentList,
			NextPageToken: nextPageToken,
		}, nil
	})
}

// SubmitForReview marks a report as SUBMITTED_FOR_REVIEW and persists the change.
func (repository *PostgresReportRepository) SubmitForReview(
	context context.Context,
	transaction *sql.Tx,
	reportIdentifier, submissionNote string,
) (*reports.Report, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.SubmitForReview")
	defer finishSpan()

	span.SetTag("report.id", reportIdentifier)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.Report, error) {
		// 1. Load existing report
		report, errorMessage := repository.GetReport(spanContext, sessionTx, reportIdentifier)
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to get report in SubmitForReview")
			return nil, fmt.Errorf("SubmitForReview: load report: %w", errorMessage)
		}

		// 2. Only allow if not already in review or terminal
		switch report.Status {
		case reports.ReportStatus_REPORT_STATUS_SUBMITTED_FOR_REVIEW,
			reports.ReportStatus_REPORT_STATUS_UNDER_REVIEW,
			reports.ReportStatus_REPORT_STATUS_APPROVED,
			reports.ReportStatus_REPORT_STATUS_REJECTED,
			reports.ReportStatus_REPORT_STATUS_CANCELLED:
			err := fmt.Errorf("SubmitForReview: invalid status transition from %s", report.Status)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation, "Invalid status transition in SubmitForReview")
			return nil, err
		}
		// Use UpdateReportStatus to update the report's status to SUBMITTED_FOR_REVIEW.
		updatedReport, err := repository.UpdateReportStatus(
			spanContext,
			sessionTx,
			reportIdentifier,
			reports.ReportStatus_REPORT_STATUS_SUBMITTED_FOR_REVIEW,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update report status in SubmitForReview")
			return nil, fmt.Errorf("SubmitForReview: update report status: %w", err)
		}
		return updatedReport, nil
	})
}

// AddReviewRound assigns a reviewer to a report at a given level, creating a new ReviewRound.
func (repository *PostgresReportRepository) AddReviewRound(
	context context.Context,
	transaction *sql.Tx,
	request *reports.AddReviewRoundRequest,
) (*reports.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.AddReviewRound")
	defer finishSpan()

	span.SetTag("report.id", request.ReviewRound.ReportId)
	span.SetTag("review_round.level", fmt.Sprintf("%d", request.ReviewRound.Level))

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ReviewRound, error) {
		// 1. Load and verify the report exists (to get its current version)
		report, err := repository.GetReport(spanContext, sessionTx, request.ReviewRound.ReportId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get report in AddReviewRound")
			return nil, fmt.Errorf("AddReviewRound: load report: %w", err)
		}

		actorAssetID, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get asset ID in AddReviewRound")
			return nil, fmt.Errorf("AddReviewRound: failed to get asset ID: %w", err)
		}

		// 3. Build the ReviewRound proto
		now := time.Now()
		timestamp := now.Format(time.RFC3339Nano)
		reviewRound := &reports.ReviewRound{
			Id:              uuid.New().String(),
			ReportId:        request.ReviewRound.ReportId,
			ReviewerAssetId: request.ReviewRound.ReviewerAssetId,
			Level:           request.ReviewRound.Level,
			Status:          reports.ReviewStatus_REVIEW_STATUS_AWAITING_ACTION,
			SentToLevel:     request.ReviewRound.SentToLevel,
			SentToAssetId:   request.ReviewRound.SentToAssetId,
			RequestedAt:     timestamp,
			DueAt:           request.ReviewRound.DueAt,
			SnapshotVersion: func() int32 {
				if request.ReviewRound.SnapshotVersion != 0 {
					return request.ReviewRound.SnapshotVersion
				}
				return report.Version
			}(),
			CreateByAssetId: actorAssetID,
			NoteForReviewer: request.ReviewRound.NoteForReviewer,
			RoundNote:       request.ReviewRound.RoundNote,
		}

		// 4. Insert into report_review_rounds (org_id column dropped)
		const insertSQL = `
			INSERT INTO report_review_rounds (
			  id, report_id,
			  reviewer_asset_id, level, status,
			  sent_to_level, sent_to_asset_id,
			  requested_at, resolved_at,
			  round_note, snapshot_version,
			  due_at, create_by_asset_id, note_for_reviewer
			) VALUES (
			  $1,$2,
			  $3,$4,$5,
			  $6,$7,
			  $8,$9,
			  $10,$11,
			  $12,$13,$14
			)
		`
		if _, err := sessionTx.ExecContext(spanContext, insertSQL,
			reviewRound.Id,
			reviewRound.ReportId,
			nullIfEmpty(reviewRound.ReviewerAssetId),
			reviewRound.Level,
			int32(reviewRound.Status),
			reviewRound.SentToLevel,
			nullIfEmpty(reviewRound.SentToAssetId),
			now,
			nil, // resolved_at remains NULL at creation
			nullIfEmpty(reviewRound.RoundNote),
			reviewRound.SnapshotVersion,
			nullIfEmpty(reviewRound.DueAt), // if empty string, returns nil
			nullIfEmpty(reviewRound.CreateByAssetId),
			nullIfEmpty(reviewRound.NoteForReviewer),
		); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert review round in AddReviewRound")
			return nil, fmt.Errorf("AddReviewRound: insert review round: %w", err)
		}

		return reviewRound, nil
	})
}

// ApproveReviewRound marks an existing review round as approved.
func (repository *PostgresReportRepository) ApproveReviewRound(
	context context.Context,
	transaction *sql.Tx,
	reviewRoundIdentifier, approvalNote string,
) (*reports.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.ApproveReviewRound")
	defer finishSpan()

	span.SetTag("review_round.id", reviewRoundIdentifier)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ReviewRound, error) {
		// 1. Load existing review round (including prior note_for_reviewer)
		const fetchSQL = `
            SELECT
              report_id,
              reviewer_asset_id,
              level,
              status,
              sent_to_level,
              sent_to_asset_id,
              requested_at,
              round_note,
              snapshot_version,
              due_at,
              create_by_asset_id,
              note_for_reviewer
            FROM report_review_rounds
            WHERE id = $1
        `
		reviewRound := &reports.ReviewRound{Id: reviewRoundIdentifier}
		var (
			origReviewerAssetID sql.NullString
			sentToAssetID       sql.NullString
			requestedAtTime     time.Time
			roundNoteNull       sql.NullString
			noteForReviewerNull sql.NullString
			dueAtNull           sql.NullTime
		)
		var (
			levelInt  int32
			statusInt int32
			sentLevel int32
			snapshotV int32
		)
		if errorMessage := sessionTx.QueryRowContext(spanContext, fetchSQL, reviewRoundIdentifier).Scan(
			&reviewRound.ReportId,
			&origReviewerAssetID,
			&levelInt,
			&statusInt,
			&sentLevel,
			&sentToAssetID,
			&requestedAtTime,
			&roundNoteNull,
			&snapshotV,
			&dueAtNull,
			&reviewRound.CreateByAssetId,
			&noteForReviewerNull,
		); errorMessage != nil {
			if errors.Is(errorMessage, sql.ErrNoRows) {
				err := fmt.Errorf("ApproveReviewRound: review round %q not found", reviewRoundIdentifier)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Review round not found in ApproveReviewRound")
				return nil, err
			}
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to fetch review round in ApproveReviewRound")
			return nil, fmt.Errorf("ApproveReviewRound: fetch: %w", errorMessage)
		}

		// Populate existing fields
		reviewRound.ReviewerAssetId = origReviewerAssetID.String
		reviewRound.Level = levelInt
		reviewRound.Status = reports.ReviewStatus(statusInt)
		reviewRound.SentToLevel = sentLevel
		reviewRound.SentToAssetId = sentToAssetID.String
		reviewRound.RequestedAt = requestedAtTime.Format(time.RFC3339Nano)
		if roundNoteNull.Valid {
			reviewRound.RoundNote = roundNoteNull.String
		}
		reviewRound.SnapshotVersion = snapshotV
		if dueAtNull.Valid {
			reviewRound.DueAt = dueAtNull.Time.Format(time.RFC3339Nano)
		}
		if noteForReviewerNull.Valid {
			reviewRound.NoteForReviewer = noteForReviewerNull.String
		}

		// 2. Update to APPROVED
		now := time.Now()
		nowStr := now.Format(time.RFC3339Nano)
		const updateSQL = `
            UPDATE report_review_rounds
            SET
              status            = $1,
              round_note        = $2,
              resolved_at       = $3
            WHERE id = $4
        `
		response, errorMessage := sessionTx.ExecContext(spanContext, updateSQL,
			int32(reports.ReviewStatus_REVIEW_STATUS_APPROVED),
			approvalNote,
			now,
			reviewRoundIdentifier,
		)
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to update review round in ApproveReviewRound")
			return nil, fmt.Errorf("ApproveReviewRound: update: %w", errorMessage)
		}
		if rowsAffected, _ := response.RowsAffected(); rowsAffected == 0 {
			err := fmt.Errorf("ApproveReviewRound: no rows updated for id %q", reviewRoundIdentifier)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "No rows updated in ApproveReviewRound")
			return nil, err
		}

		// 3. Populate updated fields and return
		reviewRound.Status = reports.ReviewStatus_REVIEW_STATUS_APPROVED
		reviewRound.RoundNote = approvalNote
		reviewRound.ResolvedAt = nowStr

		return reviewRound, nil
	})
}

// RequestChanges updates a review round to CHANGES_REQUESTED, routes it, and returns the updated round.
func (repository *PostgresReportRepository) RequestChanges(
	context context.Context,
	transaction *sql.Tx,
	request *reports.RequestChangesRequest,
) (*reports.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.RequestChanges")
	defer finishSpan()

	span.SetTag("review_round.id", request.ReviewRoundId)
	span.SetTag("sent_to_level", fmt.Sprintf("%d", request.SendToLevel))
	span.SetTag("sent_to_asset_id", request.SendToAssetId)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ReviewRound, error) {
		// 1. Fetch existing round for immutable fields
		const fetchSQL = `
            SELECT
              report_id,
              level,
              requested_at,
              snapshot_version,
              due_at,
              create_by_asset_id
            FROM report_review_rounds
            WHERE id = $1
        `
		reviewRound := &reports.ReviewRound{Id: request.ReviewRoundId}
		var (
			lvl       int32
			reqAt     time.Time
			snapVer   int32
			dueAtNull sql.NullTime
		)
		if errorMessage := sessionTx.QueryRowContext(spanContext, fetchSQL, request.ReviewRoundId).Scan(
			&reviewRound.ReportId,
			&lvl,
			&reqAt,
			&snapVer,
			&dueAtNull,
			&reviewRound.CreateByAssetId,
		); errorMessage != nil {
			if errors.Is(errorMessage, sql.ErrNoRows) {
				err := fmt.Errorf("RequestChanges: review round %q not found", request.ReviewRoundId)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Review round not found in RequestChanges")
				return nil, err
			}
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to fetch review round in RequestChanges")
			return nil, fmt.Errorf("RequestChanges: fetch: %w", errorMessage)
		}
		reviewRound.Level = lvl
		reviewRound.RequestedAt = reqAt.Format(time.RFC3339Nano)
		reviewRound.SnapshotVersion = snapVer
		if dueAtNull.Valid {
			reviewRound.DueAt = dueAtNull.Time.Format(time.RFC3339Nano)
		}

		// 2. Perform update to CHANGES_REQUESTED
		now := time.Now()
		nowStr := now.Format(time.RFC3339Nano)
		const updateSQL = `
            UPDATE report_review_rounds SET
              status            = $1,
              sent_to_level     = $2,
              sent_to_asset_id  = $3,
              round_note        = $4,
              resolved_at       = $5
            WHERE id = $6
        `
		response, errorMessage := sessionTx.ExecContext(spanContext, updateSQL,
			int32(reports.ReviewStatus_REVIEW_STATUS_CHANGES_REQUESTED),
			request.SendToLevel,
			nullIfEmpty(request.SendToAssetId),
			request.Note,
			now,
			request.ReviewRoundId,
		)
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to update review round in RequestChanges")
			return nil, fmt.Errorf("RequestChanges: exec update: %w", errorMessage)
		}
		if rowsAffected, _ := response.RowsAffected(); rowsAffected == 0 {
			err := fmt.Errorf("RequestChanges: no review round updated (id=%s)", request.ReviewRoundId)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "No rows updated in RequestChanges")
			return nil, err
		}

		// 3. Populate updated fields
		reviewRound.Status = reports.ReviewStatus_REVIEW_STATUS_CHANGES_REQUESTED
		reviewRound.SentToLevel = request.SendToLevel
		reviewRound.SentToAssetId = request.SendToAssetId
		reviewRound.RoundNote = request.Note
		reviewRound.ResolvedAt = nowStr

		return reviewRound, nil
	})
}

// UpdateAdditionalInfoJSON replaces the report's additional_info_json with the given blob.
func (repository *PostgresReportRepository) UpdateAdditionalInfoJSON(
	context context.Context,
	transaction *sql.Tx,
	request *reports.UpdateAdditionalInfoJsonRequest,
) (*reports.UpdateAdditionalInfoJsonResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.UpdateAdditionalInfoJSON")
	defer finishSpan()

	span.SetTag("report.id", request.ReportId)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.UpdateAdditionalInfoJsonResponse, error) {
		// 1. Validate incoming JSON
		var tmp interface{}
		if errorMessage := json.Unmarshal([]byte(request.AdditionalInfoJson), &tmp); errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeValidation, "Invalid JSON in UpdateAdditionalInfoJSON")
			return nil, fmt.Errorf("UpdateAdditionalInfoJSON: invalid JSON: %w", errorMessage)
		}

		// 2. Persist to reports table
		now := time.Now()
		const updateSQL = `
            UPDATE reports
            SET additional_info_json = $1,
                updated_at = $2,
                version = version + 1
            WHERE id = $3
        `
		response, errorMessage := sessionTx.ExecContext(spanContext, updateSQL,
			[]byte(request.AdditionalInfoJson), // JSONB column
			now,
			request.ReportId,
		)
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to execute update in UpdateAdditionalInfoJSON")
			return nil, fmt.Errorf("UpdateAdditionalInfoJSON: exec update: %w", errorMessage)
		}
		if rowsAffected, errorMessage := response.RowsAffected(); errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to get rows affected in UpdateAdditionalInfoJSON")
			return nil, fmt.Errorf("UpdateAdditionalInfoJSON: rows affected: %w", errorMessage)
		} else if rowsAffected == 0 {
			return nil, ErrReportNotFound
		}

		// 3. Return response
		return &reports.UpdateAdditionalInfoJsonResponse{
			ReportId:           request.ReportId,
			AdditionalInfoJson: request.AdditionalInfoJson,
		}, nil
	})
}

// GetAdditionalInfo retrieves the raw JSON metadata for a report.
func (repository *PostgresReportRepository) GetAdditionalInfo(
	context context.Context,
	transaction *sql.Tx,
	reportIdentifier string,
) (*reports.GetAdditionalInfoResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.GetAdditionalInfo")
	defer finishSpan()

	span.SetTag("report.id", reportIdentifier)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.GetAdditionalInfoResponse, error) {
		const selectSQL = `
            SELECT additional_info_json
            FROM reports
            WHERE id = $1
        `
		var raw []byte
		if errorMessage := sessionTx.QueryRowContext(spanContext, selectSQL, reportIdentifier).Scan(&raw); errorMessage != nil {
			if errors.Is(errorMessage, sql.ErrNoRows) {
				return nil, ErrReportNotFound
			}
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to query additional info in GetAdditionalInfo")
			return nil, fmt.Errorf("GetAdditionalInfo: query failed: %w", errorMessage)
		}

		return &reports.GetAdditionalInfoResponse{
			ReportId:           reportIdentifier,
			AdditionalInfoJson: string(raw),
		}, nil
	})
}

// GetReportVersion retrieves a specific version snapshot of a report.
func (repository *PostgresReportRepository) GetReportVersion(
	context context.Context,
	transaction *sql.Tx,
	reportIdentifier string,
	reportVersion int32,
) (*reports.ReportSnapshot, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.GetReportVersion")
	defer finishSpan()

	span.SetTag("report.id", reportIdentifier)
	span.SetTag("report.version", fmt.Sprintf("%d", reportVersion))

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ReportSnapshot, error) {
		const selectSQL = `
            SELECT report_snapshot, created_at
            FROM report_snapshots
            WHERE report_id = $1 AND version = $2
        `
		var (
			rawSnapshotJSON []byte
			createdAt       time.Time
		)
		if errorMessage := sessionTx.QueryRowContext(spanContext, selectSQL, reportIdentifier, reportVersion).
			Scan(&rawSnapshotJSON, &createdAt); errorMessage != nil {
			if errors.Is(errorMessage, sql.ErrNoRows) {
				return nil, ErrReportNotFound
			}
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to query report version in GetReportVersion")
			return nil, fmt.Errorf("GetReportVersion: query failed: %w", errorMessage)
		}

		// Unmarshal the embedded Report
		report := &reports.Report{}
		if errorMessage := protojson.Unmarshal(rawSnapshotJSON, report); errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeInternal)
			return nil, fmt.Errorf("GetReportVersion: failed to unmarshal snapshot JSON: %w", errorMessage)
		}

		// Build and return the snapshot message
		return &reports.ReportSnapshot{
			ReportId:  reportIdentifier,
			Version:   reportVersion,
			Report:    report,
			CreatedAt: createdAt.Format(time.RFC3339Nano),
		}, nil
	})
}

// ListReportVersions returns all available version numbers for a given report.
func (repository *PostgresReportRepository) ListReportVersions(
	context context.Context,
	transaction *sql.Tx,
	reportIdentifier string,
) ([]int32, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.ListReportVersions")
	defer finishSpan()

	span.SetTag("report.id", reportIdentifier)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) ([]int32, error) {
		const selectSQL = `
            SELECT version
            FROM report_snapshots
            WHERE report_id = $1
            ORDER BY version ASC
        `
		rows, errorMessage := sessionTx.QueryContext(spanContext, selectSQL, reportIdentifier)
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to query report versions in ListReportVersions")
			return nil, fmt.Errorf("ListReportVersions: query failed: %w", errorMessage)
		}
		defer rows.Close()

		var versions []int32
		for rows.Next() {
			var v int32
			if errorMessage := rows.Scan(&v); errorMessage != nil {
				herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to scan report version in ListReportVersions")
				return nil, fmt.Errorf("ListReportVersions: scan failed: %w", errorMessage)
			}
			versions = append(versions, v)
		}
		if errorMessage := rows.Err(); errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("ListReportVersions: rows iteration errorMessage: %w", errorMessage)
		}

		return versions, nil
	})
}

// ResolveComment marks a comment as resolved and returns the updated Comment.
func (repository *PostgresReportRepository) ResolveComment(
	context context.Context,
	transaction *sql.Tx,
	request *reports.ResolveCommentRequest,
) (*reports.Comment, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.ResolveComment")
	defer finishSpan()

	span.SetTag("comment.id", request.CommentId)
	span.SetTag("comment.resolved", "true")

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.Comment, error) {
		requesterAssetID, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
		if err != nil {
			log.Printf("error: failed to get requesterAssetID from context: %v", err)
			requesterAssetID = SystemAssetID
		}

		// 1. Fetch existing comment
		const fetchSQL = `
            SELECT
              report_id, section_id, reply_to_comment_id,
              author_asset_id, text,
              created_at, updated_at, resource_type, display_name
            FROM report_comments
            WHERE id = $1
        `
		comment := &reports.Comment{Id: request.CommentId}
		var (
			sectionNull   sql.NullString
			replyNull     sql.NullString
			updatedAtTime time.Time
			displayName   sql.NullString
		)
		if errorMessage := sessionTx.QueryRowContext(spanContext, fetchSQL, request.CommentId).Scan(
			&comment.ReportId,
			&sectionNull,
			&replyNull,
			&comment.AuthorAssetId,
			&comment.Text,
			&comment.CreatedAt,
			&updatedAtTime,
			&comment.ResourceType,
			&displayName,
		); errorMessage != nil {
			if errors.Is(errorMessage, sql.ErrNoRows) {
				return nil, ErrCommentNotFound
			}
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to fetch comment in ResolveComment")
			return nil, fmt.Errorf("ResolveComment: fetch comment: %w", errorMessage)
		}
		if sectionNull.Valid {
			comment.SectionId = sectionNull.String
		}
		if replyNull.Valid {
			comment.ReplyToCommentId = replyNull.String
		}
		if displayName.Valid {
			comment.DisplayName = displayName.String
		}
		comment.UpdatedAt = updatedAtTime.Format(time.RFC3339Nano)

		// 2. Update resolved status
		now := time.Now()
		nowStr := now.Format(time.RFC3339Nano)
		const updateSQL = `
            UPDATE report_comments
            SET
              resolved             = true,
              resolved_at          = $1,
              resolved_by_asset_id = $2,
              updated_at           = $1
            WHERE id = $3
        `
		if _, errorMessage := sessionTx.ExecContext(spanContext, updateSQL,
			now,
			requesterAssetID,
			request.CommentId,
		); errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase, "Failed to update comment in ResolveComment")
			return nil, fmt.Errorf("ResolveComment: exec update: %w", errorMessage)
		}

		// 3. Populate and return updated comment
		comment.Resolved = true
		comment.ResolvedAt = nowStr
		comment.ResolvedByAssetId = requesterAssetID
		comment.UpdatedAt = nowStr
		return comment, nil
	})
}

// CreateReportSection inserts a new report section and returns the created Section.
func (repository *PostgresReportRepository) CreateReportSection(
	context context.Context,
	transaction *sql.Tx,
	reportId string,
	section *reports.ReportSection,
) (*reports.ReportSection, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.CreateReportSection")
	defer finishSpan()

	span.SetTag("report.id", reportId)
	span.SetTag("section.type", section.Type.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ReportSection, error) {
		// 1. Ensure section ID
		if section.Id == "" {
			section.Id = uuid.New().String()
		}
		// 2. Check report ID
		if reportId == "" {
			err := fmt.Errorf("CreateReportSection: missing report_id")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, err
		}

		// 3. Timestamps
		now := time.Now()
		nowStr := now.Format(time.RFC3339Nano)
		section.CreatedAt = nowStr
		section.UpdatedAt = nowStr

		// 4. Marshal oneof content into JSON, handling missing payloads
		marshalOptions := protojson.MarshalOptions{EmitUnpopulated: true, UseEnumNumbers: true}
		var contentJSON []byte
		switch section.Type {
		case reports.SectionType_SECTION_TYPE_NARRATIVE:
			if n := section.GetNarrative(); n != nil {
				// Generate ID for narrative content if missing
				if n.Id == "" {
					n.Id = uuid.New().String()
				}
				buf, _ := marshalOptions.Marshal(n)
				contentJSON = []byte(fmt.Sprintf(`{"narrative":%s}`, buf))
			} else {
				contentJSON = []byte(`{"narrative":{}}`)
			}
		case reports.SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE,
			reports.SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE,
			reports.SectionType_SECTION_TYPE_ENTITY_LIST_PROPERTIES,
			reports.SectionType_SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS:
			entityListSection := section.GetEntityList()
			if entityListSection == nil {
				entityListSection = &reports.EntityListContent{}
			}
			// Generate ID for entity list content if missing
			if entityListSection.Id == "" {
				entityListSection.Id = uuid.New().String()
			}
			contentJSON, _ = marshalOptions.Marshal(entityListSection)
			contentJSON = []byte(fmt.Sprintf(`{"entityList":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_PROPERTY:
			propertyListSection := section.GetPropertyList()
			if propertyListSection == nil {
				propertyListSection = &reports.PropertyListContent{}
			}
			// Generate ID for property list content if missing
			if propertyListSection.Id == "" {
				propertyListSection.Id = uuid.New().String()
			}
			contentJSON, _ = marshalOptions.Marshal(propertyListSection)
			contentJSON = []byte(fmt.Sprintf(`{"propertyList":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_ARREST:
			arrestSection := section.GetArrestList()
			if arrestSection == nil {
				arrestSection = &reports.ArrestContent{}
			}
			// Generate ID for arrest content if missing
			if arrestSection.Id == "" {
				arrestSection.Id = uuid.New().String()
			}
			contentJSON, _ = marshalOptions.Marshal(arrestSection)
			contentJSON = []byte(fmt.Sprintf(`{"arrestList":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_INCIDENT_DETAILS:
			incidentDetails := section.GetIncidentDetails()
			if incidentDetails == nil {
				incidentDetails = &reports.IncidentDetailsContent{}
			}
			// Generate ID for incident details content if missing
			if incidentDetails.Id == "" {
				incidentDetails.Id = uuid.New().String()
			}
			// Generate IDs for responders if missing
			for _, responder := range incidentDetails.Responders {
				if responder.Id == "" {
					responder.Id = uuid.New().String()
				}
			}
			// Generate ID for reporting person if missing
			if incidentDetails.ReportingPerson != nil && incidentDetails.ReportingPerson.Id == "" {
				incidentDetails.ReportingPerson.Id = uuid.New().String()
			}
			// Generate IDs for involved agencies if missing
			for _, agency := range incidentDetails.InvolvedAgencies {
				if agency.Id == "" {
					agency.Id = uuid.New().String()
				}
			}
			contentJSON, _ = marshalOptions.Marshal(incidentDetails)
			contentJSON = []byte(fmt.Sprintf(`{"incidentDetails":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_OFFENSE:
			offenseSection := section.GetOffenseList()
			if offenseSection == nil {
				offenseSection = &reports.OffenseContent{}
			}
			// Generate ID for offense content if missing
			if offenseSection.Id == "" {
				offenseSection.Id = uuid.New().String()
			}
			// Generate IDs for individual offenses if missing
			for _, offense := range offenseSection.Offenses {
				if offense.Id == "" {
					offense.Id = uuid.New().String()
				}
			}
			contentJSON, _ = marshalOptions.Marshal(offenseSection)
			contentJSON = []byte(fmt.Sprintf(`{"offenseList":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_MEDIA:
			mediaSection := section.GetMediaList()
			if mediaSection == nil {
				mediaSection = &reports.MediaContent{}
			}
			// Generate ID for media content if missing
			if mediaSection.Id == "" {
				mediaSection.Id = uuid.New().String()
			}
			// Generate IDs for file references if missing
			for _, fileRef := range mediaSection.FileRefs {
				if fileRef.Id == "" {
					fileRef.Id = uuid.New().String()
				}
			}
			contentJSON, _ = marshalOptions.Marshal(mediaSection)
			contentJSON = []byte(fmt.Sprintf(`{"mediaList":%s}`, contentJSON))

		default:
			contentJSON = []byte(`{}`)
		}

		// 5. Insert into report_sections
		const insertSQL = `
            INSERT INTO report_sections (
              id, report_id, type, content, created_at, updated_at
            ) VALUES ($1,$2,$3,$4,$5,$6)
        `
		if _, err := sessionTx.ExecContext(
			spanContext,
			insertSQL,
			section.Id,
			reportId,
			int32(section.Type),
			contentJSON,
			now,
			now,
		); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("CreateReportSection: exec insert: %w", err)
		}

		// 6. Return the populated section
		return section, nil
	})
}

// GetReportSection retrieves a single section (with comments) by report ID & section ID.
func (repository *PostgresReportRepository) GetReportSection(
	context context.Context,
	parentTx *sql.Tx,
	reportID, sectionID string,
) (*reports.ReportSection, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.GetReportSection")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	span.SetTag("section.id", sectionID)

	return database.WithSession(repository.database, spanContext, parentTx, func(sessionTx *sql.Tx) (*reports.ReportSection, error) {
		//----------------------------------------------------------------------
		// 1. Fetch row
		//----------------------------------------------------------------------
		const sectionQuery = `
            SELECT id, report_id, type, content, created_at, updated_at
            FROM report_sections
            WHERE report_id = $1 AND id = $2
        `
		var (
			dbSectionID   string
			dbReportID    string
			dbTypeInt32   int32
			contentJSON   []byte
			createdAtTime time.Time
			updatedAtTime time.Time
		)
		if err := sessionTx.QueryRowContext(spanContext, sectionQuery, reportID, sectionID).
			Scan(&dbSectionID, &dbReportID, &dbTypeInt32, &contentJSON, &createdAtTime, &updatedAtTime); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				err := fmt.Errorf("GetReportSection: section %q not found in report %q", sectionID, reportID)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
				return nil, err
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("GetReportSection: query failed: %w", err)
		}

		//----------------------------------------------------------------------
		// 2. Unmarshal one-of first, THEN add scalar/enum fields
		//----------------------------------------------------------------------
		section := &reports.ReportSection{}
		if err := protojson.Unmarshal(contentJSON, section); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, fmt.Errorf("GetReportSection: unmarshal content: %w", err)
		}

		section.Id = dbSectionID
		section.ReportId = dbReportID
		section.Type = reports.SectionType(dbTypeInt32)
		section.CreatedAt = createdAtTime.Format(time.RFC3339Nano)
		section.UpdatedAt = updatedAtTime.Format(time.RFC3339Nano)

		//----------------------------------------------------------------------
		// 3. Load comments
		//----------------------------------------------------------------------
		const commentsQuery = `
            SELECT
              id, report_id, section_id, reply_to_comment_id,
              author_asset_id, text,
              created_at, updated_at,
              resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
            FROM report_comments
            WHERE report_id = $1 AND section_id = $2
            ORDER BY created_at DESC
        `
		rows, err := sessionTx.QueryContext(spanContext, commentsQuery, reportID, sectionID)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("GetReportSection: query comments: %w", err)
		}
		defer rows.Close()

		for rows.Next() {
			c := &reports.Comment{}
			var (
				replyNull         sql.NullString
				resolvedAtNull    sql.NullTime
				updatedAtNull     time.Time
				resolvedByAssetID sql.NullString
				displayName       sql.NullString
			)
			if err := rows.Scan(
				&c.Id, &c.ReportId, &c.SectionId,
				&replyNull, &c.AuthorAssetId, &c.Text,
				&c.CreatedAt, &updatedAtNull,
				&c.Resolved, &resolvedAtNull,
				&resolvedByAssetID, &c.ResourceType,
				&displayName,
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("GetReportSection: scan comment: %w", err)
			}
			if replyNull.Valid {
				c.ReplyToCommentId = replyNull.String
			}
			if resolvedByAssetID.Valid {
				c.ResolvedByAssetId = resolvedByAssetID.String
			}
			if displayName.Valid {
				c.DisplayName = displayName.String
			}
			c.UpdatedAt = updatedAtNull.Format(time.RFC3339Nano)
			if resolvedAtNull.Valid {
				c.ResolvedAt = resolvedAtNull.Time.Format(time.RFC3339Nano)
			}
			section.Comments = append(section.Comments, c)
		}
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("GetReportSection: iterate comments: %w", err)
		}

		return section, nil
	})
}

// UpdateReportSection updates the content and type of an existing section.
func (repository *PostgresReportRepository) UpdateReportSection(
	context context.Context,
	transaction *sql.Tx,
	reportID string,
	section *reports.ReportSection,
) (*reports.ReportSection, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.UpdateReportSection")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	span.SetTag("section.id", section.Id)
	span.SetTag("section.type", section.Type.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ReportSection, error) {
		// 1. Fetch existing created_at and current content to preserve IDs
		const fetchSQL = `
            SELECT created_at, content
            FROM report_sections
            WHERE report_id = $1 AND id = $2
        `
		var createdAt time.Time
		var existingContentJSON []byte
		if err := sessionTx.QueryRowContext(spanContext, fetchSQL, reportID, section.Id).
			Scan(&createdAt, &existingContentJSON); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				err := fmt.Errorf("UpdateReportSection: section %q not found in report %q", section.Id, reportID)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
				return nil, err
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("UpdateReportSection: fetch existing: %w", err)
		}
		section.CreatedAt = createdAt.Format(time.RFC3339Nano)

		// 2. Unmarshal existing content to preserve IDs
		existingSection := &reports.ReportSection{}
		if len(existingContentJSON) > 0 {
			if err := protojson.Unmarshal(existingContentJSON, existingSection); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, fmt.Errorf("UpdateReportSection: unmarshal existing content: %w", err)
			}
		}

		// 3. Merge and generate IDs only if missing
		marshalOptions := protojson.MarshalOptions{EmitUnpopulated: true, UseEnumNumbers: true}
		var contentJSON []byte
		switch section.Type {
		case reports.SectionType_SECTION_TYPE_NARRATIVE:
			narrativeSection := section.GetNarrative()
			if narrativeSection == nil {
				narrativeSection = &reports.NarrativeContent{}
			}
			// Preserve existing ID if it exists
			if existingNarrative := existingSection.GetNarrative(); existingNarrative != nil && existingNarrative.Id != "" {
				narrativeSection.Id = existingNarrative.Id
			} else if narrativeSection.Id == "" {
				narrativeSection.Id = uuid.New().String()
			}
			contentJSON, _ = marshalOptions.Marshal(narrativeSection)
			contentJSON = []byte(fmt.Sprintf(`{"narrative":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE,
			reports.SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE,
			reports.SectionType_SECTION_TYPE_ENTITY_LIST_PROPERTIES,
			reports.SectionType_SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS:
			entityListSection := section.GetEntityList()
			if entityListSection == nil {
				entityListSection = &reports.EntityListContent{}
			}
			// Preserve existing ID if it exists
			if existingEntityList := existingSection.GetEntityList(); existingEntityList != nil && existingEntityList.Id != "" {
				entityListSection.Id = existingEntityList.Id
			} else if entityListSection.Id == "" {
				entityListSection.Id = uuid.New().String()
			}
			contentJSON, _ = marshalOptions.Marshal(entityListSection)
			contentJSON = []byte(fmt.Sprintf(`{"entityList":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_PROPERTY:
			propertyListSection := section.GetPropertyList()
			if propertyListSection == nil {
				propertyListSection = &reports.PropertyListContent{}
			}
			// Preserve existing ID if it exists
			if existingPropertyList := existingSection.GetPropertyList(); existingPropertyList != nil && existingPropertyList.Id != "" {
				propertyListSection.Id = existingPropertyList.Id
			} else if propertyListSection.Id == "" {
				propertyListSection.Id = uuid.New().String()
			}
			contentJSON, _ = marshalOptions.Marshal(propertyListSection)
			contentJSON = []byte(fmt.Sprintf(`{"propertyList":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_ARREST:
			arrestSection := section.GetArrestList()
			if arrestSection == nil {
				arrestSection = &reports.ArrestContent{}
			}
			// Preserve existing ID if it exists
			if existingArrest := existingSection.GetArrestList(); existingArrest != nil && existingArrest.Id != "" {
				arrestSection.Id = existingArrest.Id
			} else if arrestSection.Id == "" {
				arrestSection.Id = uuid.New().String()
			}
			contentJSON, _ = marshalOptions.Marshal(arrestSection)
			contentJSON = []byte(fmt.Sprintf(`{"arrestList":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_INCIDENT_DETAILS:
			incidentDetails := section.GetIncidentDetails()
			if incidentDetails == nil {
				incidentDetails = &reports.IncidentDetailsContent{}
			}
			// Preserve existing ID if it exists
			if existingIncidentDetails := existingSection.GetIncidentDetails(); existingIncidentDetails != nil && existingIncidentDetails.Id != "" {
				incidentDetails.Id = existingIncidentDetails.Id
			} else if incidentDetails.Id == "" {
				incidentDetails.Id = uuid.New().String()
			}

			// Preserve existing IDs for nested objects
			if existingIncidentDetails := existingSection.GetIncidentDetails(); existingIncidentDetails != nil {
				// Preserve responder IDs
				for i, responder := range incidentDetails.Responders {
					if responder.Id == "" && i < len(existingIncidentDetails.Responders) {
						responder.Id = existingIncidentDetails.Responders[i].Id
					}
					if responder.Id == "" {
						responder.Id = uuid.New().String()
					}
				}
				// Preserve reporting person ID
				if incidentDetails.ReportingPerson != nil {
					if existingIncidentDetails.ReportingPerson != nil && existingIncidentDetails.ReportingPerson.Id != "" {
						incidentDetails.ReportingPerson.Id = existingIncidentDetails.ReportingPerson.Id
					} else if incidentDetails.ReportingPerson.Id == "" {
						incidentDetails.ReportingPerson.Id = uuid.New().String()
					}
				}
				// Preserve agency IDs
				for i, agency := range incidentDetails.InvolvedAgencies {
					if agency.Id == "" && i < len(existingIncidentDetails.InvolvedAgencies) {
						agency.Id = existingIncidentDetails.InvolvedAgencies[i].Id
					}
					if agency.Id == "" {
						agency.Id = uuid.New().String()
					}
				}
			} else {
				// Generate new IDs for all nested objects
				for _, responder := range incidentDetails.Responders {
					if responder.Id == "" {
						responder.Id = uuid.New().String()
					}
				}
				if incidentDetails.ReportingPerson != nil && incidentDetails.ReportingPerson.Id == "" {
					incidentDetails.ReportingPerson.Id = uuid.New().String()
				}
				for _, agency := range incidentDetails.InvolvedAgencies {
					if agency.Id == "" {
						agency.Id = uuid.New().String()
					}
				}
			}

			contentJSON, _ = marshalOptions.Marshal(incidentDetails)
			contentJSON = []byte(fmt.Sprintf(`{"incidentDetails":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_OFFENSE:
			offenseSection := section.GetOffenseList()
			if offenseSection == nil {
				offenseSection = &reports.OffenseContent{}
			}
			// Generate ID for offense content if missing
			if offenseSection.Id == "" {
				offenseSection.Id = uuid.New().String()
			}
			// Generate IDs for individual offenses if missing
			for _, offense := range offenseSection.Offenses {
				if offense.Id == "" {
					offense.Id = uuid.New().String()
				}
			}
			contentJSON, _ = marshalOptions.Marshal(offenseSection)
			contentJSON = []byte(fmt.Sprintf(`{"offenseList":%s}`, contentJSON))

		case reports.SectionType_SECTION_TYPE_MEDIA:
			mediaSection := section.GetMediaList()
			if mediaSection == nil {
				mediaSection = &reports.MediaContent{}
			}
			// Preserve existing ID if it exists
			if existingMedia := existingSection.GetMediaList(); existingMedia != nil && existingMedia.Id != "" {
				mediaSection.Id = existingMedia.Id
				// Preserve existing file reference IDs
				existingFileRefMap := make(map[string]*reports.FileReference)
				for _, existingFileRef := range existingMedia.FileRefs {
					if existingFileRef.FileId != "" {
						existingFileRefMap[existingFileRef.FileId] = existingFileRef
					}
				}
				for _, fileRef := range mediaSection.FileRefs {
					if existingFileRef, exists := existingFileRefMap[fileRef.FileId]; exists {
						fileRef.Id = existingFileRef.Id
					} else if fileRef.Id == "" {
						fileRef.Id = uuid.New().String()
					}
				}
			} else if mediaSection.Id == "" {
				mediaSection.Id = uuid.New().String()
				// Generate IDs for all file references if missing
				for _, fileRef := range mediaSection.FileRefs {
					if fileRef.Id == "" {
						fileRef.Id = uuid.New().String()
					}
				}
			}
			contentJSON, _ = marshalOptions.Marshal(mediaSection)
			contentJSON = []byte(fmt.Sprintf(`{"mediaList":%s}`, contentJSON))

		default:
			contentJSON = []byte(`{}`)
		}

		// 4. Update the row
		currentTime := time.Now()
		formattedUpdatedAt := currentTime.Format(time.RFC3339Nano)
		const updateSQL = `
            UPDATE report_sections
            SET type       = $1,
                content    = $2,
                updated_at = $3
            WHERE report_id = $4 AND id = $5
        `
		updateResult, errorMessage := sessionTx.ExecContext(spanContext, updateSQL,
			int32(section.Type),
			contentJSON,
			currentTime,
			reportID,
			section.Id,
		)
		if errorMessage != nil {
			herosentry.CaptureException(spanContext, errorMessage, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("UpdateReportSection: exec update: %w", errorMessage)
		}
		if rowsAffected, _ := updateResult.RowsAffected(); rowsAffected == 0 {
			err := fmt.Errorf("UpdateReportSection: no rows updated for section %q", section.Id)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return nil, err
		}

		section.UpdatedAt = formattedUpdatedAt
		return section, nil
	})
}

// DeleteReportSection permanently deletes a section (and its comments) from a report.
func (repository *PostgresReportRepository) DeleteReportSection(
	context context.Context,
	transaction *sql.Tx,
	reportID, sectionID string,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.DeleteReportSection")
	defer finishSpan()

	span.SetTag("report.id", reportID)
	span.SetTag("section.id", sectionID)

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		// 1. Delete any comments scoped to this section
		if _, errorMessage := sessionTx.ExecContext(spanContext, `
            DELETE FROM report_comments
            WHERE report_id = $1 AND section_id = $2
        `, reportID, sectionID); errorMessage != nil {
			return fmt.Errorf("DeleteReportSection: delete section comments: %w", errorMessage)
		}

		// 2. Delete the section itself
		deleteResult, errorMessage := sessionTx.ExecContext(spanContext, `
            DELETE FROM report_sections
            WHERE report_id = $1 AND id = $2
        `, reportID, sectionID)
		if errorMessage != nil {
			return fmt.Errorf("DeleteReportSection: exec delete: %w", errorMessage)
		}
		rowsAffected, errorMessage := deleteResult.RowsAffected()
		if errorMessage != nil {
			return fmt.Errorf("DeleteReportSection: rows affected: %w", errorMessage)
		}
		if rowsAffected == 0 {
			return fmt.Errorf("DeleteReportSection: section %q not found in report %q", sectionID, reportID)
		}
		return nil
	})
}

// ListReportSections returns all sections for a given report (no comments).
func (repository *PostgresReportRepository) ListReportSections(
	context context.Context,
	transaction *sql.Tx,
	reportID string,
) ([]*reports.ReportSection, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.ListReportSections")
	defer finishSpan()

	span.SetTag("report.id", reportID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) ([]*reports.ReportSection, error) {
		const query = `
            SELECT
              id, report_id, type, content, created_at, updated_at
            FROM report_sections
            WHERE report_id = $1
            ORDER BY id ASC
        `
		sectionRows, errorMessage := sessionTx.QueryContext(spanContext, query, reportID)
		if errorMessage != nil {
			return nil, fmt.Errorf("ListReportSections: query failed: %w", errorMessage)
		}
		defer sectionRows.Close()

		var reportSections []*reports.ReportSection
		for sectionRows.Next() {
			// read every column except content into locals -------------------------
			var (
				sectionID          string
				parentReportID     string
				sectionTypeInt32   int32
				sectionContentJSON []byte
				sectionCreatedTime time.Time
				sectionUpdatedTime time.Time
			)
			if err := sectionRows.Scan(
				&sectionID,
				&parentReportID,
				&sectionTypeInt32,
				&sectionContentJSON,
				&sectionCreatedTime,
				&sectionUpdatedTime,
			); err != nil {
				sectionRows.Close()
				return nil, fmt.Errorf("ListReportSections: scan failed: %w", err)
			}

			// ---------------------------------------------------------------------
			// 1. unmarshal the one-of payload into a *fresh* ReportSection
			// ---------------------------------------------------------------------
			reportSection := &reports.ReportSection{}
			if err := protojson.Unmarshal(sectionContentJSON, reportSection); err != nil {
				sectionRows.Close()
				return nil, fmt.Errorf("ListReportSections: unmarshal content: %w", err)
			}

			// ---------------------------------------------------------------------
			// 2. now add the scalar / enum fields that weren't in JSON
			// ---------------------------------------------------------------------
			reportSection.Id = sectionID
			reportSection.ReportId = parentReportID
			reportSection.Type = reports.SectionType(sectionTypeInt32)
			reportSection.CreatedAt = sectionCreatedTime.Format(time.RFC3339Nano)
			reportSection.UpdatedAt = sectionUpdatedTime.Format(time.RFC3339Nano)

			reportSections = append(reportSections, reportSection)
		}

		if errorMessage := sectionRows.Err(); errorMessage != nil {
			return nil, fmt.Errorf("ListReportSections: iteration errorMessage: %w", errorMessage)
		}

		return reportSections, nil
	})
}

// ListReportsBySituationID returns a paginated list of reports filtered by situation_id.
// listReportsByFilter is a helper function to eliminate code duplication
// between ListReportsBySituationID and ListReportsByCaseID
func (repository *PostgresReportRepository) listReportsByFilter(
	context context.Context,
	transaction *sql.Tx,
	filterColumn string,
	filterValue string,
	limit int,
	offsetToken string,
	methodName string,
) (*reports.ListReportsResponse, error) {
	return database.WithSession(repository.database, context, transaction, func(sessionTx *sql.Tx) (*reports.ListReportsResponse, error) {
		// 1. Parse pageToken into offset
		if limit <= 0 {
			limit = 50 // default page size
		}
		queryOffset := 0
		if offsetToken != "" {
			if parsedOffset, err := strconv.Atoi(offsetToken); err == nil && parsedOffset >= 0 {
				queryOffset = parsedOffset
			}
		}

		// 2. Select only IDs matching the filter
		var queryBuilder strings.Builder
		queryBuilder.WriteString(`
            SELECT id
            FROM reports
            WHERE `)
		queryBuilder.WriteString(filterColumn)
		queryBuilder.WriteString(` = $1
            ORDER BY assigned_at DESC, id ASC
            LIMIT $2 OFFSET $3
        `)
		idQuery := queryBuilder.String()
		rows, err := sessionTx.QueryContext(context, idQuery, filterValue, limit, queryOffset)
		if err != nil {
			return nil, fmt.Errorf("%s: query failed: %w", methodName, err)
		}
		defer rows.Close()

		var ids []string
		for rows.Next() {
			var id string
			if err := rows.Scan(&id); err != nil {
				return nil, fmt.Errorf("%s: scan id: %w", methodName, err)
			}
			ids = append(ids, id)
		}
		if err := rows.Err(); err != nil {
			return nil, fmt.Errorf("%s: iterate ids: %w", methodName, err)
		}

		// 3. Batch load main report data and relationships
		var reportList []*reports.Report
		if len(ids) > 0 {
			// First load main report records
			mainQuery := `
				SELECT
				  id, org_id, author_asset_id, title, status,
				  assigned_at, updated_at, completed_at,
				  additional_info_json, version,
				  situation_id, case_id,
				  watcher_asset_ids, resource_type, created_at, created_by_asset_id, report_type
				FROM reports WHERE id = ANY($1)
				ORDER BY assigned_at DESC, id ASC
			`
			mainRows, err := sessionTx.QueryContext(context, mainQuery, pq.Array(ids))
			if err != nil {
				return nil, fmt.Errorf("%s: query main reports: %w", methodName, err)
			}
			defer mainRows.Close()

			reportsMap := make(map[string]*reports.Report)
			for mainRows.Next() {
				report := &reports.Report{}
				var (
					authorAssetID                 sql.NullString
					completedAtTime               sql.NullTime
					situationIdentifier           sql.NullString
					caseIdentifier                sql.NullString
					additionalInfoJSON            []byte
					watcherAssetIDSlice           []string
					assignedAtTime, updatedAtTime time.Time
					createdAtTime                 time.Time
					createdByAssetID              sql.NullString
					reportTypeInt32               int32
				)

				if err := mainRows.Scan(
					&report.Id, &report.OrgId, &authorAssetID, &report.Title,
					(*int32)(&report.Status), &assignedAtTime, &updatedAtTime, &completedAtTime,
					&additionalInfoJSON, &report.Version,
					&situationIdentifier, &caseIdentifier,
					pq.Array(&watcherAssetIDSlice), &report.ResourceType, &createdAtTime,
					&createdByAssetID, &reportTypeInt32,
				); err != nil {
					return nil, fmt.Errorf("%s: scan report: %w", methodName, err)
				}

				report.AuthorAssetId = authorAssetID.String
				report.AssignedAt = assignedAtTime.Format(time.RFC3339Nano)
				report.UpdatedAt = updatedAtTime.Format(time.RFC3339Nano)
				report.CreatedAt = createdAtTime.Format(time.RFC3339Nano)
				report.ReportType = reports.ReportType(reportTypeInt32)
				if completedAtTime.Valid {
					report.CompletedAt = completedAtTime.Time.Format(time.RFC3339Nano)
				}
				if situationIdentifier.Valid {
					report.SituationId = situationIdentifier.String
				}
				if caseIdentifier.Valid {
					report.CaseId = caseIdentifier.String
				}
				report.WatcherAssetIds = watcherAssetIDSlice
				if createdByAssetID.Valid {
					report.CreatedByAssetId = createdByAssetID.String
				}

				var additionalInfo map[string]interface{}
				if err := json.Unmarshal(additionalInfoJSON, &additionalInfo); err != nil {
					return nil, fmt.Errorf("%s: invalid additional_info_json: %w", methodName, err)
				}
				if structData, err := structpb.NewStruct(additionalInfo); err == nil {
					report.AdditionalInfoJson = structData
				} else {
					return nil, fmt.Errorf("%s: struct conversion: %w", methodName, err)
				}

				reportsMap[report.Id] = report
			}

			// Batch load all relationships
			sectionsMap, commentsMap, reviewRoundsMap, relationsMap, err := repository.batchLoadReportRelationships(context, sessionTx, ids)
			if err != nil {
				return nil, fmt.Errorf("%s: loading relationships: %w", methodName, err)
			}

			// Assemble reports with their relationships in original order
			for _, id := range ids {
				if report, exists := reportsMap[id]; exists {
					report.Sections = sectionsMap[id]
					report.Comments = commentsMap[id]
					report.ReviewRounds = reviewRoundsMap[id]
					report.Relations = relationsMap[id]
					reportList = append(reportList, report)
				}
			}
		}

		// 4. Compute next page token
		nextPageToken := ""
		if len(ids) == limit {
			nextPageToken = strconv.Itoa(queryOffset + limit)
		}

		return &reports.ListReportsResponse{
			Reports:       reportList,
			NextPageToken: nextPageToken,
		}, nil
	})
}

func (repository *PostgresReportRepository) ListReportsBySituationID(
	context context.Context,
	transaction *sql.Tx,
	situationID string,
	limit int,
	offsetToken string,
) (*reports.ListReportsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.ListReportsBySituationID")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("pagination.limit", fmt.Sprintf("%d", limit))
	span.SetTag("pagination.offset_token", offsetToken)

	return repository.listReportsByFilter(spanContext, transaction, "situation_id", situationID, limit, offsetToken, "ListReportsBySituationID")
}

// ListReportsByCaseID returns a paginated list of reports filtered by case_id.
func (repository *PostgresReportRepository) ListReportsByCaseID(
	context context.Context,
	transaction *sql.Tx,
	caseID string,
	limit int,
	offsetToken string,
) (*reports.ListReportsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.ListReportsByCaseID")
	defer finishSpan()

	span.SetTag("case.id", caseID)
	span.SetTag("pagination.limit", fmt.Sprintf("%d", limit))
	span.SetTag("pagination.offset_token", offsetToken)

	return repository.listReportsByFilter(spanContext, transaction, "case_id", caseID, limit, offsetToken, "ListReportsByCaseID")
}

// ListReviewRoundsForReport retrieves review rounds for a specific report with pagination.
func (repository *PostgresReportRepository) ListReviewRoundsForReport(
	context context.Context,
	transaction *sql.Tx,
	reportIdentifier string,
	limit int,
	offsetToken string,
) (*reports.ListReviewRoundsForReportResponse, error) { // ← Use protobuf type
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.ListReviewRoundsForReport")
	defer finishSpan()

	span.SetTag("report.id", reportIdentifier)
	span.SetTag("pagination.limit", fmt.Sprintf("%d", limit))
	span.SetTag("pagination.offset_token", offsetToken)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ListReviewRoundsForReportResponse, error) { // ← Use protobuf type
		// 1. Parse pageToken into offset
		if limit <= 0 {
			limit = 50 // Default page size
		}
		queryOffset := 0
		if offsetToken != "" {
			if parsedOffset, err := strconv.Atoi(offsetToken); err == nil && parsedOffset >= 0 {
				queryOffset = parsedOffset
			}
		}

		// 2. Build query
		const query = `
            SELECT
              id, reviewer_asset_id, level, status,
              sent_to_level, sent_to_asset_id,
              requested_at, resolved_at,
              round_note, snapshot_version,
              due_at, create_by_asset_id, note_for_reviewer
            FROM report_review_rounds
            WHERE report_id = $1
            ORDER BY requested_at DESC, id ASC
            LIMIT $2 OFFSET $3
        `

		// 3. Execute query
		rows, err := sessionTx.QueryContext(spanContext, query, reportIdentifier, limit, queryOffset)
		if err != nil {
			return nil, fmt.Errorf("ListReviewRoundsForReport: query failed: %w", err)
		}
		defer rows.Close()

		// 4. Scan rows
		var reviewRounds []*reports.ReviewRound
		for rows.Next() {
			reviewRound := &reports.ReviewRound{ReportId: reportIdentifier} // Pre-populate report ID
			var (
				reviewStatusInt32 int32
				resolvedAtTime    sql.NullTime
				dueAtTime         sql.NullTime
				noteForReviewer   sql.NullString
				reviewerAssetID   sql.NullString
				sentToAssetID     sql.NullString
				roundNote         sql.NullString
				createByAssetID   sql.NullString
				requestedAtTime   time.Time
			)
			if err := rows.Scan(
				&reviewRound.Id, &reviewerAssetID, &reviewRound.Level,
				&reviewStatusInt32, &reviewRound.SentToLevel, &sentToAssetID,
				&requestedAtTime, &resolvedAtTime, &roundNote,
				&reviewRound.SnapshotVersion, &dueAtTime,
				&createByAssetID, &noteForReviewer,
			); err != nil {
				return nil, fmt.Errorf("ListReviewRoundsForReport: scan review round: %w", err)
			}

			// Assign scanned values
			reviewRound.ReviewerAssetId = reviewerAssetID.String
			reviewRound.Status = reports.ReviewStatus(reviewStatusInt32)
			reviewRound.SentToAssetId = sentToAssetID.String
			reviewRound.RequestedAt = requestedAtTime.Format(time.RFC3339Nano) // Format time
			if resolvedAtTime.Valid {
				reviewRound.ResolvedAt = resolvedAtTime.Time.Format(time.RFC3339Nano)
			}
			reviewRound.RoundNote = roundNote.String
			if dueAtTime.Valid {
				reviewRound.DueAt = dueAtTime.Time.Format(time.RFC3339Nano)
			}
			reviewRound.CreateByAssetId = createByAssetID.String
			reviewRound.NoteForReviewer = noteForReviewer.String

			reviewRounds = append(reviewRounds, reviewRound)
		}
		if err := rows.Err(); err != nil {
			return nil, fmt.Errorf("ListReviewRoundsForReport: iteration error: %w", err)
		}

		// 5. Compute next page token
		nextPageToken := ""
		if len(reviewRounds) == limit {
			nextPageToken = strconv.Itoa(queryOffset + limit)
		}

		// 6. Return response
		return &reports.ListReviewRoundsForReportResponse{
			ReviewRounds:  reviewRounds,
			NextPageToken: nextPageToken,
		}, nil
	})
}

// GetReviewRound retrieves a single review round by its ID.
func (repository *PostgresReportRepository) GetReviewRound(
	context context.Context,
	transaction *sql.Tx,
	reviewRoundIdentifier string,
) (*reports.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.GetReviewRound")
	defer finishSpan()

	span.SetTag("review_round.id", reviewRoundIdentifier)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ReviewRound, error) {
		const fetchSQL = `
            SELECT
              report_id, reviewer_asset_id, level, status,
              sent_to_level, sent_to_asset_id, requested_at, resolved_at,
              round_note, snapshot_version, due_at, create_by_asset_id, note_for_reviewer
            FROM report_review_rounds
            WHERE id = $1
        `
		reviewRound := &reports.ReviewRound{Id: reviewRoundIdentifier}
		var (
			reviewerAssetID sql.NullString
			sentToAssetID   sql.NullString
			requestedAtTime time.Time
			resolvedAtTime  sql.NullTime
			roundNoteNull   sql.NullString
			noteForReviewer sql.NullString
			dueAtNull       sql.NullTime
			createByAssetID sql.NullString
		)
		var (
			levelInt  int32
			statusInt int32
			sentLevel int32
			snapshotV int32
		)

		err := sessionTx.QueryRowContext(spanContext, fetchSQL, reviewRoundIdentifier).Scan(
			&reviewRound.ReportId, &reviewerAssetID, &levelInt, &statusInt,
			&sentLevel, &sentToAssetID, &requestedAtTime, &resolvedAtTime,
			&roundNoteNull, &snapshotV, &dueAtNull, &createByAssetID, &noteForReviewer,
		)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, fmt.Errorf("GetReviewRound: review round %q not found: %w", reviewRoundIdentifier, ErrReviewRoundNotFound) // Assuming ErrReviewRoundNotFound exists or is defined
			}
			return nil, fmt.Errorf("GetReviewRound: fetch failed: %w", err)
		}

		// Populate the proto message
		reviewRound.ReviewerAssetId = reviewerAssetID.String
		reviewRound.Level = levelInt
		reviewRound.Status = reports.ReviewStatus(statusInt)
		reviewRound.SentToLevel = sentLevel
		reviewRound.SentToAssetId = sentToAssetID.String
		reviewRound.RequestedAt = requestedAtTime.Format(time.RFC3339Nano)
		if resolvedAtTime.Valid {
			reviewRound.ResolvedAt = resolvedAtTime.Time.Format(time.RFC3339Nano)
		}
		reviewRound.RoundNote = roundNoteNull.String
		reviewRound.SnapshotVersion = snapshotV
		if dueAtNull.Valid {
			reviewRound.DueAt = dueAtNull.Time.Format(time.RFC3339Nano)
		}
		reviewRound.CreateByAssetId = createByAssetID.String
		reviewRound.NoteForReviewer = noteForReviewer.String

		return reviewRound, nil
	})
}

// UpdateReviewRound updates an existing review round.
// Note: This is a simple update; complex status transitions should use specific methods like ApproveReviewRound or RequestChanges.
func (repository *PostgresReportRepository) UpdateReviewRound(
	context context.Context,
	transaction *sql.Tx,
	reviewRound *reports.ReviewRound,
) (*reports.ReviewRound, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.UpdateReviewRound")
	defer finishSpan()

	span.SetTag("review_round.id", reviewRound.Id)
	span.SetTag("review_round.status", reviewRound.Status.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ReviewRound, error) {
		if reviewRound == nil || reviewRound.Id == "" {
			return nil, fmt.Errorf("UpdateReviewRound: invalid input review round")
		}

		// Update all mutable fields based on the input reviewRound.
		// Immutable fields like id, report_id, requested_at, create_by_asset_id, snapshot_version are not updated.
		const updateSQL = `
            UPDATE report_review_rounds
            SET
              reviewer_asset_id = $1,
              level             = $2,
              status            = $3,
              sent_to_level     = $4,
              sent_to_asset_id  = $5,
              resolved_at       = $6,
              round_note        = $7,
              due_at            = $8,
              note_for_reviewer = $9
            WHERE id = $10
        `

		var dueAt sql.NullTime
		if reviewRound.DueAt != "" {
			parsedTime, err := time.Parse(time.RFC3339Nano, reviewRound.DueAt)
			if err == nil {
				dueAt = sql.NullTime{Time: parsedTime, Valid: true}
			} else {
				// Log or handle parse error if necessary
				dueAt = sql.NullTime{Valid: false} // Treat invalid format as NULL
			}
		} else {
			dueAt = sql.NullTime{Valid: false}
		}

		var resolvedAt sql.NullTime
		if reviewRound.ResolvedAt != "" {
			parsedTime, err := time.Parse(time.RFC3339Nano, reviewRound.ResolvedAt)
			if err == nil {
				resolvedAt = sql.NullTime{Time: parsedTime, Valid: true}
			} else {
				// Log or handle parse error if necessary
				resolvedAt = sql.NullTime{Valid: false} // Treat invalid format as NULL
			}
		} else {
			resolvedAt = sql.NullTime{Valid: false}
		}

		result, err := sessionTx.ExecContext(spanContext, updateSQL,
			nullIfEmpty(reviewRound.ReviewerAssetId),
			reviewRound.Level,
			int32(reviewRound.Status), // Update status
			reviewRound.SentToLevel,
			nullIfEmpty(reviewRound.SentToAssetId),
			resolvedAt, // Update resolved_at
			nullIfEmpty(reviewRound.RoundNote),
			dueAt,
			nullIfEmpty(reviewRound.NoteForReviewer),
			reviewRound.Id,
		)
		if err != nil {
			return nil, fmt.Errorf("UpdateReviewRound: update failed: %w", err)
		}

		rowsAffected, _ := result.RowsAffected()
		if rowsAffected == 0 {
			// Consider returning ErrReviewRoundNotFound here
			return nil, fmt.Errorf("UpdateReviewRound: review round %q not found or no changes made", reviewRound.Id)
		}

		// Fetch the updated round to return the full object
		return repository.GetReviewRound(spanContext, sessionTx, reviewRound.Id)
	})
}

// DeleteReviewRound permanently deletes a review round.
func (repository *PostgresReportRepository) DeleteReviewRound(
	context context.Context,
	transaction *sql.Tx,
	reviewRoundIdentifier string,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.DeleteReviewRound")
	defer finishSpan()

	span.SetTag("review_round.id", reviewRoundIdentifier)

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		const deleteSQL = `DELETE FROM report_review_rounds WHERE id = $1`
		result, err := sessionTx.ExecContext(spanContext, deleteSQL, reviewRoundIdentifier)
		if err != nil {
			return fmt.Errorf("DeleteReviewRound: delete failed: %w", err)
		}
		rowsAffected, _ := result.RowsAffected()
		if rowsAffected == 0 {
			// Consider returning ErrReviewRoundNotFound here
			return fmt.Errorf("DeleteReviewRound: review round %q not found", reviewRoundIdentifier)
		}
		return nil
	})
}

// SearchReports executes a comprehensive multi-criteria search against the reports dataset.
// This function leverages optimized database indexes (GIN, JSONB, BRIN) for efficient querying
// across both top-level report fields and nested section content.
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// SEARCH CAPABILITIES
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// 🔍 TEXT SEARCH (ILIKE pattern matching using GIN trigram indexes):
//   - title                        — Report title field (reports.title)
//   - id                          — Report ID partial matching (reports.id)
//   - narrative                   — Rich text content (sections.content->'narrative'->'richText')
//   - entity_list_title           — Entity list section titles (sections.content->'entityList'->'title')
//   - incident_location           — Multiple incident location fields (streetAddress, city, state, commonName, etc.)
//   - reporting_person_name       — Reporting person names (firstName/middleName/lastName)
//   - reporting_person_phone_number — Phone numbers (sections.content->'incidentDetails'->'reportingPerson'->'phoneNumber')
//   - reporting_person_role       — Reporter role (sections.content->'incidentDetails'->'reportingPerson'->'reporterRole')
//   - reference_display_name      — Entity reference names (all refs in entityList sections)
//   - responder_display_name      — Incident responder names (all responders in incidentDetails)
//   - responder_role              — Incident responder roles (all responders in incidentDetails)
//   - offense_type                — Offense type from offense sections
//   - agency_name                 — Agency names from incident details (all agencies)
//   - agency_reference            — Agency incident reference numbers (all agencies)
//   - relation_description        — Description field from relations table
//   - relation_object_name        — Object names from relations (object_a_display_name OR object_b_display_name)
//
// 🎯 EXACT FILTERS (using btree indexes):
//   - status                      — Report status enum values (ReportStatus)
//   - report_types                — Report type enum values (ReportType)
//   - situation_ids               — Associated situation IDs array
//   - case_ids                    — Associated case IDs array
//   - created_by_asset_ids        — Asset IDs of report creators
//   - relation_types              — Relation type strings (exact match)
//   - relation_created_by_asset_ids — Asset IDs that created relations
//   - relation_involves_object_types — Object types in relations (object_a_type OR object_b_type)
//   - relation_involves_report_scoped_ids — Report-scoped IDs in relations
//   - relation_involves_global_ids — Global IDs in relations
//   - relation_involves_external_ids — External IDs in relations
//
// 📅 DATE RANGE FILTERS (using BRIN indexes for time-series data):
//   - created_at                  — Report creation timestamp range
//   - updated_at                  — Last modification timestamp range
//   - assigned_at                 — Assignment timestamp range
//   - completed_at                — Completion timestamp range
//   - incident_start_time         — Incident start time range from incident details
//   - incident_end_time           — Incident end time range from incident details
//
// 🗂️ SECTION-SPECIFIC FILTERS (using JSONB indexes on section content):
//   - entity_list_ref_ids         — Entity reference IDs within entity list sections
//   - reference_type              — Entity reference types (PERSON, VEHICLE, PROPERTY)
//   - initial_types               — Initial situation types (SituationType enum)
//   - final_types                 — Final situation types (SituationType enum)
//   - responder_asset_ids         — Asset IDs of incident responders
//   - responder_roles             — Roles of incident responders
//
// 📄 ADVANCED QUERY OPTIONS:
//   - query                       — Global search term across all/specified fields
//   - search_fields               — Limit global search to specific fields only
//   - field_queries               — Target different search terms to specific fields
//   - order_by                    — Sort by CREATED_AT, UPDATED_AT, STATUS, or RELEVANCE
//   - ascending                   — Sort direction (default: DESC)
//   - page_size                   — Results per page (max: 100, default: 20)
//   - page_token                  — Pagination offset token
//
// 🔦 RESULT HIGHLIGHTING:
//   - Generates contextual text fragments showing matched search terms
//   - Up to 3 fragments per report with 40-character context windows
//   - Supports highlighting across all searchable text fields
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// DATABASE SCHEMA INTEGRATION
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// Tables used:
//   - reports                     — Main report data (id, title, status, timestamps, etc.)
//   - report_sections             — Section content (type, JSONB content with narratives/entities/incidents)
//   - report_relations            — Relation data for relation-based filters and searches
//
// Key indexes leveraged:
//   - idx_reports_org_id          — Organization filtering
//   - idx_reports_status          — Status filtering
//   - idx_reports_report_type     — Report type filtering
//   - idx_reports_sit_id          — Situation ID filtering
//   - idx_reports_case_id         — Case ID filtering
//   - idx_reports_title_trgm      — Title full-text search (GIN trigram)
//   - gin_report_sections_content — Section content full-text search (GIN)
//   - idx_sec_*_trgm             — Field-specific trigram indexes for ILIKE queries
//   - brin_reports_*_at          — Time-range queries (BRIN for large datasets)
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// EXECUTION WORKFLOW
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// Step 1: Parameter validation and normalization
// Step 2: Build base query structure (SELECT, FROM, potential JOINs)
// Step 3: Apply report-level filters (status, report_type, IDs, dates)
// Step 4: Apply section-level filters (uses EXISTS subqueries for performance)
// Step 5: Apply relation-level filters (requires JOIN to report_relations)
// Step 6: Apply text search conditions (global query + field-specific queries)
// Step 7: Construct ORDER BY clause based on sort preferences
// Step 8: Execute count query for pagination metadata
// Step 9: Execute main query to retrieve report IDs with pagination
// Step 10: Batch fetch complete report objects for returned IDs
// Step 11: Generate search result highlights for matched terms
// Step 12: Return formatted response with reports, pagination, and highlights
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// SAMPLE QUERY STRUCTURE
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
//	SELECT DISTINCT r.id, r.created_at
//	FROM reports r
//	LEFT JOIN report_relations relations ON r.id = relations.report_id
//	WHERE r.status IN ($1, $2)                                                    -- Exact status filter
//	  AND r.report_type IN ($3, $4)                                              -- Report type filter
//	  AND r.situation_id IN ($5, $6)                                             -- Situation ID filter
//	  AND r.created_at >= $7 AND r.created_at <= $8                             -- Date range filter
//	  AND r.title ILIKE $9                                                       -- Title text search
//	  AND EXISTS (SELECT 1 FROM report_sections s WHERE s.report_id = r.id       -- Section EXISTS subquery
//	      AND s.type = 1 AND s.content #>> '{narrative,richText}' ILIKE $10)     -- Narrative text search
//	  AND EXISTS (SELECT 1 FROM report_sections s WHERE s.report_id = r.id       -- Section enum filter
//	      AND s.type = 3 AND (s.content #>> '{incidentDetails,initialType}')::int IN ($11, $12))
//	  AND relations.relation_type IN ($13, $14)                                  -- Relation type filter
//	ORDER BY r.created_at DESC, r.id ASC                                         -- Consistent ordering
//	LIMIT $15 OFFSET $16                                                         -- Pagination
func (repository *PostgresReportRepository) SearchReports(
	context context.Context,
	transaction *sql.Tx,
	request *reports.SearchReportsRequest,
) (*reports.SearchReportsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.SearchReports")
	defer finishSpan()

	span.SetTag("search.page_size", fmt.Sprintf("%d", request.PageSize))
	span.SetTag("search.page_token", request.PageToken)

	// Allowed fields for partial-match (ILIKE) search in SearchReports.
	// Only fields that are matched with ILIKE (including in JSON) should be here.
	// Fields that require exact match (e.g., enums, ids, etc.) should NOT be in this list.
	var allowedSearchFields = map[string]bool{
		"title":                         true, // reports.title ILIKE
		"id":                            true, // reports.id ILIKE
		"narrative":                     true, // sections.content #>> '{narrative,richText}' ILIKE
		"entity_list_title":             true, // sections.content #>> '{entityList,title}' ILIKE
		"incident_location":             true, // sections.content #>> '{incidentDetails,location}' ILIKE
		"reporting_person_name":         true, // sections.content #>> '{incidentDetails,reportingPerson,firstName|middleName|lastName}' ILIKE
		"reference_display_name":        true, // sections.content #>> '{entityList,entityRefs,0,displayName}' ILIKE
		"responder_display_name":        true, // sections.content #>> '{incidentDetails,responders,0,displayName}' ILIKE
		"reporting_person_phone_number": true, // sections.content #>> '{incidentDetails,reportingPerson,phoneNumber}' ILIKE
		"reporting_person_role":         true, // sections.content #>> '{incidentDetails,reportingPerson,reporterRole}' ILIKE
		"responder_role":                true, // sections.content #>> '{incidentDetails,responders,0,role}' ILIKE
		"offense_type":                  true, // sections.content #>> '{offenseList,offenses}' ILIKE (searches across all offenses)
		"agency_name":                   true, // sections.content #>> '{incidentDetails,involvedAgencies,0,agencyName}' ILIKE
		"agency_reference":              true, // sections.content #>> '{incidentDetails,involvedAgencies,0,incidentReferenceNumber}' ILIKE
		"relation_description":          true, // relations.description ILIKE
		"relation_object_name":          true, // relations.object_a_display_name ILIKE OR relations.object_b_display_name ILIKE
	}

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.SearchReportsResponse, error) {
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 1: REQUEST PARAMETER VALIDATION & NORMALIZATION
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Validate and set page size (min: 1, max: 100, default: 20)
		resultsPerPage := int(request.PageSize)
		if resultsPerPage <= 0 {
			resultsPerPage = 20 // Default page size for optimal performance
		}
		if resultsPerPage > 100 {
			resultsPerPage = 100 // Maximum page size to prevent memory issues
		}

		// Parse pagination offset from page token (numeric offset as string)
		pageOffset := 0
		if request.PageToken != "" {
			if parsedOffset, parseError := strconv.Atoi(request.PageToken); parseError == nil && parsedOffset >= 0 {
				pageOffset = parsedOffset
			}
		}

		// Determine sort direction (default: DESC for newest-first ordering)
		sortDirection := "DESC"
		if request.Ascending {
			sortDirection = "ASC"
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 2: INITIALIZE QUERY BUILDING COMPONENTS
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		var (
			whereClauseConditions []string      // WHERE clause conditions (combined with AND)
			tableJoinClauses      []string      // JOIN clauses (added when relation filtering needed)
			orderByClause         string        // ORDER BY expression based on sort preference
			queryParameters       []interface{} // Parameterized query values for SQL injection prevention
			parameterPosition     = 1           // PostgreSQL parameter counter ($1, $2, $3, ...)
		)

		// Base table reference - always query from reports table
		baseTableClause := `FROM reports`

		// Track if we need relation join (sections now use EXISTS subqueries)
		requiresRelationJoin := false

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 3: APPLY TOP-LEVEL REPORT FILTERS (reports table columns)
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// These filters operate directly on the reports table and use btree/BRIN indexes
		// ORDER MATTERS: Most selective filters first for optimal query planning

		// 🎯 Status filter: Filter by report status enum values (uses idx_reports_status_created_at_desc)
		// Supports multiple status values with IN clause for OR logic within status
		if len(request.Status) > 0 {
			statusFilterParams := make([]interface{}, len(request.Status))
			statusFilterPlaceholders := make([]string, len(request.Status))

			for index, status := range request.Status {
				statusFilterParams[index] = int32(status) // Convert enum to int32 for database
				statusFilterPlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("reports.status IN (%s)", strings.Join(statusFilterPlaceholders, ",")))
			queryParameters = append(queryParameters, statusFilterParams...)
		}

		// 🎯 ReportType filter: Filter by report type enum values (uses idx_reports_report_type_created_at_desc)
		// Supports multiple report type values with IN clause for OR logic within report types
		if len(request.ReportTypes) > 0 {
			reportTypeFilterParams := make([]interface{}, len(request.ReportTypes))
			reportTypeFilterPlaceholders := make([]string, len(request.ReportTypes))

			for index, reportType := range request.ReportTypes {
				reportTypeFilterParams[index] = int32(reportType) // Convert enum to int32 for database
				reportTypeFilterPlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("reports.report_type IN (%s)", strings.Join(reportTypeFilterPlaceholders, ",")))
			queryParameters = append(queryParameters, reportTypeFilterParams...)
		}

		// 🎯 Situation IDs filter: Filter by associated situation IDs (uses idx_reports_sit_id)
		if len(request.SituationIds) > 0 {
			situationFilterParams := make([]interface{}, len(request.SituationIds))
			situationFilterPlaceholders := make([]string, len(request.SituationIds))

			for index, identifier := range request.SituationIds {
				situationFilterParams[index] = identifier
				situationFilterPlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("reports.situation_id IN (%s)", strings.Join(situationFilterPlaceholders, ",")))
			queryParameters = append(queryParameters, situationFilterParams...)
		}

		// 🎯 Case IDs filter: Filter by associated case IDs (uses idx_reports_case_id)
		if len(request.CaseIds) > 0 {
			caseFilterParams := make([]interface{}, len(request.CaseIds))
			caseFilterPlaceholders := make([]string, len(request.CaseIds))

			for index, identifier := range request.CaseIds {
				caseFilterParams[index] = identifier
				caseFilterPlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("reports.case_id IN (%s)", strings.Join(caseFilterPlaceholders, ",")))
			queryParameters = append(queryParameters, caseFilterParams...)
		}

		// 🎯 Creator filter: Filter by asset IDs of report creators (uses idx_reports_created_by)
		if len(request.CreatedByAssetIds) > 0 {
			creatorFilterParams := make([]interface{}, len(request.CreatedByAssetIds))
			creatorFilterPlaceholders := make([]string, len(request.CreatedByAssetIds))

			for index, identifier := range request.CreatedByAssetIds {
				creatorFilterParams[index] = identifier
				creatorFilterPlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("reports.created_by_asset_id IN (%s)", strings.Join(creatorFilterPlaceholders, ",")))
			queryParameters = append(queryParameters, creatorFilterParams...)
		}

		// 🎯 ReportType filter: Filter by report type enum values (uses idx_reports_report_type_created_at_desc)
		// Supports multiple report type values with IN clause for OR logic within report types
		if len(request.ReportTypes) > 0 {
			reportTypeFilterParams := make([]interface{}, len(request.ReportTypes))
			reportTypeFilterPlaceholders := make([]string, len(request.ReportTypes))

			for index, reportType := range request.ReportTypes {
				reportTypeFilterParams[index] = int32(reportType) // Convert enum to int32 for database
				reportTypeFilterPlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("reports.report_type IN (%s)", strings.Join(reportTypeFilterPlaceholders, ",")))
			queryParameters = append(queryParameters, reportTypeFilterParams...)
		}

		// 📅 Date range filters: Apply timestamp ranges using BRIN/BTREE indexes for efficient time-series queries
		// CreatedAt filter (uses idx_reports_created_at_desc index for fast range scans and sorting)
		if request.CreatedAt != nil {
			if request.CreatedAt.From != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("reports.created_at >= $%d::timestamptz", parameterPosition))
				queryParameters = append(queryParameters, request.CreatedAt.From)
				parameterPosition++
			}
			if request.CreatedAt.To != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("reports.created_at <= $%d::timestamptz", parameterPosition))
				queryParameters = append(queryParameters, request.CreatedAt.To)
				parameterPosition++
			}
		}

		// UpdatedAt filter (uses brin_reports_updated_at index)
		if request.UpdatedAt != nil {
			if request.UpdatedAt.From != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("reports.updated_at >= $%d::timestamptz", parameterPosition))
				queryParameters = append(queryParameters, request.UpdatedAt.From)
				parameterPosition++
			}
			if request.UpdatedAt.To != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("reports.updated_at <= $%d::timestamptz", parameterPosition))
				queryParameters = append(queryParameters, request.UpdatedAt.To)
				parameterPosition++
			}
		}

		// AssignedAt filter (uses brin_reports_assigned_at index, when available)
		if request.AssignedAt != nil {
			if request.AssignedAt.From != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("reports.assigned_at >= $%d::timestamptz", parameterPosition))
				queryParameters = append(queryParameters, request.AssignedAt.From)
				parameterPosition++
			}
			if request.AssignedAt.To != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("reports.assigned_at <= $%d::timestamptz", parameterPosition))
				queryParameters = append(queryParameters, request.AssignedAt.To)
				parameterPosition++
			}
		}

		// CompletedAt filter (uses brin_reports_completed_at index)
		if request.CompletedAt != nil {
			if request.CompletedAt.From != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("reports.completed_at >= $%d::timestamptz", parameterPosition))
				queryParameters = append(queryParameters, request.CompletedAt.From)
				parameterPosition++
			}
			if request.CompletedAt.To != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("reports.completed_at <= $%d::timestamptz", parameterPosition))
				queryParameters = append(queryParameters, request.CompletedAt.To)
				parameterPosition++
			}
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 4: APPLY SECTION-SPECIFIC FILTERS (report_sections table + JSONB operations)
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// These filters require JOINing to report_sections table and use JSONB indexes

		requiresSectionJoin := false // Track if we need to JOIN report_sections table

		// 🗂️ Entity list filters: Search within entity list sections (people/vehicles/properties)
		// Section types: 4=ENTITY_LIST_PEOPLE, 5=ENTITY_LIST_VEHICLE, 6=ENTITY_LIST_PROPERTIES
		if len(request.EntityListRefIds) > 0 || request.ReferenceType != "" {
			// Entity reference IDs: Search within JSONB arrays using EXISTS subqueries
			// Uses idx_sec_entity_refs_jsonb index for efficient JSONB array searching
			if len(request.EntityListRefIds) > 0 {
				var entityRefIdConditions []string
				for _, referenceId := range request.EntityListRefIds {
					entityRefIdConditions = append(entityRefIdConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type IN (4, 5, 6) AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{entityList,entityRefs}') as reference WHERE reference->>'id' = $%d))", parameterPosition))
					queryParameters = append(queryParameters, referenceId)
					parameterPosition++
				}
				whereClauseConditions = append(whereClauseConditions, strings.Join(entityRefIdConditions, " OR "))
			}

			// Reference type filter: Filter by entity reference type (uses idx_sec_reference_type)
			if request.ReferenceType != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type IN (4, 5, 6) AND content #>> '{entityList,entityRefs,0,type}' = $%d)", parameterPosition))
				queryParameters = append(queryParameters, request.ReferenceType)
				parameterPosition++
			}
		}

		// 🗂️ Incident details filters: Search within incident details sections (type=3)
		// IncidentEndTime filter: Uses idx_sec_incident_end index for timestamp range queries
		if request.IncidentEndTime != nil {
			if request.IncidentEndTime.From != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND content #>> '{incidentDetails,incidentEndTime}' >= $%d)", parameterPosition))
				queryParameters = append(queryParameters, request.IncidentEndTime.From)
				parameterPosition++
			}
			if request.IncidentEndTime.To != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND content #>> '{incidentDetails,incidentEndTime}' <= $%d)", parameterPosition))
				queryParameters = append(queryParameters, request.IncidentEndTime.To)
				parameterPosition++
			}
		}

		// IncidentStartTime filter: Filter by incident start time in section content
		if request.IncidentStartTime != nil {
			if request.IncidentStartTime.From != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND content #>> '{incidentDetails,incidentStartTime}' >= $%d)", parameterPosition))
				queryParameters = append(queryParameters, request.IncidentStartTime.From)
				parameterPosition++
			}
			if request.IncidentStartTime.To != "" {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND content #>> '{incidentDetails,incidentStartTime}' <= $%d)", parameterPosition))
				queryParameters = append(queryParameters, request.IncidentStartTime.To)
				parameterPosition++
			}
		}

		// InitialTypes filter: Filter by initial situation type (uses idx_sec_initial_type)
		if len(request.InitialTypes) > 0 {
			initialTypePlaceholders := make([]string, len(request.InitialTypes))

			for index, situationType := range request.InitialTypes {
				initialTypePlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				queryParameters = append(queryParameters, int32(situationType)) // Convert SituationType enum to int32
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND (content #>> '{incidentDetails,initialType}')::int IN (%s))",
					strings.Join(initialTypePlaceholders, ",")))
		}

		// FinalTypes filter: Filter by final situation type (uses idx_sec_final_type)
		if len(request.FinalTypes) > 0 {
			finalTypePlaceholders := make([]string, len(request.FinalTypes))

			for index, situationType := range request.FinalTypes {
				finalTypePlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				queryParameters = append(queryParameters, int32(situationType)) // Convert SituationType enum to int32
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND (content #>> '{incidentDetails,finalType}')::int IN (%s))",
					strings.Join(finalTypePlaceholders, ",")))
		}

		// Responder-related filters - uses JSONB array operations and EXISTS subqueries
		if len(request.ResponderAssetIds) > 0 || len(request.ResponderRoles) > 0 {
			if len(request.ResponderAssetIds) > 0 {
				var responderAssetIdConditions []string
				for _, assetID := range request.ResponderAssetIds {
					responderAssetIdConditions = append(responderAssetIdConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{incidentDetails,responders}') as responder WHERE responder->>'assetId' = $%d))", parameterPosition))
					queryParameters = append(queryParameters, assetID)
					parameterPosition++
				}
				whereClauseConditions = append(whereClauseConditions, strings.Join(responderAssetIdConditions, " OR "))
			}

			if len(request.ResponderRoles) > 0 {
				roleCheck := fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{incidentDetails,responders}') as responder WHERE responder->>'role' IN (%s)))",
					buildPlaceholders(parameterPosition, len(request.ResponderRoles)))
				for _, role := range request.ResponderRoles {
					queryParameters = append(queryParameters, role)
					parameterPosition++
				}
				whereClauseConditions = append(whereClauseConditions, roleCheck)
			}
		}

		// Offense-related filters - uses JSONB array operations for multiple offenses
		if len(request.OffenseTypes) > 0 || len(request.OffenseIds) > 0 {
			if len(request.OffenseTypes) > 0 {
				offenseTypeCheck := fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 7 AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{offenseList,offenses}') as offense WHERE offense->>'offenseType' IN (%s)))",
					buildPlaceholders(parameterPosition, len(request.OffenseTypes)))
				for _, offenseType := range request.OffenseTypes {
					queryParameters = append(queryParameters, offenseType)
					parameterPosition++
				}
				whereClauseConditions = append(whereClauseConditions, offenseTypeCheck)
			}

			if len(request.OffenseIds) > 0 {
				offenseIdCheck := fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 7 AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{offenseList,offenses}') as offense WHERE offense->>'id' IN (%s)))",
					buildPlaceholders(parameterPosition, len(request.OffenseIds)))
				for _, offenseId := range request.OffenseIds {
					queryParameters = append(queryParameters, offenseId)
					parameterPosition++
				}
				whereClauseConditions = append(whereClauseConditions, offenseIdCheck)
			}
		}

		// Free-text query handler - builds complex OR conditions across multiple fields
		if request.Query != "" {
			// Determine which fields to search
			var searchableFields []string
			if len(request.SearchFields) > 0 {
				// Only use allowed fields
				for _, field := range request.SearchFields {
					if allowedSearchFields[field] {
						searchableFields = append(searchableFields, field)
					}
				}
			} else {
				// Default to all allowed fields - ensuring graceful handling when sections are missing
				// Start with basic fields that don't require joins for better reliability
				basicFields := []string{"title", "id"}
				sectionFields := []string{"narrative", "entity_list_title", "incident_location",
					"reporting_person_name", "reference_display_name", "responder_display_name",
					"reporting_person_phone_number", "reporting_person_role", "responder_role",
					"offense_type", "agency_name", "agency_reference"}
				relationFields := []string{"relation_description", "relation_object_name"}

				// Add all allowed fields in a predictable order
				for _, field := range basicFields {
					if allowedSearchFields[field] {
						searchableFields = append(searchableFields, field)
					}
				}
				for _, field := range sectionFields {
					if allowedSearchFields[field] {
						searchableFields = append(searchableFields, field)
					}
				}
				for _, field := range relationFields {
					if allowedSearchFields[field] {
						searchableFields = append(searchableFields, field)
					}
				}
			}

			// Build OR conditions for each searchable field
			var searchFieldConditions []string

			for _, field := range searchableFields {
				switch field {
				case "title":
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("reports.title ILIKE $%d", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "id":
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("reports.id ILIKE $%d", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "narrative":
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 1 AND content #>> '{narrative,richText}' ILIKE $%d)", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "entity_list_title":
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type IN (4,5,6) AND content #>> '{entityList,title}' ILIKE $%d)", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "reporting_person_name":
					// Search across first, middle, last name fields
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND (content #>> '{incidentDetails,reportingPerson,firstName}' ILIKE $%d OR content #>> '{incidentDetails,reportingPerson,middleName}' ILIKE $%d OR content #>> '{incidentDetails,reportingPerson,lastName}' ILIKE $%d))",
							parameterPosition, parameterPosition+1, parameterPosition+2))
					queryParameters = append(queryParameters, "%"+request.Query+"%", "%"+request.Query+"%", "%"+request.Query+"%")
					parameterPosition += 3
				case "reference_display_name":
					// Search across all entity references, not just the first one
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type IN (4,5,6) AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{entityList,entityRefs}') as ref WHERE ref->>'displayName' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "responder_display_name":
					// Search across all responders, not just the first one
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{incidentDetails,responders}') as resp WHERE resp->>'displayName' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "reporting_person_phone_number":
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND content #>> '{incidentDetails,reportingPerson,phoneNumber}' ILIKE $%d)", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "reporting_person_role":
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND content #>> '{incidentDetails,reportingPerson,reporterRole}' ILIKE $%d)", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "responder_role":
					// Search across all responders, not just the first one
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{incidentDetails,responders}') as resp WHERE resp->>'role' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "offense_type":
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 7 AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{offenseList,offenses}') as offense WHERE offense->>'offenseType' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "agency_name":
					// Search across all agencies, not just the first one
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{incidentDetails,involvedAgencies}') as agency WHERE agency->>'agencyName' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "agency_reference":
					// Search across all agencies, not just the first one
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(content #> '{incidentDetails,involvedAgencies}') as agency WHERE agency->>'incidentReferenceNumber' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "relation_description":
					requiresRelationJoin = true
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("relations.description ILIKE $%d", parameterPosition))
					queryParameters = append(queryParameters, "%"+request.Query+"%")
					parameterPosition++
				case "relation_object_name":
					requiresRelationJoin = true
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("relations.object_a_display_name ILIKE $%d OR relations.object_b_display_name ILIKE $%d", parameterPosition, parameterPosition+1))
					queryParameters = append(queryParameters, "%"+request.Query+"%", "%"+request.Query+"%")
					parameterPosition += 2
				case "incident_location":
					// Search across multiple location fields
					searchFieldConditions = append(searchFieldConditions,
						fmt.Sprintf("EXISTS (SELECT 1 FROM report_sections WHERE report_id = reports.id AND type = 3 AND (content #>> '{incidentDetails,incidentLocationCleryType}' ILIKE $%d OR content #>> '{incidentDetails,incidentLocationStreetAddress}' ILIKE $%d OR content #>> '{incidentDetails,incidentLocationUnitInfo}' ILIKE $%d OR content #>> '{incidentDetails,incidentLocationType}' ILIKE $%d OR content #>> '{incidentDetails,incidentLocationCommonName}' ILIKE $%d OR content #>> '{incidentDetails,incidentLocationCity}' ILIKE $%d OR content #>> '{incidentDetails,incidentLocationState}' ILIKE $%d OR content #>> '{incidentDetails,incidentLocationZipCode}' ILIKE $%d OR content #>> '{incidentDetails,incidentLocationCountry}' ILIKE $%d))",
							parameterPosition, parameterPosition+1, parameterPosition+2, parameterPosition+3, parameterPosition+4, parameterPosition+5, parameterPosition+6, parameterPosition+7, parameterPosition+8))
					queryParameters = append(queryParameters, "%"+request.Query+"%", "%"+request.Query+"%", "%"+request.Query+"%", "%"+request.Query+"%", "%"+request.Query+"%", "%"+request.Query+"%", "%"+request.Query+"%", "%"+request.Query+"%", "%"+request.Query+"%")
					parameterPosition += 9
				}
			}

			if len(searchFieldConditions) > 0 {
				whereClauseConditions = append(whereClauseConditions,
					fmt.Sprintf("(%s)", strings.Join(searchFieldConditions, " OR ")))
			}
		}

		// Handle field-specific queries - allow targeting specific fields with different search terms
		if len(request.FieldQueries) > 0 {
			for _, fieldQuery := range request.FieldQueries {
				field := fieldQuery.Field
				query := fieldQuery.Query

				if query == "" {
					continue // Skip empty queries
				}

				if !allowedSearchFields[field] {
					continue // Skip disallowed fields
				}

				switch field {
				case "title":
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("reports.title ILIKE $%d", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "id":
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("reports.id ILIKE $%d", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "narrative":
					requiresSectionJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 1 AND sections.content #>> '{narrative,richText}' ILIKE $%d)", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "entity_list_title", "list_title":
					requiresSectionJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type IN (4,5,6) AND sections.content #>> '{entityList,title}' ILIKE $%d)", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "incident_location", "location":
					requiresSectionJoin = true
					// Search across multiple location fields
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 3 AND (sections.content #>> '{incidentDetails,incidentLocationCleryType}' ILIKE $%d OR sections.content #>> '{incidentDetails,incidentLocationStreetAddress}' ILIKE $%d OR sections.content #>> '{incidentDetails,incidentLocationUnitInfo}' ILIKE $%d OR sections.content #>> '{incidentDetails,incidentLocationType}' ILIKE $%d OR sections.content #>> '{incidentDetails,incidentLocationCommonName}' ILIKE $%d OR sections.content #>> '{incidentDetails,incidentLocationCity}' ILIKE $%d OR sections.content #>> '{incidentDetails,incidentLocationState}' ILIKE $%d OR sections.content #>> '{incidentDetails,incidentLocationZipCode}' ILIKE $%d OR sections.content #>> '{incidentDetails,incidentLocationCountry}' ILIKE $%d))",
							parameterPosition, parameterPosition+1, parameterPosition+2, parameterPosition+3, parameterPosition+4, parameterPosition+5, parameterPosition+6, parameterPosition+7, parameterPosition+8))
					queryParameters = append(queryParameters, "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
					parameterPosition += 9
				case "reporting_person_name":
					requiresSectionJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 3 AND (sections.content #>> '{incidentDetails,reportingPerson,firstName}' ILIKE $%d OR sections.content #>> '{incidentDetails,reportingPerson,middleName}' ILIKE $%d OR sections.content #>> '{incidentDetails,reportingPerson,lastName}' ILIKE $%d))",
							parameterPosition, parameterPosition+1, parameterPosition+2))
					queryParameters = append(queryParameters, "%"+query+"%", "%"+query+"%", "%"+query+"%")
					parameterPosition += 3
				case "reference_display_name":
					requiresSectionJoin = true
					// Search across all entity references
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type IN (4,5,6) AND EXISTS (SELECT 1 FROM jsonb_array_elements(sections.content #> '{entityList,entityRefs}') as ref WHERE ref->>'displayName' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "responder_display_name":
					requiresSectionJoin = true
					// Search across all responders
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(sections.content #> '{incidentDetails,responders}') as resp WHERE resp->>'displayName' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "reporting_person_phone_number":
					requiresSectionJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 3 AND sections.content #>> '{incidentDetails,reportingPerson,phoneNumber}' ILIKE $%d)", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "reporting_person_role":
					requiresSectionJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 3 AND sections.content #>> '{incidentDetails,reportingPerson,reporterRole}' ILIKE $%d)", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "responder_role":
					requiresSectionJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(sections.content #> '{incidentDetails,responders}') as resp WHERE resp->>'role' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "offense_type":
					requiresSectionJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 7 AND EXISTS (SELECT 1 FROM jsonb_array_elements(sections.content #> '{offenseList,offenses}') as offense WHERE offense->>'offenseType' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "agency_name":
					requiresSectionJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(sections.content #> '{incidentDetails,involvedAgencies}') as agency WHERE agency->>'agencyName' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "agency_reference":
					requiresSectionJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("(sections.type = 3 AND EXISTS (SELECT 1 FROM jsonb_array_elements(sections.content #> '{incidentDetails,involvedAgencies}') as agency WHERE agency->>'incidentReferenceNumber' ILIKE $%d))", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "relation_description":
					requiresRelationJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("relations.description ILIKE $%d", parameterPosition))
					queryParameters = append(queryParameters, "%"+query+"%")
					parameterPosition++
				case "relation_object_name":
					requiresRelationJoin = true
					whereClauseConditions = append(whereClauseConditions,
						fmt.Sprintf("relations.object_a_display_name ILIKE $%d OR relations.object_b_display_name ILIKE $%d", parameterPosition, parameterPosition+1))
					queryParameters = append(queryParameters, "%"+query+"%", "%"+query+"%")
					parameterPosition += 2
				}
			}
		}

		// 🎯 Relation Types filter: Filter by relation type strings (exact match)
		if len(request.RelationTypes) > 0 {
			requiresRelationJoin = true
			relationTypesParams := make([]interface{}, len(request.RelationTypes))
			relationTypesPlaceholders := make([]string, len(request.RelationTypes))

			for index, relationType := range request.RelationTypes {
				relationTypesParams[index] = relationType
				relationTypesPlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("relations.relation_type IN (%s)", strings.Join(relationTypesPlaceholders, ",")))
			queryParameters = append(queryParameters, relationTypesParams...)
		}

		// 🎯 Relation Created By Asset IDs filter: Filter by asset IDs that created relations
		if len(request.RelationCreatedByAssetIds) > 0 {
			requiresRelationJoin = true
			relationCreatedByParams := make([]interface{}, len(request.RelationCreatedByAssetIds))
			relationCreatedByPlaceholders := make([]string, len(request.RelationCreatedByAssetIds))

			for index, assetID := range request.RelationCreatedByAssetIds {
				relationCreatedByParams[index] = assetID
				relationCreatedByPlaceholders[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("relations.created_by_asset_id IN (%s)", strings.Join(relationCreatedByPlaceholders, ",")))
			queryParameters = append(queryParameters, relationCreatedByParams...)
		}

		// 🎯 Relation Involves Object Types filter: Filter by object types in relations
		if len(request.RelationInvolvesObjectTypes) > 0 {
			requiresRelationJoin = true
			relationObjectTypesParams := make([]interface{}, len(request.RelationInvolvesObjectTypes)*2)
			relationObjectTypesPlaceholdersA := make([]string, len(request.RelationInvolvesObjectTypes))
			relationObjectTypesPlaceholdersB := make([]string, len(request.RelationInvolvesObjectTypes))

			for index, objectType := range request.RelationInvolvesObjectTypes {
				relationObjectTypesParams[index*2] = objectType
				relationObjectTypesParams[index*2+1] = objectType
				relationObjectTypesPlaceholdersA[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
				relationObjectTypesPlaceholdersB[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("(relations.object_a_type IN (%s) OR relations.object_b_type IN (%s))",
					strings.Join(relationObjectTypesPlaceholdersA, ","),
					strings.Join(relationObjectTypesPlaceholdersB, ",")))
			queryParameters = append(queryParameters, relationObjectTypesParams...)
		}

		// 🎯 Relation Involves Report Scoped IDs filter: Filter by report-scoped IDs in relations
		if len(request.RelationInvolvesReportScopedIds) > 0 {
			requiresRelationJoin = true
			relationReportScopedParams := make([]interface{}, len(request.RelationInvolvesReportScopedIds)*2)
			relationReportScopedPlaceholdersA := make([]string, len(request.RelationInvolvesReportScopedIds))
			relationReportScopedPlaceholdersB := make([]string, len(request.RelationInvolvesReportScopedIds))

			for index, reportScopedID := range request.RelationInvolvesReportScopedIds {
				relationReportScopedParams[index*2] = reportScopedID
				relationReportScopedParams[index*2+1] = reportScopedID
				relationReportScopedPlaceholdersA[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
				relationReportScopedPlaceholdersB[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("(relations.object_a_report_scoped_id IN (%s) OR relations.object_b_report_scoped_id IN (%s))",
					strings.Join(relationReportScopedPlaceholdersA, ","),
					strings.Join(relationReportScopedPlaceholdersB, ",")))
			queryParameters = append(queryParameters, relationReportScopedParams...)
		}

		// 🎯 Relation Involves Global IDs filter: Filter by global IDs in relations
		if len(request.RelationInvolvesGlobalIds) > 0 {
			requiresRelationJoin = true
			relationGlobalParams := make([]interface{}, len(request.RelationInvolvesGlobalIds)*2)
			relationGlobalPlaceholdersA := make([]string, len(request.RelationInvolvesGlobalIds))
			relationGlobalPlaceholdersB := make([]string, len(request.RelationInvolvesGlobalIds))

			for index, globalID := range request.RelationInvolvesGlobalIds {
				relationGlobalParams[index*2] = globalID
				relationGlobalParams[index*2+1] = globalID
				relationGlobalPlaceholdersA[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
				relationGlobalPlaceholdersB[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("(relations.object_a_global_id IN (%s) OR relations.object_b_global_id IN (%s))",
					strings.Join(relationGlobalPlaceholdersA, ","),
					strings.Join(relationGlobalPlaceholdersB, ",")))
			queryParameters = append(queryParameters, relationGlobalParams...)
		}

		// 🎯 Relation Involves External IDs filter: Filter by external IDs in relations
		if len(request.RelationInvolvesExternalIds) > 0 {
			requiresRelationJoin = true
			relationExternalParams := make([]interface{}, len(request.RelationInvolvesExternalIds)*2)
			relationExternalPlaceholdersA := make([]string, len(request.RelationInvolvesExternalIds))
			relationExternalPlaceholdersB := make([]string, len(request.RelationInvolvesExternalIds))

			for index, externalID := range request.RelationInvolvesExternalIds {
				relationExternalParams[index*2+1] = externalID
				relationExternalParams[index*2] = externalID
				relationExternalPlaceholdersA[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
				relationExternalPlaceholdersB[index] = fmt.Sprintf("$%d", parameterPosition)
				parameterPosition++
			}

			whereClauseConditions = append(whereClauseConditions,
				fmt.Sprintf("(relations.object_a_external_id IN (%s) OR relations.object_b_external_id IN (%s))",
					strings.Join(relationExternalPlaceholdersA, ","),
					strings.Join(relationExternalPlaceholdersB, ",")))
			queryParameters = append(queryParameters, relationExternalParams...)
		}

		// If we need to join to sections, add the JOIN clause
		if requiresSectionJoin {
			tableJoinClauses = append(tableJoinClauses, "JOIN report_sections sections ON reports.id = sections.report_id")
		}

		// If we need to join to relations, add the JOIN clause (sections now use EXISTS subqueries)
		if requiresRelationJoin {
			tableJoinClauses = append(tableJoinClauses, "JOIN report_relations relations ON reports.id = relations.report_id")
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 5: HANDLE ORDER BY CLAUSE CONSTRUCTION
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Build ORDER BY clause based on user preference (includes secondary sort by ID for consistency)
		switch request.OrderBy {
		case reports.SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT:
			orderByClause = fmt.Sprintf("reports.created_at %s, reports.id ASC", sortDirection)
		case reports.SearchOrderBy_SEARCH_ORDER_BY_UPDATED_AT:
			orderByClause = fmt.Sprintf("reports.updated_at %s, reports.id ASC", sortDirection)
		case reports.SearchOrderBy_SEARCH_ORDER_BY_STATUS:
			orderByClause = fmt.Sprintf("reports.status %s, reports.id ASC", sortDirection)
		case reports.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE:
			// For relevance sorting with ILIKE, we fall back to created_at DESC
			// Full-text search ranking would require ts_vector implementation
			orderByClause = "reports.created_at DESC, reports.id ASC"
		default:
			// Default: newest reports first, consistent secondary ordering by ID
			orderByClause = "reports.created_at DESC, reports.id ASC"
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 6: ASSEMBLE COMPLETE SQL QUERIES
		// ═══════════════════════════════════════════════════════════════════════════════════════════

		// Build FROM clause with conditional JOINs (only join sections table if needed)
		fromClause := baseTableClause
		if len(tableJoinClauses) > 0 {
			fromClause += " " + strings.Join(tableJoinClauses, " ")
		}

		// Build WHERE clause by combining all conditions with AND logic
		whereClause := ""
		if len(whereClauseConditions) > 0 {
			whereClause = " WHERE " + strings.Join(whereClauseConditions, " AND ")
		}

		// Count query: Get total matching results for pagination metadata
		//nolint:gosec // fromClause and whereClause are safely constructed with parameterized queries
		countQuery := fmt.Sprintf("SELECT COUNT(DISTINCT reports.id) %s%s",
			fromClause, whereClause)

		// Main query SELECT columns: Include ORDER BY columns to satisfy PostgreSQL DISTINCT requirements
		var selectColumns string
		switch request.OrderBy {
		case reports.SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT:
			selectColumns = "SELECT DISTINCT reports.id, reports.created_at"
		case reports.SearchOrderBy_SEARCH_ORDER_BY_UPDATED_AT:
			selectColumns = "SELECT DISTINCT reports.id, reports.updated_at"
		case reports.SearchOrderBy_SEARCH_ORDER_BY_STATUS:
			selectColumns = "SELECT DISTINCT reports.id, reports.status"
		default:
			// Default to created_at for consistency
			selectColumns = "SELECT DISTINCT reports.id, reports.created_at"
		}

		// Main query: Get paginated report IDs with proper ordering
		mainQuery := fmt.Sprintf("%s %s%s ORDER BY %s LIMIT $%d OFFSET $%d",
			selectColumns, fromClause, whereClause, orderByClause, parameterPosition, parameterPosition+1)

		// Clone parameters for count query (doesn't need pagination params)
		countQueryParams := make([]interface{}, len(queryParameters))
		copy(countQueryParams, queryParameters)

		// Add pagination parameters to main query
		queryParameters = append(queryParameters, resultsPerPage, pageOffset)

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 7: EXECUTE DATABASE QUERIES
		// ═══════════════════════════════════════════════════════════════════════════════════════════

		// Execute count query first to get total results for pagination
		var totalResults int32
		countError := sessionTx.QueryRowContext(spanContext, countQuery, countQueryParams...).Scan(&totalResults)
		if countError != nil {
			return nil, fmt.Errorf("SearchReports count query failed: %w", countError)
		}

		// Execute main query to get ordered, paginated report IDs
		rows, queryError := sessionTx.QueryContext(spanContext, mainQuery, queryParameters...)
		if queryError != nil {
			return nil, fmt.Errorf("SearchReports main query failed: %w", queryError)
		}
		defer rows.Close()

		// Scan report IDs from result rows (columns depend on ORDER BY selection)
		var reportIdentifiers []string
		for rows.Next() {
			var identifier string

			// Temporary variables for ORDER BY columns (not used, just required for scanning)
			var (
				createdAt time.Time
				updatedAt time.Time
				status    int32
			)

			// Scan based on SELECT columns in main query
			var scanError error
			switch request.OrderBy {
			case reports.SearchOrderBy_SEARCH_ORDER_BY_CREATED_AT:
				scanError = rows.Scan(&identifier, &createdAt)
			case reports.SearchOrderBy_SEARCH_ORDER_BY_UPDATED_AT:
				scanError = rows.Scan(&identifier, &updatedAt)
			case reports.SearchOrderBy_SEARCH_ORDER_BY_STATUS:
				scanError = rows.Scan(&identifier, &status)
			default:
				// Default case: scan ID + created_at
				scanError = rows.Scan(&identifier, &createdAt)
			}

			if scanError != nil {
				return nil, fmt.Errorf("SearchReports error scanning result row: %w", scanError)
			}

			reportIdentifiers = append(reportIdentifiers, identifier)
		}

		if rowsError := rows.Err(); rowsError != nil {
			return nil, fmt.Errorf("SearchReports error iterating results: %w", rowsError)
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 8: FETCH COMPLETE REPORT OBJECTS IN BATCHES TO AVOID N+1 QUERIES
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		var reportsList []*reports.Report
		reportsMap := make(map[string]*reports.Report)

		if len(reportIdentifiers) > 0 {
			// Fetch all report headers in one query
			const reportsQuery = `
				SELECT
					id, org_id, author_asset_id, title, status,
					assigned_at, updated_at, completed_at,
					additional_info_json, version,
					situation_id, case_id,
					watcher_asset_ids, resource_type, created_at, created_by_asset_id, report_type
				FROM reports WHERE id = ANY($1)`

			rows, err := sessionTx.QueryContext(spanContext, reportsQuery, pq.Array(reportIdentifiers))
			if err != nil {
				return nil, fmt.Errorf("SearchReports: failed to batch fetch reports: %w", err)
			}
			defer rows.Close()

			for rows.Next() {
				report := &reports.Report{}
				var (
					authorAssetID                                sql.NullString
					completedAtTime                              sql.NullTime
					situationIdentifier                          sql.NullString
					caseIdentifier                               sql.NullString
					additionalInfoJSON                           []byte
					watcherAssetIDSlice                          []string
					assignedAtTime, updatedAtTime, createdAtTime time.Time
					createdByAssetID                             sql.NullString
					reportTypeInt32                              int32
				)

				if err := rows.Scan(
					&report.Id, &report.OrgId, &authorAssetID, &report.Title,
					(*int32)(&report.Status), &assignedAtTime, &updatedAtTime, &completedAtTime,
					&additionalInfoJSON, &report.Version,
					&situationIdentifier, &caseIdentifier,
					pq.Array(&watcherAssetIDSlice), &report.ResourceType, &createdAtTime,
					&createdByAssetID, &reportTypeInt32,
				); err != nil {
					return nil, fmt.Errorf("SearchReports: failed to scan report row: %w", err)
				}

				report.AuthorAssetId = authorAssetID.String
				report.AssignedAt = assignedAtTime.Format(time.RFC3339Nano)
				report.UpdatedAt = updatedAtTime.Format(time.RFC3339Nano)
				report.CreatedAt = createdAtTime.Format(time.RFC3339Nano)
				report.ReportType = reports.ReportType(reportTypeInt32)
				if completedAtTime.Valid {
					report.CompletedAt = completedAtTime.Time.Format(time.RFC3339Nano)
				}
				if situationIdentifier.Valid {
					report.SituationId = situationIdentifier.String
				}
				if caseIdentifier.Valid {
					report.CaseId = caseIdentifier.String
				}
				report.WatcherAssetIds = watcherAssetIDSlice
				if createdByAssetID.Valid {
					report.CreatedByAssetId = createdByAssetID.String
				}
				var additionalInfo map[string]interface{}
				if err := json.Unmarshal(additionalInfoJSON, &additionalInfo); err == nil {
					if structData, err := structpb.NewStruct(additionalInfo); err == nil {
						report.AdditionalInfoJson = structData
					}
				}
				reportsList = append(reportsList, report)
				reportsMap[report.Id] = report
			}
			if err = rows.Err(); err != nil {
				return nil, fmt.Errorf("SearchReports: error iterating report rows: %w", err)
			}

			// Batch fetch sections
			const sectionsQuery = `
				SELECT id, report_id, type, content, created_at, updated_at
				FROM report_sections
				WHERE report_id = ANY($1)
				ORDER BY report_id, id ASC`
			sectionRows, err := sessionTx.QueryContext(spanContext, sectionsQuery, pq.Array(reportIdentifiers))
			if err != nil {
				return nil, fmt.Errorf("SearchReports: failed to batch fetch sections: %w", err)
			}
			defer sectionRows.Close()

			sectionMap := make(map[string]*reports.ReportSection)
			for sectionRows.Next() {
				var (
					dbSectionID        string
					dbReportID         string
					dbTypeInt32        int32
					sectionContentJSON []byte
					sectionCreatedTime time.Time
					sectionUpdatedTime time.Time
				)
				if err := sectionRows.Scan(&dbSectionID, &dbReportID, &dbTypeInt32, &sectionContentJSON, &sectionCreatedTime, &sectionUpdatedTime); err != nil {
					return nil, fmt.Errorf("SearchReports: failed to scan section row: %w", err)
				}
				if report, ok := reportsMap[dbReportID]; ok {
					section := &reports.ReportSection{}
					if err := protojson.Unmarshal(sectionContentJSON, section); err != nil {
						return nil, fmt.Errorf("SearchReports: unmarshal section content: %w", err)
					}
					section.Id = dbSectionID
					section.ReportId = dbReportID
					section.Type = reports.SectionType(dbTypeInt32)
					section.CreatedAt = sectionCreatedTime.Format(time.RFC3339Nano)
					section.UpdatedAt = sectionUpdatedTime.Format(time.RFC3339Nano)
					report.Sections = append(report.Sections, section)
					sectionMap[section.Id] = section
				}
			}
			if err = sectionRows.Err(); err != nil {
				return nil, fmt.Errorf("SearchReports: error iterating section rows: %w", err)
			}

			// Batch fetch comments
			const commentsQuery = `
				SELECT id, report_id, section_id, reply_to_comment_id, author_asset_id, text,
					created_at, updated_at, resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
				FROM report_comments
				WHERE report_id = ANY($1)
				ORDER BY created_at DESC`
			commentRows, err := sessionTx.QueryContext(spanContext, commentsQuery, pq.Array(reportIdentifiers))
			if err != nil {
				return nil, fmt.Errorf("SearchReports: failed to batch fetch comments: %w", err)
			}
			defer commentRows.Close()

			for commentRows.Next() {
				comment := &reports.Comment{}
				var (
					sectionID, replyToCommentID, resolvedByAssetID, displayName sql.NullString
					resolvedAtTime                                              sql.NullTime
					createdAtCommentTime, updatedAtCommentTime                  time.Time
				)
				if err := commentRows.Scan(
					&comment.Id, &comment.ReportId, &sectionID, &replyToCommentID, &comment.AuthorAssetId, &comment.Text,
					&createdAtCommentTime, &updatedAtCommentTime, &comment.Resolved, &resolvedAtTime,
					&resolvedByAssetID, &comment.ResourceType, &displayName,
				); err != nil {
					return nil, fmt.Errorf("SearchReports: failed to scan comment row: %w", err)
				}

				comment.CreatedAt = createdAtCommentTime.Format(time.RFC3339Nano)
				comment.UpdatedAt = updatedAtCommentTime.Format(time.RFC3339Nano)
				if replyToCommentID.Valid {
					comment.ReplyToCommentId = replyToCommentID.String
				}
				if resolvedByAssetID.Valid {
					comment.ResolvedByAssetId = resolvedByAssetID.String
				}
				if resolvedAtTime.Valid {
					comment.ResolvedAt = resolvedAtTime.Time.Format(time.RFC3339Nano)
				}
				if displayName.Valid {
					comment.DisplayName = displayName.String
				}

				if sectionID.Valid {
					comment.SectionId = sectionID.String
					if section, ok := sectionMap[comment.SectionId]; ok {
						section.Comments = append(section.Comments, comment)
					}
				} else {
					if report, ok := reportsMap[comment.ReportId]; ok {
						report.Comments = append(report.Comments, comment)
					}
				}
			}
			if err = commentRows.Err(); err != nil {
				return nil, fmt.Errorf("SearchReports: error iterating comment rows: %w", err)
			}
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 9: PREPARE HIGHLIGHTING DATA
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		var searchTermMapping = make(map[string][]string) // Maps field name -> search terms for highlighting

		// Collect search terms from global query for highlighting
		if request.Query != "" {
			// Add global query term to all searchable text fields
			for field := range allowedSearchFields {
				searchTermMapping[field] = append(searchTermMapping[field], request.Query)
			}
		}

		// Collect search terms from field-specific queries for highlighting
		for _, fieldQuery := range request.FieldQueries {
			if fieldQuery.Query != "" && allowedSearchFields[fieldQuery.Field] {
				searchTermMapping[fieldQuery.Field] = append(searchTermMapping[fieldQuery.Field], fieldQuery.Query)
			}
		}

		// Generate search result highlights with contextual fragments
		highlightResults := generateHighlights(reportsList, searchTermMapping)

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 10: PREPARE PAGINATION TOKEN FOR NEXT PAGE
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// Calculate next page token only if more results exist
		var nextPageToken string
		nextOffset := pageOffset + resultsPerPage
		if len(reportsList) == resultsPerPage && totalResults > int32(nextOffset) { //nolint:gosec // nextOffset is bounded by pagination
			nextPageToken = strconv.Itoa(nextOffset) // Next offset as string
		}

		// ═══════════════════════════════════════════════════════════════════════════════════════════
		// STEP 11: RETURN FORMATTED SEARCH RESPONSE
		// ═══════════════════════════════════════════════════════════════════════════════════════════
		return &reports.SearchReportsResponse{
			Reports:       reportsList,
			NextPageToken: nextPageToken,
			Highlights:    highlightResults,
			TotalResults:  totalResults,
		}, nil
	})
}

// buildPlaceholders creates a comma-separated string of SQL placeholders
// starting at startPos for count placeholders: "$startPos, $(startPos+1), ..."
func buildPlaceholders(startPos, count int) string {
	if count <= 0 {
		return ""
	}

	placeholders := make([]string, count)
	for i := 0; i < count; i++ {
		placeholders[i] = fmt.Sprintf("$%d", startPos+i)
	}
	return strings.Join(placeholders, ", ")
}

// generateHighlights creates highlighted text fragments for search results
// based on the search terms that matched different fields in each report.
func generateHighlights(reportsList []*reports.Report, searchTerms map[string][]string) map[string]*reports.HighlightResult {
	highlights := make(map[string]*reports.HighlightResult)

	// Only generate highlights if there are search terms
	if len(searchTerms) == 0 {
		return highlights
	}

	// For each report in the results
	for _, report := range reportsList {
		// For each field that was searched
		for field, terms := range searchTerms {
			// Skip fields that don't exist in the report or have no terms
			if len(terms) == 0 {
				continue
			}

			// Extract the field value based on field name
			var fieldValue string
			switch field {
			case "title":
				fieldValue = report.Title
			case "id":
				fieldValue = report.Id
			case "narrative":
				// Try to find narrative section
				for _, section := range report.Sections {
					if section.Type == reports.SectionType_SECTION_TYPE_NARRATIVE {
						if narrative := section.GetNarrative(); narrative != nil {
							fieldValue = narrative.RichText
							break
						}
					}
				}
			case "entity_list_title":
				// Try to find entity list sections
				for _, section := range report.Sections {
					if section.Type == reports.SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE ||
						section.Type == reports.SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE ||
						section.Type == reports.SectionType_SECTION_TYPE_ENTITY_LIST_PROPERTIES {
						if entityList := section.GetEntityList(); entityList != nil {
							fieldValue = entityList.Title
							break
						}
					}
				}
			case "reference_display_name":
				// Search in entity references
				for _, section := range report.Sections {
					if section.Type == reports.SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE ||
						section.Type == reports.SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE ||
						section.Type == reports.SectionType_SECTION_TYPE_ENTITY_LIST_PROPERTIES {
						if entityList := section.GetEntityList(); entityList != nil && len(entityList.EntityRefs) > 0 {
							names := make([]string, 0, len(entityList.EntityRefs))
							for _, ref := range entityList.EntityRefs {
								names = append(names, ref.DisplayName)
							}
							fieldValue = strings.Join(names, ", ")
							break
						}
					}
				}
			case "incident_location":
				// Try to find incident details section
				for _, section := range report.Sections {
					if section.Type == reports.SectionType_SECTION_TYPE_INCIDENT_DETAILS {
						if details := section.GetIncidentDetails(); details != nil {
							// Combine multiple location fields into a single searchable value
							locationParts := []string{
								details.IncidentLocationStreetAddress,
								details.IncidentLocationCity,
								details.IncidentLocationState,
								details.IncidentLocationCommonName,
							}
							var nonEmptyParts []string
							for _, part := range locationParts {
								if part != "" {
									nonEmptyParts = append(nonEmptyParts, part)
								}
							}
							fieldValue = strings.Join(nonEmptyParts, ", ")
							break
						}
					}
				}
			case "reporting_person_name":
				// Try to find reporting person in incident details
				for _, section := range report.Sections {
					if section.Type == reports.SectionType_SECTION_TYPE_INCIDENT_DETAILS {
						if details := section.GetIncidentDetails(); details != nil && details.ReportingPerson != nil {
							names := []string{
								details.ReportingPerson.FirstName,
								details.ReportingPerson.MiddleName,
								details.ReportingPerson.LastName,
							}
							fieldValue = strings.Join(names, " ")
							break
						}
					}
				}
			case "responder_display_name":
				// Try to find responders in incident details
				for _, section := range report.Sections {
					if section.Type == reports.SectionType_SECTION_TYPE_INCIDENT_DETAILS {
						if details := section.GetIncidentDetails(); details != nil && len(details.Responders) > 0 {
							names := make([]string, 0, len(details.Responders))
							for _, responder := range details.Responders {
								names = append(names, responder.DisplayName)
							}
							fieldValue = strings.Join(names, ", ")
							break
						}
					}
				}
			default:
				continue // Skip unrecognized fields
			}

			// Skip empty fields
			if fieldValue == "" {
				continue
			}

			// Check if any term matches this field
			for _, term := range terms {
				if strings.Contains(strings.ToLower(fieldValue), strings.ToLower(term)) {
					// Create highlight result if it doesn't exist for this report
					if _, exists := highlights[report.Id]; !exists {
						highlights[report.Id] = &reports.HighlightResult{
							Field:     field,
							Fragments: []string{},
						}
					}

					// Only add a fragment if the field matches the highlight field
					// or if we haven't found any highlights yet
					if highlights[report.Id].Field == field || len(highlights[report.Id].Fragments) == 0 {
						// Generate a fragment for this match
						// Extract a context window around the match
						termLower := strings.ToLower(term)
						fieldValueLower := strings.ToLower(fieldValue)
						termIndex := strings.Index(fieldValueLower, termLower)

						if termIndex >= 0 {
							// Get context around the match
							startPosition := max(0, termIndex-40)
							endPosition := min(len(fieldValue), termIndex+len(term)+40)

							// Add ellipsis if we truncated
							prefix := ""
							if startPosition > 0 {
								prefix = "..."
							}

							suffix := ""
							if endPosition < len(fieldValue) {
								suffix = "..."
							}

							// Extract the fragment with the term
							fragment := prefix + fieldValue[startPosition:endPosition] + suffix

							// Add the fragment if it's not already present
							fragmentExists := false
							for _, existingFragment := range highlights[report.Id].Fragments {
								if existingFragment == fragment {
									fragmentExists = true
									break
								}
							}

							if !fragmentExists {
								// Update field if this is our first fragment
								if len(highlights[report.Id].Fragments) == 0 {
									highlights[report.Id].Field = field
								}

								// Add the fragment to the result
								highlights[report.Id].Fragments = append(
									highlights[report.Id].Fragments, fragment)

								// Limit to 3 fragments per report for readability
								if len(highlights[report.Id].Fragments) >= 3 {
									break
								}
							}
						}
					}

					// Only process the first matches - we don't need excessive highlights
					if len(highlights[report.Id].Fragments) >= 3 {
						break
					}
				}
			}
		}
	}

	return highlights
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// max returns the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// ═══════════════════════════════════════════════════════════════════════════════════════════
// RELATION CRUD OPERATIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════

// CreateRelation creates a new relation between two objects within a report.
func (repository *PostgresReportRepository) CreateRelation(
	context context.Context,
	transaction *sql.Tx,
	request *reports.CreateRelationRequest,
) (*reports.Relation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.CreateRelation")
	defer finishSpan()

	span.SetTag("report.id", request.Relation.ReportId)
	span.SetTag("relation.type", request.Relation.RelationType)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.Relation, error) {
		// Validate required fields
		if request.Relation == nil {
			return nil, fmt.Errorf("CreateRelation: relation cannot be nil")
		}

		relation := request.Relation
		if relation.ReportId == "" {
			return nil, fmt.Errorf("CreateRelation: report_id is required")
		}
		if relation.RelationType == "" {
			return nil, fmt.Errorf("CreateRelation: relation_type is required")
		}
		if relation.ObjectA == nil || relation.ObjectB == nil {
			return nil, fmt.Errorf("CreateRelation: both object_a and object_b are required")
		}

		// Ensure the parent report exists
		var exists bool
		if err := sessionTx.QueryRowContext(spanContext, `SELECT true FROM reports WHERE id = $1`, relation.ReportId).Scan(&exists); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, ErrReportNotFound
			}
			return nil, fmt.Errorf("CreateRelation: verifying report exists: %w", err)
		}

		// Get creator asset ID
		createdByAssetID, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
		if err != nil {
			log.Printf("error: failed to get createdByAssetID from context: %v", err)
			createdByAssetID = SystemAssetID
		}

		// Generate ID if not provided
		if relation.Id == "" {
			relation.Id = uuid.New().String()
		}

		// Prepare timestamps
		now := time.Now()
		nowStr := now.Format(time.RFC3339Nano)
		relation.CreatedAt = nowStr
		relation.UpdatedAt = nowStr
		relation.CreatedByAssetId = createdByAssetID

		// Marshal metadata to JSON
		var objectAMetadataJSON, objectBMetadataJSON, relationMetadataJSON interface{}
		if relation.ObjectA.Metadata != nil {
			objectAMetadataMap := relation.ObjectA.Metadata.AsMap()
			if len(objectAMetadataMap) > 0 {
				jsonBytes, _ := json.Marshal(objectAMetadataMap)
				objectAMetadataJSON = jsonBytes
			} else {
				objectAMetadataJSON = nil
			}
		} else {
			objectAMetadataJSON = nil
		}

		if relation.ObjectB.Metadata != nil {
			objectBMetadataMap := relation.ObjectB.Metadata.AsMap()
			if len(objectBMetadataMap) > 0 {
				jsonBytes, _ := json.Marshal(objectBMetadataMap)
				objectBMetadataJSON = jsonBytes
			} else {
				objectBMetadataJSON = nil
			}
		} else {
			objectBMetadataJSON = nil
		}

		if relation.Metadata != nil {
			relationMetadataMap := relation.Metadata.AsMap()
			if len(relationMetadataMap) > 0 {
				jsonBytes, _ := json.Marshal(relationMetadataMap)
				relationMetadataJSON = jsonBytes
			} else {
				relationMetadataJSON = nil
			}
		} else {
			relationMetadataJSON = nil
		}

		const insertSQL = `
			INSERT INTO report_relations (
				id, report_id, relation_type, description,
				object_a_type, object_a_report_scoped_id, object_a_global_id, object_a_external_id, object_a_section_id, object_a_display_name, object_a_metadata,
				object_b_type, object_b_report_scoped_id, object_b_global_id, object_b_external_id, object_b_section_id, object_b_display_name, object_b_metadata,
				metadata, created_at, updated_at, created_by_asset_id
			) VALUES (
				$1, $2, $3, $4,
				$5, $6, $7, $8, $9, $10, $11,
				$12, $13, $14, $15, $16, $17, $18,
				$19, $20, $21, $22
			)
		`
		if _, err := sessionTx.ExecContext(spanContext, insertSQL,
			relation.Id,
			relation.ReportId,
			relation.RelationType,
			nullIfEmpty(relation.Description),
			nullIfEmpty(relation.ObjectA.ObjectType),
			nullIfEmpty(relation.ObjectA.ReportScopedId),
			nullIfEmpty(relation.ObjectA.GlobalId),
			nullIfEmpty(relation.ObjectA.ExternalId),
			nullIfEmpty(relation.ObjectA.SectionId),
			nullIfEmpty(relation.ObjectA.DisplayName),
			objectAMetadataJSON,
			nullIfEmpty(relation.ObjectB.ObjectType),
			nullIfEmpty(relation.ObjectB.ReportScopedId),
			nullIfEmpty(relation.ObjectB.GlobalId),
			nullIfEmpty(relation.ObjectB.ExternalId),
			nullIfEmpty(relation.ObjectB.SectionId),
			nullIfEmpty(relation.ObjectB.DisplayName),
			objectBMetadataJSON,
			relationMetadataJSON,
			now,
			now,
			relation.CreatedByAssetId,
		); err != nil {
			return nil, fmt.Errorf("CreateRelation: exec insert: %w", err)
		}

		return relation, nil
	})
}

// GetRelation retrieves a relation by its ID.
func (repository *PostgresReportRepository) GetRelation(
	context context.Context,
	transaction *sql.Tx,
	request *reports.GetRelationRequest,
) (*reports.Relation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.GetRelation")
	defer finishSpan()

	span.SetTag("relation.id", request.RelationId)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.Relation, error) {
		const fetchSQL = `
			SELECT
				id, report_id, relation_type, description,
				object_a_type, object_a_report_scoped_id, object_a_global_id, object_a_external_id, object_a_section_id, object_a_display_name, object_a_metadata,
				object_b_type, object_b_report_scoped_id, object_b_global_id, object_b_external_id, object_b_section_id, object_b_display_name, object_b_metadata,
				metadata, created_at, updated_at, created_by_asset_id
			FROM report_relations
			WHERE id = $1
		`

		relation := &reports.Relation{}
		var (
			description      sql.NullString
			objectAMetadata  sql.NullString
			objectBMetadata  sql.NullString
			relationMetadata sql.NullString
			createdAt        time.Time
			updatedAt        time.Time
			createdByAssetID sql.NullString
			// Object A fields
			objectAType        sql.NullString
			objectAScopedID    sql.NullString
			objectAGlobalID    sql.NullString
			objectAExternalID  sql.NullString
			objectASectionID   sql.NullString
			objectADisplayName sql.NullString
			// Object B fields
			objectBType        sql.NullString
			objectBScopedID    sql.NullString
			objectBGlobalID    sql.NullString
			objectBExternalID  sql.NullString
			objectBSectionID   sql.NullString
			objectBDisplayName sql.NullString
		)

		err := sessionTx.QueryRowContext(spanContext, fetchSQL, request.RelationId).Scan(
			&relation.Id, &relation.ReportId, &relation.RelationType, &description,
			&objectAType, &objectAScopedID, &objectAGlobalID, &objectAExternalID, &objectASectionID, &objectADisplayName, &objectAMetadata,
			&objectBType, &objectBScopedID, &objectBGlobalID, &objectBExternalID, &objectBSectionID, &objectBDisplayName, &objectBMetadata,
			&relationMetadata, &createdAt, &updatedAt, &createdByAssetID,
		)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, fmt.Errorf("GetRelation: relation %q not found", request.RelationId)
			}
			return nil, fmt.Errorf("GetRelation: fetch failed: %w", err)
		}

		// Populate relation fields
		if description.Valid {
			relation.Description = description.String
		}
		relation.CreatedAt = createdAt.Format(time.RFC3339Nano)
		relation.UpdatedAt = updatedAt.Format(time.RFC3339Nano)
		if createdByAssetID.Valid {
			relation.CreatedByAssetId = createdByAssetID.String
		}

		// Populate object A
		relation.ObjectA = &reports.ObjectReference{}
		if objectAType.Valid {
			relation.ObjectA.ObjectType = objectAType.String
		}
		if objectAScopedID.Valid {
			relation.ObjectA.ReportScopedId = objectAScopedID.String
		}
		if objectAGlobalID.Valid {
			relation.ObjectA.GlobalId = objectAGlobalID.String
		}
		if objectAExternalID.Valid {
			relation.ObjectA.ExternalId = objectAExternalID.String
		}
		if objectASectionID.Valid {
			relation.ObjectA.SectionId = objectASectionID.String
		}
		if objectADisplayName.Valid {
			relation.ObjectA.DisplayName = objectADisplayName.String
		}

		// Populate object B
		relation.ObjectB = &reports.ObjectReference{}
		if objectBType.Valid {
			relation.ObjectB.ObjectType = objectBType.String
		}
		if objectBScopedID.Valid {
			relation.ObjectB.ReportScopedId = objectBScopedID.String
		}
		if objectBGlobalID.Valid {
			relation.ObjectB.GlobalId = objectBGlobalID.String
		}
		if objectBExternalID.Valid {
			relation.ObjectB.ExternalId = objectBExternalID.String
		}
		if objectBSectionID.Valid {
			relation.ObjectB.SectionId = objectBSectionID.String
		}
		if objectBDisplayName.Valid {
			relation.ObjectB.DisplayName = objectBDisplayName.String
		}

		// Parse JSON metadata
		if objectAMetadata.Valid && objectAMetadata.String != "" {
			var metadataMap map[string]interface{}
			if err := json.Unmarshal([]byte(objectAMetadata.String), &metadataMap); err == nil {
				if structData, err := structpb.NewStruct(metadataMap); err == nil {
					relation.ObjectA.Metadata = structData
				}
			}
		}

		if objectBMetadata.Valid && objectBMetadata.String != "" {
			var metadataMap map[string]interface{}
			if err := json.Unmarshal([]byte(objectBMetadata.String), &metadataMap); err == nil {
				if structData, err := structpb.NewStruct(metadataMap); err == nil {
					relation.ObjectB.Metadata = structData
				}
			}
		}

		if relationMetadata.Valid && relationMetadata.String != "" {
			var metadataMap map[string]interface{}
			if err := json.Unmarshal([]byte(relationMetadata.String), &metadataMap); err == nil {
				if structData, err := structpb.NewStruct(metadataMap); err == nil {
					relation.Metadata = structData
				}
			}
		}

		return relation, nil
	})
}

// UpdateRelation updates an existing relation.
func (repository *PostgresReportRepository) UpdateRelation(
	context context.Context,
	transaction *sql.Tx,
	request *reports.UpdateRelationRequest,
) (*reports.Relation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.UpdateRelation")
	defer finishSpan()

	span.SetTag("relation.id", request.Relation.Id)
	span.SetTag("relation.type", request.Relation.RelationType)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.Relation, error) {
		if request.Relation == nil || request.Relation.Id == "" {
			return nil, fmt.Errorf("UpdateRelation: invalid relation")
		}

		relation := request.Relation
		now := time.Now()
		relation.UpdatedAt = now.Format(time.RFC3339Nano)

		// Marshal metadata to JSON
		var objectAMetadataJSON, objectBMetadataJSON, relationMetadataJSON interface{}
		if relation.ObjectA != nil && relation.ObjectA.Metadata != nil {
			objectAMetadataMap := relation.ObjectA.Metadata.AsMap()
			if len(objectAMetadataMap) > 0 {
				jsonBytes, _ := json.Marshal(objectAMetadataMap)
				objectAMetadataJSON = jsonBytes
			} else {
				objectAMetadataJSON = nil
			}
		} else {
			objectAMetadataJSON = nil
		}

		if relation.ObjectB != nil && relation.ObjectB.Metadata != nil {
			objectBMetadataMap := relation.ObjectB.Metadata.AsMap()
			if len(objectBMetadataMap) > 0 {
				jsonBytes, _ := json.Marshal(objectBMetadataMap)
				objectBMetadataJSON = jsonBytes
			} else {
				objectBMetadataJSON = nil
			}
		} else {
			objectBMetadataJSON = nil
		}

		if relation.Metadata != nil {
			relationMetadataMap := relation.Metadata.AsMap()
			if len(relationMetadataMap) > 0 {
				jsonBytes, _ := json.Marshal(relationMetadataMap)
				relationMetadataJSON = jsonBytes
			} else {
				relationMetadataJSON = nil
			}
		} else {
			relationMetadataJSON = nil
		}

		const updateSQL = `
			UPDATE report_relations SET
				relation_type = $1,
				description = $2,
				object_a_type = $3,
				object_a_report_scoped_id = $4,
				object_a_global_id = $5,
				object_a_external_id = $6,
				object_a_section_id = $7,
				object_a_display_name = $8,
				object_a_metadata = $9,
				object_b_type = $10,
				object_b_report_scoped_id = $11,
				object_b_global_id = $12,
				object_b_external_id = $13,
				object_b_section_id = $14,
				object_b_display_name = $15,
				object_b_metadata = $16,
				metadata = $17,
				updated_at = $18
			WHERE id = $19
		`

		var objectAType, objectAScopedID, objectAGlobalID, objectAExternalID, objectASectionID, objectADisplayName string
		var objectBType, objectBScopedID, objectBGlobalID, objectBExternalID, objectBSectionID, objectBDisplayName string

		if relation.ObjectA != nil {
			objectAType = relation.ObjectA.ObjectType
			objectAScopedID = relation.ObjectA.ReportScopedId
			objectAGlobalID = relation.ObjectA.GlobalId
			objectAExternalID = relation.ObjectA.ExternalId
			objectASectionID = relation.ObjectA.SectionId
			objectADisplayName = relation.ObjectA.DisplayName
		}

		if relation.ObjectB != nil {
			objectBType = relation.ObjectB.ObjectType
			objectBScopedID = relation.ObjectB.ReportScopedId
			objectBGlobalID = relation.ObjectB.GlobalId
			objectBExternalID = relation.ObjectB.ExternalId
			objectBSectionID = relation.ObjectB.SectionId
			objectBDisplayName = relation.ObjectB.DisplayName
		}

		result, err := sessionTx.ExecContext(spanContext, updateSQL,
			relation.RelationType,
			nullIfEmpty(relation.Description),
			nullIfEmpty(objectAType),
			nullIfEmpty(objectAScopedID),
			nullIfEmpty(objectAGlobalID),
			nullIfEmpty(objectAExternalID),
			nullIfEmpty(objectASectionID),
			nullIfEmpty(objectADisplayName),
			objectAMetadataJSON,
			nullIfEmpty(objectBType),
			nullIfEmpty(objectBScopedID),
			nullIfEmpty(objectBGlobalID),
			nullIfEmpty(objectBExternalID),
			nullIfEmpty(objectBSectionID),
			nullIfEmpty(objectBDisplayName),
			objectBMetadataJSON,
			relationMetadataJSON,
			now,
			relation.Id,
		)
		if err != nil {
			return nil, fmt.Errorf("UpdateRelation: update failed: %w", err)
		}

		rowsAffected, _ := result.RowsAffected()
		if rowsAffected == 0 {
			return nil, fmt.Errorf("UpdateRelation: relation %q not found", relation.Id)
		}

		// Return the updated relation
		return repository.GetRelation(context, sessionTx, &reports.GetRelationRequest{RelationId: relation.Id})
	})
}

// DeleteRelation permanently deletes a relation.
func (repository *PostgresReportRepository) DeleteRelation(
	context context.Context,
	transaction *sql.Tx,
	request *reports.DeleteRelationRequest,
) error {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.DeleteRelation")
	defer finishSpan()

	span.SetTag("relation.id", request.RelationId)

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		const deleteSQL = `DELETE FROM report_relations WHERE id = $1`
		result, err := sessionTx.ExecContext(spanContext, deleteSQL, request.RelationId)
		if err != nil {
			return fmt.Errorf("DeleteRelation: delete failed: %w", err)
		}
		rowsAffected, _ := result.RowsAffected()
		if rowsAffected == 0 {
			return fmt.Errorf("DeleteRelation: relation %q not found", request.RelationId)
		}
		return nil
	})
}

// ListRelations retrieves relations for a report with pagination and filtering.
func (repository *PostgresReportRepository) ListRelations(
	context context.Context,
	transaction *sql.Tx,
	request *reports.ListRelationsRequest,
) (*reports.ListRelationsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "ReportRepository.ListRelations")
	defer finishSpan()

	span.SetTag("report.id", request.ReportId)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))
	span.SetTag("pagination.page_token", request.PageToken)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*reports.ListRelationsResponse, error) {
		// Parse pagination
		if request.PageSize <= 0 {
			request.PageSize = 50 // Default page size
		}
		queryOffset := 0
		if request.PageToken != "" {
			if parsedOffset, err := strconv.Atoi(request.PageToken); err == nil && parsedOffset >= 0 {
				queryOffset = parsedOffset
			}
		}

		// Build WHERE clauses
		var (
			whereClauses []string
			queryArgs    []interface{}
			argIdx       = 1
		)

		// Filter by report ID (required)
		if request.ReportId != "" {
			whereClauses = append(whereClauses, fmt.Sprintf("report_id = $%d", argIdx))
			queryArgs = append(queryArgs, request.ReportId)
			argIdx++
		}

		// Filter by relation types
		if request.RelationType != "" {
			whereClauses = append(whereClauses, fmt.Sprintf("relation_type = $%d", argIdx))
			queryArgs = append(queryArgs, request.RelationType)
			argIdx++
		}

		// Filter by involves object type
		if request.InvolvesObjectType != "" {
			whereClauses = append(whereClauses, fmt.Sprintf("(object_a_type = $%d OR object_b_type = $%d)", argIdx, argIdx+1))
			queryArgs = append(queryArgs, request.InvolvesObjectType, request.InvolvesObjectType)
			argIdx += 2
		}

		// Filter by involves object ID
		if request.InvolvesObjectId != "" {
			whereClauses = append(whereClauses, fmt.Sprintf("(object_a_report_scoped_id = $%d OR object_b_report_scoped_id = $%d OR object_a_global_id = $%d OR object_b_global_id = $%d OR object_a_external_id = $%d OR object_b_external_id = $%d)", argIdx, argIdx+1, argIdx+2, argIdx+3, argIdx+4, argIdx+5))
			queryArgs = append(queryArgs, request.InvolvesObjectId, request.InvolvesObjectId, request.InvolvesObjectId, request.InvolvesObjectId, request.InvolvesObjectId, request.InvolvesObjectId)
			argIdx += 6
		}

		// Build WHERE clause
		whereClause := ""
		if len(whereClauses) > 0 {
			whereClause = " WHERE " + strings.Join(whereClauses, " AND ")
		}

		// Execute query - Build safely without string formatting in main query
		baseQuery := `
			SELECT
				id, report_id, relation_type, description,
				object_a_type, object_a_report_scoped_id, object_a_global_id, object_a_external_id, object_a_section_id, object_a_display_name, object_a_metadata,
				object_b_type, object_b_report_scoped_id, object_b_global_id, object_b_external_id, object_b_section_id, object_b_display_name, object_b_metadata,
				metadata, created_at, updated_at, created_by_asset_id
			FROM report_relations`

		orderAndLimit := fmt.Sprintf(" ORDER BY created_at DESC, id ASC LIMIT $%d OFFSET $%d", argIdx, argIdx+1)
		query := baseQuery + whereClause + orderAndLimit

		queryArgs = append(queryArgs, request.PageSize, queryOffset)

		rows, err := sessionTx.QueryContext(spanContext, query, queryArgs...)
		if err != nil {
			return nil, fmt.Errorf("ListRelations: query failed: %w", err)
		}
		defer rows.Close()

		var relations []*reports.Relation
		for rows.Next() {
			relation := &reports.Relation{}
			var (
				description      sql.NullString
				objectAMetadata  sql.NullString
				objectBMetadata  sql.NullString
				relationMetadata sql.NullString
				createdAt        time.Time
				updatedAt        time.Time
				createdByAssetID sql.NullString
				// Object A fields
				objectAType        sql.NullString
				objectAScopedID    sql.NullString
				objectAGlobalID    sql.NullString
				objectAExternalID  sql.NullString
				objectASectionID   sql.NullString
				objectADisplayName sql.NullString
				// Object B fields
				objectBType        sql.NullString
				objectBScopedID    sql.NullString
				objectBGlobalID    sql.NullString
				objectBExternalID  sql.NullString
				objectBSectionID   sql.NullString
				objectBDisplayName sql.NullString
			)

			err := rows.Scan(
				&relation.Id, &relation.ReportId, &relation.RelationType, &description,
				&objectAType, &objectAScopedID, &objectAGlobalID, &objectAExternalID, &objectASectionID, &objectADisplayName, &objectAMetadata,
				&objectBType, &objectBScopedID, &objectBGlobalID, &objectBExternalID, &objectBSectionID, &objectBDisplayName, &objectBMetadata,
				&relationMetadata, &createdAt, &updatedAt, &createdByAssetID,
			)
			if err != nil {
				return nil, fmt.Errorf("ListRelations: scan relation: %w", err)
			}

			relation.CreatedAt = createdAt.Format(time.RFC3339Nano)
			relation.UpdatedAt = updatedAt.Format(time.RFC3339Nano)
			if createdByAssetID.Valid {
				relation.CreatedByAssetId = createdByAssetID.String
			}

			relation.ObjectA = &reports.ObjectReference{
				ObjectType:     objectAType.String,
				ReportScopedId: objectAScopedID.String,
				GlobalId:       objectAGlobalID.String,
				ExternalId:     objectAExternalID.String,
				SectionId:      objectASectionID.String,
				DisplayName:    objectADisplayName.String,
			}

			relation.ObjectB = &reports.ObjectReference{
				ObjectType:     objectBType.String,
				ReportScopedId: objectBScopedID.String,
				GlobalId:       objectBGlobalID.String,
				ExternalId:     objectBExternalID.String,
				SectionId:      objectBSectionID.String,
				DisplayName:    objectBDisplayName.String,
			}

			if objectAMetadata.Valid && objectAMetadata.String != "" {
				var metadataMap map[string]interface{}
				if err := json.Unmarshal([]byte(objectAMetadata.String), &metadataMap); err == nil {
					if structData, err := structpb.NewStruct(metadataMap); err == nil {
						relation.ObjectA.Metadata = structData
					}
				}
			}

			if objectBMetadata.Valid && objectBMetadata.String != "" {
				var metadataMap map[string]interface{}
				if err := json.Unmarshal([]byte(objectBMetadata.String), &metadataMap); err == nil {
					if structData, err := structpb.NewStruct(metadataMap); err == nil {
						relation.ObjectB.Metadata = structData
					}
				}
			}

			if relationMetadata.Valid && relationMetadata.String != "" {
				var metadataMap map[string]interface{}
				if err := json.Unmarshal([]byte(relationMetadata.String), &metadataMap); err == nil {
					if structData, err := structpb.NewStruct(metadataMap); err == nil {
						relation.Metadata = structData
					}
				}
			}

			relations = append(relations, relation)
		}

		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("ListRelations: rows iteration error: %w", err)
		}

		return &reports.ListRelationsResponse{
			Relations:     relations,
			NextPageToken: strconv.Itoa(queryOffset + int(request.PageSize)),
		}, nil
	})
}

// batchLoadReportRelationships loads all relationships for multiple reports in batches to avoid N+1 queries
// This replaces individual GetReport calls in ListReports and BatchGetReports for better performance.
// Instead of N*4 queries (N reports * 4 relationships each), this executes only 4 total queries.
func (repository *PostgresReportRepository) batchLoadReportRelationships(ctx context.Context, tx *sql.Tx, reportIds []string) (
	map[string][]*reports.ReportSection,
	map[string][]*reports.Comment,
	map[string][]*reports.ReviewRound,
	map[string][]*reports.Relation,
	error,
) {
	if len(reportIds) == 0 {
		return make(map[string][]*reports.ReportSection),
			make(map[string][]*reports.Comment),
			make(map[string][]*reports.ReviewRound),
			make(map[string][]*reports.Relation),
			nil
	}

	// Initialize result maps with empty slices for all report IDs
	sectionsMap := make(map[string][]*reports.ReportSection)
	commentsMap := make(map[string][]*reports.Comment)
	reviewRoundsMap := make(map[string][]*reports.ReviewRound)
	relationsMap := make(map[string][]*reports.Relation)

	for _, reportId := range reportIds {
		sectionsMap[reportId] = []*reports.ReportSection{}
		commentsMap[reportId] = []*reports.Comment{}
		reviewRoundsMap[reportId] = []*reports.ReviewRound{}
		relationsMap[reportId] = []*reports.Relation{}
	}

	// Batch load sections - 1 query instead of N queries
	sectionRows, err := tx.QueryContext(ctx, `
		SELECT id, report_id, type, content, created_at, updated_at
		FROM report_sections
		WHERE report_id = ANY($1)
		ORDER BY report_id, id ASC
	`, pq.Array(reportIds))
	if err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return nil, nil, nil, nil, fmt.Errorf("failed to batch load sections: %w", err)
	}
	defer sectionRows.Close()

	// Map to store sections by section ID for comment loading
	sectionIdToReportId := make(map[string]string)
	var allSectionIds []string

	for sectionRows.Next() {
		var (
			dbSectionID        string
			dbReportID         string
			dbTypeInt32        int32
			sectionContentJSON []byte
			sectionCreatedTime time.Time
			sectionUpdatedTime time.Time
		)
		if err := sectionRows.Scan(&dbSectionID, &dbReportID, &dbTypeInt32, &sectionContentJSON, &sectionCreatedTime, &sectionUpdatedTime); err != nil {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return nil, nil, nil, nil, fmt.Errorf("failed to scan section row: %w", err)
		}

		section := &reports.ReportSection{}
		if err := protojson.Unmarshal(sectionContentJSON, section); err != nil {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal)
			return nil, nil, nil, nil, fmt.Errorf("failed to unmarshal section content: %w", err)
		}

		section.Id = dbSectionID
		section.ReportId = dbReportID
		section.Type = reports.SectionType(dbTypeInt32)
		section.CreatedAt = sectionCreatedTime.Format(time.RFC3339Nano)
		section.UpdatedAt = sectionUpdatedTime.Format(time.RFC3339Nano)
		section.Comments = []*reports.Comment{} // Initialize empty comments slice

		sectionsMap[dbReportID] = append(sectionsMap[dbReportID], section)
		sectionIdToReportId[dbSectionID] = dbReportID
		allSectionIds = append(allSectionIds, dbSectionID)
	}

	// Batch load all comments (both section-scoped and global) - 1 query instead of N queries
	commentRows, err := tx.QueryContext(ctx, `
		SELECT
		  id, report_id, section_id, reply_to_comment_id,
		  author_asset_id, text,
		  created_at, updated_at,
		  resolved, resolved_at, resolved_by_asset_id, resource_type, display_name
		FROM report_comments
		WHERE report_id = ANY($1)
		ORDER BY report_id, created_at DESC
	`, pq.Array(reportIds))
	if err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return nil, nil, nil, nil, fmt.Errorf("failed to batch load comments: %w", err)
	}
	defer commentRows.Close()

	// Map to organize section comments by section ID
	sectionCommentsMap := make(map[string][]*reports.Comment)

	for commentRows.Next() {
		comment := &reports.Comment{}
		var (
			sectionID            sql.NullString
			replyToCommentID     sql.NullString
			resolvedAtTime       sql.NullTime
			resolvedByAssetID    sql.NullString
			displayName          sql.NullString
			createdAtCommentTime time.Time
			updatedAtCommentTime time.Time
		)

		if err := commentRows.Scan(
			&comment.Id, &comment.ReportId, &sectionID,
			&replyToCommentID, &comment.AuthorAssetId, &comment.Text,
			&createdAtCommentTime, &updatedAtCommentTime,
			&comment.Resolved, &resolvedAtTime,
			&resolvedByAssetID, &comment.ResourceType, &displayName,
		); err != nil {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return nil, nil, nil, nil, fmt.Errorf("failed to scan comment row: %w", err)
		}

		comment.CreatedAt = createdAtCommentTime.Format(time.RFC3339Nano)
		comment.UpdatedAt = updatedAtCommentTime.Format(time.RFC3339Nano)
		if replyToCommentID.Valid {
			comment.ReplyToCommentId = replyToCommentID.String
		}
		if resolvedByAssetID.Valid {
			comment.ResolvedByAssetId = resolvedByAssetID.String
		}
		if resolvedAtTime.Valid {
			comment.ResolvedAt = resolvedAtTime.Time.Format(time.RFC3339Nano)
		}
		if displayName.Valid {
			comment.DisplayName = displayName.String
		}

		if sectionID.Valid {
			// Section-scoped comment
			comment.SectionId = sectionID.String
			sectionCommentsMap[sectionID.String] = append(sectionCommentsMap[sectionID.String], comment)
		} else {
			// Global comment
			commentsMap[comment.ReportId] = append(commentsMap[comment.ReportId], comment)
		}
	}

	// Assign section comments to their respective sections
	for _, reportId := range reportIds {
		for _, section := range sectionsMap[reportId] {
			if comments, exists := sectionCommentsMap[section.Id]; exists {
				section.Comments = comments
			}
		}
	}

	// Batch load review rounds - 1 query instead of N queries
	reviewRows, err := tx.QueryContext(ctx, `
		SELECT
		  id, report_id, reviewer_asset_id, level, status,
		  sent_to_level, sent_to_asset_id,
		  requested_at, resolved_at,
		  round_note, snapshot_version,
		  due_at, create_by_asset_id, note_for_reviewer
		FROM report_review_rounds
		WHERE report_id = ANY($1)
		ORDER BY report_id, requested_at DESC
	`, pq.Array(reportIds))
	if err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return nil, nil, nil, nil, fmt.Errorf("failed to batch load review rounds: %w", err)
	}
	defer reviewRows.Close()

	for reviewRows.Next() {
		reviewRound := &reports.ReviewRound{}
		var (
			reportId          string
			reviewStatusInt32 int32
			resolvedAtTime    sql.NullTime
			dueAtTime         sql.NullTime
			noteForReviewer   sql.NullString
			reviewerAssetID   sql.NullString
			sentToAssetID     sql.NullString
			roundNote         sql.NullString
			createByAssetID   sql.NullString
		)

		if err := reviewRows.Scan(
			&reviewRound.Id, &reportId, &reviewerAssetID, &reviewRound.Level,
			&reviewStatusInt32, &reviewRound.SentToLevel, &sentToAssetID,
			&reviewRound.RequestedAt, &resolvedAtTime, &roundNote,
			&reviewRound.SnapshotVersion, &dueAtTime,
			&createByAssetID, &noteForReviewer,
		); err != nil {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return nil, nil, nil, nil, fmt.Errorf("failed to scan review round row: %w", err)
		}

		reviewRound.Status = reports.ReviewStatus(reviewStatusInt32)
		if reviewerAssetID.Valid {
			reviewRound.ReviewerAssetId = reviewerAssetID.String
		}
		if sentToAssetID.Valid {
			reviewRound.SentToAssetId = sentToAssetID.String
		}
		if resolvedAtTime.Valid {
			reviewRound.ResolvedAt = resolvedAtTime.Time.Format(time.RFC3339Nano)
		}
		if roundNote.Valid {
			reviewRound.RoundNote = roundNote.String
		}
		if dueAtTime.Valid {
			reviewRound.DueAt = dueAtTime.Time.Format(time.RFC3339Nano)
		}
		if createByAssetID.Valid {
			reviewRound.CreateByAssetId = createByAssetID.String
		}
		if noteForReviewer.Valid {
			reviewRound.NoteForReviewer = noteForReviewer.String
		}

		reviewRoundsMap[reportId] = append(reviewRoundsMap[reportId], reviewRound)
	}

	// Batch load relations - 1 query instead of N queries
	relationsRows, err := tx.QueryContext(ctx, `
		SELECT
		  id, report_id, relation_type, description,
		  object_a_type, object_a_report_scoped_id, object_a_global_id, object_a_external_id, object_a_section_id, object_a_display_name, object_a_metadata,
		  object_b_type, object_b_report_scoped_id, object_b_global_id, object_b_external_id, object_b_section_id, object_b_display_name, object_b_metadata,
		  metadata, created_at, updated_at, created_by_asset_id
		FROM report_relations
		WHERE report_id = ANY($1)
		ORDER BY report_id, created_at DESC
	`, pq.Array(reportIds))
	if err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return nil, nil, nil, nil, fmt.Errorf("failed to batch load relations: %w", err)
	}
	defer relationsRows.Close()

	for relationsRows.Next() {
		relation := &reports.Relation{}
		var (
			reportId         string
			description      sql.NullString
			objectAMetadata  sql.NullString
			objectBMetadata  sql.NullString
			relationMetadata sql.NullString
			createdAt        time.Time
			updatedAt        time.Time
			createdByAssetID sql.NullString
			// Object A fields
			objectAType        sql.NullString
			objectAScopedID    sql.NullString
			objectAGlobalID    sql.NullString
			objectAExternalID  sql.NullString
			objectASectionID   sql.NullString
			objectADisplayName sql.NullString
			// Object B fields
			objectBType        sql.NullString
			objectBScopedID    sql.NullString
			objectBGlobalID    sql.NullString
			objectBExternalID  sql.NullString
			objectBSectionID   sql.NullString
			objectBDisplayName sql.NullString
		)

		if err := relationsRows.Scan(
			&relation.Id, &reportId, &relation.RelationType, &description,
			&objectAType, &objectAScopedID, &objectAGlobalID, &objectAExternalID, &objectASectionID, &objectADisplayName, &objectAMetadata,
			&objectBType, &objectBScopedID, &objectBGlobalID, &objectBExternalID, &objectBSectionID, &objectBDisplayName, &objectBMetadata,
			&relationMetadata, &createdAt, &updatedAt, &createdByAssetID,
		); err != nil {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return nil, nil, nil, nil, fmt.Errorf("failed to scan relation row: %w", err)
		}

		// Populate relation fields
		if description.Valid {
			relation.Description = description.String
		}
		relation.CreatedAt = createdAt.Format(time.RFC3339Nano)
		relation.UpdatedAt = updatedAt.Format(time.RFC3339Nano)
		if createdByAssetID.Valid {
			relation.CreatedByAssetId = createdByAssetID.String
		}

		// Populate object A
		relation.ObjectA = &reports.ObjectReference{}
		if objectAType.Valid {
			relation.ObjectA.ObjectType = objectAType.String
		}
		if objectAScopedID.Valid {
			relation.ObjectA.ReportScopedId = objectAScopedID.String
		}
		if objectAGlobalID.Valid {
			relation.ObjectA.GlobalId = objectAGlobalID.String
		}
		if objectAExternalID.Valid {
			relation.ObjectA.ExternalId = objectAExternalID.String
		}
		if objectASectionID.Valid {
			relation.ObjectA.SectionId = objectASectionID.String
		}
		if objectADisplayName.Valid {
			relation.ObjectA.DisplayName = objectADisplayName.String
		}

		// Populate object B
		relation.ObjectB = &reports.ObjectReference{}
		if objectBType.Valid {
			relation.ObjectB.ObjectType = objectBType.String
		}
		if objectBScopedID.Valid {
			relation.ObjectB.ReportScopedId = objectBScopedID.String
		}
		if objectBGlobalID.Valid {
			relation.ObjectB.GlobalId = objectBGlobalID.String
		}
		if objectBExternalID.Valid {
			relation.ObjectB.ExternalId = objectBExternalID.String
		}
		if objectBSectionID.Valid {
			relation.ObjectB.SectionId = objectBSectionID.String
		}
		if objectBDisplayName.Valid {
			relation.ObjectB.DisplayName = objectBDisplayName.String
		}

		// Parse JSON metadata if present
		if objectAMetadata.Valid && objectAMetadata.String != "" {
			var metadataMap map[string]interface{}
			if err := json.Unmarshal([]byte(objectAMetadata.String), &metadataMap); err == nil {
				if structData, err := structpb.NewStruct(metadataMap); err == nil {
					relation.ObjectA.Metadata = structData
				}
			}
		}

		if objectBMetadata.Valid && objectBMetadata.String != "" {
			var metadataMap map[string]interface{}
			if err := json.Unmarshal([]byte(objectBMetadata.String), &metadataMap); err == nil {
				if structData, err := structpb.NewStruct(metadataMap); err == nil {
					relation.ObjectB.Metadata = structData
				}
			}
		}

		if relationMetadata.Valid && relationMetadata.String != "" {
			var metadataMap map[string]interface{}
			if err := json.Unmarshal([]byte(relationMetadata.String), &metadataMap); err == nil {
				if structData, err := structpb.NewStruct(metadataMap); err == nil {
					relation.Metadata = structData
				}
			}
		}

		relationsMap[reportId] = append(relationsMap[reportId], relation)
	}

	return sectionsMap, commentsMap, reviewRoundsMap, relationsMap, nil
}

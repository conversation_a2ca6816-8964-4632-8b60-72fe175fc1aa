// reports_execute_side_effects.go
package usecase

import (
	"context"
	"database/sql"
	"fmt"
	"math"

	"common/herosentry"
	orderpb "proto/hero/orders/v2"
	reports "proto/hero/reports/v2"

	"crypto/rand"
	"math/big"
	assetpb "proto/hero/assets/v2"
	assetRepository "workflow/internal/assets/data"
	orderRepository "workflow/internal/orders/data"
	reportRepository "workflow/internal/reports/data"
)

// ReportSideEffectExecutor orchestrates order side-effects
// for report lifecycle transitions.
type ReportSideEffectExecutor struct {
}

// NewReportSideEffectExecutor creates an executor instance.
func NewReportSideEffectExecutor() *ReportSideEffectExecutor {
	return &ReportSideEffectExecutor{}
}

// ExecuteSideEffect dispatches the side-effect based on the type.
// Must run within a transaction for atomicity.
func (exec *ReportSideEffectExecutor) ExecuteSideEffect(
	ctx context.Context,
	tx *sql.Tx,
	effect ReportSideEffectType,
	updatedReport *reports.Report,
	updatedRound *reports.ReviewRound,
	reportUseCase *ReportUseCase,
	note string,
	preferredReviewerAssetID string,
) error {
	// Add side effect details to current span
	if span := herosentry.CurrentSpan(ctx); span != nil {
		span.SetTag("side_effect.type", fmt.Sprintf("%v", effect))
		if updatedReport != nil {
			span.SetTag("side_effect.report_id", updatedReport.Id)
			span.SetData("side_effect.report_status", updatedReport.Status.String())
		}
		if updatedRound != nil {
			span.SetTag("side_effect.review_round_id", updatedRound.Id)
			span.SetData("side_effect.review_status", updatedRound.Status.String())
		}
	}
	switch effect {
	case ReportSideEffect_None:
		return nil
	case ReportSideEffect_CreateWriteReportOrder:
		return exec.createWriteReportOrder(ctx, tx, updatedReport, reportUseCase)
	case ReportSideEffect_CreateReviewReportOrder:
		return exec.createReviewReportOrder(ctx, tx, updatedReport, reportUseCase, note, preferredReviewerAssetID)
	case ReportSideEffect_CreateReviseReportOrder:
		return exec.createReviseReportOrder(ctx, tx, updatedRound, reportUseCase)
	case ReportSideEffect_CreateNextLevelReviewReportOrder:
		return exec.createNextLevelReviewReportOrder(ctx, tx, updatedRound, reportUseCase)
	case ReportSideEffect_CompleteOrdersOnReportApproval:
		return exec.completeOrdersOnReportApproval(ctx, tx, updatedReport, reportUseCase)
	case ReportSideEffect_CancelOrdersOnReportCancel:
		return exec.cancelOrdersOnReportCancel(ctx, tx, updatedReport, reportUseCase)
	default:
		return nil
	}
}

// terminalOrder returns true if the order status is final.
func terminalOrder(status orderpb.OrderStatus) bool {
	switch status {
	case orderpb.OrderStatus_ORDER_STATUS_COMPLETED,
		orderpb.OrderStatus_ORDER_STATUS_CANCELLED,
		orderpb.OrderStatus_ORDER_STATUS_REJECTED:
		return true
	default:
		return false
	}
}

// listAllOrdersForReport handles pagination for ListOrdersForReport.
func listAllOrdersForReport(
	ctx context.Context,
	repo orderRepository.OrderRepository,
	tx *sql.Tx,
	reportID string,
	status orderpb.OrderStatus,
) ([]*orderpb.Order, error) {
	var allOrders []*orderpb.Order
	pageToken := ""
	for {
		resp, err := repo.ListOrdersForReport(ctx, tx, reportID, defaultPageSize, pageToken, status)
		if err != nil {
			return nil, err
		}
		allOrders = append(allOrders, resp.Orders...)
		if resp.PageToken == "" {
			break
		}
		pageToken = resp.PageToken
	}
	return allOrders, nil
}

// listAllOrdersForReviewRound handles pagination for ListOrdersForReviewRound.
func listAllOrdersForReviewRound(
	ctx context.Context,
	repo orderRepository.OrderRepository,
	tx *sql.Tx,
	roundID string,
	status orderpb.OrderStatus,
) ([]*orderpb.Order, error) {
	var allOrders []*orderpb.Order
	pageToken := ""
	for {
		resp, err := repo.ListOrdersForReviewRound(ctx, tx, roundID, defaultPageSize, pageToken, status)
		if err != nil {
			return nil, err
		}
		allOrders = append(allOrders, resp.Orders...)
		if resp.PageToken == "" {
			break
		}
		pageToken = resp.PageToken
	}
	return allOrders, nil
}

// listAllReviewRoundsForReport handles pagination for ListReviewRoundsForReport.
func listAllReviewRoundsForReport(
	ctx context.Context,
	repo reportRepository.ReportRepository,
	tx *sql.Tx,
	reportID string,
) ([]*reports.ReviewRound, error) {
	var allRounds []*reports.ReviewRound
	pageToken := ""
	for {
		resp, err := repo.ListReviewRoundsForReport(ctx, tx, reportID, defaultPageSize, pageToken)
		if err != nil {
			if err == sql.ErrNoRows {
				// Return empty list if no rows found
				return allRounds, nil
			}
			return nil, err
		}
		allRounds = append(allRounds, resp.ReviewRounds...)
		if resp.NextPageToken == "" {
			break
		}
		pageToken = resp.NextPageToken
	}
	return allRounds, nil
}

// listAllAssetsOfType handles pagination for ListAssets with a specific type filter.
func listAllAssetsOfType(
	ctx context.Context,
	repo assetRepository.AssetRepository,
	tx *sql.Tx,
	assetType assetpb.AssetType,
	excludeAuthorID string,
) ([]*assetpb.Asset, error) {
	var allAssets []*assetpb.Asset
	pageToken := ""
	for {
		resp, err := repo.ListAssets(
			ctx,
			tx,
			defaultPageSize,
			pageToken,
			assetType,
			assetpb.AssetStatus_ASSET_STATUS_UNSPECIFIED,
			"",
		)
		if err != nil {
			return nil, fmt.Errorf("failed to list assets of type %s: %w", assetType, err)
		}

		for _, asset := range resp.Assets {
			// Skip the author and deactivated assets
			if asset.Id != excludeAuthorID && asset.Status != assetpb.AssetStatus_ASSET_STATUS_DEACTIVATED {
				allAssets = append(allAssets, asset)
			}
		}

		if resp.NextPageToken == "" {
			break
		}
		pageToken = resp.NextPageToken
	}
	return allAssets, nil
}

// createWriteReportOrder enqueues the first WRITE_REPORT order.
func (exec *ReportSideEffectExecutor) createWriteReportOrder(
	ctx context.Context,
	tx *sql.Tx,
	updatedReport *reports.Report,
	reportUseCase *ReportUseCase,
) error {
	// Create span for this operation
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportSideEffectExecutor.createWriteReportOrder")
	defer finishSpan()

	span.SetTag("report.id", updatedReport.Id)
	span.SetTag("report.author_asset_id", updatedReport.AuthorAssetId)
	span.SetTag("report.title", updatedReport.Title)

	// Log the author asset ID for debugging purposes
	fmt.Printf("Creating WRITE_REPORT order for report %s with author asset ID: %s\n",
		updatedReport.Id, updatedReport.AuthorAssetId)
	if updatedReport.AuthorAssetId == "" {
		// Log the case but continue execution
		fmt.Printf("Warning: report %s has no author asset id\n", updatedReport.Id)
		return nil
	}
	order := &orderpb.Order{
		AssetId:      updatedReport.AuthorAssetId,
		ReportId:     updatedReport.Id,
		Type:         orderpb.OrderType_ORDER_TYPE_WRITE_REPORT,
		Status:       orderpb.OrderStatus_ORDER_STATUS_CREATED,
		Instructions: fmt.Sprintf("Write report '%s'", updatedReport.Title),
	}
	_, err := reportUseCase.orderRepository.CreateOrder(spanContext, tx, order)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create write report order for report %s", updatedReport.Id))
	}
	return err
}

// createReviewReportOrder transitions to level-1 review or the next appropriate level.
func (exec *ReportSideEffectExecutor) createReviewReportOrder(
	ctx context.Context,
	tx *sql.Tx,
	updatedReport *reports.Report,
	reportUseCase *ReportUseCase,
	noteFromAuthor string,
	preferredReviewerAssetID string,
) error {
	// Create span for this operation
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportSideEffectExecutor.createReviewReportOrder")
	defer finishSpan()

	span.SetTag("report.id", updatedReport.Id)
	span.SetTag("report.org_id", fmt.Sprintf("%d", updatedReport.OrgId))
	span.SetTag("report.title", updatedReport.Title)
	if preferredReviewerAssetID != "" {
		span.SetTag("preferred_reviewer", preferredReviewerAssetID)
	}

	// complete hanging WRITE_REPORT/REVISE_REPORT orders
	writes, err := listAllOrdersForReport(spanContext, reportUseCase.orderRepository, tx, updatedReport.Id, orderpb.OrderStatus_ORDER_STATUS_UNSPECIFIED)
	if err != nil {
		err := fmt.Errorf("failed to list write orders: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}
	for _, order := range writes {
		if (order.Type == orderpb.OrderType_ORDER_TYPE_WRITE_REPORT || order.Type == orderpb.OrderType_ORDER_TYPE_REVISE_REPORT) && !terminalOrder(order.Status) {
			if _, cerr := reportUseCase.orderRepository.CompleteOrder(spanContext, tx, order.Id); cerr != nil {
				herosentry.CaptureException(spanContext, cerr, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to complete write order %s", order.Id))
				return fmt.Errorf("failed to complete write order %s: %w", order.Id, cerr)
			}
		}
	}

	// Get the reviewReportWorkflow configuration for the report's org
	reviewReportWorkflow, err := GetReviewReportWorkflowForOrg(fmt.Sprintf("%d", updatedReport.OrgId))
	if err != nil {
		err := fmt.Errorf("failed to get reviewReportWorkflow: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return err
	}

	// Determine the current review level based on the latest existing review round
	currentLevel := 1 // Default to level 1
	existingRounds, err := listAllReviewRoundsForReport(spanContext, reportUseCase.reportRepository, tx, updatedReport.Id)
	if err != nil {
		err := fmt.Errorf("failed to list existing review rounds: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if len(existingRounds) > 0 {
		maxLevel := 0
		for _, round := range existingRounds {
			if int(round.Level) > maxLevel {
				maxLevel = int(round.Level)
			}
		}
		if maxLevel > 0 {
			currentLevel = maxLevel
		}
	}

	// Get the review level configuration
	reviewLevel, err := reviewReportWorkflow.GetReviewLevel(currentLevel)
	if err != nil {
		err := fmt.Errorf("failed to get review level config for level %d: %w", currentLevel, err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return err
	}

	// List all available assets of the required type, handling pagination
	var availableReviewers []string
	var reviewerID string

	// Check if a preferred reviewer was specified
	if preferredReviewerAssetID != "" {
		// Validate the preferred reviewer is eligible
		isEligible := false
		for _, assetType := range reviewLevel.AssetTypes {
			assets, err := listAllAssetsOfType(spanContext, reportUseCase.assetRepository, tx, assetType, updatedReport.AuthorAssetId)
			if err != nil {
				return fmt.Errorf("failed to list available assets of type %s: %w", assetType, err)
			}
			for _, asset := range assets {
				if asset.Id == preferredReviewerAssetID {
					isEligible = true
					reviewerID = preferredReviewerAssetID
					break
				}
			}
			if isEligible {
				break
			}
		}
		if !isEligible {
			// Log warning but continue with normal selection
			fmt.Printf("Warning: Preferred reviewer %s is not eligible for level %d, selecting randomly\n",
				preferredReviewerAssetID, currentLevel)
		}
	}

	// If no preferred reviewer or not eligible, proceed with normal selection
	if reviewerID == "" {
		// First check if there's an existing review round at this level
		existingRounds, err = listAllReviewRoundsForReport(spanContext, reportUseCase.reportRepository, tx, updatedReport.Id)
		if err != nil {
			err := fmt.Errorf("failed to list existing review rounds: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		// Look for a reviewer from a previous round at the same level
		for _, round := range existingRounds {
			if int(round.Level) == currentLevel && round.ReviewerAssetId != "" {
				existingReviewerID := round.ReviewerAssetId
				reviewerID = existingReviewerID
				break
			}
		}

		// If we found an existing reviewer at this level, use them
		if reviewerID != "" {
			// No need to find additional reviewers
		} else {
			// Otherwise, proceed with finding available reviewers
			for _, assetType := range reviewLevel.AssetTypes {
				assets, err := listAllAssetsOfType(spanContext, reportUseCase.assetRepository, tx, assetType, updatedReport.AuthorAssetId)
				if err != nil {
					err := fmt.Errorf("failed to list available assets of type %s: %w", assetType, err)
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
					return err
				}

				for _, asset := range assets {
					availableReviewers = append(availableReviewers, asset.Id)
				}
			}

			if len(availableReviewers) == 0 {
				return fmt.Errorf("no available reviewers found for level %d in org %d (excluding author %s)", currentLevel, updatedReport.OrgId, updatedReport.AuthorAssetId)
			}

			// Randomly select a reviewer
			reviewerIndex, err := secureRandomInt(len(availableReviewers))
			if err != nil {
				err := fmt.Errorf("failed to select random reviewer: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return err
			}
			reviewerID = availableReviewers[reviewerIndex]
		}
	}

	// create round
	roundReq := &reports.AddReviewRoundRequest{
		ReviewRound: &reports.ReviewRound{
			ReportId:        updatedReport.Id,
			ReviewerAssetId: reviewerID,
			Level:           int32(safeInt32Conversion(currentLevel)),
			NoteForReviewer: noteFromAuthor,
		},
	}
	round, err := reportUseCase.reportRepository.AddReviewRound(spanContext, tx, roundReq)
	if err != nil {
		err := fmt.Errorf("failed to assign review round: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}

	// enqueue REVIEW_REPORT order
	review := &orderpb.Order{
		AssetId:       reviewerID,
		ReportId:      updatedReport.Id,
		ReviewRoundId: round.Id,
		Type:          orderpb.OrderType_ORDER_TYPE_REVIEW_REPORT,
		Status:        orderpb.OrderStatus_ORDER_STATUS_CREATED,
		Instructions:  fmt.Sprintf("Review report '%s' at level %d", updatedReport.Title, round.Level),
	}
	_, err = reportUseCase.orderRepository.CreateOrder(spanContext, tx, review)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create review order for report %s", updatedReport.Id))
		return fmt.Errorf("failed to create review order: %w", err)
	}
	return nil
}

// createReviseReportOrder handles the logic when changes are requested in a review round.
func (exec *ReportSideEffectExecutor) createReviseReportOrder(
	ctx context.Context,
	tx *sql.Tx,
	updatedRound *reports.ReviewRound,
	reportUseCase *ReportUseCase,
) error {
	// Create span for this operation
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportSideEffectExecutor.createReviseReportOrder")
	defer finishSpan()

	span.SetTag("review_round.id", updatedRound.Id)
	span.SetTag("report.id", updatedRound.ReportId)
	span.SetTag("review_round.level", fmt.Sprintf("%d", updatedRound.Level))
	span.SetTag("sent_to_level", fmt.Sprintf("%d", updatedRound.SentToLevel))
	span.SetTag("sent_to_asset_id", updatedRound.SentToAssetId)

	// 1. Complete hanging REVIEW_REPORT orders for this round
	reviews, err := listAllOrdersForReviewRound(ctx, reportUseCase.orderRepository, tx, updatedRound.Id, orderpb.OrderStatus_ORDER_STATUS_UNSPECIFIED)
	if err != nil {
		err := fmt.Errorf("failed to list review orders for round %s: %w", updatedRound.Id, err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}
	for _, order := range reviews {
		if order.Type == orderpb.OrderType_ORDER_TYPE_REVIEW_REPORT && !terminalOrder(order.Status) {
			if _, cerr := reportUseCase.orderRepository.CompleteOrder(spanContext, tx, order.Id); cerr != nil {
				// Log the error but continue if possible, or decide if it's fatal
				herosentry.CaptureException(spanContext, cerr, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to complete review order %s", order.Id))
				fmt.Printf("ERROR: failed to complete review order %s: %v\n", order.Id, cerr)
			}
		}
	}

	// 2. Fetch the report
	rep, err := reportUseCase.reportRepository.GetReport(ctx, tx, updatedRound.ReportId)
	if err != nil {
		err := fmt.Errorf("failed to get report %s for revise order: %w", updatedRound.ReportId, err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}

	// 3. Update report status to CHANGES_REQUESTED
	rep.Status = reports.ReportStatus_REPORT_STATUS_CHANGES_REQUESTED
	_, err = reportUseCase.reportRepository.UpdateReport(ctx, tx, rep)
	if err != nil {
		err := fmt.Errorf("failed to update report status to CHANGES_REQUESTED for report %s: %w", rep.Id, err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}

	// 4. Determine routing based on SentToLevel and SentToAssetId
	targetLevel := int(updatedRound.SentToLevel)
	targetAssetId := updatedRound.SentToAssetId

	// Case 1: Specific Asset & Level provided
	if targetLevel > 0 && targetAssetId != "" {
		fmt.Printf("INFO: Revising: Routing to specific asset and level. ReportID: %s, TargetLevel: %d, TargetAssetID: %s\n", rep.Id, targetLevel, targetAssetId)
		// Create a new review round for the specified reviewer and level
		nextRoundReq := &reports.AddReviewRoundRequest{
			ReviewRound: &reports.ReviewRound{
				ReportId:        rep.Id,
				ReviewerAssetId: targetAssetId,
				Level:           int32(safeInt32Conversion(targetLevel)),
				NoteForReviewer: updatedRound.RoundNote, // Pass the reviewer's note
			},
		}
		nextRound, err := reportUseCase.reportRepository.AddReviewRound(spanContext, tx, nextRoundReq)
		if err != nil {
			err := fmt.Errorf("failed to assign re-review round to asset %s at level %d: %w", targetAssetId, targetLevel, err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		// Create the REVIEW_REPORT order for the new round
		order := &orderpb.Order{
			AssetId:       targetAssetId,
			ReportId:      rep.Id,
			ReviewRoundId: nextRound.Id,
			Type:          orderpb.OrderType_ORDER_TYPE_REVIEW_REPORT,
			Status:        orderpb.OrderStatus_ORDER_STATUS_CREATED,
			Instructions:  fmt.Sprintf("Re-review report '%s' at level %d based on feedback", rep.Title, nextRound.Level),
		}
		_, err = reportUseCase.orderRepository.CreateOrder(spanContext, tx, order)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create re-review order for asset %s", targetAssetId))
			return fmt.Errorf("failed to create re-review order for asset %s: %w", targetAssetId, err)
		}
		return nil // Successfully routed for re-review
	}

	// Case 2: Specific Level provided, but no specific Asset
	if targetLevel > 0 && targetAssetId == "" {
		fmt.Printf("INFO: Revising: Routing to specific level, finding previous reviewer. ReportID: %s, TargetLevel: %d\n", rep.Id, targetLevel)
		// Find the reviewer from a previous round at the target level
		var previousReviewerAssetId string
		// List *all* previous rounds for this report to find the reviewer at the target level
		allRounds, err := listAllReviewRoundsForReport(spanContext, reportUseCase.reportRepository, tx, rep.Id)
		if err != nil {
			err := fmt.Errorf("failed to list all review rounds for report %s to find previous reviewer: %w", rep.Id, err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}
		for _, round := range allRounds {
			// Find a completed round at the target level (could refine logic here if needed)
			if int(round.Level) == targetLevel && round.ReviewerAssetId != "" {
				previousReviewerAssetId = round.ReviewerAssetId
				fmt.Printf("INFO: Found previous reviewer for re-review. ReportID: %s, Level: %d, ReviewerAssetID: %s\n", rep.Id, targetLevel, previousReviewerAssetId)
				break
			}
		}

		if previousReviewerAssetId == "" {
			// If no previous reviewer found at that level, fall back to author? Or error?
			// Current decision: Error, as the intent was specific level re-review.
			fmt.Printf("ERROR: Could not find previous reviewer at specified level for re-review. ReportID: %s, TargetLevel: %d\n", rep.Id, targetLevel)
			err := fmt.Errorf("changes requested for level %d, but no previous reviewer found at that level for report %s", targetLevel, rep.Id)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
			return err
		}

		// Create a new review round for the found reviewer and level
		nextRoundReq := &reports.AddReviewRoundRequest{
			ReviewRound: &reports.ReviewRound{
				ReportId:        rep.Id,
				ReviewerAssetId: previousReviewerAssetId,
				Level:           int32(safeInt32Conversion(targetLevel)),
				NoteForReviewer: updatedRound.RoundNote, // Pass the reviewer's note
			},
		}
		nextRound, err := reportUseCase.reportRepository.AddReviewRound(spanContext, tx, nextRoundReq)
		if err != nil {
			err := fmt.Errorf("failed to assign re-review round to previous reviewer %s at level %d: %w", previousReviewerAssetId, targetLevel, err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		// Create the REVIEW_REPORT order for the new round
		order := &orderpb.Order{
			AssetId:       previousReviewerAssetId,
			ReportId:      rep.Id,
			ReviewRoundId: nextRound.Id,
			Type:          orderpb.OrderType_ORDER_TYPE_REVIEW_REPORT,
			Status:        orderpb.OrderStatus_ORDER_STATUS_CREATED,
			Instructions:  fmt.Sprintf("Re-review report '%s' at level %d based on feedback", rep.Title, nextRound.Level),
		}
		_, err = reportUseCase.orderRepository.CreateOrder(spanContext, tx, order)
		if err != nil {
			err := fmt.Errorf("failed to create re-review order for previous reviewer %s: %w", previousReviewerAssetId, err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create re-review order for previous reviewer %s", previousReviewerAssetId))
			return err
		}
		return nil // Successfully routed for re-review
	}

	// Case 3: Default - Return to Author for revision
	fmt.Printf("INFO: Revising: Routing back to author. ReportID: %s, AuthorAssetID: %s\n", rep.Id, rep.AuthorAssetId)
	// enqueue REVISE_REPORT order for the original author
	reviseOrder := &orderpb.Order{
		AssetId:       rep.AuthorAssetId,
		ReportId:      updatedRound.ReportId,
		ReviewRoundId: updatedRound.Id, // Link to the round that requested changes
		Type:          orderpb.OrderType_ORDER_TYPE_REVISE_REPORT,
		Status:        orderpb.OrderStatus_ORDER_STATUS_CREATED,
		Instructions:  fmt.Sprintf("Revise report '%s' per feedback from round %s", rep.Title, updatedRound.Id),
	}
	_, err = reportUseCase.orderRepository.CreateOrder(spanContext, tx, reviseOrder)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create revise report order for author %s", rep.AuthorAssetId))
		return fmt.Errorf("failed to create revise report order for author %s: %w", rep.AuthorAssetId, err)
	}

	return nil // Successfully routed back to author
}

// createNextLevelReviewReportOrder escalates or finalizes approval.
func (exec *ReportSideEffectExecutor) createNextLevelReviewReportOrder(
	ctx context.Context,
	tx *sql.Tx,
	updatedRound *reports.ReviewRound,
	reportUseCase *ReportUseCase,
) error {
	// Create span for this operation
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportSideEffectExecutor.createNextLevelReviewReportOrder")
	defer finishSpan()

	span.SetTag("review_round.id", updatedRound.Id)
	span.SetTag("report.id", updatedRound.ReportId)
	span.SetTag("current_level", fmt.Sprintf("%d", updatedRound.Level))
	span.SetTag("sent_to_level", fmt.Sprintf("%d", updatedRound.SentToLevel))
	span.SetTag("sent_to_asset_id", updatedRound.SentToAssetId)

	// complete hanging REVIEW_REPORT orders for the *previous* round
	reviews, err := listAllOrdersForReviewRound(ctx, reportUseCase.orderRepository, tx, updatedRound.Id, orderpb.OrderStatus_ORDER_STATUS_UNSPECIFIED)
	if err != nil {
		err := fmt.Errorf("failed to list orders for previous round %s: %w", updatedRound.Id, err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}
	for _, order := range reviews {
		if order.Type == orderpb.OrderType_ORDER_TYPE_REVIEW_REPORT && !terminalOrder(order.Status) {
			if _, cerr := reportUseCase.orderRepository.CompleteOrder(spanContext, tx, order.Id); cerr != nil {
				err := fmt.Errorf("failed to complete previous review order %s: %w", order.Id, cerr)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to complete previous review order %s", order.Id))
				return err
			}
		}
	}

	// Fetch the report to get OrgID and Title
	rep, err := reportUseCase.reportRepository.GetReport(spanContext, tx, updatedRound.ReportId)
	if err != nil {
		err := fmt.Errorf("failed to get report %s: %w", updatedRound.ReportId, err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}

	// Get the reviewReportWorkflow configuration to check if this is the last level
	reviewReportWorkflow, err := GetReviewReportWorkflowForOrg(fmt.Sprintf("%d", rep.OrgId))
	if err != nil {
		err := fmt.Errorf("failed to get review reviewReportWorkflow: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return err
	}

	// Check if the approved round was the last level in the reviewReportWorkflow
	if reviewReportWorkflow.IsLastLevel(int(updatedRound.Level)) {
		// If it was the last level, update the report status to Approved
		rep.Status = reports.ReportStatus_REPORT_STATUS_APPROVED
		_, err = reportUseCase.reportRepository.UpdateReport(spanContext, tx, rep)
		if err != nil {
			err := fmt.Errorf("failed to update report status to approved: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		// Then complete all orders related to the report
		return exec.completeOrdersOnReportApproval(spanContext, tx, rep, reportUseCase)
	}

	// If not the last level, proceed to escalate: Create a new review round for the next level
	// Determine the next level using the reviewReportWorkflow config
	nextLevelInt := int(updatedRound.SentToLevel)
	if nextLevelInt == 0 {
		nextLevelInt = reviewReportWorkflow.GetNextLevel(int(updatedRound.Level))
	}
	if nextLevelInt == 0 { // Safety check, should have been caught by IsLastLevel
		err := fmt.Errorf("reviewReportWorkflow inconsistency: level %d was not the last, but GetNextLevel returned 0", updatedRound.Level)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
		return err
	}
	nextLevel := safeInt32Conversion(nextLevelInt)

	// Determine the next reviewer. Use SentToAssetId if provided, otherwise find one.
	nextReviewerAssetId := updatedRound.SentToAssetId

	if nextReviewerAssetId == "" {
		// If SentToAssetId wasn't specified, first check for existing reviewers at this level
		existingRounds, err := listAllReviewRoundsForReport(spanContext, reportUseCase.reportRepository, tx, updatedRound.ReportId)
		if err != nil {
			err := fmt.Errorf("failed to list existing review rounds: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return err
		}

		// Look for a reviewer from a previous round at the same level
		for _, round := range existingRounds {
			if int(round.Level) == int(nextLevel) && round.ReviewerAssetId != "" {
				nextReviewerAssetId = round.ReviewerAssetId
				break
			}
		}

		// If no existing reviewer found, proceed with finding available reviewers
		if nextReviewerAssetId == "" {
			// If SentToAssetId wasn't specified, find an appropriate reviewer for the next level
			reviewLevelConfig, err := reviewReportWorkflow.GetReviewLevel(int(nextLevel))
			if err != nil {
				err := fmt.Errorf("failed to get review level config for escalation level %d: %w", nextLevel, err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return err
			}

			var availableReviewers []string
			for _, assetType := range reviewLevelConfig.AssetTypes {
				assets, err := listAllAssetsOfType(spanContext, reportUseCase.assetRepository, tx, assetType, rep.AuthorAssetId)
				if err != nil {
					err := fmt.Errorf("failed to list available assets for escalation level %d, type %s: %w", nextLevel, assetType, err)
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
					return err
				}

				for _, asset := range assets {
					// Ensure the next reviewer is not the previous reviewer
					// (author is already excluded by listAllAssetsOfType)
					if asset.Id != updatedRound.ReviewerAssetId {
						availableReviewers = append(availableReviewers, asset.Id)
					}
				}
			}
			if len(availableReviewers) == 0 {
				err := fmt.Errorf("no available reviewers found for escalation level %d in org %d (excluding author %s and previous reviewer %s)", nextLevel, rep.OrgId, rep.AuthorAssetId, updatedRound.ReviewerAssetId)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound)
				return err
			}
			// Randomly select from available reviewers
			reviewerIndex, err := secureRandomInt(len(availableReviewers))
			if err != nil {
				err := fmt.Errorf("failed to select random reviewer: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return err
			}
			nextReviewerAssetId = availableReviewers[reviewerIndex]
		}
	}

	// Assign the new review round for the next level
	nextRoundReq := &reports.AddReviewRoundRequest{
		ReviewRound: &reports.ReviewRound{
			ReportId:        updatedRound.ReportId,
			ReviewerAssetId: nextReviewerAssetId,
			Level:           int32(safeInt32Conversion(int(nextLevel))),
			NoteForReviewer: updatedRound.NoteForReviewer,
		},
	}
	nextRound, err := reportUseCase.reportRepository.AddReviewRound(spanContext, tx, nextRoundReq)
	if err != nil {
		err := fmt.Errorf("failed to assign next level review round: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}

	// Create the REVIEW_REPORT order for the newly assigned round
	order := &orderpb.Order{
		AssetId:       nextReviewerAssetId,
		ReportId:      updatedRound.ReportId,
		ReviewRoundId: nextRound.Id,
		Type:          orderpb.OrderType_ORDER_TYPE_REVIEW_REPORT,
		Status:        orderpb.OrderStatus_ORDER_STATUS_CREATED,
		Instructions:  fmt.Sprintf("Review report '%s' at level %d", rep.Title, nextRound.Level),
	}
	_, err = reportUseCase.orderRepository.CreateOrder(spanContext, tx, order)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to create next level review order for report %s", updatedRound.ReportId))
		return fmt.Errorf("failed to create next level review order: %w", err)
	}
	return nil
}

// completeOrdersOnReportApproval finishes hanging orders after full approval.
func (exec *ReportSideEffectExecutor) completeOrdersOnReportApproval(
	ctx context.Context,
	tx *sql.Tx,
	updatedReport *reports.Report,
	reportUseCase *ReportUseCase,
) error {
	// Create span for this operation
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportSideEffectExecutor.completeOrdersOnReportApproval")
	defer finishSpan()

	span.SetTag("report.id", updatedReport.Id)
	span.SetTag("report.status", updatedReport.Status.String())

	orders, err := listAllOrdersForReport(spanContext, reportUseCase.orderRepository, tx, updatedReport.Id, orderpb.OrderStatus_ORDER_STATUS_UNSPECIFIED)
	if err != nil {
		err := fmt.Errorf("failed to list orders for report approval: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}
	for _, order := range orders {
		if !terminalOrder(order.Status) {
			if _, cerr := reportUseCase.orderRepository.CompleteOrder(spanContext, tx, order.Id); cerr != nil {
				herosentry.CaptureException(spanContext, cerr, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to complete order %s on report approval", order.Id))
				return cerr
			}
		}
	}
	return nil
}

// cancelOrdersOnReportCancel cancels hanging orders after rejection or cancellation.
func (exec *ReportSideEffectExecutor) cancelOrdersOnReportCancel(
	ctx context.Context,
	tx *sql.Tx,
	updatedReport *reports.Report,
	reportUseCase *ReportUseCase,
) error {
	// Create span for this operation
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "ReportSideEffectExecutor.cancelOrdersOnReportCancel")
	defer finishSpan()

	span.SetTag("report.id", updatedReport.Id)
	span.SetTag("report.status", updatedReport.Status.String())

	orders, err := listAllOrdersForReport(spanContext, reportUseCase.orderRepository, tx, updatedReport.Id, orderpb.OrderStatus_ORDER_STATUS_UNSPECIFIED)
	if err != nil {
		err := fmt.Errorf("failed to list orders for report cancellation: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return err
	}
	for _, order := range orders {
		if !terminalOrder(order.Status) {
			if _, cerr := reportUseCase.orderRepository.CancelOrder(spanContext, tx, order.Id, "Report cancelled"); cerr != nil {
				herosentry.CaptureException(spanContext, cerr, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to cancel order %s on report cancellation", order.Id))
				return cerr
			}
		}
	}
	return nil
}

// Replace rand.Intn with secure random number generation
func secureRandomInt(max int) (int, error) {
	if max <= 0 {
		return 0, fmt.Errorf("max must be positive")
	}
	n, err := rand.Int(rand.Reader, big.NewInt(int64(max)))
	if err != nil {
		return 0, err
	}
	return int(n.Int64()), nil
}

// Add safe conversion function
func safeInt32Conversion(n int) int32 {
	if n > math.MaxInt32 {
		return math.MaxInt32
	}
	if n < math.MinInt32 {
		return math.MinInt32
	}
	return int32(n)
}

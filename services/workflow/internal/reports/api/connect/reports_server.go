package connect

import (
	"context"
	"fmt"
	"log/slog"

	connecthelper "common/connect"
	"common/herosentry"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/emptypb"

	reportpb "proto/hero/reports/v2"
	reportsconnect "proto/hero/reports/v2/reportsconnect"

	"workflow/internal/reports/usecase"
)

// ReportServer implements all RPCs defined in ReportService.
// Each handler validates the request, invokes the corresponding
// business-logic method on ReportUseCase, logs the call, and converts
// domain errors into Connect error codes.
type ReportServer struct {
	reportsconnect.UnimplementedReportServiceHandler

	reportUseCase *usecase.ReportUseCase
	logger        *slog.Logger
}

// NewReportServer constructs a ReportServer. If logger is nil, slog.Default()
// is used so that a non-nil logger is always available.
func NewReportServer(reportUseCase *usecase.ReportUseCase, logger *slog.Logger) *ReportServer {
	if logger == nil {
		logger = slog.Default()
	}
	return &ReportServer{
		reportUseCase: reportUseCase,
		logger:        logger.With("component", "ReportServer"),
	}
}

// -----------------------------------------------------------------------------
// Section CRUD
// -----------------------------------------------------------------------------

func (reportServer *ReportServer) CreateReportSection(ctx context.Context, request *connect.Request[reportpb.CreateReportSectionRequest]) (*connect.Response[reportpb.ReportSection], error) {
	reportServer.logger.InfoContext(ctx, "CreateReportSection called")
	if request.Msg == nil || request.Msg.Section == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("section payload missing"), "CreateReportSection", herosentry.ErrorTypeValidation)
	}
	created, err := reportServer.reportUseCase.CreateReportSection(ctx, request.Msg.ReportId, request.Msg.Section)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "CreateReportSection")
	}
	return connect.NewResponse(created), nil
}

func (reportServer *ReportServer) GetReportSection(ctx context.Context, request *connect.Request[reportpb.GetReportSectionRequest]) (*connect.Response[reportpb.ReportSection], error) {
	reportServer.logger.InfoContext(ctx, "GetReportSection called")
	if request.Msg == nil || request.Msg.ReportId == "" || request.Msg.SectionId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id or section_id missing"), "GetReportSection", herosentry.ErrorTypeValidation)
	}
	section, err := reportServer.reportUseCase.GetReportSection(ctx, request.Msg.ReportId, request.Msg.SectionId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetReportSection")
	}
	return connect.NewResponse(section), nil
}

func (reportServer *ReportServer) UpdateReportSection(ctx context.Context, request *connect.Request[reportpb.UpdateReportSectionRequest]) (*connect.Response[reportpb.ReportSection], error) {
	reportServer.logger.InfoContext(ctx, "UpdateReportSection called")
	if request.Msg == nil || request.Msg.Section == nil || request.Msg.Section.Id == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("section payload invalid"), "UpdateReportSection", herosentry.ErrorTypeValidation)
	}
	updated, err := reportServer.reportUseCase.UpdateReportSection(ctx, request.Msg.ReportId, request.Msg.Section)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateReportSection")
	}
	return connect.NewResponse(updated), nil
}

func (reportServer *ReportServer) DeleteReportSection(ctx context.Context, request *connect.Request[reportpb.DeleteReportSectionRequest]) (*connect.Response[emptypb.Empty], error) {
	reportServer.logger.InfoContext(ctx, "DeleteReportSection called")
	if request.Msg == nil || request.Msg.ReportId == "" || request.Msg.SectionId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id or section_id missing"), "DeleteReportSection", herosentry.ErrorTypeValidation)
	}
	err := reportServer.reportUseCase.DeleteReportSection(ctx, request.Msg.ReportId, request.Msg.SectionId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeleteReportSection")
	}
	return connect.NewResponse(&emptypb.Empty{}), nil // Fixed: Added comma
}

func (reportServer *ReportServer) ListReportSections(ctx context.Context, request *connect.Request[reportpb.ListReportSectionsRequest]) (*connect.Response[reportpb.ListReportSectionsResponse], error) {
	reportServer.logger.InfoContext(ctx, "ListReportSections called")
	if request.Msg == nil || request.Msg.ReportId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "ListReportSections", herosentry.ErrorTypeValidation)
	}
	sections, err := reportServer.reportUseCase.ListReportSections(ctx, request.Msg.ReportId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListReportSections")
	}
	return connect.NewResponse(&reportpb.ListReportSectionsResponse{Sections: sections}), nil
}

// -----------------------------------------------------------------------------
// Report CRUD & Metadata
// -----------------------------------------------------------------------------

func (reportServer *ReportServer) CreateReport(ctx context.Context, request *connect.Request[reportpb.CreateReportRequest]) (*connect.Response[reportpb.CreateReportResponse], error) {
	reportServer.logger.InfoContext(ctx, "CreateReport called")
	if request.Msg == nil || request.Msg.Report == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report payload missing"), "CreateReport", herosentry.ErrorTypeValidation)
	}
	created, err := reportServer.reportUseCase.CreateReport(ctx, request.Msg.Report)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "CreateReport")
	}
	return connect.NewResponse(&reportpb.CreateReportResponse{Report: created}), nil
}

func (reportServer *ReportServer) GetReport(ctx context.Context, request *connect.Request[reportpb.GetReportRequest]) (*connect.Response[reportpb.Report], error) {
	reportServer.logger.InfoContext(ctx, "GetReport called")
	if request.Msg == nil || request.Msg.Id == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "GetReport", herosentry.ErrorTypeValidation)
	}
	report, err := reportServer.reportUseCase.GetReport(ctx, request.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetReport")
	}
	return connect.NewResponse(report), nil
}

func (reportServer *ReportServer) UpdateReport(ctx context.Context, request *connect.Request[reportpb.UpdateReportRequest]) (*connect.Response[reportpb.Report], error) {
	reportServer.logger.InfoContext(ctx, "UpdateReport called")
	if request.Msg == nil || request.Msg.Report == nil || request.Msg.Report.Id == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report payload invalid"), "UpdateReport", herosentry.ErrorTypeValidation)
	}
	updated, err := reportServer.reportUseCase.UpdateReport(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateReport")
	}
	return connect.NewResponse(updated), nil
}

// UpdateReportStatus handles the RPC to update only the status of a report.
func (reportServer *ReportServer) UpdateReportStatus(ctx context.Context, request *connect.Request[reportpb.UpdateReportStatusRequest]) (*connect.Response[reportpb.UpdateReportStatusResponse], error) {
	reportServer.logger.InfoContext(ctx, "UpdateReportStatus called", "report_id", request.Msg.GetId(), "new_status", request.Msg.GetStatus().String())
	if request.Msg == nil || request.Msg.Id == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "UpdateReportStatus", herosentry.ErrorTypeValidation)
	}
	// Basic validation for status enum might be needed depending on requirements
	if request.Msg.Status == reportpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("cannot set status to unspecified"), "UpdateReportStatus", herosentry.ErrorTypeValidation)
	}

	updatedReport, err := reportServer.reportUseCase.UpdateReportStatus(ctx, request.Msg.Id, request.Msg.Status)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateReportStatus")
	}

	return connect.NewResponse(&reportpb.UpdateReportStatusResponse{Report: updatedReport}), nil
}

func (reportServer *ReportServer) ListReports(ctx context.Context, request *connect.Request[reportpb.ListReportsRequest]) (*connect.Response[reportpb.ListReportsResponse], error) {
	reportServer.logger.InfoContext(ctx, "ListReports called")
	if request.Msg == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("request message nil"), "ListReports", herosentry.ErrorTypeValidation)
	}
	reports, next, err := reportServer.reportUseCase.ListReports(ctx, request.Msg.PageSize, request.Msg.PageToken, request.Msg.Status, request.Msg.OrgId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListReports")
	}
	return connect.NewResponse(&reportpb.ListReportsResponse{Reports: reports, NextPageToken: next}), nil
}

// ListReportsBySituationId handles the new RPC for filtering by situation_id.
func (reportServer *ReportServer) ListReportsBySituationId(
	ctx context.Context,
	request *connect.Request[reportpb.ListReportsBySituationIdRequest],
) (*connect.Response[reportpb.ListReportsResponse], error) {
	reportServer.logger.InfoContext(ctx, "ListReportsBySituationId called")
	if request.Msg == nil || request.Msg.SituationId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("situation_id missing"), "ListReportsBySituationId", herosentry.ErrorTypeValidation)
	}
	reports, nextPageToken, err := reportServer.reportUseCase.ListReportsBySituationID(
		ctx,
		request.Msg.SituationId,
		request.Msg.PageSize,
		request.Msg.PageToken,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListReportsBySituationId")
	}
	return connect.NewResponse(&reportpb.ListReportsResponse{
		Reports:       reports,
		NextPageToken: nextPageToken,
	}), nil
}

// ListReportsByCaseId handles the new RPC for filtering by case_id.
func (reportServer *ReportServer) ListReportsByCaseId(
	ctx context.Context,
	request *connect.Request[reportpb.ListReportsByCaseIdRequest],
) (*connect.Response[reportpb.ListReportsResponse], error) {
	reportServer.logger.InfoContext(ctx, "ListReportsByCaseId called")
	if request.Msg == nil || request.Msg.CaseId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("case_id missing"), "ListReportsByCaseId", herosentry.ErrorTypeValidation)
	}
	reports, nextPageToken, err := reportServer.reportUseCase.ListReportsByCaseID(
		ctx,
		request.Msg.CaseId,
		request.Msg.PageSize,
		request.Msg.PageToken,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListReportsByCaseId")
	}
	return connect.NewResponse(&reportpb.ListReportsResponse{
		Reports:       reports,
		NextPageToken: nextPageToken,
	}), nil
}

func (reportServer *ReportServer) BatchGetReports(ctx context.Context, request *connect.Request[reportpb.BatchGetReportsRequest]) (*connect.Response[reportpb.BatchGetReportsResponse], error) {
	reportServer.logger.InfoContext(ctx, "BatchGetReports called")
	if request.Msg == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("request nil"), "BatchGetReports", herosentry.ErrorTypeValidation)
	}
	batch, err := reportServer.reportUseCase.BatchGetReports(ctx, request.Msg.ReportIds)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "BatchGetReports")
	}
	return connect.NewResponse(&reportpb.BatchGetReportsResponse{Reports: batch}), nil
}

func (reportServer *ReportServer) DeleteReport(ctx context.Context, request *connect.Request[reportpb.DeleteReportRequest]) (*connect.Response[emptypb.Empty], error) {
	reportServer.logger.InfoContext(ctx, "DeleteReport called")
	if request.Msg == nil || request.Msg.Id == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "DeleteReport", herosentry.ErrorTypeValidation)
	}
	err := reportServer.reportUseCase.DeleteReport(ctx, request.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeleteReport")
	}
	return connect.NewResponse(&emptypb.Empty{}), nil
}

// -----------------------------------------------------------------------------
// Comments
// -----------------------------------------------------------------------------

func (reportServer *ReportServer) AddComment(ctx context.Context, request *connect.Request[reportpb.AddCommentRequest]) (*connect.Response[reportpb.Comment], error) {
	reportServer.logger.InfoContext(ctx, "AddComment called")
	if request.Msg == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("request nil"), "AddComment", herosentry.ErrorTypeValidation)
	}
	comment, err := reportServer.reportUseCase.AddComment(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddComment")
	}
	return connect.NewResponse(comment), nil
}

func (reportServer *ReportServer) GetComments(ctx context.Context, request *connect.Request[reportpb.GetCommentsRequest]) (*connect.Response[reportpb.GetCommentsResponse], error) {
	reportServer.logger.InfoContext(ctx, "GetComments called")
	if request.Msg == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("request nil"), "GetComments", herosentry.ErrorTypeValidation)
	}
	comments, next, err := reportServer.reportUseCase.GetComments(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetComments")
	}
	return connect.NewResponse(&reportpb.GetCommentsResponse{Comments: comments, NextPageToken: next}), nil
}

func (reportServer *ReportServer) UpdateComment(ctx context.Context, request *connect.Request[reportpb.UpdateCommentRequest]) (*connect.Response[reportpb.Comment], error) {
	reportServer.logger.InfoContext(ctx, "UpdateComment called")
	if request.Msg == nil || request.Msg.Comment == nil || request.Msg.Comment.Id == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("comment payload invalid"), "UpdateComment", herosentry.ErrorTypeValidation)
	}
	updated, err := reportServer.reportUseCase.UpdateComment(ctx, request.Msg.Comment)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateComment")
	}
	return connect.NewResponse(updated), nil
}

func (reportServer *ReportServer) DeleteComment(ctx context.Context, request *connect.Request[reportpb.DeleteCommentRequest]) (*connect.Response[emptypb.Empty], error) {
	reportServer.logger.InfoContext(ctx, "DeleteComment called")
	if request.Msg == nil || request.Msg.CommentId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("comment_id missing"), "DeleteComment", herosentry.ErrorTypeValidation)
	}
	err := reportServer.reportUseCase.DeleteComment(ctx, request.Msg.CommentId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeleteComment")
	}
	return connect.NewResponse(&emptypb.Empty{}), nil
}

func (reportServer *ReportServer) ResolveComment(ctx context.Context, request *connect.Request[reportpb.ResolveCommentRequest]) (*connect.Response[reportpb.Comment], error) {
	reportServer.logger.InfoContext(ctx, "ResolveComment called")
	if request.Msg == nil || request.Msg.CommentId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("comment_id missing"), "ResolveComment", herosentry.ErrorTypeValidation)
	}
	// For now we map Resolve to UpdateComment via usecase.UpdateComment with Resolved=true .
	updated, err := reportServer.reportUseCase.ResolveComment(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ResolveComment")
	}
	return connect.NewResponse(updated), nil
}

// -----------------------------------------------------------------------------
// Review Workflow
// -----------------------------------------------------------------------------

func (reportServer *ReportServer) SubmitForReview(ctx context.Context, request *connect.Request[reportpb.SubmitForReviewRequest]) (*connect.Response[reportpb.Report], error) {
	reportServer.logger.InfoContext(ctx, "SubmitForReview called")
	if request.Msg == nil || request.Msg.ReportId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "SubmitForReview", herosentry.ErrorTypeValidation)
	}
	report, err := reportServer.reportUseCase.SubmitForReview(ctx, request.Msg.ReportId, request.Msg.Note, request.Msg.PreferredReviewerAssetId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "SubmitForReview")
	}
	return connect.NewResponse(report), nil
}

func (reportServer *ReportServer) AddReviewRound(ctx context.Context, request *connect.Request[reportpb.AddReviewRoundRequest]) (*connect.Response[reportpb.ReviewRound], error) {
	reportServer.logger.InfoContext(ctx, "AddReviewRound called")
	if request.Msg == nil || request.Msg.ReviewRound.ReportId == "" || request.Msg.ReviewRound.ReviewerAssetId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("required fields missing"), "AddReviewRound", herosentry.ErrorTypeValidation)
	}
	round, err := reportServer.reportUseCase.AddReviewRound(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddReviewRound")
	}
	return connect.NewResponse(round), nil
}

func (reportServer *ReportServer) GetEligibleReviewers(ctx context.Context, request *connect.Request[reportpb.GetEligibleReviewersRequest]) (*connect.Response[reportpb.GetEligibleReviewersResponse], error) {
	reportServer.logger.InfoContext(ctx, "GetEligibleReviewers called")
	if request.Msg == nil || request.Msg.ReportId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "GetEligibleReviewers", herosentry.ErrorTypeValidation)
	}
	response, err := reportServer.reportUseCase.GetEligibleReviewers(ctx, request.Msg.ReportId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetEligibleReviewers")
	}
	return connect.NewResponse(response), nil
}

func (reportServer *ReportServer) ApproveReviewRound(ctx context.Context, request *connect.Request[reportpb.ApproveReviewRoundRequest]) (*connect.Response[reportpb.ReviewRound], error) {
	reportServer.logger.InfoContext(ctx, "ApproveReviewRound called")
	if request.Msg == nil || request.Msg.ReviewRoundId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("required fields missing"), "ApproveReviewRound", herosentry.ErrorTypeValidation)
	}
	round, err := reportServer.reportUseCase.ApproveReviewRound(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ApproveReviewRound")
	}
	return connect.NewResponse(round), nil
}

func (reportServer *ReportServer) RequestChanges(ctx context.Context, request *connect.Request[reportpb.RequestChangesRequest]) (*connect.Response[reportpb.ReviewRound], error) {
	reportServer.logger.InfoContext(ctx, "RequestChanges called")
	if request.Msg == nil || request.Msg.ReviewRoundId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("required fields missing"), "RequestChanges", herosentry.ErrorTypeValidation)
	}
	round, err := reportServer.reportUseCase.RequestChanges(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RequestChanges")
	}
	return connect.NewResponse(round), nil
}

// GetReviewRound handles the RPC to retrieve a single review round.
func (reportServer *ReportServer) GetReviewRound(
	ctx context.Context,
	request *connect.Request[reportpb.GetReviewRoundRequest],
) (*connect.Response[reportpb.ReviewRound], error) {
	reportServer.logger.InfoContext(ctx, "GetReviewRound called")
	if request.Msg == nil || request.Msg.ReviewRoundId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("review_round_id missing"), "GetReviewRound", herosentry.ErrorTypeValidation)
	}
	round, err := reportServer.reportUseCase.GetReviewRound(ctx, request.Msg.ReviewRoundId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetReviewRound")
	}
	return connect.NewResponse(round), nil
}

// UpdateReviewRound handles the RPC to update a review round.
func (reportServer *ReportServer) UpdateReviewRound(
	ctx context.Context,
	request *connect.Request[reportpb.UpdateReviewRoundRequest],
) (*connect.Response[reportpb.ReviewRound], error) {
	reportServer.logger.InfoContext(ctx, "UpdateReviewRound called")
	if request.Msg == nil || request.Msg.ReviewRound == nil || request.Msg.ReviewRound.Id == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("review_round payload invalid"), "UpdateReviewRound", herosentry.ErrorTypeValidation)
	}
	updatedRound, err := reportServer.reportUseCase.UpdateReviewRound(ctx, request.Msg.ReviewRound)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateReviewRound")
	}
	return connect.NewResponse(updatedRound), nil
}

// DeleteReviewRound handles the RPC to delete a review round.
func (reportServer *ReportServer) DeleteReviewRound(
	ctx context.Context,
	request *connect.Request[reportpb.DeleteReviewRoundRequest],
) (*connect.Response[emptypb.Empty], error) {
	reportServer.logger.InfoContext(ctx, "DeleteReviewRound called")
	if request.Msg == nil || request.Msg.ReviewRoundId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("review_round_id missing"), "DeleteReviewRound", herosentry.ErrorTypeValidation)
	}
	err := reportServer.reportUseCase.DeleteReviewRound(ctx, request.Msg.ReviewRoundId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeleteReviewRound")
	}
	return connect.NewResponse(&emptypb.Empty{}), nil
}

// ListReviewRoundsForReport handles the RPC to list review rounds for a report.
func (reportServer *ReportServer) ListReviewRoundsForReport(
	ctx context.Context,
	request *connect.Request[reportpb.ListReviewRoundsForReportRequest],
) (*connect.Response[reportpb.ListReviewRoundsForReportResponse], error) {
	reportServer.logger.InfoContext(ctx, "ListReviewRoundsForReport called")
	if request.Msg == nil || request.Msg.ReportId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "ListReviewRoundsForReport", herosentry.ErrorTypeValidation)
	}

	rounds, nextPageToken, err := reportServer.reportUseCase.ListReviewRoundsForReport(
		ctx,
		request.Msg.ReportId,
		request.Msg.PageSize,
		request.Msg.PageToken,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListReviewRoundsForReport")
	}

	return connect.NewResponse(&reportpb.ListReviewRoundsForReportResponse{
		ReviewRounds:  rounds,
		NextPageToken: nextPageToken,
	}), nil
}

// -----------------------------------------------------------------------------
// JSON Metadata
// -----------------------------------------------------------------------------

func (reportServer *ReportServer) UpdateAdditionalInfoJson(ctx context.Context, request *connect.Request[reportpb.UpdateAdditionalInfoJsonRequest]) (*connect.Response[reportpb.UpdateAdditionalInfoJsonResponse], error) {
	reportServer.logger.InfoContext(ctx, "UpdateAdditionalInfoJson called")
	if request.Msg == nil || request.Msg.ReportId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "UpdateAdditionalInfoJson", herosentry.ErrorTypeValidation)
	}
	response, err := reportServer.reportUseCase.UpdateAdditionalInfoJSON(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateAdditionalInfoJson")
	}
	return connect.NewResponse(response), nil
}

func (reportServer *ReportServer) GetAdditionalInfo(ctx context.Context, request *connect.Request[reportpb.GetAdditionalInfoRequest]) (*connect.Response[reportpb.GetAdditionalInfoResponse], error) {
	reportServer.logger.InfoContext(ctx, "GetAdditionalInfo called")
	if request.Msg == nil || request.Msg.ReportId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "GetAdditionalInfo", herosentry.ErrorTypeValidation)
	}
	info, err := reportServer.reportUseCase.GetAdditionalInfo(ctx, request.Msg.ReportId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetAdditionalInfo")
	}
	return connect.NewResponse(info), nil
}

// -----------------------------------------------------------------------------
// Versioning
// -----------------------------------------------------------------------------

func (reportServer *ReportServer) GetReportVersion(ctx context.Context, request *connect.Request[reportpb.GetReportVersionRequest]) (*connect.Response[reportpb.ReportSnapshot], error) {
	reportServer.logger.InfoContext(ctx, "GetReportVersion called")
	if request.Msg == nil || request.Msg.ReportId == "" || request.Msg.Version <= 0 {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("invalid request"), "GetReportVersion", herosentry.ErrorTypeValidation)
	}
	snapshot, err := reportServer.reportUseCase.GetReportVersion(ctx, request.Msg.ReportId, request.Msg.Version)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetReportVersion")
	}
	return connect.NewResponse(snapshot), nil
}

func (reportServer *ReportServer) ListReportVersions(ctx context.Context, request *connect.Request[reportpb.ListReportVersionsRequest]) (*connect.Response[reportpb.ListReportVersionsResponse], error) {
	reportServer.logger.InfoContext(ctx, "ListReportVersions called")
	if request.Msg == nil || request.Msg.ReportId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("report_id missing"), "ListReportVersions", herosentry.ErrorTypeValidation)
	}
	versions, err := reportServer.reportUseCase.ListReportVersions(ctx, request.Msg.ReportId)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListReportVersions")
	}
	return connect.NewResponse(&reportpb.ListReportVersionsResponse{Versions: versions}), nil
}

// SearchReports handles the RPC to search reports with rich filtering options.
func (reportServer *ReportServer) SearchReports(
	ctx context.Context,
	request *connect.Request[reportpb.SearchReportsRequest],
) (*connect.Response[reportpb.SearchReportsResponse], error) {
	reportServer.logger.InfoContext(ctx, "SearchReports called")
	if request.Msg == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("request nil"), "SearchReports", herosentry.ErrorTypeValidation)
	}

	response, err := reportServer.reportUseCase.SearchReports(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "SearchReports")
	}

	return connect.NewResponse(response), nil
}

// -----------------------------------------------------------------------------
// Relations CRUD
// -----------------------------------------------------------------------------

// CreateRelation handles the RPC to create a new relation.
func (reportServer *ReportServer) CreateRelation(ctx context.Context, request *connect.Request[reportpb.CreateRelationRequest]) (*connect.Response[reportpb.Relation], error) {
	reportServer.logger.InfoContext(ctx, "CreateRelation called")
	if request.Msg == nil || request.Msg.Relation == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("relation payload missing"), "CreateRelation", herosentry.ErrorTypeValidation)
	}
	created, err := reportServer.reportUseCase.CreateRelation(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "CreateRelation")
	}
	return connect.NewResponse(created), nil
}

// GetRelation handles the RPC to retrieve a relation by ID.
func (reportServer *ReportServer) GetRelation(ctx context.Context, request *connect.Request[reportpb.GetRelationRequest]) (*connect.Response[reportpb.Relation], error) {
	reportServer.logger.InfoContext(ctx, "GetRelation called")
	if request.Msg == nil || request.Msg.RelationId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("relation_id missing"), "GetRelation", herosentry.ErrorTypeValidation)
	}
	relation, err := reportServer.reportUseCase.GetRelation(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetRelation")
	}
	return connect.NewResponse(relation), nil
}

// UpdateRelation handles the RPC to update an existing relation.
func (reportServer *ReportServer) UpdateRelation(ctx context.Context, request *connect.Request[reportpb.UpdateRelationRequest]) (*connect.Response[reportpb.Relation], error) {
	reportServer.logger.InfoContext(ctx, "UpdateRelation called")
	if request.Msg == nil || request.Msg.Relation == nil || request.Msg.Relation.Id == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("relation payload invalid"), "UpdateRelation", herosentry.ErrorTypeValidation)
	}
	updated, err := reportServer.reportUseCase.UpdateRelation(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateRelation")
	}
	return connect.NewResponse(updated), nil
}

// DeleteRelation handles the RPC to delete a relation.
func (reportServer *ReportServer) DeleteRelation(ctx context.Context, request *connect.Request[reportpb.DeleteRelationRequest]) (*connect.Response[emptypb.Empty], error) {
	reportServer.logger.InfoContext(ctx, "DeleteRelation called")
	if request.Msg == nil || request.Msg.RelationId == "" {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("relation_id missing"), "DeleteRelation", herosentry.ErrorTypeValidation)
	}
	err := reportServer.reportUseCase.DeleteRelation(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeleteRelation")
	}
	return connect.NewResponse(&emptypb.Empty{}), nil
}

// ListRelations handles the RPC to list relations for a report.
func (reportServer *ReportServer) ListRelations(ctx context.Context, request *connect.Request[reportpb.ListRelationsRequest]) (*connect.Response[reportpb.ListRelationsResponse], error) {
	reportServer.logger.InfoContext(ctx, "ListRelations called")
	if request.Msg == nil {
		return nil, connecthelper.AsConnectError(ctx, fmt.Errorf("request nil"), "ListRelations", herosentry.ErrorTypeValidation)
	}
	response, err := reportServer.reportUseCase.ListRelations(ctx, request.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListRelations")
	}
	return connect.NewResponse(response), nil
}

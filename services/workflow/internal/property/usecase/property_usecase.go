package usecase

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	property "proto/hero/property/v1"
	propertyRepository "workflow/internal/property/data"

	database "common/database"
	herosentry "common/herosentry"

	_ "modernc.org/sqlite" // required to support transaction for in-memory db
)

const FixedResourceTypeProperty = "PROPERTY"

// PropertyUseCase defines the use-case layer for property operations.
type PropertyUseCase struct {
	database     *sql.DB // Used for transactional operations like AddCustodyEvent
	propertyRepo propertyRepository.PropertyRepository
}

// NewPropertyUseCase creates a new PropertyUseCase.
func NewPropertyUseCase(
	database *sql.DB,
	propertyRepo propertyRepository.PropertyRepository) (*PropertyUseCase, error) {
	// For in-memory propertyRepository, we need a dummy DB to support transactions.
	if database == nil {
		return nil, errors.New("database is nil: cannot initialize PropertyUseCase")
	}

	return &PropertyUseCase{
		database:     database,
		propertyRepo: propertyRepo,
	}, nil
}

// executeInTx executes the provided function within a database transaction.
func (propertyUseCase *PropertyUseCase) executeInTx(ctx context.Context, transactionalWork func(transaction *sql.Tx) error) error {
	spanContext, _, finishSpan := herosentry.StartSpan(ctx, "PropertyUseCase.executeInTx")
	defer finishSpan()

	tx, err := propertyUseCase.database.BeginTx(spanContext, &sql.TxOptions{
		Isolation: sql.LevelReadCommitted,
		ReadOnly:  false,
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to begin transaction")
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if p := recover(); p != nil {
			// A panic occurred, rollback and re-panic
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				herosentry.CaptureException(spanContext, rollbackErr, herosentry.ErrorTypeDatabase, "Failed to rollback transaction after panic")
			}
			panic(p) // re-throw panic after Rollback
		} else if err != nil {
			// Something went wrong, rollback
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				herosentry.CaptureException(spanContext, rollbackErr, herosentry.ErrorTypeDatabase, "Failed to rollback transaction")
			}
		} else {
			// All good, commit
			commitErr := tx.Commit()
			if commitErr != nil {
				herosentry.CaptureException(spanContext, commitErr, herosentry.ErrorTypeDatabase, "Failed to commit transaction")
				err = fmt.Errorf("failed to commit transaction: %w", commitErr)
			}
		}
	}()

	err = transactionalWork(tx)
	return err
}

// CreateProperty creates a new property.
func (propertyUseCase *PropertyUseCase) CreateProperty(ctx context.Context, propertyRecord *property.Property) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.CreateProperty")
	defer finish()
	span.SetTag("property.org_id", fmt.Sprintf("%d", propertyRecord.OrgId))
	if propertyRecord.PropertySchema != nil {
		span.SetTag("property.type", propertyRecord.PropertySchema.PropertyType.String())
	}
	span.SetTag("property.status", propertyRecord.PropertyStatus.String())

	if propertyRecord.OrgId == 0 {
		herosentry.CaptureException(spanContext, errors.New("organization ID is required"), herosentry.ErrorTypeValidation, "Property creation validation failed")
		return fmt.Errorf("organization ID is required")
	}

	// Set default values
	if propertyRecord.CreateTime == "" {
		propertyRecord.CreateTime = time.Now().UTC().Format(time.RFC3339)
	}
	if propertyRecord.UpdateTime == "" {
		propertyRecord.UpdateTime = propertyRecord.CreateTime
	}
	if propertyRecord.Version == 0 {
		propertyRecord.Version = 1
	}
	if propertyRecord.Status == 0 {
		propertyRecord.Status = 1 // ACTIVE
	}
	propertyRecord.ResourceType = FixedResourceTypeProperty

	// Validate property type in schema
	if propertyRecord.PropertySchema == nil || propertyRecord.PropertySchema.PropertyType == property.PropertyType_PROPERTY_TYPE_UNSPECIFIED {
		herosentry.CaptureException(spanContext, errors.New("property type is required in schema"), herosentry.ErrorTypeValidation, "Property creation validation failed")
		return fmt.Errorf("property type is required in schema")
	}

	// Validate property status
	if propertyRecord.PropertyStatus == property.PropertyStatus_PROPERTY_STATUS_UNSPECIFIED {
		herosentry.CaptureException(spanContext, errors.New("property status is required"), herosentry.ErrorTypeValidation, "Property creation validation failed")
		return fmt.Errorf("property status is required")
	}

	// Use transaction for property creation and initial custody event
	err := database.WithSessionErr(propertyUseCase.database, spanContext, nil, func(tx *sql.Tx) error {
		// Create the property
		createErr := propertyUseCase.propertyRepo.CreateProperty(spanContext, tx, propertyRecord)
		if createErr != nil {
			return createErr
		}

		// Determine appropriate custody action type and notes based on property type
		var custodyActionType property.CustodyActionType
		var custodyNotes string

		switch propertyRecord.PropertySchema.PropertyType {
		case property.PropertyType_PROPERTY_TYPE_FOUND:
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED
			custodyNotes = "Property found and collected into custody"
		case property.PropertyType_PROPERTY_TYPE_SEIZED:
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED
			custodyNotes = "Property seized and collected into custody"
		case property.PropertyType_PROPERTY_TYPE_STOLEN:
			// For stolen property, we're just logging the report - not in physical custody
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_LOGGED
			custodyNotes = "Stolen property report filed"
		case property.PropertyType_PROPERTY_TYPE_SAFEKEEPING:
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED
			custodyNotes = "Property taken into safekeeping custody"
		case property.PropertyType_PROPERTY_TYPE_RECOVERED:
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED
			custodyNotes = "Previously missing/stolen property recovered and collected"
		case property.PropertyType_PROPERTY_TYPE_MISSING:
			// For missing property, we're just logging the report - no physical custody
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_LOGGED
			custodyNotes = "Missing property report filed"
		default:
			custodyActionType = property.CustodyActionType_CUSTODY_ACTION_TYPE_COLLECTED
			custodyNotes = "Property collected into custody"
		}

		// Create initial chain of custody event
		initialCustodyEvent := &property.CustodyEvent{
			Timestamp:          time.Now().UTC().Format(time.RFC3339),
			TransferringUserId: propertyRecord.CreatedBy,
			ReceivingUserId:    propertyRecord.CurrentCustodian,
			NewLocation:        propertyRecord.CurrentLocation,
			ActionType:         custodyActionType,
			Notes:              custodyNotes,
		}

		// Add initial custody event
		custodyErr := propertyUseCase.propertyRepo.AddCustodyEvent(spanContext, tx, propertyRecord.Id, initialCustodyEvent)
		if custodyErr != nil {
			return custodyErr
		}

		return nil
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to create property")
		return err
	}

	span.SetTag("property.id", propertyRecord.Id)
	return nil
}

// GetProperty retrieves a property by its ID.
func (propertyUseCase *PropertyUseCase) GetProperty(ctx context.Context, propertyID string) (*property.Property, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.GetProperty")
	defer finish()
	span.SetTag("property.id", propertyID)

	if propertyID == "" {
		herosentry.CaptureException(spanContext, errors.New("property ID is required"), herosentry.ErrorTypeValidation, "Property retrieval validation failed")
		return nil, fmt.Errorf("property ID is required")
	}

	propertyRecord, err := propertyUseCase.propertyRepo.GetProperty(spanContext, nil, propertyID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get property %s", propertyID))
		return nil, err
	}

	if propertyRecord != nil {
		span.SetTag("property.org_id", fmt.Sprintf("%d", propertyRecord.OrgId))
		if propertyRecord.PropertySchema != nil {
			span.SetTag("property.type", propertyRecord.PropertySchema.PropertyType.String())
		}
		span.SetTag("property.status", propertyRecord.PropertyStatus.String())
	}

	return propertyRecord, nil
}

// DeleteProperty deletes a property.
func (propertyUseCase *PropertyUseCase) DeleteProperty(ctx context.Context, propertyID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.DeleteProperty")
	defer finish()
	span.SetTag("property.id", propertyID)

	if propertyID == "" {
		herosentry.CaptureException(spanContext, errors.New("property ID is required"), herosentry.ErrorTypeValidation, "Property deletion validation failed")
		return fmt.Errorf("property ID is required")
	}

	err := propertyUseCase.propertyRepo.DeleteProperty(spanContext, nil, propertyID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to delete property %s", propertyID))
		return err
	}

	return nil
}

// ListProperties lists properties with pagination and filtering.
func (propertyUseCase *PropertyUseCase) ListProperties(ctx context.Context, pageSize int, pageToken string, propertyType property.PropertyType, propertyStatus property.PropertyStatus, orderBy string) ([]*property.Property, string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.ListProperties")
	defer finish()
	span.SetTag("list.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("list.property_type", propertyType.String())
	span.SetTag("list.property_status", propertyStatus.String())
	span.SetTag("list.order_by", orderBy)

	if pageSize <= 0 {
		pageSize = 50 // Default page size
	}
	if pageSize > 1000 {
		pageSize = 1000 // Maximum page size
	}

	result, err := propertyUseCase.propertyRepo.ListProperties(spanContext, nil, pageSize, pageToken, propertyType, propertyStatus, orderBy)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list properties")
		return nil, "", err
	}

	span.SetTag("list.results_count", fmt.Sprintf("%d", len(result.Properties)))
	return result.Properties, result.NextPageToken, nil
}

// UpdateProperty updates an existing property.
func (propertyUseCase *PropertyUseCase) UpdateProperty(ctx context.Context, updatedProperty *property.Property) (*property.Property, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.UpdateProperty")
	defer finish()
	span.SetTag("property.id", updatedProperty.Id)
	span.SetTag("property.org_id", fmt.Sprintf("%d", updatedProperty.OrgId))
	if updatedProperty.PropertySchema != nil {
		span.SetTag("property.type", updatedProperty.PropertySchema.PropertyType.String())
	}
	span.SetTag("property.status", updatedProperty.PropertyStatus.String())

	if updatedProperty.Id == "" {
		herosentry.CaptureException(spanContext, errors.New("property ID is required"), herosentry.ErrorTypeValidation, "Property update validation failed")
		return nil, fmt.Errorf("property ID is required")
	}

	// Update the update time
	updatedProperty.UpdateTime = time.Now().UTC().Format(time.RFC3339)

	propertyRecord, err := propertyUseCase.propertyRepo.UpdateProperty(spanContext, nil, updatedProperty)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update property %s", updatedProperty.Id))
		return nil, err
	}

	return propertyRecord, nil
}

// SearchProperties performs advanced search on properties.
func (propertyUseCase *PropertyUseCase) SearchProperties(ctx context.Context, searchRequest *property.SearchPropertiesRequest) (*property.SearchPropertiesResponse, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.SearchProperties")
	defer finish()
	span.SetTag("search.page_size", fmt.Sprintf("%d", searchRequest.PageSize))
	if searchRequest.Query != "" {
		span.SetTag("search.has_query", "true")
	} else {
		span.SetTag("search.has_query", "false")
	}

	if searchRequest == nil {
		herosentry.CaptureException(spanContext, errors.New("search request is required"), herosentry.ErrorTypeValidation, "Property search validation failed")
		return nil, fmt.Errorf("search request is required")
	}

	response, err := propertyUseCase.propertyRepo.SearchProperties(spanContext, nil, searchRequest)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to search properties")
		return nil, err
	}

	span.SetTag("search.results_count", fmt.Sprintf("%d", len(response.Properties)))
	return response, nil
}

// BatchGetProperties retrieves multiple properties by their IDs.
func (propertyUseCase *PropertyUseCase) BatchGetProperties(ctx context.Context, propertyIDs []string) ([]*property.Property, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.BatchGetProperties")
	defer finish()
	span.SetTag("batch.property_count", fmt.Sprintf("%d", len(propertyIDs)))

	if len(propertyIDs) == 0 {
		return []*property.Property{}, nil
	}

	properties, err := propertyUseCase.propertyRepo.BatchGetProperties(spanContext, nil, propertyIDs)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to batch get properties")
		return nil, err
	}

	span.SetTag("batch.results_count", fmt.Sprintf("%d", len(properties)))
	return properties, nil
}

// AddCustodyEvent adds a new custody event to a property's chain of custody.
func (propertyUseCase *PropertyUseCase) AddCustodyEvent(ctx context.Context, propertyID string, custodyEvent *property.CustodyEvent) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.AddCustodyEvent")
	defer finish()
	span.SetTag("property.id", propertyID)
	span.SetTag("custody.action_type", custodyEvent.ActionType.String())
	span.SetTag("custody.timestamp", custodyEvent.Timestamp)

	if propertyID == "" {
		herosentry.CaptureException(spanContext, errors.New("property ID is required"), herosentry.ErrorTypeValidation, "Custody event validation failed")
		return fmt.Errorf("property ID is required")
	}
	if custodyEvent == nil {
		herosentry.CaptureException(spanContext, errors.New("custody event is required"), herosentry.ErrorTypeValidation, "Custody event validation failed")
		return fmt.Errorf("custody event is required")
	}

	// Set timestamp if not provided
	if custodyEvent.Timestamp == "" {
		custodyEvent.Timestamp = time.Now().UTC().Format(time.RFC3339)
	}

	// Validate action type
	if custodyEvent.ActionType == property.CustodyActionType_CUSTODY_ACTION_TYPE_UNSPECIFIED {
		herosentry.CaptureException(spanContext, errors.New("custody action type is required"), herosentry.ErrorTypeValidation, "Custody event validation failed")
		return fmt.Errorf("custody action type is required")
	}

	// Use transaction for atomic operation
	err := database.WithSessionErr(propertyUseCase.database, spanContext, nil, func(tx *sql.Tx) error {
		return propertyUseCase.propertyRepo.AddCustodyEvent(spanContext, tx, propertyID, custodyEvent)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add custody event for property %s", propertyID))
		return err
	}

	return nil
}

// GetCustodyChain retrieves the complete chain of custody for a property.
func (propertyUseCase *PropertyUseCase) GetCustodyChain(ctx context.Context, propertyID string) ([]*property.CustodyEvent, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "PropertyUseCase.GetCustodyChain")
	defer finish()
	span.SetTag("property.id", propertyID)

	custodyChain, err := propertyUseCase.propertyRepo.GetCustodyChain(spanContext, nil, propertyID)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get custody chain")
		return nil, err
	}

	span.SetTag("custody.events_count", fmt.Sprintf("%d", len(custodyChain)))
	return custodyChain, nil
}

// ListPropertyFileAttachments lists all file attachments for a property.
func (propertyUseCase *PropertyUseCase) ListPropertyFileAttachments(ctx context.Context, request *property.ListPropertyFileAttachmentsRequest) (*property.ListPropertyFileAttachmentsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PropertyUseCase.ListPropertyFileAttachments")
	defer finishSpan()

	span.SetTag("property.id", request.PropertyId)

	if request.PageSize <= 0 {
		request.PageSize = 50 // Default page size
	}
	if request.PageSize > 1000 {
		request.PageSize = 1000 // Max page size
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	response, err := propertyUseCase.propertyRepo.ListPropertyFileAttachments(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list property file attachments")
		return nil, err
	}

	if response != nil && response.FileAttachments != nil {
		span.SetTag("result.count", fmt.Sprintf("%d", len(response.FileAttachments)))
	}

	return response, nil
}

// AddPropertyFileAttachment adds a file attachment to a property.
func (propertyUseCase *PropertyUseCase) AddPropertyFileAttachment(ctx context.Context, request *property.AddPropertyFileAttachmentRequest) (*property.AddPropertyFileAttachmentResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PropertyUseCase.AddPropertyFileAttachment")
	defer finishSpan()

	span.SetTag("property.id", request.PropertyId)
	span.SetTag("file.id", request.FileAttachment.FileId)

	// Validate that the property exists
	_, err := propertyUseCase.propertyRepo.GetProperty(spanContext, nil, request.PropertyId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeNotFound, "Property not found")
		return nil, fmt.Errorf("property not found: %s", request.PropertyId)
	}

	var response *property.AddPropertyFileAttachmentResponse
	updateErr := propertyUseCase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		response, repositoryErr = propertyUseCase.propertyRepo.AddPropertyFileAttachment(spanContext, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to add property file attachment")
		return nil, updateErr
	}

	return response, nil
}

// RemovePropertyFileAttachment removes a file attachment from a property.
func (propertyUseCase *PropertyUseCase) RemovePropertyFileAttachment(ctx context.Context, request *property.RemovePropertyFileAttachmentRequest) (*property.RemovePropertyFileAttachmentResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PropertyUseCase.RemovePropertyFileAttachment")
	defer finishSpan()

	span.SetTag("property.id", request.PropertyId)
	span.SetTag("attachment.id", request.AttachmentId)

	var response *property.RemovePropertyFileAttachmentResponse
	updateErr := propertyUseCase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		response, repositoryErr = propertyUseCase.propertyRepo.RemovePropertyFileAttachment(spanContext, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to remove property file attachment")
		return nil, updateErr
	}

	return response, nil
}

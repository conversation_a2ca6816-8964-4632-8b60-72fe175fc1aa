package usecase

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"common/herosentry"
	casespb "proto/hero/cases/v1"
	caseRepository "workflow/internal/cases/data"
	reportRepository "workflow/internal/reports/data"

	"google.golang.org/protobuf/types/known/structpb"

	permspb "proto/hero/permissions/v1"

	"connectrpc.com/connect"

	clients "common/clients/services"
)

const (
	defaultPageSize = 100
	maxPageSize     = 100
)

// CaseUseCase groups business-level operations for the case domain and
// delegates data persistence to a CaseRepository implementation.
type CaseUseCase struct {
	databaseConnection *sql.DB
	caseRepository     caseRepository.CaseRepository
	reportRepository   reportRepository.ReportRepository
	permsClient        clients.PermissionClient
}

// NewCaseUseCase constructs a fully-initialised CaseUseCase instance.
func NewCaseUseCase(
	databaseConnection *sql.DB,
	caseRepository caseRepository.CaseRepository,
	reportRepository reportRepository.ReportRepository,
	permsClient clients.PermissionClient,
) (*CaseUseCase, error) {
	if databaseConnection == nil {
		return nil, fmt.Errorf("database connection is nil")
	}
	if caseRepository == nil {
		return nil, fmt.Errorf("case repository must not be nil")
	}
	if reportRepository == nil {
		return nil, fmt.Errorf("report repository must not be nil")
	}
	return &CaseUseCase{
		databaseConnection: databaseConnection,
		caseRepository:     caseRepository,
		reportRepository:   reportRepository,
		permsClient:        permsClient,
	}, nil
}

// executeInTx wraps a series of repository calls in a SQL transaction, ensuring
// commit on success and rollback on error or panic.
func (usecase *CaseUseCase) executeInTx(ctx context.Context, transactionalWork func(transaction *sql.Tx) error) error {
	transaction, transactionErr := usecase.databaseConnection.BeginTx(ctx, nil)
	if transactionErr != nil {
		herosentry.CaptureException(ctx, transactionErr, herosentry.ErrorTypeDatabase, "Failed to begin transaction")
		return fmt.Errorf("begin transaction: %w", transactionErr)
	}
	defer func() {
		if recoveredPanic := recover(); recoveredPanic != nil {
			_ = transaction.Rollback()
			panic(recoveredPanic)
		}
	}()

	if workErr := transactionalWork(transaction); workErr != nil {
		_ = transaction.Rollback()
		return workErr
	}
	if commitErr := transaction.Commit(); commitErr != nil {
		herosentry.CaptureException(ctx, commitErr, herosentry.ErrorTypeDatabase, "Failed to commit transaction")
		return fmt.Errorf("commit transaction: %w", commitErr)
	}
	return nil
}

// -----------------------------------------------------------------------------
// Core CRUD
// -----------------------------------------------------------------------------

// CreateCase creates a new case.
func (usecase *CaseUseCase) CreateCase(ctx context.Context, request *casespb.CreateCaseRequest) (*casespb.Case, error) {
	// Start span for business logic
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.CreateCase")
	defer finishSpan()

	// Add business context
	if request.Case_ != nil {
		span.SetTag("case.type", request.Case_.Type.String())
		span.SetTag("case.priority", fmt.Sprintf("%d", request.Case_.Priority))
		span.SetTag("case.status", request.Case_.Status.String())
	}

	var createdCase *casespb.Case
	createErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		createdCase, repositoryErr = usecase.caseRepository.CreateCase(spanContext, transaction, request.Case_)
		return repositoryErr
	})
	if createErr != nil {
		herosentry.CaptureException(spanContext, createErr, herosentry.ErrorTypeDatabase, "Failed to create case")
		return nil, createErr
	}

	// Track created case ID
	if createdCase != nil {
		span.SetTag("case.id", createdCase.Id)
	}

	return createdCase, nil
}

// GetCase retrieves a case by its ID.
func (usecase *CaseUseCase) GetCase(ctx context.Context, request *casespb.GetCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.GetCase")
	defer finishSpan()

	span.SetTag("case.id", request.Id)

	caseData, err := usecase.caseRepository.GetCase(spanContext, nil, request.Id)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get case %s", request.Id))
		return nil, err
	}

	// Add retrieved case details
	if caseData != nil {
		span.SetTag("case.type", caseData.Type.String())
		span.SetTag("case.status", caseData.Status.String())
		span.SetTag("case.priority", fmt.Sprintf("%d", caseData.Priority))
	}

	return caseData, nil
}

// UpdateCase updates an existing case.
func (usecase *CaseUseCase) UpdateCase(ctx context.Context, request *casespb.UpdateCaseRequest) (*casespb.Case, error) {
	// Start span for business logic
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.UpdateCase")
	defer finishSpan()

	if request.GetCase_() != nil {
		span.SetTag("case.id", request.GetCase_().Id)
		span.SetTag("case.status", request.GetCase_().Status.String())
		span.SetTag("case.type", request.GetCase_().Type.String())
	}

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		// First get the existing case
		existingCase, err := usecase.caseRepository.GetCase(spanContext, transaction, request.GetCase_().Id)
		if err != nil {
			return fmt.Errorf("failed to get existing case: %w", err)
		}

		// Create a merged case by preserving existing values when new ones aren't provided
		mergedCase := existingCase
		updateCase := request.GetCase_()

		// Only update fields that are explicitly provided and supported by repository
		if updateCase.Title != "" {
			mergedCase.Title = updateCase.Title
		}
		if updateCase.Description != "" {
			mergedCase.Description = updateCase.Description
		}
		if updateCase.Type != casespb.CaseType_CASE_TYPE_UNSPECIFIED {
			mergedCase.Type = updateCase.Type
		}
		if updateCase.Status != casespb.CaseStatus_CASE_STATUS_UNSPECIFIED {
			mergedCase.Status = updateCase.Status
		}
		if updateCase.Priority != 0 {
			mergedCase.Priority = updateCase.Priority
		}
		if updateCase.ReleaseStatus != casespb.ReleaseStatus_RELEASE_STATUS_UNSPECIFIED {
			mergedCase.ReleaseStatus = updateCase.ReleaseStatus
		}

		// Handle tags update - if provided in update request, use them; otherwise keep existing
		if updateCase.Tags != nil { // Check if Tags field is actually provided
			mergedCase.Tags = updateCase.Tags
		}

		// Handle due date update - if provided in update request, use it; otherwise keep existing
		if updateCase.DueDate != "" {
			if updateCase.DueDate == "0" {
				mergedCase.DueDate = ""
			} else {
				// Validate the due date format
				if _, err := time.Parse(time.RFC3339Nano, updateCase.DueDate); err != nil {
					return fmt.Errorf("invalid due date format (must be RFC3339Nano): %w", err)
				}
				mergedCase.DueDate = updateCase.DueDate
			}
		}
		// If due date is not provided in update, keep existing due date

		// For additional info, merge with existing if provided
		if updateCase.AdditionalInfoJson != nil {
			if mergedCase.AdditionalInfoJson == nil {
				mergedCase.AdditionalInfoJson = updateCase.AdditionalInfoJson
			} else {
				// Merge the maps
				existingMap := mergedCase.AdditionalInfoJson.AsMap()
				updateMap := updateCase.AdditionalInfoJson.AsMap()
				for k, v := range updateMap {
					existingMap[k] = v
				}
				newStruct, err := structpb.NewStruct(existingMap)
				if err != nil {
					return fmt.Errorf("failed to merge additional info: %w", err)
				}
				mergedCase.AdditionalInfoJson = newStruct
			}
		}

		// Preserve the etag from the update request for optimistic locking
		mergedCase.Etag = updateCase.Etag

		// Create a new update request with the merged case
		mergedReq := &casespb.UpdateCaseRequest{
			Case_: mergedCase,
		}

		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.UpdateCase(spanContext, transaction, mergedReq)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update case %s", request.GetCase_().Id))
		return nil, updateErr
	}

	// Track updated case version
	if updatedCase != nil {
		span.SetTag("case.updated_version", fmt.Sprintf("%d", updatedCase.Version))
	}

	return updatedCase, nil
}

// DeleteCase deletes a case by its ID.
func (usecase *CaseUseCase) DeleteCase(ctx context.Context, request *casespb.DeleteCaseRequest) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.DeleteCase")
	defer finishSpan()
	span.SetTag("case.id", request.Id)

	err := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		return usecase.caseRepository.DeleteCase(spanContext, transaction, request.Id)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete case")
		return err
	}
	return nil
}

// -----------------------------------------------------------------------------
// Listing
// -----------------------------------------------------------------------------

// ListCases returns a paginated list of cases.
func (usecase *CaseUseCase) ListCases(ctx context.Context, request *casespb.ListCasesRequest) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCases")
	defer finishSpan()

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))
	if request.Status != casespb.CaseStatus_CASE_STATUS_UNSPECIFIED {
		span.SetTag("filter.status", request.Status.String())
	}
	if request.Type != casespb.CaseType_CASE_TYPE_UNSPECIFIED {
		span.SetTag("filter.type", request.Type.String())
	}
	if request.StakeholderAssetId != "" {
		span.SetTag("filter.stakeholder_asset_id", request.StakeholderAssetId)
	}

	result, err := usecase.caseRepository.ListCases(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list cases")
		return nil, err
	}

	resultFiltered, err := usecase.filterCases(spanContext, result.Cases, "ListCases")
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to filter cases")
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(resultFiltered)))

	return &casespb.ListCasesResponse{
		Cases:         resultFiltered,
		NextPageToken: result.NextPageToken,
	}, nil
}

// BatchGetCases retrieves multiple cases by their IDs.
func (usecase *CaseUseCase) BatchGetCases(ctx context.Context, request *casespb.BatchGetCasesRequest) (*casespb.BatchGetCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.BatchGetCases")
	defer finishSpan()

	span.SetTag("case.ids_count", fmt.Sprintf("%d", len(request.Ids)))

	if len(request.Ids) == 0 {
		return &casespb.BatchGetCasesResponse{Cases: []*casespb.Case{}}, nil
	}
	cases, err := usecase.caseRepository.BatchGetCases(spanContext, nil, request.Ids)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to batch get cases")
		return nil, err
	}

	resultFiltered, err := usecase.filterCases(spanContext, cases, "BatchGetCases")
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to filter cases")
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(resultFiltered)))

	return &casespb.BatchGetCasesResponse{Cases: resultFiltered}, nil
}

// ListCasesBySituationID returns cases linked to a specific situation.
func (usecase *CaseUseCase) ListCasesBySituationID(ctx context.Context, request *casespb.ListCasesBySituationIdRequest) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCasesBySituationID")
	defer finishSpan()

	if request.SituationId == "" {
		return nil, fmt.Errorf("situationID must be provided")
	}

	span.SetTag("situation.id", request.SituationId)

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	result, err := usecase.caseRepository.ListCasesBySituationID(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list cases by situation ID")
		return nil, err
	}
	resultFiltered, err := usecase.filterCases(spanContext, result.Cases, "ListCasesBySituationID")
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to filter cases")
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(resultFiltered)))

	return &casespb.ListCasesResponse{Cases: resultFiltered}, nil
}

// ListCasesByReportID returns cases linked to a specific report.
func (usecase *CaseUseCase) ListCasesByReportID(ctx context.Context, request *casespb.ListCasesByReportIdRequest) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCasesByReportID")
	defer finishSpan()

	if request.ReportId == "" {
		return nil, fmt.Errorf("reportID must be provided")
	}

	span.SetTag("report.id", request.ReportId)

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	result, err := usecase.caseRepository.ListCasesByReportID(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list cases by report ID")
		return nil, err
	}
	resultFiltered, err := usecase.filterCases(spanContext, result.Cases, "ListCasesByReportID")
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to filter cases")
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(resultFiltered)))

	return &casespb.ListCasesResponse{Cases: resultFiltered}, nil
}

// ListCasesByAssetID returns cases associated with a specific asset.
func (usecase *CaseUseCase) ListCasesByAssetID(ctx context.Context, request *casespb.ListCasesByAssetIdRequest) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCasesByAssetID")
	defer finishSpan()

	if request.AssetId == "" {
		return nil, fmt.Errorf("assetID must be provided")
	}

	span.SetTag("asset.id", request.AssetId)

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	result, err := usecase.caseRepository.ListCasesByAssetID(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list cases by asset ID")
		return nil, err
	}
	resultFiltered, err := usecase.filterCases(spanContext, result.Cases, "ListCasesByAssetID")
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to filter cases")
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(resultFiltered)))

	return &casespb.ListCasesResponse{Cases: resultFiltered}, nil
}

// ListCasesByEntityID returns cases referencing a specific entity.
func (usecase *CaseUseCase) ListCasesByEntityID(ctx context.Context, request *casespb.ListCasesByEntityIdRequest) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCasesByEntityID")
	defer finishSpan()

	if request.EntityId == "" {
		return nil, fmt.Errorf("entityID must be provided")
	}

	span.SetTag("entity.id", request.EntityId)

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	result, err := usecase.caseRepository.ListCasesByEntityID(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list cases by entity ID")
		return nil, err
	}
	resultFiltered, err := usecase.filterCases(spanContext, result.Cases, "ListCasesByEntityID")
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal, "Failed to filter cases")
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(resultFiltered)))

	return &casespb.ListCasesResponse{Cases: resultFiltered}, nil
}

// -----------------------------------------------------------------------------
// Relationship Mutators
// -----------------------------------------------------------------------------

// AddSituation links a situation to a case.
func (usecase *CaseUseCase) AddSituation(ctx context.Context, request *casespb.AddSituationToCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.AddSituation")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("situation.id", request.SituationId)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.AddSituation(spanContext, transaction, request.CaseId, request.SituationId)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to add situation to case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// RemoveSituation unlinks a situation from a case.
func (usecase *CaseUseCase) RemoveSituation(ctx context.Context, request *casespb.RemoveSituationFromCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.RemoveSituation")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("situation.id", request.SituationId)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.RemoveSituation(spanContext, transaction, request.CaseId, request.SituationId)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to remove situation from case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// AddReport links a report to a case.
func (usecase *CaseUseCase) AddReport(ctx context.Context, request *casespb.AddReportToCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.AddReport")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("report.id", request.ReportId)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.AddReport(spanContext, transaction, request.CaseId, request.ReportId)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to add report to case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// RemoveReport unlinks a report from a case.
func (usecase *CaseUseCase) RemoveReport(ctx context.Context, request *casespb.RemoveReportFromCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.RemoveReport")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("report.id", request.ReportId)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.RemoveReport(spanContext, transaction, request.CaseId, request.ReportId)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to remove report from case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// AddEntityRef adds an entity reference to a case.
func (usecase *CaseUseCase) AddEntityRef(ctx context.Context, request *casespb.AddEntityRefToCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.AddEntityRef")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	if request.EntityRef != nil {
		span.SetTag("entity.id", request.EntityRef.Id)
		span.SetTag("entity.type", request.EntityRef.Type)
	}

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.AddEntityRef(spanContext, transaction, request.CaseId, request.EntityRef)
		if repositoryErr != nil {
			return repositoryErr
		}

		// Check if we should automatically change status to investigating
		// Safety check: Move to investigating if case is OPEN or UNSPECIFIED and has entity associations
		if (updatedCase.Status == casespb.CaseStatus_CASE_STATUS_OPEN ||
			updatedCase.Status == casespb.CaseStatus_CASE_STATUS_UNSPECIFIED) &&
			len(updatedCase.EntityRefs) > 0 {
			updateRequest := &casespb.UpdateCaseStatusRequest{
				CaseId: updatedCase.Id,
				Status: casespb.CaseStatus_CASE_STATUS_INVESTIGATING,
				Note:   "Case moved to investigating due to entity association",
			}
			_, err := usecase.caseRepository.UpdateCaseStatus(spanContext, transaction, updateRequest)
			if err != nil {
				return fmt.Errorf("failed to update case status to investigating: %w", err)
			}

			// Fetch the updated case after status change to get the current status
			updatedCase, repositoryErr = usecase.caseRepository.GetCase(spanContext, transaction, request.CaseId)
			if repositoryErr != nil {
				return fmt.Errorf("failed to fetch updated case after status change: %w", repositoryErr)
			}
		}

		return nil
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to add entity ref to case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// RemoveEntityRef removes an entity reference from a case.
func (usecase *CaseUseCase) RemoveEntityRef(ctx context.Context, request *casespb.RemoveEntityRefFromCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.RemoveEntityRef")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	if request.EntityRef != nil {
		span.SetTag("entity.id", request.EntityRef.Id)
		span.SetTag("entity.type", request.EntityRef.Type)
	}

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.RemoveEntityRef(spanContext, transaction, request.CaseId, request.EntityRef)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to remove entity ref from case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// LinkRelatedCase links two cases together.
func (usecase *CaseUseCase) LinkRelatedCase(ctx context.Context, request *casespb.LinkRelatedCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.LinkRelatedCase")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("related_case.id", request.RelatedCaseId)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.LinkRelatedCase(spanContext, transaction, request.CaseId, request.RelatedCaseId)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to link related case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// UnlinkRelatedCase removes a link between two cases.
func (usecase *CaseUseCase) UnlinkRelatedCase(ctx context.Context, request *casespb.UnlinkRelatedCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.UnlinkRelatedCase")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("related_case.id", request.RelatedCaseId)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.UnlinkRelatedCase(spanContext, transaction, request.CaseId, request.RelatedCaseId)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to unlink related case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// -----------------------------------------------------------------------------
// Asset Associations
// -----------------------------------------------------------------------------

// AssociateAsset associates an asset with a case.
func (usecase *CaseUseCase) AssociateAsset(ctx context.Context, request *casespb.AssociateAssetToCaseRequest) (*casespb.CaseAssetAssociation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.AssociateAsset")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	if request.Association != nil {
		span.SetTag("asset.id", request.Association.AssetId)
		span.SetTag("association.type", request.Association.AssociationType.String())
	}

	var createdAssociation *casespb.CaseAssetAssociation
	var updatedCase *casespb.Case
	associateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		fmt.Println("AssociateAsset", request.CaseId, request.Association)
		createdAssociation, repositoryErr = usecase.caseRepository.AssociateAsset(spanContext, transaction, request.CaseId, request.Association)
		if repositoryErr != nil {
			return repositoryErr
		}

		// Get the updated case to check for side effects
		updatedCase, repositoryErr = usecase.caseRepository.GetCase(spanContext, transaction, request.CaseId)
		if repositoryErr != nil {
			return repositoryErr
		}

		// Check if we should automatically change status to investigating
		// Safety check: Move to investigating if case is OPEN or UNSPECIFIED and has asset associations
		if (updatedCase.Status == casespb.CaseStatus_CASE_STATUS_OPEN ||
			updatedCase.Status == casespb.CaseStatus_CASE_STATUS_UNSPECIFIED) &&
			len(updatedCase.AssetAssociations) > 0 {
			updateRequest := &casespb.UpdateCaseStatusRequest{
				CaseId: updatedCase.Id,
				Status: casespb.CaseStatus_CASE_STATUS_INVESTIGATING,
				Note:   "Case moved to investigating due to asset association",
			}
			_, err := usecase.caseRepository.UpdateCaseStatus(spanContext, transaction, updateRequest)
			if err != nil {
				return fmt.Errorf("failed to update case status to investigating: %w", err)
			}

			// Fetch the updated case after status change to get the current status
			// Note: This is for consistency, but since we return CaseAssetAssociation,
			// the updated case status won't be reflected in the response
			_, repositoryErr = usecase.caseRepository.GetCase(spanContext, transaction, request.CaseId)
			if repositoryErr != nil {
				return fmt.Errorf("failed to fetch updated case after status change: %w", repositoryErr)
			}
		}

		return nil
	})
	if associateErr != nil {
		herosentry.CaptureException(spanContext, associateErr, herosentry.ErrorTypeDatabase, "Failed to associate asset with case")
		return nil, associateErr
	}
	return createdAssociation, nil
}

// UpdateAssetAssociation updates an asset's association with a case.
func (usecase *CaseUseCase) UpdateAssetAssociation(ctx context.Context, request *casespb.UpdateAssetAssociationRequest) (*casespb.CaseAssetAssociation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.UpdateAssetAssociation")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	if request.Association != nil {
		span.SetTag("association.id", request.Association.Id)
		span.SetTag("asset.id", request.Association.AssetId)
		span.SetTag("association.type", request.Association.AssociationType.String())
	}

	var updatedAssociation *casespb.CaseAssetAssociation
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedAssociation, repositoryErr = usecase.caseRepository.UpdateAssetAssociation(spanContext, transaction, request.CaseId, request.Association)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to update asset association")
		return nil, updateErr
	}
	return updatedAssociation, nil
}

// DisassociateAsset removes an asset's association with a case.
func (usecase *CaseUseCase) DisassociateAsset(ctx context.Context, request *casespb.DisassociateAssetFromCaseRequest) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.DisassociateAsset")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("association.id", request.AssociationId)

	err := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		return usecase.caseRepository.DisassociateAsset(spanContext, transaction, request.CaseId, request.AssociationId)
	})
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to disassociate asset from case")
		return err
	}
	return nil
}

// ListAssetAssociations lists all asset associations for a case.
func (usecase *CaseUseCase) ListAssetAssociations(ctx context.Context, request *casespb.ListAssetAssociationsForCaseRequest) (*casespb.ListAssetAssociationsForCaseResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListAssetAssociations")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	response, err := usecase.caseRepository.ListAssetAssociations(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list asset associations")
		return nil, err
	}

	if response != nil && response.Associations != nil {
		span.SetTag("result.count", fmt.Sprintf("%d", len(response.Associations)))
	}

	return response, nil
}

// -----------------------------------------------------------------------------
// Status / Updates / Tags
// -----------------------------------------------------------------------------

// UpdateCaseStatus updates a case's status.
func (usecase *CaseUseCase) UpdateCaseStatus(ctx context.Context, request *casespb.UpdateCaseStatusRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.UpdateCaseStatus")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("case.new_status", request.Status.String())

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.UpdateCaseStatus(spanContext, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to update case status")
		return nil, updateErr
	}

	if updatedCase != nil {
		span.SetTag("case.old_status", updatedCase.Status.String())
	}

	return updatedCase, nil
}

// AddCaseUpdate adds an update entry to a case.
func (usecase *CaseUseCase) AddCaseUpdate(ctx context.Context, request *casespb.AddCaseUpdateRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.AddCaseUpdate")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	if request.Update != nil {
		span.SetTag("update.source", request.Update.UpdateSource.String())
	}

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.AddCaseUpdate(spanContext, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to add case update")
		return nil, updateErr
	}
	return updatedCase, nil
}

// RemoveCaseUpdate removes an update entry from a case.
func (usecase *CaseUseCase) RemoveCaseUpdate(ctx context.Context, request *casespb.RemoveCaseUpdateRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.RemoveCaseUpdate")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	if request.Update != nil {
		span.SetTag("update.message", request.Update.Message)
	}

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.RemoveCaseUpdate(spanContext, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to remove case update")
		return nil, updateErr
	}
	return updatedCase, nil
}

// ListCaseUpdates lists all updates for a case.
func (usecase *CaseUseCase) ListCaseUpdates(ctx context.Context, request *casespb.ListCaseUpdatesRequest) (*casespb.ListCaseUpdatesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCaseUpdates")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	response, err := usecase.caseRepository.ListCaseUpdates(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list case updates")
		return nil, err
	}

	if response != nil && response.Updates != nil {
		span.SetTag("result.count", fmt.Sprintf("%d", len(response.Updates)))
	}

	return response, nil
}

// ListCaseFileAttachments lists all file attachments for a case.
func (usecase *CaseUseCase) ListCaseFileAttachments(ctx context.Context, request *casespb.ListCaseFileAttachmentsRequest) (*casespb.ListCaseFileAttachmentsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCaseFileAttachments")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	response, err := usecase.caseRepository.ListCaseFileAttachments(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list case file attachments")
		return nil, err
	}

	if response != nil && response.FileAttachments != nil {
		span.SetTag("result.count", fmt.Sprintf("%d", len(response.FileAttachments)))
	}

	return response, nil
}

// ListCaseStatusHistory lists the status history for a case.
func (usecase *CaseUseCase) ListCaseStatusHistory(ctx context.Context, request *casespb.ListCaseStatusHistoryRequest) (*casespb.ListCaseStatusHistoryResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCaseStatusHistory")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	response, err := usecase.caseRepository.ListCaseStatusHistory(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list case status history")
		return nil, err
	}

	if response != nil && response.StatusUpdates != nil {
		span.SetTag("result.count", fmt.Sprintf("%d", len(response.StatusUpdates)))
	}

	return response, nil
}

// AddCaseTag adds a tag to a case.
func (usecase *CaseUseCase) AddCaseTag(ctx context.Context, request *casespb.AddCaseTagRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.AddCaseTag")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("tag", request.Tag)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.AddCaseTag(spanContext, transaction, request.CaseId, request.Tag)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to add case tag")
		return nil, updateErr
	}
	return updatedCase, nil
}

// RemoveCaseTag removes a tag from a case.
func (usecase *CaseUseCase) RemoveCaseTag(ctx context.Context, request *casespb.RemoveCaseTagRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.RemoveCaseTag")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("tag", request.Tag)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.RemoveCaseTag(spanContext, transaction, request.CaseId, request.Tag)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to remove case tag")
		return nil, updateErr
	}
	return updatedCase, nil
}

// UpdateAdditionalInfo updates a case's additional info.
func (usecase *CaseUseCase) UpdateAdditionalInfo(ctx context.Context, request *casespb.AddAdditionalInfoRequest) (*casespb.AddAdditionalInfoResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.UpdateAdditionalInfo")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)

	var response *casespb.AddAdditionalInfoResponse
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		response, repositoryErr = usecase.caseRepository.UpdateAdditionalInfo(spanContext, transaction, request)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to update case additional info")
		return nil, updateErr
	}
	return response, nil
}

// -----------------------------------------------------------------------------
// Versioning / Audit
// -----------------------------------------------------------------------------

// GetCaseVersion retrieves a specific version of a case.
func (usecase *CaseUseCase) GetCaseVersion(ctx context.Context, request *casespb.GetCaseVersionRequest) (*casespb.CaseSnapshot, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.GetCaseVersion")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("case.version", fmt.Sprintf("%d", request.Version))

	snapshot, err := usecase.caseRepository.GetCaseVersion(spanContext, nil, request.CaseId, request.Version)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to get case %s version %d", request.CaseId, request.Version))
		return nil, err
	}
	return snapshot, nil
}

// ListCaseVersions lists all versions of a case.
func (usecase *CaseUseCase) ListCaseVersions(ctx context.Context, request *casespb.ListCaseVersionsRequest) (*casespb.ListCaseVersionsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCaseVersions")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)

	fmt.Println("ListCaseVersions", request.CaseId)
	versions, err := usecase.caseRepository.ListCaseVersions(spanContext, nil, request.CaseId)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to list versions for case %s", request.CaseId))
		return nil, err
	}

	span.SetTag("result.count", fmt.Sprintf("%d", len(versions)))

	return &casespb.ListCaseVersionsResponse{Versions: versions}, nil
}

// ListCaseAuditLog lists the audit log entries for a case.
func (usecase *CaseUseCase) ListCaseAuditLog(ctx context.Context, request *casespb.ListCaseAuditLogRequest) (*casespb.ListCaseAuditLogResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.ListCaseAuditLog")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)

	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))

	response, err := usecase.caseRepository.ListCaseAuditLog(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list case audit log")
		return nil, err
	}

	if response != nil && response.Entries != nil {
		span.SetTag("result.count", fmt.Sprintf("%d", len(response.Entries)))
	}

	return response, nil
}

// AddWatcher adds a watcher to a case.
func (usecase *CaseUseCase) AddWatcher(ctx context.Context, request *casespb.AddWatcherRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.AddWatcher")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("watcher.asset_id", request.AssetId)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.AddWatcher(spanContext, transaction, request.CaseId, request.AssetId)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to add watcher to case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// RemoveWatcher removes a watcher from a case.
func (usecase *CaseUseCase) RemoveWatcher(ctx context.Context, request *casespb.RemoveWatcherRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.RemoveWatcher")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("watcher.asset_id", request.AssetId)

	var updatedCase *casespb.Case
	updateErr := usecase.executeInTx(spanContext, func(transaction *sql.Tx) error {
		var repositoryErr error
		updatedCase, repositoryErr = usecase.caseRepository.RemoveWatcher(spanContext, transaction, request.CaseId, request.AssetId)
		return repositoryErr
	})
	if updateErr != nil {
		herosentry.CaptureException(spanContext, updateErr, herosentry.ErrorTypeDatabase, "Failed to remove watcher from case")
		return nil, updateErr
	}
	return updatedCase, nil
}

// -----------------------------------------------------------------------------
// Search
// -----------------------------------------------------------------------------

// SearchCases performs comprehensive case search with hierarchical ranking.
func (usecase *CaseUseCase) SearchCases(ctx context.Context, request *casespb.SearchCasesRequest) (*casespb.SearchCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseUseCase.SearchCases")
	defer finishSpan()

	// Set pagination defaults
	if request.PageSize <= 0 {
		request.PageSize = defaultPageSize
	}
	if request.PageSize > maxPageSize {
		request.PageSize = maxPageSize
	}

	span.SetTag("pagination.page_size", fmt.Sprintf("%d", request.PageSize))
	if request.Query != "" {
		span.SetTag("search.query", request.Query)
	}

	// Search operations are read-only, so we don't need a transaction
	response, err := usecase.caseRepository.SearchCases(spanContext, nil, request)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to search cases")
		return nil, err
	}

	if response != nil && response.Cases != nil {
		span.SetTag("result.count", fmt.Sprintf("%d", len(response.Cases)))
	}

	return response, nil
}

// helper function to filter cases based on permissions
func (usecase *CaseUseCase) filterCases(ctx context.Context, cases []*casespb.Case, action string) ([]*casespb.Case, error) {
	resultFiltered := []*casespb.Case{}

	// for each situation, check if the user has permission to view it
	objectIds := make([]string, 0)
	for _, case_ := range cases {
		objectIds = append(objectIds, case_.Id)
	}
	permRequest := connect.NewRequest(&permspb.BatchCheckPermissionRequest{
		Category:  "Case",
		Action:    action,
		ObjectIds: objectIds,
	})
	perms, err := usecase.permsClient.BatchCheckPermission(ctx, permRequest)
	if err != nil {
		return nil, err
	}
	for _, case_ := range cases {
		if perms.Msg.Results[case_.Id] {
			resultFiltered = append(resultFiltered, case_)
		}
	}
	return resultFiltered, nil
}

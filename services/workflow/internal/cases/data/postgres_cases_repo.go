package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"
	"common/database"
	"common/herosentry"
	workflowUtils "workflow/internal/common/utils"

	casespb "proto/hero/cases/v1"
	entitypb "proto/hero/entity/v1"
	situationpb "proto/hero/situations/v2"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
)

// Default pagination size; can be driven from config/env
const defaultPageSize = 50

// Allowed ORDER BY columns for ListCases to prevent SQL injection
var allowedOrderBy = map[string]struct{}{
	"update_time":    {},
	"create_time":    {},
	"priority":       {},
	"status":         {},
	"type":           {},
	"release_status": {},
}

// slicesEqual checks if two string slices are equal (order matters).
// Example:
//
//	slicesEqual([]string{"a", "b"}, []string{"a", "b"}) -> true
//	slicesEqual([]string{"a", "b"}, []string{"b", "a"}) -> false
//	slicesEqual([]string{"a", "b"}, []string{"a"}) -> false
func slicesEqual(slice1, slice2 []string) bool {
	if len(slice1) != len(slice2) {
		return false
	}
	for index, value := range slice1 {
		if value != slice2[index] {
			return false
		}
	}
	return true
}

// PostgresCaseRepository is the Postgres implementation of CaseRepository.
type PostgresCaseRepository struct {
	db *sql.DB
}

// NewPostgresCaseRepository constructs a new PostgresCaseRepository.
func NewPostgresCaseRepository(db *sql.DB) *PostgresCaseRepository {
	return &PostgresCaseRepository{db: db}
}

// nullIfEmpty returns nil for empty strings so ExecContext writes SQL NULL.
func nullIfEmpty(inputStr string) interface{} {
	if strings.TrimSpace(inputStr) == "" {
		return nil
	}
	return inputStr
}

// insertSnapshot writes a full JSON snapshot into case_snapshots.
func (repo *PostgresCaseRepository) insertSnapshot(context context.Context, transaction *sql.Tx, caseID string) error {
	caseObject, err := repo.GetCase(context, transaction, caseID)
	if err != nil {
		herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
		return fmt.Errorf("insertSnapshot: fetch case: %w", err)
	}
	marshalOptions := protojson.MarshalOptions{EmitUnpopulated: true}
	data, err := marshalOptions.Marshal(caseObject)
	if err != nil {
		herosentry.CaptureException(context, err, herosentry.ErrorTypeInternal)
		return fmt.Errorf("insertSnapshot: marshal case: %w", err)
	}
	timestamp, err := time.Parse(time.RFC3339Nano, caseObject.UpdateTime)
	if err != nil {
		log.Printf("error: failed to parse update time %q for case %s: %v", caseObject.UpdateTime, caseObject.Id, err)
		timestamp = time.Now().UTC()
	}
	_, err = transaction.ExecContext(context, `
		INSERT INTO case_snapshots (case_id, version, snapshot, timestamp, org_id)
		VALUES ($1,$2,$3,$4,$5)
		ON CONFLICT (case_id, version) 
		DO UPDATE SET 
			snapshot = EXCLUDED.snapshot,
			timestamp = EXCLUDED.timestamp
	`, caseObject.Id, caseObject.Version, data, timestamp, caseObject.OrgId)
	if err != nil {
		herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
		return fmt.Errorf("insertSnapshot: exec insert: %w", err)
	}
	return nil
}

// modifyLink is a generic helper function for adding or removing links between cases and other entities.
// It handles the common pattern of inserting or deleting rows in link tables that connect cases
// with other resources like situations, reports, or related cases.
//
// Parameters:
//   - context: The context for the database operation
//   - transaction: The SQL transaction to use
//   - table: The name of the link table (e.g., "case_situations", "case_reports", "related_cases")
//   - column: The name of the column that holds the linked entity ID (e.g., "situation_id", "report_id")
//   - caseID: The ID of the case being modified
//   - link: The ID of the entity to link to or unlink from the case
//   - add: Boolean flag - true to add a link, false to remove a link
//
// Examples:
//   - To link a situation to a case:
//     modifyLink(ctx, tx, "case_situations", "situation_id", "case-123", "situation-456", true)
//
//   - To unlink a report from a case:
//     modifyLink(ctx, tx, "case_reports", "report_id", "case-123", "report-789", false)
//
//   - To link two cases as related:
//     modifyLink(ctx, tx, "related_cases", "related_case_id", "case-123", "case-456", true)
//
// Returns an error if the operation fails or if attempting to remove a link that doesn't exist.

func (repo *PostgresCaseRepository) modifyLink(context context.Context, transaction *sql.Tx, table, column, caseID, link string, add bool) error {
	orgId := cmncontext.GetOrgId(context)
	if add {
		_, err := transaction.ExecContext(context,
			fmt.Sprintf(`INSERT INTO %s (case_id,%s,org_id) VALUES ($1,$2,$3)`, table, column),
			caseID, link, orgId,
		)
		if err != nil {
			herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
			return fmt.Errorf("modifyLink insert on %s: %w", table, err)
		}
		return nil
	}
	result, err := transaction.ExecContext(context,
		fmt.Sprintf(`DELETE FROM %s WHERE case_id=$1 AND %s=$2`, table, column),
		caseID, link,
	)
	if err != nil {
		herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
		return fmt.Errorf("modifyLink delete on %s: %w", table, err)
	}
	if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 {
		err := fmt.Errorf("modifyLink: no link in %s found", table)
		herosentry.CaptureException(context, err, herosentry.ErrorTypeNotFound)
		return err
	}
	return nil
}

// validTableNames is a whitelist of allowed table names for listBySingleFilter
var validTableNames = map[string]bool{
	"case_situations":         true,
	"case_reports":            true,
	"related_cases":           true,
	"case_tags":               true,
	"case_watchers":           true,
	"case_entities":           true,
	"case_asset_associations": true,
}

// validColumnNames is a whitelist of allowed column names for listBySingleFilter
var validColumnNames = map[string]bool{
	"situation_id":    true,
	"report_id":       true,
	"related_case_id": true,
	"tag":             true,
	"asset_id":        true,
	"ref_id":          true,
}

// listBySingleFilter returns paginated case IDs for a single‐column link table.
// This function is used by various list methods to retrieve case IDs that are linked
// to a specific entity (situation, report, asset, etc.) through a join table.
//
// Parameters:
//   - context: The context for the database operation
//   - transaction: The SQL transaction to use
//   - table: The name of the link table (e.g., "case_situations", "case_reports")
//   - column: The name of the column that holds the linked entity ID (e.g., "situation_id")
//   - value: The ID value to filter by (e.g., the specific situation ID)
//   - pageSize: The maximum number of results to return
//   - pageToken: The pagination token (offset) for retrieving subsequent pages
//
// Returns:
//   - []string: A slice of case IDs that match the filter criteria
//   - string: The next page token (empty if there are no more results)
//   - error: Any error that occurred during the operation
//
// Examples:
//
//   - To get cases linked to situation "sit-123" (first page of 10 results):
//     ids, nextToken, err := repo.listBySingleFilter(ctx, tx, "case_situations", "situation_id", "sit-123", 10, "")
//
//   - To get cases linked to report "rep-456" (second page using the nextToken):
//     ids, nextToken, err := repo.listBySingleFilter(ctx, tx, "case_reports", "report_id", "rep-456", 10, "10")
//
//   - To get cases associated with asset "asset-789":
//     ids, nextToken, err := repo.listBySingleFilter(ctx, tx, "case_asset_associations", "asset_id", "asset-789", 20, "")
func (repo *PostgresCaseRepository) listBySingleFilter(context context.Context, transaction *sql.Tx, table, column, value string, pageSize int, pageToken string) ([]string, string, error) {
	if !validTableNames[table] {
		err := fmt.Errorf("invalid table name: %s", table)
		herosentry.CaptureException(context, err, herosentry.ErrorTypeValidation)
		return nil, "", err
	}
	if !validColumnNames[column] {
		err := fmt.Errorf("invalid column name: %s", column)
		herosentry.CaptureException(context, err, herosentry.ErrorTypeValidation)
		return nil, "", err
	}

	offset := 0
	if pageToken != "" {
		if offsetValue, err := strconv.Atoi(pageToken); err == nil {
			offset = offsetValue
		}
	}
	// listBySingleFilterQuery is a constant query template for listBySingleFilter
	const listBySingleFilterQuery = `SELECT case_id FROM %s WHERE %s = $1 ORDER BY case_id DESC LIMIT $2 OFFSET $3`

	// Use the constant query template with validated table and column names
	query := fmt.Sprintf(listBySingleFilterQuery, table, column)
	rows, err := transaction.QueryContext(context, query, value, pageSize, offset)
	if err != nil {
		herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
		return nil, "", fmt.Errorf("listBySingleFilter query %s: %w", table, err)
	}
	defer func() {
		if closeErr := rows.Close(); closeErr != nil {
			log.Printf("warning: failed to close rows: %v", closeErr)
		}
	}()
	var caseIds []string
	for rows.Next() {
		var caseId string
		if err := rows.Scan(&caseId); err != nil {
			herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
			return nil, "", fmt.Errorf("listBySingleFilter scan: %w", err)
		}
		caseIds = append(caseIds, caseId)
	}
	if err := rows.Err(); err != nil {
		herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
		return nil, "", fmt.Errorf("listBySingleFilter rows err: %w", err)
	}
	nextToken := ""
	if len(caseIds) == pageSize {
		nextToken = strconv.Itoa(offset + pageSize)
	}
	return caseIds, nextToken, nil
}

// scanCaseRow handles scanning the master case columns from a SQL row into a Case protobuf object.
// It processes all fields from the cases table, handling nullable fields appropriately.
func scanCaseRow(rows *sql.Rows) (*casespb.Case, error) {
	var (
		caseObject             = &casespb.Case{}
		titleNull              sql.NullString
		descNull               sql.NullString
		typeInt                int32
		statusInt              int32
		relStatusInt           int32
		createTime, updateTime time.Time
		dueDateNull            sql.NullTime
		resolvedNull           sql.NullTime
		closedNull             sql.NullTime
		additionalInfo         []byte
		createdBy              sql.NullString
		updatedBy              sql.NullString
		resourceType           sql.NullString
	)
	if err := rows.Scan(
		&caseObject.Id, &caseObject.OrgId,
		&typeInt, &titleNull, &descNull,
		&statusInt, &caseObject.Priority, &relStatusInt,
		&createTime, &updateTime,
		&dueDateNull, &resolvedNull, &closedNull,
		&caseObject.Etag, &caseObject.Version,
		&additionalInfo,
		&createdBy, &updatedBy,
		&resourceType,
	); err != nil {
		return nil, err
	}

	// Set basic fields
	caseObject.Type = casespb.CaseType(typeInt)
	caseObject.Status = casespb.CaseStatus(statusInt)
	caseObject.ReleaseStatus = casespb.ReleaseStatus(relStatusInt)

	// Handle nullable string fields
	if titleNull.Valid {
		caseObject.Title = titleNull.String
	}
	if descNull.Valid {
		caseObject.Description = descNull.String
	}
	if createdBy.Valid {
		caseObject.CreatedByAssetId = createdBy.String
	}
	if updatedBy.Valid {
		caseObject.UpdatedByAssetId = updatedBy.String
	}
	if resourceType.Valid {
		caseObject.ResourceType = resourceType.String
	}

	// Set required timestamps
	caseObject.CreateTime = createTime.Format(time.RFC3339Nano)
	caseObject.UpdateTime = updateTime.Format(time.RFC3339Nano)

	// Handle status-based timestamps
	caseObject.ResolvedTime = ""
	caseObject.CloseTime = ""
	caseObject.DueDate = ""

	// Only set resolved_time if status is RESOLVED and time is valid
	if caseObject.Status == casespb.CaseStatus_CASE_STATUS_RESOLVED && resolvedNull.Valid {
		caseObject.ResolvedTime = resolvedNull.Time.Format(time.RFC3339Nano)
	}

	// Only set close_time if status is CLOSED and time is valid
	if caseObject.Status == casespb.CaseStatus_CASE_STATUS_CLOSED && closedNull.Valid {
		caseObject.CloseTime = closedNull.Time.Format(time.RFC3339Nano)
	}

	// Handle due_date if valid
	if dueDateNull.Valid {
		caseObject.DueDate = dueDateNull.Time.Format(time.RFC3339Nano)
	}

	// Handle additional info JSON
	var m map[string]interface{}
	if err := json.Unmarshal(additionalInfo, &m); err != nil {
		// Continue even if JSON parsing fails, just log the error
		log.Printf("warning: failed to unmarshal additional_info_json for case %s: %v", caseObject.Id, err)
	} else {
		if s, err := structpb.NewStruct(m); err != nil {
			log.Printf("warning: failed to convert additional_info_json to struct for case %s: %v", caseObject.Id, err)
		} else {
			caseObject.AdditionalInfoJson = s
		}
	}

	return caseObject, nil
}

// addAuditLogEntry adds an entry to the case audit log
func (repo *PostgresCaseRepository) addAuditLogEntry(
	context context.Context,
	transaction *sql.Tx,
	caseId string,
	action casespb.CaseAuditAction,
	fieldPath string,
	oldValue, newValue string,
	note string,
) error {
	callerAssetId, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(context, transaction)
	if err != nil {
		log.Printf("error: failed to get callerAssetId from context: %v", err)
		callerAssetId = "SYSTEM"
	}

	orgId := cmncontext.GetOrgId(context)

	_, err = transaction.ExecContext(context, `
		INSERT INTO case_audit_logs(
			id, case_id, action, actor_asset_id, timestamp,
			field_path, old_value, new_value, note, org_id
		) VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)
	`,
		uuid.New().String(),
		caseId,
		int32(action),
		callerAssetId,
		time.Now().UTC(),
		fieldPath,
		oldValue,
		newValue,
		note,
		orgId,
	)
	if err != nil {
		herosentry.CaptureException(context, err, herosentry.ErrorTypeDatabase)
		return fmt.Errorf("addAuditLogEntry: %w", err)
	}
	return nil
}

// ---------------- Core CRUD ----------------

func (repo *PostgresCaseRepository) CreateCase(context context.Context, transaction *sql.Tx, caseObject *casespb.Case) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.CreateCase")
	defer finishSpan()

	span.SetTag("case.type", caseObject.Type.String())
	span.SetTag("case.priority", fmt.Sprintf("%d", caseObject.Priority))
	span.SetTag("case.status", caseObject.Status.String())

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if caseObject.Id == "" {
			caseObject.Id = uuid.New().String()
		}
		orgId := cmncontext.GetOrgId(spanContext)
		caseObject.OrgId = orgId

		callerAssetId, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
		if err != nil {
			log.Printf("error: failed to get callerAssetId from context: %v", err)
			callerAssetId = ""
		}
		if caseObject.CreatedByAssetId == "" {
			caseObject.CreatedByAssetId = callerAssetId
		}
		caseObject.UpdatedByAssetId = caseObject.CreatedByAssetId
		if caseObject.Version == 0 {
			caseObject.Version = 1
		}
		caseObject.Etag = 1

		// Set base timestamps internally
		currentTime := time.Now().UTC()
		currentTimeStr := currentTime.Format(time.RFC3339Nano)
		caseObject.CreateTime = currentTimeStr
		caseObject.UpdateTime = currentTimeStr

		// Initialize timestamps as null by default
		var resolvedTime interface{}
		var closeTime interface{}
		caseObject.ResolvedTime = ""
		caseObject.CloseTime = ""

		// Only set resolved_time if status is RESOLVED
		if caseObject.Status == casespb.CaseStatus_CASE_STATUS_RESOLVED {
			caseObject.ResolvedTime = currentTimeStr
			resolvedTime = currentTime
		}

		// Only set close_time if status is CLOSED
		if caseObject.Status == casespb.CaseStatus_CASE_STATUS_CLOSED {
			caseObject.CloseTime = currentTimeStr
			closeTime = currentTime
		}

		// Validate that timestamps match status
		if caseObject.Status != casespb.CaseStatus_CASE_STATUS_RESOLVED && caseObject.ResolvedTime != "" {
			err := fmt.Errorf("CreateCase: resolved_time should not be set for non-RESOLVED status")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, err
		}
		if caseObject.Status != casespb.CaseStatus_CASE_STATUS_CLOSED && caseObject.CloseTime != "" {
			err := fmt.Errorf("CreateCase: close_time should not be set for non-CLOSED status")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, err
		}

		// Handle due_date - validate format if provided by user
		var dueDate interface{} = nil
		if caseObject.DueDate != "" {
			// Validate the due date format
			if parsedTime, err := time.Parse(time.RFC3339Nano, caseObject.DueDate); err == nil {
				// Ensure the due date is in the future
				if parsedTime.Before(time.Now().UTC()) {
					err := fmt.Errorf("CreateCase: due_date must be in the future")
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
					return nil, err
				}
				dueDate = parsedTime
			} else {
				err := fmt.Errorf("CreateCase: invalid due_date format (must be RFC3339Nano): %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
				return nil, err
			}
		}

		additionalInfo := []byte("{}")
		if caseObject.AdditionalInfoJson != nil {
			if jsonMap := caseObject.AdditionalInfoJson.AsMap(); jsonMap != nil {
				jsonBytes, err := json.Marshal(jsonMap)
				if err != nil {
					log.Printf("error: failed to marshal additional info for case %s: %v", caseObject.Id, err)
				} else {
					additionalInfo = jsonBytes
				}
			}
		}

		const insertQuery = `
			INSERT INTO cases (
			  id, org_id, type, title, description,
			  status, priority, release_status,
			  create_time, update_time, due_date,
			  resolved_time, close_time,
			  etag, version,
			  additional_info_json,
			  created_by_asset_id, updated_by_asset_id
			) VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18)
		`
		result, err := sessionTx.ExecContext(spanContext, insertQuery,
			caseObject.Id, caseObject.OrgId, int32(caseObject.Type),
			nullIfEmpty(caseObject.Title), nullIfEmpty(caseObject.Description),
			int32(caseObject.Status), caseObject.Priority, int32(caseObject.ReleaseStatus),
			currentTime, currentTime, dueDate, resolvedTime, closeTime,
			caseObject.Etag, caseObject.Version,
			additionalInfo,
			nullIfEmpty(caseObject.CreatedByAssetId), nullIfEmpty(caseObject.UpdatedByAssetId),
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute case insert")
			return nil, fmt.Errorf("CreateCase: exec insert: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return nil, fmt.Errorf("CreateCase: get rows affected: %w", err)
		}
		if rowsAffected == 0 {
			err := fmt.Errorf("CreateCase: no rows affected")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, err
		}

		// -- relationships --

		// situations
		for _, situationId := range caseObject.SituationIds {
			if err := repo.modifyLink(spanContext, sessionTx, "case_situations", "situation_id", caseObject.Id, situationId, true); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to link situation %q", situationId))
				return nil, fmt.Errorf("CreateCase: link situation %q: %w", situationId, err)
			}
		}

		// reports
		for _, reportId := range caseObject.ReportIds {
			if err := repo.modifyLink(spanContext, sessionTx, "case_reports", "report_id", caseObject.Id, reportId, true); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to link report %q", reportId))
				return nil, fmt.Errorf("CreateCase: link report %q: %w", reportId, err)
			}
		}

		// related cases
		for _, relatedCaseId := range caseObject.RelatedCaseIds {
			if err := repo.modifyLink(spanContext, sessionTx, "related_cases", "related_case_id", caseObject.Id, relatedCaseId, true); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to link related case %q", relatedCaseId))
				return nil, fmt.Errorf("CreateCase: link related case %q: %w", relatedCaseId, err)
			}
		}

		// entity refs
		for _, entityRef := range caseObject.EntityRefs {
			if _, err := sessionTx.ExecContext(
				spanContext,
				`INSERT INTO case_entities(case_id, ref_id, ref_type, ref_version, ref_display_name, relation_type, org_id)
                 VALUES($1,$2,$3,$4,$5,$6,$7)`,
				caseObject.Id, entityRef.Id, entityRef.Type, entityRef.Version, entityRef.DisplayName, entityRef.RelationType, caseObject.OrgId,
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add entity_ref %q", entityRef.Id))
				return nil, fmt.Errorf("CreateCase: add entity_ref %q: %w", entityRef.Id, err)
			}
		}

		// asset associations
		for _, assetAssoc := range caseObject.AssetAssociations {
			if assetAssoc.Id == "" {
				assetAssoc.Id = uuid.New().String()
			}
			if assetAssoc.AssignedAt == "" {
				assetAssoc.AssignedAt = currentTimeStr
			}
			if _, err := sessionTx.ExecContext(
				spanContext,
				`INSERT INTO case_asset_associations(
                     id, case_id, asset_id, association_type,
                     assigned_at, notes, assigner_asset_id, org_id
                 ) VALUES($1,$2,$3,$4,$5,$6,$7,$8)`,
				assetAssoc.Id, caseObject.Id, assetAssoc.AssetId, int32(assetAssoc.AssociationType),
				assetAssoc.AssignedAt, assetAssoc.Notes, assetAssoc.AssignerAssetId, caseObject.OrgId,
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add asset_assoc %q", assetAssoc.Id))
				return nil, fmt.Errorf("CreateCase: add asset_assoc %q: %w", assetAssoc.Id, err)
			}
		}

		// watchers
		for _, watcherAssetId := range caseObject.WatcherAssetIds {
			if err := repo.modifyLink(spanContext, sessionTx, "case_watchers", "asset_id", caseObject.Id, watcherAssetId, true); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add watcher %q", watcherAssetId))
				return nil, fmt.Errorf("CreateCase: add watcher %q: %w", watcherAssetId, err)
			}
		}

		// tags
		for _, tagValue := range caseObject.Tags {
			if _, err := sessionTx.ExecContext(
				spanContext,
				`INSERT INTO case_tags(case_id, tag, org_id) VALUES($1,$2,$3)`,
				caseObject.Id, tagValue, caseObject.OrgId,
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add tag %q", tagValue))
				return nil, fmt.Errorf("CreateCase: add tag %q: %w", tagValue, err)
			}
		}

		// -- snapshot last, after all FK rows are in place --
		if err := repo.insertSnapshot(spanContext, sessionTx, caseObject.Id); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert snapshot")
			return nil, fmt.Errorf("CreateCase: %w", err)
		}

		// Add audit log entry for case creation
		if err := repo.addAuditLogEntry(
			spanContext,
			sessionTx,
			caseObject.Id,
			casespb.CaseAuditAction_CASE_AUDIT_ACTION_CREATE,
			"",
			"",
			"",
			"Case created",
		); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add audit log")
			return nil, fmt.Errorf("CreateCase: add audit log: %w", err)
		}

		return caseObject, nil
	})
}

// GetCase retrieves a case from the database
func (repo *PostgresCaseRepository) GetCase(context context.Context, transaction *sql.Tx, caseId string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.GetCase")
	defer finishSpan()

	span.SetTag("case.id", caseId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		const selectQuery = `
			SELECT
			  id, org_id, type, title, description,
			  status, priority, release_status,
			  create_time, update_time,
			  due_date, resolved_time, close_time,
			  etag, version,
			  additional_info_json,
			  created_by_asset_id, updated_by_asset_id,
			  resource_type
			FROM cases
			WHERE id=$1
		`
		queryRow := sessionTx.QueryRowContext(spanContext, selectQuery, caseId)

		var (
			caseObject             = &casespb.Case{}
			titleNull              sql.NullString
			descNull               sql.NullString
			typeInt                int32
			statusInt              int32
			relStatusInt           int32
			createTime, updateTime time.Time
			dueDateNull            sql.NullTime
			resolvedNull           sql.NullTime
			closedNull             sql.NullTime
			additionalInfo         []byte
			createdBy              sql.NullString
			updatedBy              sql.NullString
			resourceType           sql.NullString
		)
		if err := queryRow.Scan(
			&caseObject.Id, &caseObject.OrgId,
			&typeInt, &titleNull, &descNull,
			&statusInt, &caseObject.Priority, &relStatusInt,
			&createTime, &updateTime,
			&dueDateNull, &resolvedNull, &closedNull,
			&caseObject.Etag, &caseObject.Version,
			&additionalInfo,
			&createdBy, &updatedBy,
			&resourceType,
		); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, ErrCaseNotFound
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan case row")
			return nil, fmt.Errorf("GetCase: scan row: %w", err)
		}
		caseObject.Type = casespb.CaseType(typeInt)
		caseObject.Status = casespb.CaseStatus(statusInt)
		caseObject.ReleaseStatus = casespb.ReleaseStatus(relStatusInt)
		if titleNull.Valid {
			caseObject.Title = titleNull.String
		}
		if descNull.Valid {
			caseObject.Description = descNull.String
		}
		caseObject.CreateTime = createTime.Format(time.RFC3339Nano)
		caseObject.UpdateTime = updateTime.Format(time.RFC3339Nano)
		if dueDateNull.Valid {
			caseObject.DueDate = dueDateNull.Time.Format(time.RFC3339Nano)
		}
		if resolvedNull.Valid {
			caseObject.ResolvedTime = resolvedNull.Time.Format(time.RFC3339Nano)
		}
		if closedNull.Valid {
			caseObject.CloseTime = closedNull.Time.Format(time.RFC3339Nano)
		}
		if createdBy.Valid {
			caseObject.CreatedByAssetId = createdBy.String
		}
		if updatedBy.Valid {
			caseObject.UpdatedByAssetId = updatedBy.String
		}
		if resourceType.Valid {
			caseObject.ResourceType = resourceType.String
		}

		var jsonMap map[string]interface{}
		if err := json.Unmarshal(additionalInfo, &jsonMap); err != nil {
			log.Printf("error: failed to unmarshal additional_info_json for case %s: %v", caseObject.Id, err)
			jsonMap = make(map[string]interface{})
		}
		if structObj, err := structpb.NewStruct(jsonMap); err != nil {
			log.Printf("error: failed to convert additional_info_json to struct for case %s: %v", caseObject.Id, err)
		} else {
			caseObject.AdditionalInfoJson = structObj
		}

		// Load all relationships using optimized batch loading (even for single case)
		// This uses the same efficient pattern as SearchCases for consistency
		tagsMap, situationsMap, reportsMap, relatedCasesMap, watchersMap, err := repo.batchLoadCaseRelationships(spanContext, sessionTx, []string{caseId})
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("GetCase: load relationships: %w", err)
		}

		// Extract relationships for this specific case
		caseObject.Tags = tagsMap[caseId]
		caseObject.SituationIds = situationsMap[caseId]
		caseObject.ReportIds = reportsMap[caseId]
		caseObject.RelatedCaseIds = relatedCasesMap[caseId]
		caseObject.WatcherAssetIds = watchersMap[caseId]

		// Load entity_refs and asset_associations using batch loading pattern (even for single case)
		entityRefsMap, assetAssociationsMap, err := repo.batchLoadCaseEntityRefsAndAssetAssociations(spanContext, sessionTx, []string{caseId})
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("GetCase: load entity refs and asset associations: %w", err)
		}

		// Extract relationships for this specific case
		caseObject.EntityRefs = entityRefsMap[caseId]
		caseObject.AssetAssociations = assetAssociationsMap[caseId]

		// Watchers are already loaded by batchLoadCaseRelationships above

		// Load updates with batch loading for file attachments
		{
			rows, err := sessionTx.QueryContext(spanContext, `
				SELECT id, message, event_time, update_source, updater_id,
					event_type, display_name, data
				FROM case_updates
				WHERE case_id=$1 ORDER BY event_time ASC
			`, caseId)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("GetCase updates: %w", err)
			}
			defer func() {
				if closeErr := rows.Close(); closeErr != nil {
					log.Printf("error: failed to close database rows: %v, context: %s", closeErr, context)
				}
			}()

			// First pass: collect case update IDs and build update entries
			var updates []*casespb.CaseUpdateEntry
			var updateIds []int
			for rows.Next() {
				var (
					updateId                                                    int
					updateMessage, eventTime, updaterId, eventType, displayName string
					updateSource                                                int32
					dataBytes                                                   []byte
				)
				if err := rows.Scan(&updateId, &updateMessage, &eventTime, &updateSource, &updaterId, &eventType, &displayName, &dataBytes); err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
					return nil, fmt.Errorf("GetCase updates scan: %w", err)
				}
				var dataStruct *structpb.Struct
				if len(dataBytes) > 0 {
					var m map[string]interface{}
					if err := json.Unmarshal(dataBytes, &m); err == nil {
						dataStruct, _ = structpb.NewStruct(m)
					}
				}

				updateIds = append(updateIds, updateId)
				updates = append(updates, &casespb.CaseUpdateEntry{
					Message:      updateMessage,
					EventTime:    eventTime,
					UpdateSource: situationpb.UpdateSource(updateSource),
					UpdaterId:    updaterId,
					EventType:    eventType,
					DisplayName:  displayName,
					Data:         dataStruct,
					// FileAttachments will be populated below
				})
			}
			if err := rows.Err(); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("GetCase updates rows err: %w", err)
			}

			// Second pass: batch load file attachments for all updates
			fileAttachmentsMap, err := repo.batchLoadCaseUpdateFileAttachments(spanContext, sessionTx, updateIds)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("GetCase batch load file attachments: %w", err)
			}

			// Third pass: assign file attachments to updates
			for i, updateId := range updateIds {
				updates[i].FileAttachments = fileAttachmentsMap[updateId]
			}

			caseObject.Updates = updates
		}

		// Load status history
		{
			rows, err := sessionTx.QueryContext(spanContext, `
				SELECT timestamp, new_status, previous_status, note, updater_id, update_source
				FROM case_status_updates WHERE case_id=$1 ORDER BY timestamp ASC
			`, caseId)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("GetCase status history: %w", err)
			}
			defer func() {
				if closeErr := rows.Close(); closeErr != nil {
					log.Printf("error: failed to close database rows: %v, context: %s", closeErr, context)
				}
			}()
			for rows.Next() {
				var (
					statusTimestamp, statusNote, updaterId  string
					newStatus, previousStatus, updateSource int32
				)
				if err := rows.Scan(&statusTimestamp, &newStatus, &previousStatus, &statusNote, &updaterId, &updateSource); err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
					return nil, fmt.Errorf("GetCase status history scan: %w", err)
				}
				caseObject.StatusUpdates = append(caseObject.StatusUpdates, &casespb.CaseStatusUpdateEntry{
					Timestamp:      statusTimestamp,
					NewStatus:      casespb.CaseStatus(newStatus),
					PreviousStatus: casespb.CaseStatus(previousStatus),
					Note:           statusNote,
					UpdaterId:      updaterId,
					UpdateSource:   situationpb.UpdateSource(updateSource),
				})
			}
			if err := rows.Err(); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("GetCase status history rows err: %w", err)
			}
		}

		return caseObject, nil
	})
}

// UpdateCase updates a case in the database.
func (repo *PostgresCaseRepository) UpdateCase(context context.Context, transaction *sql.Tx, request *casespb.UpdateCaseRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.UpdateCase")
	defer finishSpan()

	inputCase := request.GetCase_()
	if inputCase != nil {
		span.SetTag("case.id", inputCase.Id)
		span.SetTag("case.status", inputCase.Status.String())
	}

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if inputCase == nil {
			return nil, errors.New("UpdateCase: missing case payload")
		}

		// Get the existing case to compare changes
		existingCase, err := repo.GetCase(spanContext, sessionTx, inputCase.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get existing case")
			return nil, fmt.Errorf("UpdateCase: get existing case: %w", err)
		}

		// Only increment version if status changes
		if inputCase.Status != existingCase.Status {
			inputCase.Version++
		}

		callerAssetId, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
		if err != nil {
			log.Printf("error: failed to get callerAssetId from context: %v", err)
			callerAssetId = ""
		}
		inputCase.UpdatedByAssetId = callerAssetId

		// Update timestamps internally
		currentTime := time.Now().UTC()
		currentTimeStr := currentTime.Format(time.RFC3339Nano)
		inputCase.UpdateTime = currentTimeStr

		// Handle status-based timestamps
		var resolvedTime interface{}
		var closeTime interface{}

		// Handle resolved_time
		if inputCase.Status == casespb.CaseStatus_CASE_STATUS_RESOLVED {
			if existingCase.Status != casespb.CaseStatus_CASE_STATUS_RESOLVED {
				// Status changing to RESOLVED - set new resolved_time
				resolvedTime = currentTime
				inputCase.ResolvedTime = currentTimeStr
			} else if existingCase.ResolvedTime != "" {
				// Keep existing resolved_time
				if parsedTime, err := time.Parse(time.RFC3339Nano, existingCase.ResolvedTime); err == nil {
					resolvedTime = parsedTime
				}
			}
		} else {
			// Clear resolved_time if status is not RESOLVED
			inputCase.ResolvedTime = ""
		}

		// Handle close_time
		if inputCase.Status == casespb.CaseStatus_CASE_STATUS_CLOSED {
			if existingCase.Status != casespb.CaseStatus_CASE_STATUS_CLOSED {
				// Status changing to CLOSED - set new close_time
				closeTime = currentTime
				inputCase.CloseTime = currentTimeStr
			} else if existingCase.CloseTime != "" {
				// Keep existing close_time
				if parsedTime, err := time.Parse(time.RFC3339Nano, existingCase.CloseTime); err == nil {
					closeTime = parsedTime
				}
			}
		} else {
			// Clear close_time if status is not CLOSED
			inputCase.CloseTime = ""
		}

		// Handle due_date - validate format if provided by user
		var dueDate interface{} = nil
		switch {
		case inputCase.DueDate != "":
			// Validate the due date format
			if parsedTime, err := time.Parse(time.RFC3339Nano, inputCase.DueDate); err == nil {
				// Ensure the due date is in the future
				if parsedTime.Before(time.Now().UTC()) {
					err := fmt.Errorf("UpdateCase: due_date must be in the future")
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
					return nil, err
				}
				dueDate = parsedTime
			} else {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
				return nil, fmt.Errorf("UpdateCase: invalid due_date format (must be RFC3339Nano): %w", err)
			}
		case inputCase.DueDate == "" && existingCase.DueDate != "":
			// If due date is explicitly set to empty string, clear it
			dueDate = nil
		case existingCase.DueDate != "":
			// Keep existing due date if not being updated
			if parsedTime, err := time.Parse(time.RFC3339Nano, existingCase.DueDate); err == nil {
				dueDate = parsedTime
			}
		}

		additionalInfo := []byte("{}")
		if inputCase.AdditionalInfoJson != nil {
			if jsonMap := inputCase.AdditionalInfoJson.AsMap(); jsonMap != nil {
				jsonBytes, err := json.Marshal(jsonMap)
				if err != nil {
					log.Printf("error: failed to marshal additional info for case %s: %v", inputCase.Id, err)
				} else {
					additionalInfo = jsonBytes
				}
			}
		}

		const updateQuery = `
			UPDATE cases SET
			  type=$1, title=$2, description=$3,
			  status=$4, priority=$5, release_status=$6,
			  update_time=$7, due_date=$8, resolved_time=$9, close_time=$10,
			  additional_info_json=$11,
			  updated_by_asset_id=$12,
			  version=$13,
			  etag=etag+1
			WHERE id=$14 AND etag=$15
		`
		result, err := sessionTx.ExecContext(spanContext, updateQuery,
			int32(inputCase.Type), nullIfEmpty(inputCase.Title), nullIfEmpty(inputCase.Description),
			int32(inputCase.Status), inputCase.Priority, int32(inputCase.ReleaseStatus),
			currentTime, dueDate, resolvedTime, closeTime,
			additionalInfo,
			nullIfEmpty(inputCase.UpdatedByAssetId),
			inputCase.Version,
			inputCase.Id, inputCase.Etag,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute case update")
			return nil, fmt.Errorf("UpdateCase: exec update: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return nil, fmt.Errorf("UpdateCase: get rows affected: %w", err)
		}
		if rowsAffected == 0 {
			err := fmt.Errorf("UpdateCase: optimistic lock failed (etag=%d)", inputCase.Etag)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeConflict)
			return nil, err
		}

		// Take snapshot on every update, regardless of version change
		if err := repo.insertSnapshot(spanContext, sessionTx, inputCase.Id); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert snapshot")
			return nil, fmt.Errorf("UpdateCase: %w", err)
		}

		// Add audit log entries for changed fields
		if existingCase.Title != inputCase.Title {
			if err := repo.addAuditLogEntry(
				spanContext,
				sessionTx,
				inputCase.Id,
				casespb.CaseAuditAction_CASE_AUDIT_ACTION_UPDATE,
				"title",
				existingCase.Title,
				inputCase.Title,
				"Title updated",
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("UpdateCase: add audit log for title: %w", err)
			}
		}

		if existingCase.Description != inputCase.Description {
			if err := repo.addAuditLogEntry(
				spanContext,
				sessionTx,
				inputCase.Id,
				casespb.CaseAuditAction_CASE_AUDIT_ACTION_UPDATE,
				"description",
				existingCase.Description,
				inputCase.Description,
				"Description updated",
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("UpdateCase: add audit log for description: %w", err)
			}
		}

		if existingCase.Status != inputCase.Status {
			if err := repo.addAuditLogEntry(
				spanContext,
				sessionTx,
				inputCase.Id,
				casespb.CaseAuditAction_CASE_AUDIT_ACTION_STATUS,
				"status",
				existingCase.Status.String(),
				inputCase.Status.String(),
				"Status updated",
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("UpdateCase: add audit log for status: %w", err)
			}
		}

		if existingCase.Priority != inputCase.Priority {
			if err := repo.addAuditLogEntry(
				spanContext,
				sessionTx,
				inputCase.Id,
				casespb.CaseAuditAction_CASE_AUDIT_ACTION_UPDATE,
				"priority",
				strconv.Itoa(int(existingCase.Priority)),
				strconv.Itoa(int(inputCase.Priority)),
				"Priority updated",
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("UpdateCase: add audit log for priority: %w", err)
			}
		}

		if existingCase.ReleaseStatus != inputCase.ReleaseStatus {
			if err := repo.addAuditLogEntry(
				spanContext,
				sessionTx,
				inputCase.Id,
				casespb.CaseAuditAction_CASE_AUDIT_ACTION_UPDATE,
				"release_status",
				existingCase.ReleaseStatus.String(),
				inputCase.ReleaseStatus.String(),
				"Release status updated",
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("UpdateCase: add audit log for release status: %w", err)
			}
		}

		// Tags - clear existing and add new ones
		if inputCase.Tags != nil && !slicesEqual(existingCase.Tags, inputCase.Tags) {
			// Delete existing tags
			if _, err := sessionTx.ExecContext(spanContext, `DELETE FROM case_tags WHERE case_id=$1`, inputCase.Id); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("UpdateCase: delete existing tags: %w", err)
			}
			// Add new tags
			orgId := cmncontext.GetOrgId(spanContext)
			for _, tagValue := range inputCase.Tags {
				if _, err := sessionTx.ExecContext(
					spanContext,
					`INSERT INTO case_tags(case_id, tag, org_id) VALUES($1,$2,$3) ON CONFLICT (case_id, tag) DO NOTHING`,
					inputCase.Id, tagValue, orgId,
				); err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
					return nil, fmt.Errorf("UpdateCase: add tag %q: %w", tagValue, err)
				}
			}
			// Add audit log for tags update
			oldTagsJson, err := json.Marshal(existingCase.Tags)
			if err != nil {
				log.Printf("error: failed to marshal existingCase.Tags for case %s: %v", inputCase.Id, err)
				// Continue with an empty string for oldTagsJson in the audit log
				oldTagsJson = []byte{}
			}
			newTagsJson, err := json.Marshal(inputCase.Tags)
			if err != nil {
				log.Printf("error: failed to marshal inputCase.Tags for case %s: %v", inputCase.Id, err)
				// Continue with an empty string for newTagsJson in the audit log
				newTagsJson = []byte{}
			}
			if err := repo.addAuditLogEntry(
				spanContext,
				sessionTx,
				inputCase.Id,
				casespb.CaseAuditAction_CASE_AUDIT_ACTION_UPDATE,
				"tags",
				string(oldTagsJson),
				string(newTagsJson),
				"Tags updated",
			); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add audit log for tags")
				return nil, fmt.Errorf("UpdateCase: add audit log for tags: %w", err)
			}
		}

		return repo.GetCase(spanContext, sessionTx, inputCase.Id)
	})
}

func (repo *PostgresCaseRepository) DeleteCase(context context.Context, transaction *sql.Tx, caseId string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.DeleteCase")
	defer finishSpan()

	span.SetTag("case.id", caseId)

	return database.WithSessionErr(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) error {
		// Delete the case - this will CASCADE delete all related data
		result, err := sessionTx.ExecContext(spanContext, `DELETE FROM cases WHERE id=$1`, caseId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute case delete")
			return fmt.Errorf("DeleteCase: exec delete: %w", err)
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return fmt.Errorf("DeleteCase: get rows affected: %w", err)
		}
		if rowsAffected == 0 {
			return ErrCaseNotFound
		}
		return nil
	})
}

// ---------------- Listing ----------------

func (repo *PostgresCaseRepository) ListCases(context context.Context, transaction *sql.Tx, request *casespb.ListCasesRequest) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListCases")
	defer finishSpan()

	span.SetTag("list.page_size", fmt.Sprintf("%d", request.PageSize))
	if request.Status != casespb.CaseStatus_CASE_STATUS_UNSPECIFIED {
		span.SetTag("filter.status", request.Status.String())
	}
	if request.Type != casespb.CaseType_CASE_TYPE_UNSPECIFIED {
		span.SetTag("filter.type", request.Type.String())
	}

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.ListCasesResponse, error) {
		limit := int(request.PageSize)
		if limit <= 0 {
			limit = defaultPageSize
		}
		offset := 0
		if request.PageToken != "" {
			if offsetValue, err := strconv.Atoi(request.PageToken); err != nil {
				log.Printf("error: failed to parse page token %q: %v", request.PageToken, err)
			} else {
				offset = offsetValue
			}
		}

		var whereConditions []string
		var queryArgs []interface{}
		paramIndex := 1

		if request.Status != casespb.CaseStatus_CASE_STATUS_UNSPECIFIED {
			whereConditions = append(whereConditions, fmt.Sprintf("status=$%d", paramIndex))
			queryArgs = append(queryArgs, int32(request.Status))
			paramIndex++
		}
		if request.Type != casespb.CaseType_CASE_TYPE_UNSPECIFIED {
			whereConditions = append(whereConditions, fmt.Sprintf("type=$%d", paramIndex))
			queryArgs = append(queryArgs, int32(request.Type))
			paramIndex++
		}
		if request.StakeholderAssetId != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("id IN (SELECT case_id FROM case_asset_associations WHERE asset_id=$%d)", paramIndex))
			queryArgs = append(queryArgs, request.StakeholderAssetId)
			paramIndex++
		}
		if request.SituationId != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("id IN (SELECT case_id FROM case_situations WHERE situation_id=$%d)", paramIndex))
			queryArgs = append(queryArgs, request.SituationId)
			paramIndex++
		}
		if request.ReportId != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("id IN (SELECT case_id FROM case_reports WHERE report_id=$%d)", paramIndex))
			queryArgs = append(queryArgs, request.ReportId)
			paramIndex++
		}
		if len(request.Tags) > 0 {
			whereConditions = append(whereConditions, fmt.Sprintf(
				"EXISTS (SELECT 1 FROM case_tags WHERE case_tags.case_id=cases.id AND case_tags.tag=ANY($%d))",
				paramIndex))
			queryArgs = append(queryArgs, pq.Array(request.Tags))
			paramIndex++
		}

		var stringBuilder strings.Builder
		stringBuilder.WriteString("SELECT id FROM cases")
		if len(whereConditions) > 0 {
			stringBuilder.WriteString(" WHERE " + strings.Join(whereConditions, " AND "))
		}

		orderBy := "update_time DESC"
		if request.OrderBy != "" {
			orderParts := strings.Fields(request.OrderBy)
			column := orderParts[0]
			direction := "DESC"
			if len(orderParts) > 1 {
				if dirValue := strings.ToUpper(orderParts[1]); dirValue == "ASC" || dirValue == "DESC" {
					direction = dirValue
				}
			}
			if _, ok := allowedOrderBy[column]; ok {
				orderBy = fmt.Sprintf("%s %s", column, direction)
			}
		}
		stringBuilder.WriteString(" ORDER BY " + orderBy)

		stringBuilder.WriteString(fmt.Sprintf(" LIMIT $%d OFFSET $%d", paramIndex, paramIndex+1))
		queryArgs = append(queryArgs, limit, offset)

		rows, err := sessionTx.QueryContext(spanContext, stringBuilder.String(), queryArgs...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("ListCases: query failed: %w", err)
		}
		defer func() {
			if closeErr := rows.Close(); closeErr != nil {
				log.Printf("error: failed to close database rows: %v, context: %s", closeErr, context)
			}
		}()

		var caseIds []string
		for rows.Next() {
			var caseId string
			if err := rows.Scan(&caseId); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("ListCases: scan id: %w", err)
			}
			caseIds = append(caseIds, caseId)
		}
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("ListCases: rows iteration: %w", err)
		}

		if len(caseIds) == 0 {
			return &casespb.ListCasesResponse{}, nil
		}

		// Use BatchGetCases to get complete case objects with all relationships
		resultCases, err := repo.BatchGetCases(spanContext, sessionTx, caseIds)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("ListCases: batch get cases: %w", err)
		}

		// Filter out nil cases (cases that weren't found)
		var filteredCases []*casespb.Case
		for _, caseObj := range resultCases {
			if caseObj != nil {
				filteredCases = append(filteredCases, caseObj)
			}
		}
		resultCases = filteredCases

		nextToken := ""
		if len(caseIds) == limit {
			nextToken = strconv.Itoa(offset + limit)
		}
		return &casespb.ListCasesResponse{Cases: resultCases, NextPageToken: nextToken}, nil
	})
}

func (repo *PostgresCaseRepository) BatchGetCases(context context.Context, transaction *sql.Tx, caseIds []string) ([]*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.BatchGetCases")
	defer finishSpan()

	span.SetTag("batch.size", fmt.Sprintf("%d", len(caseIds)))

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) ([]*casespb.Case, error) {
		if len(caseIds) == 0 {
			return []*casespb.Case{}, nil
		}

		// Step 1: Load main case data
		rows, err := sessionTx.QueryContext(spanContext,
			`SELECT id, org_id, type, title, description, status, priority, release_status,
			        create_time, update_time, due_date, resolved_time, close_time,
			        etag, version, additional_info_json, created_by_asset_id, updated_by_asset_id,
			        resource_type
			 FROM cases WHERE id = ANY($1)`,
			pq.Array(caseIds),
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query cases batch")
			return nil, fmt.Errorf("BatchGetCases: query failed: %w", err)
		}
		defer func() {
			if closeErr := rows.Close(); closeErr != nil {
				log.Printf("warning: failed to close rows: %v", closeErr)
			}
		}()

		// Create map for efficient lookup and result slice for maintaining order
		casesMap := make(map[string]*casespb.Case)
		var foundCaseIds []string

		for rows.Next() {
			caseObject, err := scanCaseRow(rows)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("BatchGetCases: scan case: %w", err)
			}
			casesMap[caseObject.Id] = caseObject
			foundCaseIds = append(foundCaseIds, caseObject.Id)
		}
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("BatchGetCases: rows iteration: %w", err)
		}

		// Step 2: Batch load all relationships for found cases
		if len(foundCaseIds) > 0 {
			tagsMap, situationsMap, reportsMap, relatedCasesMap, watchersMap, err := repo.batchLoadCaseRelationships(spanContext, sessionTx, foundCaseIds)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("BatchGetCases: load relationships: %w", err)
			}

			// Step 3: Assign relationships to cases
			for _, caseId := range foundCaseIds {
				if caseObj, exists := casesMap[caseId]; exists {
					caseObj.Tags = tagsMap[caseId]
					caseObj.SituationIds = situationsMap[caseId]
					caseObj.ReportIds = reportsMap[caseId]
					caseObj.RelatedCaseIds = relatedCasesMap[caseId]
					caseObj.WatcherAssetIds = watchersMap[caseId]
				}
			}

			// Step 4: Load entity_refs and asset_associations for all cases using batch loading
			entityRefsMap, assetAssociationsMap, err := repo.batchLoadCaseEntityRefsAndAssetAssociations(spanContext, sessionTx, foundCaseIds)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("BatchGetCases: load entity refs and asset associations: %w", err)
			}

			// Step 5: Assign entity refs and asset associations to cases
			for _, caseId := range foundCaseIds {
				if caseObj, exists := casesMap[caseId]; exists {
					caseObj.EntityRefs = entityRefsMap[caseId]
					caseObj.AssetAssociations = assetAssociationsMap[caseId]
				}
			}

			// Step 6: Load case updates and status history for all cases using batch loading
			updatesMap, statusHistoryMap, err := repo.batchLoadCaseUpdatesAndStatusHistory(spanContext, sessionTx, foundCaseIds)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("BatchGetCases: load updates and status history: %w", err)
			}

			// Step 7: Assign case updates and status history to cases
			for _, caseId := range foundCaseIds {
				if caseObj, exists := casesMap[caseId]; exists {
					caseObj.Updates = updatesMap[caseId]
					caseObj.StatusUpdates = statusHistoryMap[caseId]
				}
			}
		}

		// Step 8: Build result slice maintaining original order, with nil for missing cases
		resultCases := make([]*casespb.Case, len(caseIds))
		for i, caseId := range caseIds {
			if caseObj, exists := casesMap[caseId]; exists {
				resultCases[i] = caseObj
			} else {
				// Case not found - return nil in that position
				resultCases[i] = nil
			}
		}

		return resultCases, nil
	})
}

// ---------------- Relationship mutators ----------------

func (repo *PostgresCaseRepository) AddSituation(context context.Context, transaction *sql.Tx, caseId, situationId string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.AddSituation")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("situation.id", situationId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if err := repo.modifyLink(spanContext, sessionTx, "case_situations", "situation_id", caseId, situationId, true); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add situation %s to case %s", situationId, caseId))
			return nil, fmt.Errorf("AddSituation: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) RemoveSituation(context context.Context, transaction *sql.Tx, caseId, situationId string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.RemoveSituation")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("situation.id", situationId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if err := repo.modifyLink(spanContext, sessionTx, "case_situations", "situation_id", caseId, situationId, false); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to remove situation %s from case %s", situationId, caseId))
			return nil, fmt.Errorf("RemoveSituation: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) AddReport(context context.Context, transaction *sql.Tx, caseId, reportId string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.AddReport")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("report.id", reportId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if err := repo.modifyLink(spanContext, sessionTx, "case_reports", "report_id", caseId, reportId, true); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add report %s to case %s", reportId, caseId))
			return nil, fmt.Errorf("AddReport: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) RemoveReport(context context.Context, transaction *sql.Tx, caseId, reportId string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.RemoveReport")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("report.id", reportId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if err := repo.modifyLink(spanContext, sessionTx, "case_reports", "report_id", caseId, reportId, false); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to remove report %s from case %s", reportId, caseId))
			return nil, fmt.Errorf("RemoveReport: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) AddEntityRef(context context.Context, transaction *sql.Tx, caseId string, entityRef *entitypb.Reference) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.AddEntityRef")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("entity.ref_id", entityRef.Id)
	span.SetTag("entity.ref_type", entityRef.Type)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		orgId := cmncontext.GetOrgId(spanContext)
		_, err := sessionTx.ExecContext(spanContext, `
			INSERT INTO case_entities(case_id, ref_id, ref_type, ref_version, ref_display_name, relation_type, org_id)
			VALUES($1,$2,$3,$4,$5,$6,$7)
		`, caseId, entityRef.Id, entityRef.Type, entityRef.Version, entityRef.DisplayName, entityRef.RelationType, orgId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add entity ref %s to case %s", entityRef.Id, caseId))
			return nil, fmt.Errorf("AddEntityRef: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) RemoveEntityRef(context context.Context, transaction *sql.Tx, caseId string, entityRef *entitypb.Reference) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.RemoveEntityRef")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("entity.ref_id", entityRef.Id)
	span.SetTag("entity.ref_type", entityRef.Type)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		result, err := sessionTx.ExecContext(spanContext, `
			DELETE FROM case_entities WHERE case_id=$1 AND ref_id=$2 AND ref_type=$3
		`, caseId, entityRef.Id, entityRef.Type)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to remove entity ref %s from case %s", entityRef.Id, caseId))
			return nil, fmt.Errorf("RemoveEntityRef: %w", err)
		}
		if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 {
			return nil, errors.New("entity ref not found")
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) LinkRelatedCase(context context.Context, transaction *sql.Tx, caseId, relatedCaseId string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.LinkRelatedCase")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("related_case.id", relatedCaseId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if err := repo.modifyLink(spanContext, sessionTx, "related_cases", "related_case_id", caseId, relatedCaseId, true); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to link related case %s to case %s", relatedCaseId, caseId))
			return nil, fmt.Errorf("LinkRelatedCase: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) UnlinkRelatedCase(context context.Context, transaction *sql.Tx, caseId, relatedCaseId string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.UnlinkRelatedCase")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("related_case.id", relatedCaseId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if err := repo.modifyLink(spanContext, sessionTx, "related_cases", "related_case_id", caseId, relatedCaseId, false); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to unlink related case %s from case %s", relatedCaseId, caseId))
			return nil, fmt.Errorf("UnlinkRelatedCase: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

// ---------------- Asset associations ----------------

func (repo *PostgresCaseRepository) AssociateAsset(context context.Context, transaction *sql.Tx, caseId string, association *casespb.CaseAssetAssociation) (*casespb.CaseAssetAssociation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.AssociateAsset")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("asset.id", association.AssetId)
	span.SetTag("association.type", association.AssociationType.String())

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.CaseAssetAssociation, error) {
		orgId := cmncontext.GetOrgId(spanContext)
		if association.Id == "" {
			association.Id = uuid.New().String()
		}
		if association.AssignedAt == "" {
			association.AssignedAt = time.Now().Format(time.RFC3339Nano)
		}
		_, err := sessionTx.ExecContext(spanContext, `
			INSERT INTO case_asset_associations(
			  id, case_id, asset_id, association_type,
			  assigned_at, notes, assigner_asset_id, org_id
			) VALUES($1,$2,$3,$4,$5,$6,$7,$8)
		`, association.Id, caseId, association.AssetId, int32(association.AssociationType),
			association.AssignedAt, association.Notes, association.AssignerAssetId, orgId,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to associate asset %s with case %s", association.AssetId, caseId))
			return nil, fmt.Errorf("AssociateAsset: %w", err)
		}
		return association, nil
	})
}

func (repo *PostgresCaseRepository) UpdateAssetAssociation(context context.Context, transaction *sql.Tx, caseId string, association *casespb.CaseAssetAssociation) (*casespb.CaseAssetAssociation, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.UpdateAssetAssociation")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("association.id", association.Id)
	span.SetTag("asset.id", association.AssetId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.CaseAssetAssociation, error) {
		result, err := sessionTx.ExecContext(spanContext, `
			UPDATE case_asset_associations SET
			  asset_id=$1, association_type=$2,
			  notes=$3, assigner_asset_id=$4
			WHERE id=$5 AND case_id=$6
		`, association.AssetId, int32(association.AssociationType),
			association.Notes, association.AssignerAssetId,
			association.Id, caseId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to update asset association %s for case %s", association.Id, caseId))
			return nil, fmt.Errorf("UpdateAssetAssociation: %w", err)
		}
		if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 {
			return nil, ErrAssociationNotFound
		}
		return association, nil
	})
}

func (repo *PostgresCaseRepository) DisassociateAsset(context context.Context, transaction *sql.Tx, caseId, associationId string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.DisassociateAsset")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("association.id", associationId)

	return database.WithSessionErr(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) error {
		result, err := sessionTx.ExecContext(spanContext, `
			DELETE FROM case_asset_associations
			WHERE id=$1 AND case_id=$2
		`, associationId, caseId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to disassociate asset %s from case %s", associationId, caseId))
			return fmt.Errorf("DisassociateAsset: %w", err)
		}
		if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 {
			return ErrAssociationNotFound
		}
		return nil
	})
}

func (repo *PostgresCaseRepository) ListAssetAssociations(context context.Context, transaction *sql.Tx, request *casespb.ListAssetAssociationsForCaseRequest) (*casespb.ListAssetAssociationsForCaseResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListAssetAssociations")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", request.PageSize))

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.ListAssetAssociationsForCaseResponse, error) {
		// Convert PageToken to offset like other listing functions
		offset := 0
		if request.PageToken != "" {
			if offsetValue, err := strconv.Atoi(request.PageToken); err != nil {
				log.Printf("error: failed to parse page token %q: %v", request.PageToken, err)
			} else {
				offset = offsetValue
			}
		}

		rows, err := sessionTx.QueryContext(spanContext, `
				SELECT id, asset_id, association_type, assigned_at, notes, assigner_asset_id
				FROM case_asset_associations
				WHERE case_id=$1
				ORDER BY assigned_at DESC
				LIMIT $2 OFFSET $3
			`, request.CaseId, request.PageSize, offset)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to list asset associations for case %s", request.CaseId))
			return nil, fmt.Errorf("ListAssetAssociations: %w", err)
		}
		defer func() {
			if closeErr := rows.Close(); closeErr != nil {
				log.Printf("warning: failed to close rows: %v", closeErr)
			}
		}()
		var associations []*casespb.CaseAssetAssociation
		for rows.Next() {
			var (
				associationId, assetId, notes, assignerId sql.NullString
				associationType                           sql.NullInt64
				assignedAt                                time.Time
			)
			if err := rows.Scan(&associationId, &assetId, &associationType, &assignedAt, &notes, &assignerId); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("ListAssetAssociations scan: %w", err)
			}
			var convertedType int32
			if associationType.Valid {
				var err error
				convertedType, err = safeInt64ToInt32(associationType.Int64)
				if err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
					return nil, fmt.Errorf("ListAssetAssociations type conversion: %w", err)
				}
			}

			associations = append(associations, &casespb.CaseAssetAssociation{
				Id:              associationId.String,
				CaseId:          request.CaseId,
				AssetId:         assetId.String,
				AssociationType: casespb.CaseAssetAssociationType(convertedType),
				AssignedAt:      assignedAt.Format(time.RFC3339Nano),
				Notes:           notes.String,
				AssignerAssetId: assignerId.String,
			})
		}
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("ListAssetAssociations rows err: %w", err)
		}
		return &casespb.ListAssetAssociationsForCaseResponse{Associations: associations}, nil
	})
}

// ---------------- Watchers ----------------

func (repo *PostgresCaseRepository) AddWatcher(context context.Context, transaction *sql.Tx, caseId, assetId string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.AddWatcher")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("watcher.asset_id", assetId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if err := repo.modifyLink(spanContext, sessionTx, "case_watchers", "asset_id", caseId, assetId, true); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to add watcher %s to case %s", assetId, caseId))
			return nil, fmt.Errorf("AddWatcher: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) RemoveWatcher(context context.Context, transaction *sql.Tx, caseId, assetId string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.RemoveWatcher")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("watcher.asset_id", assetId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		if err := repo.modifyLink(spanContext, sessionTx, "case_watchers", "asset_id", caseId, assetId, false); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, fmt.Sprintf("Failed to remove watcher %s from case %s", assetId, caseId))
			return nil, fmt.Errorf("RemoveWatcher: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

// ---------------- Status / updates / tags ----------------

func (repo *PostgresCaseRepository) UpdateCaseStatus(context context.Context, transaction *sql.Tx, request *casespb.UpdateCaseStatusRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.UpdateCaseStatus")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("case.status_new", request.Status.String())

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		orgId := cmncontext.GetOrgId(spanContext)

		// Get callerAssetId for updater_id
		callerAssetId, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get asset ID from context")
			return nil, fmt.Errorf("UpdateCaseStatus: %w", err)
		}

		// Get current case to check status change
		existingCase, err := repo.GetCase(spanContext, sessionTx, request.CaseId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get existing case")
			return nil, fmt.Errorf("UpdateCaseStatus: get existing case: %w", err)
		}

		// Validate status change
		if request.Status == existingCase.Status {
			err := fmt.Errorf("UpdateCaseStatus: status unchanged (%s)", request.Status)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, err
		}

		// Set timestamps internally
		currentTime := time.Now().UTC()

		// Add status update entry
		_, err = sessionTx.ExecContext(spanContext, `
			INSERT INTO case_status_updates(
				case_id, timestamp, new_status, previous_status, note, updater_id, update_source, org_id
			) VALUES($1,$2,$3,$4,$5,$6,$7,$8)
		`, request.CaseId, currentTime, int32(request.Status), int32(existingCase.Status), request.Note, callerAssetId, int32(situationpb.UpdateSource_UPDATE_SOURCE_HUMAN_OPERATOR), orgId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert case status update entry")
			return nil, fmt.Errorf("UpdateCaseStatus insert: %w", err)
		}

		// Handle status-based timestamps
		var resolvedTime interface{}
		var closeTime interface{}

		// Handle resolved_time
		if request.Status == casespb.CaseStatus_CASE_STATUS_RESOLVED {
			if existingCase.Status != casespb.CaseStatus_CASE_STATUS_RESOLVED {
				// Status changing to RESOLVED - set new resolved_time
				resolvedTime = currentTime
			} else if existingCase.ResolvedTime != "" {
				// Keep existing resolved_time
				if parsedTime, err := time.Parse(time.RFC3339Nano, existingCase.ResolvedTime); err == nil {
					resolvedTime = parsedTime
				}
			}
		} else if existingCase.Status == casespb.CaseStatus_CASE_STATUS_RESOLVED {
			// Status changing from RESOLVED - keep existing resolved_time
			if existingCase.ResolvedTime != "" {
				if parsedTime, err := time.Parse(time.RFC3339Nano, existingCase.ResolvedTime); err == nil {
					resolvedTime = parsedTime
				}
			}
		}

		// Handle close_time
		if request.Status == casespb.CaseStatus_CASE_STATUS_CLOSED {
			if existingCase.Status != casespb.CaseStatus_CASE_STATUS_CLOSED {
				// Status changing to CLOSED - set new close_time
				closeTime = currentTime
			} else if existingCase.CloseTime != "" {
				// Keep existing close_time
				if parsedTime, err := time.Parse(time.RFC3339Nano, existingCase.CloseTime); err == nil {
					closeTime = parsedTime
				}
			}
		} else if existingCase.Status == casespb.CaseStatus_CASE_STATUS_CLOSED {
			// Status changing from CLOSED - keep existing close_time
			if existingCase.CloseTime != "" {
				if parsedTime, err := time.Parse(time.RFC3339Nano, existingCase.CloseTime); err == nil {
					closeTime = parsedTime
				}
			}
		}

		// Update case with new status, version, and timestamps
		_, err = sessionTx.ExecContext(spanContext, `
			UPDATE cases SET
				status = $1,
				update_time = $2,
				resolved_time = $3,
				close_time = $4,
				version = version + 1,
				etag = etag + 1
			WHERE id = $5
		`, int32(request.Status), currentTime, resolvedTime, closeTime, request.CaseId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update case status")
			return nil, fmt.Errorf("UpdateCaseStatus update: %w", err)
		}

		// Take snapshot after version change
		if err := repo.insertSnapshot(spanContext, sessionTx, request.CaseId); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert case snapshot")
			return nil, fmt.Errorf("UpdateCaseStatus: %w", err)
		}

		return repo.GetCase(spanContext, sessionTx, request.CaseId)
	})
}

func (repo *PostgresCaseRepository) AddCaseUpdate(context context.Context, transaction *sql.Tx, request *casespb.AddCaseUpdateRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.AddCaseUpdate")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	if request.Update != nil {
		span.SetTag("update.event_type", request.Update.EventType)
		span.SetTag("update.source", request.Update.UpdateSource.String())
	}

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		orgId := cmncontext.GetOrgId(spanContext)

		update := request.Update
		now := time.Now().UTC()
		update.EventTime = now.Format(time.RFC3339Nano)

		// Get callerAssetId for updater_id
		callerAssetId, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(spanContext, sessionTx)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get asset ID from context")
			return nil, fmt.Errorf("AddCaseUpdate: %w", err)
		}
		update.UpdaterId = callerAssetId

		// serialise the free-form data payload
		var dataBytes interface{} = nil
		if update.Data != nil {
			if b, err := json.Marshal(update.Data.AsMap()); err == nil {
				dataBytes = b
			}
		}

		// Insert the case update and get the ID
		var caseUpdateId int
		err = sessionTx.QueryRowContext(spanContext, `
			INSERT INTO case_updates(
				case_id, message, event_time, update_source, updater_id,
				event_type, display_name, data, org_id
			) VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9)
			RETURNING id
		`, request.CaseId, update.Message, now, int32(update.UpdateSource),
			update.UpdaterId, update.EventType, update.DisplayName, dataBytes, orgId).Scan(&caseUpdateId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert case update")
			return nil, fmt.Errorf("AddCaseUpdate: %w", err)
		}

		// Insert file attachments if any
		if len(update.FileAttachments) > 0 {
			for _, fileRef := range update.FileAttachments {
				// Generate ID for file reference if not provided
				fileRefId := fileRef.Id
				if fileRefId == "" {
					if uuid, err := uuid.NewRandom(); err == nil {
						fileRefId = uuid.String()
					} else {
						herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
						return nil, fmt.Errorf("AddCaseUpdate: failed to generate file ref ID: %w", err)
					}
				}

				// Always use request case_id to ensure consistency
				fileRef.CaseId = request.CaseId

				// Serialize metadata if present
				var metadataBytes interface{} = nil
				if fileRef.Metadata != nil {
					if b, err := json.Marshal(fileRef.Metadata.AsMap()); err == nil {
						metadataBytes = b
					}
				}

				_, err = sessionTx.ExecContext(spanContext, `
					INSERT INTO case_update_file_attachments(
						id, case_id, case_update_id, file_id, caption, display_name,
						display_order, file_category, metadata, org_id
					) VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)
				`, fileRefId, request.CaseId, caseUpdateId, fileRef.FileId, fileRef.Caption,
					fileRef.DisplayName, fileRef.DisplayOrder, fileRef.FileCategory,
					metadataBytes, orgId)
				if err != nil {
					herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert case update file attachment")
					return nil, fmt.Errorf("AddCaseUpdate: failed to insert file attachment: %w", err)
				}
			}
		}

		return repo.GetCase(spanContext, sessionTx, request.CaseId)
	})
}

func (repo *PostgresCaseRepository) RemoveCaseUpdate(context context.Context, transaction *sql.Tx, request *casespb.RemoveCaseUpdateRequest) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.RemoveCaseUpdate")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	if request.Update != nil {
		span.SetTag("update.message", request.Update.Message)
		span.SetTag("update.event_time", request.Update.EventTime)
	}

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		result, err := sessionTx.ExecContext(spanContext, `
			DELETE FROM case_updates
			WHERE case_id=$1 AND message=$2 AND event_time=$3
		`, request.CaseId, request.Update.Message, request.Update.EventTime)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to delete case update")
			return nil, fmt.Errorf("RemoveCaseUpdate: %w", err)
		}
		if rowsAffected, _ := result.RowsAffected(); rowsAffected == 0 {
			return nil, errors.New("update entry not found")
		}
		return repo.GetCase(spanContext, sessionTx, request.CaseId)
	})
}

func (repo *PostgresCaseRepository) ListCaseUpdates(context context.Context, transaction *sql.Tx, request *casespb.ListCaseUpdatesRequest) (*casespb.ListCaseUpdatesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListCaseUpdates")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", request.PageSize))
	span.SetTag("list.page_token", request.PageToken)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.ListCaseUpdatesResponse, error) {
		offset := 0
		if request.PageToken != "" {
			if offsetValue, err := strconv.Atoi(request.PageToken); err != nil {
				log.Printf("error: failed to parse page token %q: %v", request.PageToken, err)
			} else {
				offset = offsetValue
			}
		}

		rows, err := sessionTx.QueryContext(spanContext, `
			SELECT id, message, event_time, update_source, updater_id,
			       event_type, display_name, data
			FROM case_updates
			WHERE case_id=$1
			ORDER BY event_time DESC
			LIMIT $2 OFFSET $3
		`, request.CaseId, request.PageSize, offset)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query case updates")
			return nil, fmt.Errorf("ListCaseUpdates: %w", err)
		}
		defer func() {
			if closeErr := rows.Close(); closeErr != nil {
				log.Printf("warning: failed to close rows: %v", closeErr)
			}
		}()

		// First pass: collect case update IDs and build update entries
		var updates []*casespb.CaseUpdateEntry
		var updateIds []int
		for rows.Next() {
			var (
				updateId                                              int
				message, eventTime, updaterId, eventType, displayName string
				updateSource                                          int32
				dataBytes                                             []byte
			)
			if err := rows.Scan(&updateId, &message, &eventTime, &updateSource, &updaterId, &eventType, &displayName, &dataBytes); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan case update row")
				return nil, fmt.Errorf("ListCaseUpdates scan: %w", err)
			}
			var dataStruct *structpb.Struct
			if len(dataBytes) > 0 {
				var m map[string]interface{}
				if err := json.Unmarshal(dataBytes, &m); err == nil {
					dataStruct, _ = structpb.NewStruct(m)
				}
			}

			updateIds = append(updateIds, updateId)
			updates = append(updates, &casespb.CaseUpdateEntry{
				Message:      message,
				EventTime:    eventTime,
				UpdateSource: situationpb.UpdateSource(updateSource),
				UpdaterId:    updaterId,
				EventType:    eventType,
				DisplayName:  displayName,
				Data:         dataStruct,
				// FileAttachments will be populated below
			})
		}
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Error iterating case update rows")
			return nil, fmt.Errorf("ListCaseUpdates rows err: %w", err)
		}

		// Second pass: batch load file attachments for all updates
		fileAttachmentsMap, err := repo.batchLoadCaseUpdateFileAttachments(spanContext, sessionTx, updateIds)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to batch load case update file attachments")
			return nil, fmt.Errorf("ListCaseUpdates batch load file attachments: %w", err)
		}

		// Third pass: assign file attachments to updates
		for i, updateId := range updateIds {
			updates[i].FileAttachments = fileAttachmentsMap[updateId]
		}

		// Set next page token if we got a full page of results
		nextToken := ""
		if len(updates) == int(request.PageSize) {
			nextToken = strconv.Itoa(offset + int(request.PageSize))
		}

		return &casespb.ListCaseUpdatesResponse{
			Updates:       updates,
			NextPageToken: nextToken,
		}, nil
	})
}

// ListCaseFileAttachments retrieves all file attachments for a specific case with pagination
func (repo *PostgresCaseRepository) ListCaseFileAttachments(ctx context.Context, tx *sql.Tx, request *casespb.ListCaseFileAttachmentsRequest) (*casespb.ListCaseFileAttachmentsResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseRepository.ListCaseFileAttachments")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", request.PageSize))
	span.SetTag("list.page_token", request.PageToken)
	if request.FileCategory != "" {
		span.SetTag("filter.file_category", request.FileCategory)
	}

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*casespb.ListCaseFileAttachmentsResponse, error) {
		// Build the base query
		baseQuery := `
			SELECT id, case_id, file_id, caption, display_name, display_order, file_category, metadata
			FROM case_update_file_attachments
			WHERE case_id = $1
		`

		queryParams := []interface{}{request.CaseId}
		paramIndex := 2

		// Add file category filter if specified
		if request.FileCategory != "" {
			baseQuery += fmt.Sprintf(" AND file_category = $%d", paramIndex)
			queryParams = append(queryParams, request.FileCategory)
			paramIndex++
		}

		// Add ordering
		baseQuery += " ORDER BY display_order, id"

		// Handle pagination
		pageSize := int(request.PageSize)
		if pageSize <= 0 {
			pageSize = 50 // Default page size
		}
		if pageSize > 1000 {
			pageSize = 1000 // Max page size
		}

		offset := 0
		if request.PageToken != "" {
			parsedOffset, err := strconv.Atoi(request.PageToken)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation, "Invalid page token")
				return nil, fmt.Errorf("invalid page token: %w", err)
			}
			offset = parsedOffset
		}

		// Add pagination to query
		baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", paramIndex, paramIndex+1)
		queryParams = append(queryParams, pageSize+1, offset) // +1 to check if there are more results

		// Execute query
		rows, err := sessionTx.QueryContext(spanContext, baseQuery, queryParams...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query case file attachments")
			return nil, fmt.Errorf("failed to list case file attachments: %w", err)
		}
		defer rows.Close()

		var fileAttachments []*casespb.CaseFileReference
		for rows.Next() {
			var (
				attachmentId, caseId, fileId, caption, displayName, fileCategory string
				displayOrder                                                     int32
				metadataBytes                                                    []byte
			)
			if err := rows.Scan(&attachmentId, &caseId, &fileId, &caption, &displayName, &displayOrder, &fileCategory, &metadataBytes); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan file attachment row")
				return nil, fmt.Errorf("failed to scan file attachment row: %w", err)
			}

			var metadataStruct *structpb.Struct
			if len(metadataBytes) > 0 {
				var m map[string]interface{}
				if err := json.Unmarshal(metadataBytes, &m); err == nil {
					metadataStruct, _ = structpb.NewStruct(m)
				}
			}

			fileAttachments = append(fileAttachments, &casespb.CaseFileReference{
				Id:           attachmentId,
				CaseId:       caseId,
				FileId:       fileId,
				Caption:      caption,
				DisplayName:  displayName,
				DisplayOrder: displayOrder,
				FileCategory: fileCategory,
				Metadata:     metadataStruct,
			})
		}

		// Check for next page
		var nextPageToken string
		if len(fileAttachments) > pageSize {
			fileAttachments = fileAttachments[:pageSize] // Trim to actual page size
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		return &casespb.ListCaseFileAttachmentsResponse{
			FileAttachments: fileAttachments,
			NextPageToken:   nextPageToken,
		}, nil
	})
}

func (repo *PostgresCaseRepository) ListCaseStatusHistory(context context.Context, transaction *sql.Tx, request *casespb.ListCaseStatusHistoryRequest) (*casespb.ListCaseStatusHistoryResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListCaseStatusHistory")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", request.PageSize))
	span.SetTag("list.page_token", request.PageToken)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.ListCaseStatusHistoryResponse, error) {
		// Convert page token to integer for OFFSET
		offset := 0
		if request.PageToken != "" {
			if offsetValue, err := strconv.Atoi(request.PageToken); err != nil {
				log.Printf("error: failed to parse page token %q: %v", request.PageToken, err)
			} else {
				offset = offsetValue
			}
		}

		rows, err := sessionTx.QueryContext(spanContext, `
			SELECT timestamp, new_status, previous_status, note, updater_id, update_source
			FROM case_status_updates
			WHERE case_id=$1
			ORDER BY timestamp DESC
			LIMIT $2 OFFSET $3
		`, request.CaseId, request.PageSize, offset)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query case status history")
			return nil, fmt.Errorf("ListCaseStatusHistory: %w", err)
		}
		defer func() {
			if closeErr := rows.Close(); closeErr != nil {
				log.Printf("warning: failed to close rows: %v", closeErr)
			}
		}()
		var statusUpdates []*casespb.CaseStatusUpdateEntry
		for rows.Next() {
			var (
				timestamp, note, updaterId          string
				newStatus, prevStatus, updateSource int32
			)
			if err := rows.Scan(&timestamp, &newStatus, &prevStatus, &note, &updaterId, &updateSource); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan case status history row")
				return nil, fmt.Errorf("ListCaseStatusHistory scan: %w", err)
			}
			statusUpdates = append(statusUpdates, &casespb.CaseStatusUpdateEntry{
				Timestamp:      timestamp,
				NewStatus:      casespb.CaseStatus(newStatus),
				PreviousStatus: casespb.CaseStatus(prevStatus),
				Note:           note,
				UpdaterId:      updaterId,
				UpdateSource:   situationpb.UpdateSource(updateSource),
			})
		}
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Error iterating case status history rows")
			return nil, fmt.Errorf("ListCaseStatusHistory rows err: %w", err)
		}

		// Set next page token if we got a full page of results
		nextToken := ""
		if len(statusUpdates) == int(request.PageSize) {
			nextToken = strconv.Itoa(offset + int(request.PageSize))
		}

		return &casespb.ListCaseStatusHistoryResponse{
			StatusUpdates: statusUpdates,
			NextPageToken: nextToken,
		}, nil
	})
}

func (repo *PostgresCaseRepository) AddCaseTag(context context.Context, transaction *sql.Tx, caseId, tag string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.AddCaseTag")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("case.tag", tag)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		// Add audit log entry for tag addition
		if err := repo.addAuditLogEntry(
			spanContext,
			sessionTx,
			caseId,
			casespb.CaseAuditAction_CASE_AUDIT_ACTION_UPDATE,
			"tags",
			"",
			tag,
			"Tag added",
		); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add audit log entry for tag addition")
			return nil, fmt.Errorf("AddCaseTag: add audit log: %w", err)
		}

		if err := repo.modifyLink(spanContext, sessionTx, "case_tags", "tag", caseId, tag, true); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add case tag")
			return nil, fmt.Errorf("AddCaseTag: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) RemoveCaseTag(context context.Context, transaction *sql.Tx, caseId, tag string) (*casespb.Case, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.RemoveCaseTag")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("case.tag", tag)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.Case, error) {
		// Add audit log entry for tag removal
		if err := repo.addAuditLogEntry(
			spanContext,
			sessionTx,
			caseId,
			casespb.CaseAuditAction_CASE_AUDIT_ACTION_UPDATE,
			"tags",
			tag,
			"",
			"Tag removed",
		); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add audit log entry for tag removal")
			return nil, fmt.Errorf("RemoveCaseTag: add audit log: %w", err)
		}

		if err := repo.modifyLink(spanContext, sessionTx, "case_tags", "tag", caseId, tag, false); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to remove case tag")
			return nil, fmt.Errorf("RemoveCaseTag: %w", err)
		}
		return repo.GetCase(spanContext, sessionTx, caseId)
	})
}

func (repo *PostgresCaseRepository) UpdateAdditionalInfo(context context.Context, transaction *sql.Tx, request *casespb.AddAdditionalInfoRequest) (*casespb.AddAdditionalInfoResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.UpdateAdditionalInfo")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.AddAdditionalInfoResponse, error) {
		// Get existing additional info for audit log
		var existingInfo []byte
		err := sessionTx.QueryRowContext(spanContext, `
			SELECT additional_info_json
			FROM cases
			WHERE id = $1
		`, request.CaseId).Scan(&existingInfo)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to fetch existing additional info")
			return nil, fmt.Errorf("UpdateAdditionalInfo: fetch existing data: %w", err)
		}

		// Add audit log entry for additional info update
		if err := repo.addAuditLogEntry(
			spanContext,
			sessionTx,
			request.CaseId,
			casespb.CaseAuditAction_CASE_AUDIT_ACTION_UPDATE,
			"additional_info_json",
			string(existingInfo),
			request.AdditionalInfoJson,
			"Additional info updated",
		); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to add audit log entry for additional info update")
			return nil, fmt.Errorf("UpdateAdditionalInfo: add audit log: %w", err)
		}

		_, err = sessionTx.ExecContext(spanContext, `
			UPDATE cases
			SET additional_info_json = additional_info_json || $1::jsonb,
				etag = etag + 1
			WHERE id = $2
		`, request.AdditionalInfoJson, request.CaseId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update additional info")
			return nil, fmt.Errorf("UpdateAdditionalInfo: %w", err)
		}

		// Fetch the fresh additional info JSON
		var additionalInfo []byte
		err = sessionTx.QueryRowContext(spanContext, `
			SELECT additional_info_json
			FROM cases
			WHERE id = $1
		`, request.CaseId).Scan(&additionalInfo)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to fetch fresh additional info")
			return nil, fmt.Errorf("UpdateAdditionalInfo: fetch fresh data: %w", err)
		}

		return &casespb.AddAdditionalInfoResponse{
			CaseId:             request.CaseId,
			AdditionalInfoJson: string(additionalInfo),
		}, nil
	})
}

// ---------------- Versioning / Audit ----------------

func (repo *PostgresCaseRepository) GetCaseVersion(context context.Context, transaction *sql.Tx, caseId string, version int32) (*casespb.CaseSnapshot, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.GetCaseVersion")
	defer finishSpan()

	span.SetTag("case.id", caseId)
	span.SetTag("case.version", fmt.Sprintf("%d", version))

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.CaseSnapshot, error) {
		var snapshotData []byte
		var timestamp time.Time
		if err := sessionTx.QueryRowContext(spanContext, `
			SELECT snapshot, timestamp
			FROM case_snapshots
			WHERE case_id=$1 AND version=$2
		`, caseId, version).Scan(&snapshotData, &timestamp); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, ErrCaseNotFound
			}
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query case version")
			return nil, fmt.Errorf("GetCaseVersion: %w", err)
		}
		caseObject := &casespb.Case{}
		if err := protojson.Unmarshal(snapshotData, caseObject); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to unmarshal case snapshot")
			return nil, fmt.Errorf("GetCaseVersion: unmarshal: %w", err)
		}
		return &casespb.CaseSnapshot{
			CaseId:       caseId,
			Version:      version,
			CaseSnapshot: caseObject,
			Timestamp:    timestamp.Format(time.RFC3339Nano),
		}, nil
	})
}

func (repo *PostgresCaseRepository) ListCaseVersions(context context.Context, transaction *sql.Tx, caseId string) ([]int32, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListCaseVersions")
	defer finishSpan()

	span.SetTag("case.id", caseId)

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) ([]int32, error) {
		rows, err := sessionTx.QueryContext(spanContext, `
			SELECT version FROM case_snapshots
			WHERE case_id=$1 ORDER BY version ASC
		`, caseId)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query case versions")
			return nil, fmt.Errorf("ListCaseVersions: %w", err)
		}
		defer func() {
			if closeErr := rows.Close(); closeErr != nil {
				log.Printf("warning: failed to close rows: %v", closeErr)
			}
		}()
		var versions []int32
		for rows.Next() {
			var version int32
			if err := rows.Scan(&version); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan case version row")
				return nil, fmt.Errorf("ListCaseVersions scan: %w", err)
			}
			versions = append(versions, version)
		}
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Error iterating case version rows")
			return nil, fmt.Errorf("ListCaseVersions rows err: %w", err)
		}
		return versions, nil
	})
}

func (repo *PostgresCaseRepository) ListCaseAuditLog(context context.Context, transaction *sql.Tx, request *casespb.ListCaseAuditLogRequest) (*casespb.ListCaseAuditLogResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListCaseAuditLog")
	defer finishSpan()

	span.SetTag("case.id", request.CaseId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", request.PageSize))
	span.SetTag("list.page_token", request.PageToken)
	if request.FilterAction != casespb.CaseAuditAction_CASE_AUDIT_ACTION_UNSPECIFIED {
		span.SetTag("filter.action", request.FilterAction.String())
	}

	return database.WithSession(repo.db, spanContext, transaction, func(sessionTx *sql.Tx) (*casespb.ListCaseAuditLogResponse, error) {
		var (
			stringBuilder strings.Builder
			queryArgs     []interface{}
		)
		stringBuilder.WriteString(`
			SELECT id, actor_asset_id, timestamp, field_path, old_value, new_value, note, action
			FROM case_audit_logs
			WHERE case_id=$1
		`)
		queryArgs = append(queryArgs, request.CaseId)
		if actionFilter := request.FilterAction; actionFilter != casespb.CaseAuditAction_CASE_AUDIT_ACTION_UNSPECIFIED {
			stringBuilder.WriteString(" AND action=$2")
			queryArgs = append(queryArgs, int32(actionFilter))
		}

		// Convert page token to integer for OFFSET
		offset := 0
		if request.PageToken != "" {
			if offsetValue, err := strconv.Atoi(request.PageToken); err != nil {
				log.Printf("error: failed to parse page token %q: %v", request.PageToken, err)
			} else {
				offset = offsetValue
			}
		}

		stringBuilder.WriteString(fmt.Sprintf(" ORDER BY timestamp DESC LIMIT $%d OFFSET $%d", len(queryArgs)+1, len(queryArgs)+2))
		queryArgs = append(queryArgs, request.PageSize, offset)

		rows, err := sessionTx.QueryContext(spanContext, stringBuilder.String(), queryArgs...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query case audit log")
			return nil, fmt.Errorf("ListCaseAuditLog: query failed: %w", err)
		}
		defer func() {
			if closeErr := rows.Close(); closeErr != nil {
				log.Printf("warning: failed to close rows: %v", closeErr)
			}
		}()

		var auditEntries []*casespb.CaseAuditLogEntry
		for rows.Next() {
			var (
				entryId, actorId, timestamp, fieldPath, oldValue, newValue, note string
				actionType                                                       int32
			)
			if err := rows.Scan(&entryId, &actorId, &timestamp, &fieldPath, &oldValue, &newValue, &note, &actionType); err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan case audit log row")
				return nil, fmt.Errorf("ListCaseAuditLog scan: %w", err)
			}
			auditEntries = append(auditEntries, &casespb.CaseAuditLogEntry{
				Id:           entryId,
				CaseId:       request.CaseId,
				Action:       casespb.CaseAuditAction(actionType),
				ActorAssetId: actorId,
				Timestamp:    timestamp,
				FieldPath:    fieldPath,
				OldValue:     oldValue,
				NewValue:     newValue,
				Note:         note,
			})
		}
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Error iterating case audit log rows")
			return nil, fmt.Errorf("ListCaseAuditLog rows err: %w", err)
		}

		// Set next page token if we got a full page of results
		nextToken := ""
		if len(auditEntries) == int(request.PageSize) {
			nextToken = strconv.Itoa(offset + int(request.PageSize))
		}

		return &casespb.ListCaseAuditLogResponse{
			Entries:       auditEntries,
			NextPageToken: nextToken,
		}, nil
	})
}

// ---------------- Shortcut listing helpers ----------------

// ListCasesBySituationID returns all cases linked to the given situation.
func (repo *PostgresCaseRepository) ListCasesBySituationID(
	context context.Context, tx *sql.Tx, req *casespb.ListCasesBySituationIdRequest,
) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListCasesBySituationID")
	defer finishSpan()

	span.SetTag("situation.id", req.SituationId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", req.PageSize))
	span.SetTag("list.page_token", req.PageToken)

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*casespb.ListCasesResponse, error) {
		ids, next, err := repo.listBySingleFilter(
			spanContext, sessionTx,
			"case_situations", "situation_id",
			req.SituationId,
			int(req.PageSize),
			req.PageToken,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list cases by situation ID")
			return nil, fmt.Errorf("ListCasesBySituationID: %w", err)
		}
		// Batch‐fetch full Case objects
		cases, err := repo.BatchGetCases(spanContext, sessionTx, ids)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to batch get cases")
			return nil, fmt.Errorf("ListCasesBySituationID: load cases: %w", err)
		}
		return &casespb.ListCasesResponse{
			Cases:         cases,
			NextPageToken: next,
		}, nil
	})
}

// ListCasesByReportID returns all cases linked to the given report.
func (repo *PostgresCaseRepository) ListCasesByReportID(
	context context.Context, tx *sql.Tx, req *casespb.ListCasesByReportIdRequest,
) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListCasesByReportID")
	defer finishSpan()

	span.SetTag("report.id", req.ReportId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", req.PageSize))
	span.SetTag("list.page_token", req.PageToken)

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*casespb.ListCasesResponse, error) {
		ids, next, err := repo.listBySingleFilter(
			spanContext, sessionTx,
			"case_reports", "report_id",
			req.ReportId,
			int(req.PageSize),
			req.PageToken,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list cases by report ID")
			return nil, fmt.Errorf("ListCasesByReportID: %w", err)
		}
		cases, err := repo.BatchGetCases(spanContext, sessionTx, ids)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to batch get cases")
			return nil, fmt.Errorf("ListCasesByReportID: load cases: %w", err)
		}
		return &casespb.ListCasesResponse{
			Cases:         cases,
			NextPageToken: next,
		}, nil
	})
}

// ListCasesByAssetID returns all cases for which the given asset is associated.
func (repo *PostgresCaseRepository) ListCasesByAssetID(
	context context.Context, tx *sql.Tx, req *casespb.ListCasesByAssetIdRequest,
) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListCasesByAssetID")
	defer finishSpan()

	span.SetTag("asset.id", req.AssetId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", req.PageSize))
	span.SetTag("list.page_token", req.PageToken)

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*casespb.ListCasesResponse, error) {
		ids, next, err := repo.listBySingleFilter(
			spanContext, sessionTx,
			"case_asset_associations", "asset_id",
			req.AssetId,
			int(req.PageSize),
			req.PageToken,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list cases by asset ID")
			return nil, fmt.Errorf("ListCasesByAssetID: %w", err)
		}
		cases, err := repo.BatchGetCases(spanContext, sessionTx, ids)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to batch get cases")
			return nil, fmt.Errorf("ListCasesByAssetID: load cases: %w", err)
		}
		return &casespb.ListCasesResponse{
			Cases:         cases,
			NextPageToken: next,
		}, nil
	})
}

// ListCasesByEntityID returns all cases referencing the given entity.
func (repo *PostgresCaseRepository) ListCasesByEntityID(
	context context.Context, tx *sql.Tx, req *casespb.ListCasesByEntityIdRequest,
) (*casespb.ListCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(context, "CaseRepository.ListCasesByEntityID")
	defer finishSpan()

	span.SetTag("entity.id", req.EntityId)
	span.SetTag("list.page_size", fmt.Sprintf("%d", req.PageSize))
	span.SetTag("list.page_token", req.PageToken)

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*casespb.ListCasesResponse, error) {
		ids, next, err := repo.listBySingleFilter(
			spanContext, sessionTx,
			"case_entities", "ref_id",
			req.EntityId,
			int(req.PageSize),
			req.PageToken,
		)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to list cases by entity ID")
			return nil, fmt.Errorf("ListCasesByEntityID: %w", err)
		}
		cases, err := repo.BatchGetCases(spanContext, sessionTx, ids)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to batch get cases")
			return nil, fmt.Errorf("ListCasesByEntityID: load cases: %w", err)
		}
		return &casespb.ListCasesResponse{
			Cases:         cases,
			NextPageToken: next,
		}, nil
	})
}

// safeInt64ToInt32 converts an int64 to int32 with bounds checking
func safeInt64ToInt32(v int64) (int32, error) {
	if v > math.MaxInt32 || v < math.MinInt32 {
		return 0, fmt.Errorf("integer overflow: %d is outside int32 range", v)
	}
	return int32(v), nil
}

// safeInt32Conversion safely converts an int to int32, returning an error if overflow would occur
func safeInt32Conversion(n int) (int32, error) {
	if n > math.MaxInt32 || n < math.MinInt32 {
		return 0, fmt.Errorf("integer overflow: %d cannot be represented as int32", n)
	}
	return int32(n), nil
}

// validateSearchCasesRequest validates search request inputs to prevent DoS attacks and malformed queries
func (repo *PostgresCaseRepository) validateSearchCasesRequest(req *casespb.SearchCasesRequest) error {
	// Validate query length to prevent memory exhaustion
	if len(req.Query) > 1000 {
		return fmt.Errorf("search query too long: %d characters (max 1000)", len(req.Query))
	}

	// Validate filter counts to prevent filter explosion attacks
	if len(req.Tags) > 50 {
		return fmt.Errorf("too many tag filters: %d (max 50)", len(req.Tags))
	}
	if len(req.Status) > 20 {
		return fmt.Errorf("too many status filters: %d (max 20)", len(req.Status))
	}
	if len(req.Type) > 20 {
		return fmt.Errorf("too many type filters: %d (max 20)", len(req.Type))
	}
	if len(req.Priority) > 10 {
		return fmt.Errorf("too many priority filters: %d (max 10)", len(req.Priority))
	}

	// Validate page size to prevent excessive memory usage
	if req.PageSize > 100 {
		return fmt.Errorf("page size too large: %d (max 100)", req.PageSize)
	}
	if req.PageSize < 0 {
		return fmt.Errorf("page size cannot be negative: %d", req.PageSize)
	}

	// Validate search fields to prevent SQL injection
	validSearchFields := map[string]bool{
		"id":          true,
		"title":       true,
		"description": true,
	}
	for _, field := range req.SearchFields {
		if !validSearchFields[field] {
			return fmt.Errorf("invalid search field: %s", field)
		}
	}

	// Validate field queries to prevent injection attacks
	for _, fq := range req.FieldQueries {
		if !validSearchFields[fq.Field] {
			return fmt.Errorf("invalid field query field: %s", fq.Field)
		}
		if len(fq.Query) > 500 {
			return fmt.Errorf("field query too long for field %s: %d characters (max 500)", fq.Field, len(fq.Query))
		}
	}

	// Validate query complexity to prevent regex DoS
	if strings.Count(req.Query, "%") > 5 {
		return fmt.Errorf("query contains too many wildcards: %d (max 5)", strings.Count(req.Query, "%"))
	}

	// Validate relationship filter counts
	if len(req.SituationIds) > 100 {
		return fmt.Errorf("too many situation ID filters: %d (max 100)", len(req.SituationIds))
	}
	if len(req.ReportIds) > 100 {
		return fmt.Errorf("too many report ID filters: %d (max 100)", len(req.ReportIds))
	}
	if len(req.AssetIds) > 100 {
		return fmt.Errorf("too many asset ID filters: %d (max 100)", len(req.AssetIds))
	}

	return nil
}

// SearchCases executes a comprehensive multi-criteria search against the cases dataset.
// This function uses a HYBRID SEARCH STRATEGY that balances optimal performance with API contract compliance:
//
// **HYBRID APPROACH:**
// - Default/full searches → search_vector column (GIN indexed, maximum performance)
// - Restricted searches → field-specific vectors (API compliant, respects search_fields)
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// SEARCH CAPABILITIES
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// 🔍 HIERARCHICAL TEXT SEARCH (PostgreSQL full-text search with intelligent path selection):
//   - Phrase matching (highest priority)    — Exact phrase matches using phraseto_tsquery()
//   - All-words matching (medium priority)  — All search terms present using to_tsquery() with &
//   - Individual word matching (lower priority) — Any search terms present using to_tsquery() with |
//   - Partial matching (fallback)          — ILIKE pattern matching for partial word matches
//
// 📝 SEARCHABLE FIELDS (using trigram indexes for ILIKE and search_vector for full-text):
//   - id                          — Case unique identifier (cases.id)
//   - title                       — Case title field (cases.title)
//   - description                 — Case description content (cases.description)
//
// 🎯 EXACT FILTERS (using btree indexes):
//   - status                      — Case status enum values (CaseStatus array)
//   - type                        — Case type enum values (CaseType array)
//   - priority                    — Case priority int32 values (int32 array)
//   - release_status              — Case release status enum values (ReleaseStatus array)
//   - tags                        — Case tags array (exact matching - filter only, not searchable text)
//   - asset_ids                   — Associated asset IDs array
//   - association_types           — Asset association types array
//   - entity_ref_ids              — Associated entity reference IDs array
//   - entity_ref_types            — Associated entity reference types array
//   - situation_ids               — Associated situation IDs array
//   - report_ids                  — Associated report IDs array
//   - related_case_ids            — Related case IDs array
//   - watcher_asset_ids           — Watcher asset IDs array
//   - created_by_asset_ids        — Asset IDs of case creators
//   - updated_by_asset_ids        — Asset IDs of last updaters
//
// 📅 DATE RANGE FILTERS (using BRIN indexes for time-series data):
//   - create_time                 — Case creation timestamp range
//   - update_time                 — Last modification timestamp range
//   - due_date                    — Case due date range
//   - resolved_time               — Case resolution timestamp range
//   - close_time                  — Case closure timestamp range
//
// 🗂️ FIELD-SPECIFIC QUERIES (targeted search with field validation):
//   - field_queries               — Array of {field, query} pairs for targeted searches
//   - Supported fields: id, title, description (tags not supported for text search)
//   - Uses ILIKE pattern matching with security validation
//
// 📄 ADVANCED QUERY OPTIONS:
//   - query                       — Global search term with hierarchical ranking
//   - search_fields               — Limit global search to specific fields (id, title, description only)
//   - field_queries               — Target different search terms to specific fields (id, title, description only)
//   - order_by                    — Sort by RELEVANCE, CREATE_TIME, UPDATE_TIME, PRIORITY, STATUS, DUE_DATE
//   - ascending                   — Sort direction (default: DESC)
//   - page_size                   — Results per page (max: 100, default: 50)
//   - page_token                  — Pagination offset token
//
// 🔦 RESULT HIGHLIGHTING:
//   - Generates contextual text fragments showing matched search terms
//   - 30-character context windows around matches
//   - Supports highlighting across id, title, and description fields only
//   - Deduplicates fragments to avoid repetition
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// DATABASE SCHEMA INTEGRATION
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// Tables used:
//   - cases                       — Main case data (id, title, description, status, timestamps, etc.)
//   - case_tags                   — Case tag associations (case_id, tag)
//   - case_asset_associations     — Asset associations (case_id, asset_id, association_type)
//   - case_entities               — Entity associations (case_id, entity_id, entity_type)
//   - case_situations             — Situation associations (case_id, situation_id)
//   - case_reports                — Report associations (case_id, report_id)
//   - related_cases               — Related case associations (case_id, related_case_id)
//   - case_watchers               — Watcher associations (case_id, asset_id)
//
// Key indexes leveraged:
//   - idx_cases_status            — Status filtering
//   - idx_cases_search_vector     — Full-text search with ts_rank ranking
//   - idx_cases_title_trgm        — Title trigram search
//   - idx_cases_description_trgm  — Description trigram search
//   - idx_cases_id_trgm           — ID trigram search
//   - brin_cases_create_time      — Creation time range queries
//   - brin_cases_update_time      — Update time range queries
//   - brin_cases_due_date         — Due date range queries
//   - idx_case_tags_case_id       — Tag filtering
//   - idx_case_assets_case_id     — Asset association filtering
//   - idx_case_entities_case_id   — Entity association filtering
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// EXECUTION WORKFLOW
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// Step 1: Build comprehensive search query with all filters and conditions
// Step 2: Set pagination defaults and validate bounds (max 100, default 50)
// Step 3: Calculate offset from page token for stateless pagination
// Step 4: Get total count of matching results for pagination metadata
// Step 5: Build ORDER BY clause with relevance ranking or specified ordering
// Step 6: Complete query with ordering and pagination clauses
// Step 7: Execute search query against database with transaction consistency
// Step 8: Scan and collect all matching cases from result set
// Step 9: Check for iteration errors during row processing
// Step 10: Generate search term highlights for UI display (if text search performed)
// Step 11: Calculate next page token for pagination continuation
// Step 12: Return complete search response with results and metadata
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// SAMPLE QUERY STRUCTURE
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
//	SELECT id, org_id, type, title, description, status, priority, release_status,
//	       create_time, update_time, due_date, resolved_time, close_time,
//	       etag, version, additional_info_json, created_by_asset_id, updated_by_asset_id,
//	       resource_type
//	FROM cases
//	WHERE (                                                                       -- Hierarchical text search
//	    (search_vector @@ phraseto_tsquery('english', $1)) OR                    -- Phrase matching (highest priority)
//	    (search_vector @@ to_tsquery('english', $2)) OR                          -- All-words matching (medium priority)
//	    (search_vector @@ to_tsquery('english', $3)) OR                          -- Individual word matching (lower priority)
//	    (id ILIKE $4 OR title ILIKE $4 OR description ILIKE $4)                 -- Partial matching (fallback)
//	  )
//	  AND title ILIKE $5                                                         -- Field-specific search
//	  AND status = ANY($6)                                                       -- Status array filter
//	  AND type = ANY($7)                                                         -- Type array filter
//	  AND priority = ANY($8)                                                     -- Priority array filter
//	  AND create_time >= $9 AND create_time <= $10                             -- Date range filter
//	  AND id IN (SELECT case_id FROM case_tags WHERE tag = ANY($11))            -- Tag filtering
//	  AND id IN (SELECT case_id FROM case_asset_associations WHERE asset_id = ANY($12)) -- Asset filtering
//	ORDER BY ts_rank(search_vector, phraseto_tsquery('english', $1)) DESC,      -- Relevance ranking
//	         update_time DESC, id ASC                                            -- Secondary sorting
//	LIMIT $13 OFFSET $14                                                         -- Pagination
//
// ═══════════════════════════════════════════════════════════════════════════════════════════
// SAMPLE INPUT/OUTPUT
// ═══════════════════════════════════════════════════════════════════════════════════════════
//
// INPUT:
//
//	&casespb.SearchCasesRequest{
//		Query: "Green Jacket theft",
//		SearchFields: []string{"title", "description"},
//		FieldQueries: []*casespb.FieldQuery{
//			{Field: "title", Query: "theft"},
//		},
//		Status: []casespb.CaseStatus{casespb.CaseStatus_CASE_STATUS_OPEN, casespb.CaseStatus_CASE_STATUS_IN_PROGRESS},
//		Type: []casespb.CaseType{casespb.CaseType_CASE_TYPE_INCIDENT},
//		Priority: []int32{3, 4}, // High and Critical priority values
//		CreateTime: &casespb.DateRange{From: "2024-01-01T00:00:00Z", To: "2024-12-31T23:59:59Z"},
//		Tags: []string{"theft", "property"},
//		OrderBy: casespb.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE,
//		Ascending: false,
//		PageSize: 20,
//		PageToken: "",
//	}
//
// OUTPUT:
//
//	&casespb.SearchCasesResponse{
//		Cases: []*casespb.Case{
//			{
//				Id: "case-123",
//				Title: "Green Jacket Theft Investigation",
//				Description: "Investigation into theft of green jacket from downtown store",
//				Status: casespb.CaseStatus_CASE_STATUS_OPEN,
//				Type: casespb.CaseType_CASE_TYPE_INCIDENT,
//				Priority: 3, // High priority (int32 value)
//				CreateTime: "2024-06-15T10:30:00Z",
//				UpdateTime: "2024-06-15T14:22:00Z",
//				Tags: []string{"theft", "property"},
//				SituationIds: []string{"sit-456"},
//				ReportIds: []string{"rpt-789"},
//				RelatedCaseIds: []string{"case-124"},
//				WatcherAssetIds: []string{"asset-999"},
//				// ... other fields
//			},
//		},
//		TotalResults: 42,
//		NextPageToken: "20",
//		Highlights: map[string]*casespb.HighlightResult{
//			"case-123": {
//				Field: "title, description",
//				Fragments: []string{
//					"…Green Jacket Theft Investigation…",
//					"…theft of green jacket from downtown…",
//				},
//			},
//		},
//	}
func (repo *PostgresCaseRepository) SearchCases(ctx context.Context, tx *sql.Tx, req *casespb.SearchCasesRequest) (*casespb.SearchCasesResponse, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "CaseRepository.SearchCases")
	defer finishSpan()

	span.SetTag("search.query", req.Query)
	span.SetTag("search.page_size", fmt.Sprintf("%d", req.PageSize))
	span.SetTag("search.page_token", req.PageToken)
	if len(req.FieldQueries) > 0 {
		span.SetTag("search.field_queries_count", fmt.Sprintf("%d", len(req.FieldQueries)))
	}

	return database.WithSession(repo.db, spanContext, tx, func(sessionTx *sql.Tx) (*casespb.SearchCasesResponse, error) {
		// Step 0: Input validation and security checks to prevent DoS attacks
		if err := repo.validateSearchCasesRequest(req); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation, "Invalid search request")
			return nil, fmt.Errorf("invalid search request: %w", err)
		}

		// Step 0.5: Add timeout to prevent runaway queries
		searchCtx, cancel := context.WithTimeout(spanContext, 30*time.Second)
		defer cancel()

		// Step 1: Build the comprehensive search query with all filters and conditions.
		// RLS (Row Level Security) automatically handles organization filtering for multi-tenant isolation.
		// This constructs the base SQL query, WHERE conditions, and parameterized arguments.
		// Also populates searchTermsMap for later use in result highlighting.
		var searchTermsMap map[string][]string
		baseQuery, whereConditions, queryArgs := repo.buildCaseSearchQuery(req, &searchTermsMap)

		// Step 2: Set pagination defaults and validate bounds.
		// Default page size is 50, maximum is 100 to prevent resource exhaustion.
		pageSize := int(req.PageSize)
		if pageSize <= 0 {
			pageSize = 50 // Default page size for reasonable response times
		}
		if pageSize > 100 {
			pageSize = 100 // Maximum page size to prevent memory/performance issues
		}

		// Step 3: Calculate offset from page token for pagination.
		// Page token is a simple integer offset encoded as string for stateless pagination.
		offset := 0
		if req.PageToken != "" {
			if offsetValue, parseErr := strconv.Atoi(req.PageToken); parseErr == nil {
				offset = offsetValue
			}
			// Invalid page tokens are silently ignored, defaulting to offset 0
		}

		// Step 4: Get total count of matching results for pagination metadata.
		// This executes a separate COUNT(*) query with the same WHERE conditions.
		totalCount, err := repo.getCaseSearchTotalCount(searchCtx, sessionTx, whereConditions, queryArgs)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get total count for case search")
			return nil, fmt.Errorf("failed to get total count for case search: %w", err)
		}

		// Step 5: Build ORDER BY clause based on search criteria.
		// Supports ordering by relevance, timestamps, priority, and status.
		// Always includes ID as secondary sort for consistent pagination.
		orderByClause := repo.buildCaseOrderByClause(req.OrderBy, req.Ascending)

		// Step 6: Complete the query with WHERE conditions, ordering, and pagination clauses.
		// First add WHERE conditions if any exist
		finalQuery := baseQuery
		if len(whereConditions) > 0 {
			finalQuery += " WHERE " + strings.Join(whereConditions, " AND ")
		}

		// Then add ordering and pagination
		finalQuery += " " + orderByClause
		finalQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", len(queryArgs)+1, len(queryArgs)+2)
		queryArgs = append(queryArgs, pageSize, offset)

		// Step 7: Execute the search query against the database.
		// Use the transaction provided by WithSession for consistency.
		rows, err := sessionTx.QueryContext(searchCtx, finalQuery, queryArgs...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to execute case search query")
			return nil, fmt.Errorf("failed to execute case search query: %w", err)
		}
		defer func() {
			if closeErr := rows.Close(); closeErr != nil {
				log.Printf("warning: failed to close case search rows: %v", closeErr)
			}
		}()

		// Step 8: Scan and collect all matching cases from the result set.
		// Each row is converted from database types to protobuf Case objects.
		var cases []*casespb.Case
		for rows.Next() {
			caseObj, err := scanCaseRow(rows)
			if err != nil {
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan case search row")
				return nil, fmt.Errorf("failed to scan case search row: %w", err)
			}
			cases = append(cases, caseObj)
		}

		// Step 9: Check for any errors that occurred during row iteration.
		// This catches errors that may have occurred after QueryContext but during scanning.
		if err := rows.Err(); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Error iterating case search results")
			return nil, fmt.Errorf("error iterating case search results: %w", err)
		}

		// Step 9.5: Load relationship data (tags, entity refs, etc.) for each case
		// This ensures search results have complete case data like GetCase method
		// PERFORMANCE OPTIMIZATION: Use batch loading instead of N+1 individual queries
		if len(cases) > 0 {
			// Extract case IDs for batch loading
			caseIds := make([]string, len(cases))
			for i, caseObj := range cases {
				caseIds[i] = caseObj.Id
			}

			// Batch load all relationships in 5 queries instead of N*5 queries
			tagsMap, situationsMap, reportsMap, relatedCasesMap, watchersMap, err := repo.batchLoadCaseRelationships(searchCtx, sessionTx, caseIds)
			if err != nil {
				herosentry.CaptureException(searchCtx, err, herosentry.ErrorTypeDatabase)
				return nil, fmt.Errorf("failed to batch load case relationships: %w", err)
			}

			// Assign loaded relationships to each case
			for _, caseObj := range cases {
				caseObj.Tags = tagsMap[caseObj.Id]
				caseObj.SituationIds = situationsMap[caseObj.Id]
				caseObj.ReportIds = reportsMap[caseObj.Id]
				caseObj.RelatedCaseIds = relatedCasesMap[caseObj.Id]
				caseObj.WatcherAssetIds = watchersMap[caseObj.Id]
			}
		}

		// Step 10: Generate search term highlights for UI display if text search was performed.
		// Creates highlighted fragments showing where search terms were found in case fields.
		var highlights map[string]*casespb.HighlightResult
		if req.Query != "" && len(cases) > 0 {
			highlights = repo.generateCaseSearchHighlights(cases, searchTermsMap)
		}

		// Step 11: Calculate next page token for pagination.
		// Only provide next page token if there might be more results.
		nextPageToken := ""
		if len(cases) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		// Step 12: Return the complete search response with all results and metadata.
		totalResultsInt32, err := safeInt32Conversion(totalCount)
		if err != nil {
			herosentry.CaptureException(searchCtx, err, herosentry.ErrorTypeInternal)
			return nil, fmt.Errorf("total count too large: %w", err)
		}
		return &casespb.SearchCasesResponse{
			Cases:         cases,
			TotalResults:  totalResultsInt32,
			NextPageToken: nextPageToken,
			Highlights:    highlights,
		}, nil
	})
}

// buildCaseSearchQuery constructs the SQL query for case search with all filters and conditions.
// This method is responsible for building a comprehensive search query that supports:
// - Hierarchical text search using PostgreSQL full-text search capabilities
// - Field-specific searches with exact term matching
// - Case type, status, and priority filtering using enum values
// - Date range filtering on all timestamp columns
// - Entity relationship filtering (situations, reports, assets, entities)
// - Tag-based filtering with exact matching
//
// The method returns three components:
// 1. baseQueryString: The SELECT clause with field list
// 2. whereConditionsList: Array of WHERE conditions to be joined with AND
// 3. queryArguments: Parameterized arguments for secure SQL execution
//
// Security: RLS (Row Level Security) automatically handles organization filtering for multi-tenant data isolation.
// Performance: Leverages indexes on search_vector, timestamps, and relationship tables.
//
// SAMPLE INPUT/OUTPUT:
//
// INPUT:
//
//	req: &casespb.SearchCasesRequest{
//		Query: "Green Jacket theft",
//		Status: []casespb.CaseStatus{casespb.CaseStatus_CASE_STATUS_OPEN},
//		Type: []casespb.CaseType{casespb.CaseType_CASE_TYPE_INCIDENT},
//		CreateTime: &casespb.DateRange{From: "2024-01-01T00:00:00Z", To: "2024-12-31T23:59:59Z"},
//		Tags: []string{"theft", "property"},
//	}
//	searchTermsMap: &map[string][]string{}
//
// OUTPUT:
//
//	baseQueryString: `
//		SELECT id, org_id, type, title, description, status, priority, release_status,
//		       create_time, update_time, due_date, resolved_time, close_time,
//		       etag, version, additional_info_json, created_by_asset_id, updated_by_asset_id,
//		       resource_type
//		FROM cases`
//	whereConditionsList: []string{
//		"((to_tsvector('english', COALESCE(title, '')) || to_tsvector('english', COALESCE(description, ''))) @@ phraseto_tsquery('english', $1)) OR ((to_tsvector('english', COALESCE(title, '')) || to_tsvector('english', COALESCE(description, ''))) @@ to_tsquery('english', $2)) OR ((to_tsvector('english', COALESCE(title, '')) || to_tsvector('english', COALESCE(description, ''))) @@ to_tsquery('english', $3)) OR (title ILIKE $4 OR description ILIKE $4))",
//		"status = ANY($5)",
//		"type = ANY($6)",
//		"create_time >= $7",
//		"create_time <= $8",
//		"EXISTS (SELECT 1 FROM case_tags WHERE case_tags.case_id = cases.id AND case_tags.tag = ANY($9))",
//	}
//	queryArguments: []interface{}{
//		"Green Jacket theft",                                 // phrase query
//		"Green & Jacket & theft",                            // all-words query
//		"Green | Jacket | theft",                            // any-words query
//		"%Green Jacket theft%",                              // ILIKE pattern
//		pq.Array([]int32{1}),                               // status array
//		pq.Array([]int32{2}),                               // type array
//		"2024-01-01T00:00:00Z",                             // create_time from
//		"2024-12-31T23:59:59Z",                             // create_time to
//		pq.Array([]string{"theft", "property"}),            // tags array
//	}
//	*searchTermsMap: map[string][]string{
//		"query": []string{"Green Jacket theft"},
//	}
func (repo *PostgresCaseRepository) buildCaseSearchQuery(req *casespb.SearchCasesRequest, searchTermsMap *map[string][]string) (string, []string, []interface{}) {
	// Step 1: Initialize the search terms map for later highlighting.
	// This map tracks which terms were searched in which fields for UI highlighting.
	*searchTermsMap = make(map[string][]string)

	// Step 2: Define the base SELECT query with all case fields.
	// Selects all columns needed to construct complete Case protobuf objects.
	// RLS (Row Level Security) automatically handles organization filtering,
	// so no explicit WHERE org_id clause is needed.
	baseQuery := `
		SELECT id, org_id, type, title, description, status, priority, release_status,
		       create_time, update_time, due_date, resolved_time, close_time,
		       etag, version, additional_info_json, created_by_asset_id, updated_by_asset_id,
		       resource_type
		FROM cases`

	// Step 3: Initialize WHERE conditions and query arguments.
	// RLS handles organization filtering, so we start with an empty WHERE clause.
	var whereConditions []string
	var queryArgs []interface{}
	paramIndex := 1 // Start with parameter index 1

	// Step 4: Handle hierarchical text search query across multiple fields.
	// This provides "search box" functionality with intelligent ranking:
	// - Phrase matching gets highest priority
	// - All-words matching gets medium priority
	// - Individual word matching gets lower priority
	if req.Query != "" {
		textCondition := repo.buildCaseTextSearchCondition(req.Query, req.SearchFields, &paramIndex, &queryArgs, searchTermsMap)
		if textCondition != "" {
			whereConditions = append(whereConditions, textCondition)
		}
	}

	// Step 5: Handle field-specific queries for targeted searches.
	// These allow searching specific fields with dedicated terms,
	// providing more precise search capabilities than general text search.
	if len(req.FieldQueries) > 0 {
		fieldCondition := repo.buildCaseFieldSearchCondition(req.FieldQueries, &paramIndex, &queryArgs)
		if fieldCondition != "" {
			whereConditions = append(whereConditions, fieldCondition)
		}
	}

	// Step 6: Add exact match filters for structured data.
	// These filters use indexed columns for efficient exact matching.
	repo.addCaseExactMatchFilters(req, &whereConditions, &queryArgs, &paramIndex)

	// Step 7: Add date range filters for temporal queries.
	// Each date range filter can specify 'from' and/or 'to' bounds.
	// Uses B-tree indexes on timestamp columns for efficient range queries.
	repo.addCaseDateRangeFilters(req, &whereConditions, &queryArgs, &paramIndex)

	// Step 8: Return all query components for final assembly.
	return baseQuery, whereConditions, queryArgs
}

// buildCaseTextSearchCondition creates hierarchical text search conditions using PostgreSQL full-text search.
// This method implements intelligent text matching with multiple ranking levels using a HYBRID APPROACH:
//
// **HYBRID STRATEGY:**
// - When search_fields is empty or contains all default fields → Uses search_vector (optimal performance)
// - When search_fields is restricted (e.g., only "title") → Uses field-specific vectors (API compliance)
//
// This provides the best balance between performance and API contract adherence.
//
// **SEARCH HIERARCHY:**
// 1. **Phrase Matching (Highest Priority)**: Exact phrase matches using phraseto_tsquery()
//   - Example: "Green Jacket" finds cases with that exact phrase
//   - Uses ts_rank() for relevance scoring
//
// 2. **All-Words Matching (Medium Priority)**: All search terms present using to_tsquery() with &
//   - Example: "Green Jacket" finds cases containing both "Green" AND "Jacket" anywhere
//   - Higher relevance than individual word matches
//
// 3. **Individual Word Matching (Lower Priority)**: Any search terms present using to_tsquery() with |
//   - Example: "Green Jacket" finds cases containing "Green" OR "Jacket"
//   - Lower relevance than all-words matches
//
// 4. **Partial Matching (Fallback)**: ILIKE pattern matching for partial word matches
//   - Example: "Gree" finds cases containing "Green", "Agreement", etc.
//   - Lowest priority, used when full-text search doesn't match
//   - **ALWAYS honors search_fields restrictions**
//
// **PERFORMANCE OPTIMIZATION:**
// - search_vector path: Uses GIN index on search_vector for maximum speed
// - field-specific path: Uses trigram indexes on individual columns
// - Both paths maintain API contract compliance
//
// Supported fields: "id", "title", "description"
// If searchFields is empty, defaults to all three fields
//
// Performance: Hybrid approach balances GIN index usage with API contract compliance.
// Security: All queries use parameterized arguments to prevent SQL injection.
//
// SAMPLE INPUT/OUTPUT:
//
// INPUT:
//
//	query: "Green Jacket theft"
//	searchFields: []string{"title", "description"}  // NEW: restrict to specific fields
//	paramIndex: &int(1)
//	queryArgs: &[]interface{}{}
//	searchTermsMap: &map[string][]string{}
//
// OUTPUT:
//
//	conditionString: "((search_vector @@ phraseto_tsquery('english', $1)) OR (search_vector @@ to_tsquery('english', $2)) OR (search_vector @@ to_tsquery('english', $3)) OR (title ILIKE $4 OR description ILIKE $4))"
//	*queryArgs: []interface{}{
//		"Green Jacket theft",          // phrase query
//		"Green & Jacket & theft",      // all-words query
//		"Green | Jacket | theft",      // any-words query
//		"%Green Jacket theft%",        // ILIKE pattern (now restricted to specified fields)
//	}
//	*paramIndex: 5
//	*searchTermsMap: map[string][]string{
//		"query": []string{"Green Jacket theft"},
//	}
func (repo *PostgresCaseRepository) buildCaseTextSearchCondition(query string, searchFields []string, paramIndex *int, queryArgs *[]interface{}, searchTermsMap *map[string][]string) string {
	// Step 1: Validate input parameters before processing.
	// Empty query means no search conditions to build.
	if query == "" {
		return ""
	}

	// Step 2: Store search terms for highlighting.
	// Track the general query for later use in result highlighting.
	(*searchTermsMap)["query"] = []string{query}

	// Step 3: Sanitize the search query for PostgreSQL full-text search.
	// Remove special characters that could interfere with tsquery parsing.
	// Replace multiple spaces with single spaces for consistent processing.
	sanitizedQuery := strings.TrimSpace(query)
	// Remove characters that can break tsquery: &, |, !, (, ), :, *, <, >
	sanitizedQuery = strings.ReplaceAll(sanitizedQuery, "&", " ")
	sanitizedQuery = strings.ReplaceAll(sanitizedQuery, "|", " ")
	sanitizedQuery = strings.ReplaceAll(sanitizedQuery, "!", " ")
	sanitizedQuery = strings.ReplaceAll(sanitizedQuery, "(", " ")
	sanitizedQuery = strings.ReplaceAll(sanitizedQuery, ")", " ")
	sanitizedQuery = strings.ReplaceAll(sanitizedQuery, ":", " ")
	sanitizedQuery = strings.ReplaceAll(sanitizedQuery, "*", " ")
	sanitizedQuery = strings.ReplaceAll(sanitizedQuery, "<", " ")
	sanitizedQuery = strings.ReplaceAll(sanitizedQuery, ">", " ")
	// Normalize multiple spaces to single spaces
	for strings.Contains(sanitizedQuery, "  ") {
		sanitizedQuery = strings.ReplaceAll(sanitizedQuery, "  ", " ")
	}
	sanitizedQuery = strings.TrimSpace(sanitizedQuery)

	if sanitizedQuery == "" {
		return "" // No valid search terms after sanitization
	}

	// Step 4: Determine search strategy based on search_fields parameter
	// HYBRID APPROACH: Use search_vector for optimal performance when searching all fields,
	// but respect API contract by using field-specific vectors when search_fields is restricted

	defaultFields := []string{"id", "title", "description"}
	useSearchVector := false

	if len(searchFields) == 0 {
		// Empty search_fields means search all default fields -> use search_vector for performance
		useSearchVector = true
		searchFields = defaultFields // Set for ILIKE fallback logic
	} else {
		// Check if search_fields contains exactly the default fields (any order)
		fieldSet := make(map[string]bool)
		for _, field := range searchFields {
			fieldSet[field] = true
		}
		defaultSet := make(map[string]bool)
		for _, field := range defaultFields {
			defaultSet[field] = true
		}

		// If requesting exactly the same fields as search_vector contains, use it
		if len(fieldSet) == len(defaultSet) {
			useSearchVector = true
			for field := range fieldSet {
				if !defaultSet[field] {
					useSearchVector = false
					break
				}
			}
		}
	}

	var searchConditions []string

	if useSearchVector {
		// OPTIMIZED PATH: Use pre-built search_vector for maximum performance
		// This leverages the GIN index on search_vector column

		// Level 1: Phrase matching (highest priority)
		phraseCondition := fmt.Sprintf("search_vector @@ phraseto_tsquery('english', $%d)", *paramIndex)
		searchConditions = append(searchConditions, phraseCondition)
		*queryArgs = append(*queryArgs, sanitizedQuery)
		*paramIndex++

		// Level 2: All-words matching (medium priority)
		words := strings.Fields(sanitizedQuery)
		if len(words) > 1 {
			allWordsQuery := strings.Join(words, " & ")
			allWordsCondition := fmt.Sprintf("search_vector @@ to_tsquery('english', $%d)", *paramIndex)
			searchConditions = append(searchConditions, allWordsCondition)
			*queryArgs = append(*queryArgs, allWordsQuery)
			*paramIndex++
		}

		// Level 3: Individual word matching (lower priority)
		if len(words) > 1 {
			anyWordsQuery := strings.Join(words, " | ")
			anyWordsCondition := fmt.Sprintf("search_vector @@ to_tsquery('english', $%d)", *paramIndex)
			searchConditions = append(searchConditions, anyWordsCondition)
			*queryArgs = append(*queryArgs, anyWordsQuery)
			*paramIndex++
		}
	} else {
		// FIELD-SPECIFIC PATH: Honor search_fields restrictions with dynamic vectors
		// Build field-specific search vectors for precise API contract compliance

		allowedSearchFields := map[string]string{
			"id":          "to_tsvector('english', COALESCE(id, ''))",
			"title":       "to_tsvector('english', COALESCE(title, ''))",
			"description": "to_tsvector('english', COALESCE(description, ''))",
		}

		// Build list of valid field vectors
		var fieldVectors []string
		var validFields []string
		for _, field := range searchFields {
			if vectorExpr, exists := allowedSearchFields[field]; exists {
				fieldVectors = append(fieldVectors, vectorExpr)
				validFields = append(validFields, field)
			}
		}

		if len(fieldVectors) == 0 {
			// No valid fields, fall back to default for safety
			for _, field := range defaultFields {
				fieldVectors = append(fieldVectors, allowedSearchFields[field])
			}
			validFields = defaultFields
		}

		// Create combined search vector from only the specified fields
		combinedVector := strings.Join(fieldVectors, " || ")

		// Level 1: Phrase matching using field-restricted vector
		phraseCondition := fmt.Sprintf("(%s) @@ phraseto_tsquery('english', $%d)", combinedVector, *paramIndex)
		searchConditions = append(searchConditions, phraseCondition)
		*queryArgs = append(*queryArgs, sanitizedQuery)
		*paramIndex++

		// Level 2: All-words matching using field-restricted vector
		words := strings.Fields(sanitizedQuery)
		if len(words) > 1 {
			allWordsQuery := strings.Join(words, " & ")
			allWordsCondition := fmt.Sprintf("(%s) @@ to_tsquery('english', $%d)", combinedVector, *paramIndex)
			searchConditions = append(searchConditions, allWordsCondition)
			*queryArgs = append(*queryArgs, allWordsQuery)
			*paramIndex++
		}

		// Level 3: Individual word matching using field-restricted vector
		if len(words) > 1 {
			anyWordsQuery := strings.Join(words, " | ")
			anyWordsCondition := fmt.Sprintf("(%s) @@ to_tsquery('english', $%d)", combinedVector, *paramIndex)
			searchConditions = append(searchConditions, anyWordsCondition)
			*queryArgs = append(*queryArgs, anyWordsQuery)
			*paramIndex++
		}

		// Update searchFields for ILIKE fallback to match what we actually searched
		searchFields = validFields
	}

	// Level 4: ILIKE fallback for both paths
	// This handles partial word matches and serves as a fallback for complex queries
	// Uses the searchFields determined by the chosen path above
	likePattern := "%" + sanitizedQuery + "%"

	// Define allowed fields for ILIKE search with their column mappings
	allowedLikeFields := map[string]string{
		"id":          "id",
		"title":       "title",
		"description": "description",
	}

	// Build ILIKE conditions only for the fields determined by our search strategy
	var likeConditions []string
	for _, field := range searchFields {
		if columnExpr, exists := allowedLikeFields[field]; exists {
			likeConditions = append(likeConditions, fmt.Sprintf("COALESCE(%s, '') ILIKE $%d", columnExpr, *paramIndex))
		}
	}

	if len(likeConditions) > 0 {
		likeCondition := "(" + strings.Join(likeConditions, " OR ") + ")"
		searchConditions = append(searchConditions, likeCondition)
		*queryArgs = append(*queryArgs, likePattern)
		*paramIndex++
	}

	// Step 5: Combine all search conditions with OR for comprehensive matching.
	// This creates a hierarchy where more precise matches are preferred but fallbacks ensure coverage.
	finalCondition := "(" + strings.Join(searchConditions, " OR ") + ")"

	return finalCondition
}

// buildCaseFieldSearchCondition creates search conditions for field-specific queries.
// This method handles targeted searches where users want to search a specific field
// with a specific term, providing more precise search capabilities than general text search.
// Each field-specific query is treated as an independent search condition.
//
// Supported fields for field-specific search:
// - id: Partial matching on case unique identifier
// - title: Partial matching on case title
// - description: Partial matching on case description
//
// All field searches use ILIKE for case-insensitive partial matching with '%term%' pattern.
// The method validates field names for security and uses parameterized queries to prevent injection.
//
// Performance: Leverages trigram indexes on text fields for fast ILIKE operations.
// Security: Field names are validated against an allowlist to prevent injection attacks.
//
// SAMPLE INPUT/OUTPUT:
//
// INPUT:
//
//	fieldQueries: []*casespb.FieldQuery{
//		{Field: "title", Query: "theft"},
//		{Field: "description", Query: "downtown"},
//		{Field: "invalid_field", Query: "ignored"},  // Will be skipped
//	}
//	paramIndex: &int(5)
//	queryArgs: &[]interface{}{int32(123), "other", "args"}
//
// OUTPUT:
//
//	conditionString: "(title ILIKE $5 AND description ILIKE $6)"
//	*queryArgs: []interface{}{
//		int32(123), "other", "args",  // existing args
//		"%theft%",                    // title query
//		"%downtown%",                 // description query
//	}
//	*paramIndex: 7
func (repo *PostgresCaseRepository) buildCaseFieldSearchCondition(fieldQueries []*casespb.FieldQuery, paramIndex *int, queryArgs *[]interface{}) string {
	// Step 1: Validate input parameters before processing.
	// Empty field queries means no search conditions to build.
	if len(fieldQueries) == 0 {
		return ""
	}

	// Step 2: Define allowed fields for security and performance.
	// Only these fields have trigram indexes and are safe for ILIKE searches.
	// This prevents injection attacks through field names and ensures good performance.
	allowedFields := map[string]string{
		"id":          "id",
		"title":       "title",
		"description": "description",
	}

	// Step 3: Build field-specific search conditions.
	// Each field query creates an independent ILIKE condition.
	var fieldConditions []string

	for _, fieldQuery := range fieldQueries {
		// Step 4: Validate field query has required components.
		if fieldQuery.Field == "" || fieldQuery.Query == "" {
			continue // Skip invalid field queries
		}

		// Step 5: Validate field name against allowlist for security.
		columnName, isAllowed := allowedFields[strings.ToLower(fieldQuery.Field)]
		if !isAllowed {
			continue // Skip unsupported fields for security
		}

		// Step 6: Create parameterized ILIKE condition for the field.
		// Use partial matching pattern for flexible search behavior.
		fieldCondition := fmt.Sprintf("COALESCE(%s, '') ILIKE $%d", columnName, *paramIndex)
		fieldConditions = append(fieldConditions, fieldCondition)
		*queryArgs = append(*queryArgs, "%"+fieldQuery.Query+"%")
		*paramIndex++
	}

	// Step 7: Combine all field conditions with AND for precise matching.
	// All field-specific queries must match for the case to be included.
	if len(fieldConditions) == 0 {
		return "" // No valid field conditions were created
	}

	// Step 8: Return the complete field search condition.
	return "(" + strings.Join(fieldConditions, " AND ") + ")"
}

// addCaseExactMatchFilters adds exact match filtering conditions
func (repo *PostgresCaseRepository) addCaseExactMatchFilters(req *casespb.SearchCasesRequest, whereConditions *[]string, queryArgs *[]interface{}, paramIndex *int) {
	// Status filter (array) - follow entity repo pattern
	if len(req.Status) > 0 {
		statusValuesList := make([]interface{}, len(req.Status))
		for statusIndex, statusValue := range req.Status {
			statusValuesList[statusIndex] = int32(statusValue)
		}
		*whereConditions = append(*whereConditions, fmt.Sprintf("status = ANY($%d)", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(statusValuesList))
		*paramIndex++
	}

	// Type filter (array) - follow entity repo pattern
	if len(req.Type) > 0 {
		typeValuesList := make([]interface{}, len(req.Type))
		for typeIndex, caseType := range req.Type {
			typeValuesList[typeIndex] = int32(caseType)
		}
		*whereConditions = append(*whereConditions, fmt.Sprintf("type = ANY($%d)", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(typeValuesList))
		*paramIndex++
	}

	// Priority filter (array) - follow entity repo pattern
	if len(req.Priority) > 0 {
		priorityValuesList := make([]interface{}, len(req.Priority))
		for priorityIndex, priorityValue := range req.Priority {
			priorityValuesList[priorityIndex] = int32(priorityValue)
		}
		*whereConditions = append(*whereConditions, fmt.Sprintf("priority = ANY($%d)", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(priorityValuesList))
		*paramIndex++
	}

	// Created by asset IDs filter - follow entity repo pattern
	if len(req.CreatedByAssetIds) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf("created_by_asset_id = ANY($%d)", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.CreatedByAssetIds))
		*paramIndex++
	}

	// Updated by asset IDs filter - follow entity repo pattern
	if len(req.UpdatedByAssetIds) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf("updated_by_asset_id = ANY($%d)", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.UpdatedByAssetIds))
		*paramIndex++
	}

	// Note: OrgIds filter removed - RLS automatically handles organization filtering

	// Tags filter - follow entity repo pattern
	if len(req.Tags) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf(
			"EXISTS (SELECT 1 FROM case_tags WHERE case_tags.case_id = cases.id AND case_tags.tag = ANY($%d))", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.Tags))
		*paramIndex++
	}

	// Asset IDs filter - follow entity repo pattern
	if len(req.AssetIds) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf(
			"EXISTS (SELECT 1 FROM case_asset_associations WHERE case_asset_associations.case_id = cases.id AND case_asset_associations.asset_id = ANY($%d))", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.AssetIds))
		*paramIndex++
	}

	// Situation IDs filter - follow entity repo pattern
	if len(req.SituationIds) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf(
			"EXISTS (SELECT 1 FROM case_situations WHERE case_situations.case_id = cases.id AND case_situations.situation_id = ANY($%d))", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.SituationIds))
		*paramIndex++
	}

	// Report IDs filter - follow entity repo pattern
	if len(req.ReportIds) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf(
			"EXISTS (SELECT 1 FROM case_reports WHERE case_reports.case_id = cases.id AND case_reports.report_id = ANY($%d))", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.ReportIds))
		*paramIndex++
	}

	// Entity reference IDs filter - follow entity repo pattern
	if len(req.EntityRefIds) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf(
			"EXISTS (SELECT 1 FROM case_entities WHERE case_entities.case_id = cases.id AND case_entities.ref_id = ANY($%d))", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.EntityRefIds))
		*paramIndex++
	}

	// Related case IDs filter - follow entity repo pattern
	if len(req.RelatedCaseIds) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf(
			"EXISTS (SELECT 1 FROM related_cases WHERE related_cases.case_id = cases.id AND related_cases.related_case_id = ANY($%d))", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.RelatedCaseIds))
		*paramIndex++
	}

	// Release status filter (array) - follow entity repo pattern
	if len(req.ReleaseStatus) > 0 {
		releaseStatusValuesList := make([]interface{}, len(req.ReleaseStatus))
		for statusIndex, statusValue := range req.ReleaseStatus {
			releaseStatusValuesList[statusIndex] = int32(statusValue)
		}
		*whereConditions = append(*whereConditions, fmt.Sprintf("release_status = ANY($%d)", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(releaseStatusValuesList))
		*paramIndex++
	}

	// Association types filter - follow entity repo pattern
	if len(req.AssociationTypes) > 0 {
		associationTypesValuesList := make([]interface{}, len(req.AssociationTypes))
		for typeIndex, typeValue := range req.AssociationTypes {
			associationTypesValuesList[typeIndex] = int32(typeValue)
		}
		*whereConditions = append(*whereConditions, fmt.Sprintf(
			"EXISTS (SELECT 1 FROM case_asset_associations WHERE case_asset_associations.case_id = cases.id AND case_asset_associations.association_type = ANY($%d))", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(associationTypesValuesList))
		*paramIndex++
	}

	// Entity reference types filter - follow entity repo pattern
	if len(req.EntityRefTypes) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf(
			"EXISTS (SELECT 1 FROM case_entities WHERE case_entities.case_id = cases.id AND case_entities.entity_type = ANY($%d))", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.EntityRefTypes))
		*paramIndex++
	}

	// Watcher asset IDs filter - follow entity repo pattern
	if len(req.WatcherAssetIds) > 0 {
		*whereConditions = append(*whereConditions, fmt.Sprintf(
			"EXISTS (SELECT 1 FROM case_watchers WHERE case_watchers.case_id = cases.id AND case_watchers.asset_id = ANY($%d))", *paramIndex))
		*queryArgs = append(*queryArgs, pq.Array(req.WatcherAssetIds))
		*paramIndex++
	}

	// Note: Version and Etag fields don't exist in SearchCasesRequest, so removing those filters
}

// addCaseDateRangeFilters adds date range filtering conditions
func (repo *PostgresCaseRepository) addCaseDateRangeFilters(req *casespb.SearchCasesRequest, whereConditions *[]string, queryArgs *[]interface{}, paramIndex *int) {
	// Create time range
	if req.CreateTime != nil {
		if req.CreateTime.From != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("create_time >= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.CreateTime.From)
			*paramIndex++
		}
		if req.CreateTime.To != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("create_time <= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.CreateTime.To)
			*paramIndex++
		}
	}

	// Update time range
	if req.UpdateTime != nil {
		if req.UpdateTime.From != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("update_time >= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.UpdateTime.From)
			*paramIndex++
		}
		if req.UpdateTime.To != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("update_time <= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.UpdateTime.To)
			*paramIndex++
		}
	}

	// Due date range
	if req.DueDate != nil {
		if req.DueDate.From != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("due_date >= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.DueDate.From)
			*paramIndex++
		}
		if req.DueDate.To != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("due_date <= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.DueDate.To)
			*paramIndex++
		}
	}

	// Resolved time range
	if req.ResolvedTime != nil {
		if req.ResolvedTime.From != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("resolved_time >= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.ResolvedTime.From)
			*paramIndex++
		}
		if req.ResolvedTime.To != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("resolved_time <= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.ResolvedTime.To)
			*paramIndex++
		}
	}

	// Close time range
	if req.CloseTime != nil {
		if req.CloseTime.From != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("close_time >= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.CloseTime.From)
			*paramIndex++
		}
		if req.CloseTime.To != "" {
			*whereConditions = append(*whereConditions, fmt.Sprintf("close_time <= $%d", *paramIndex))
			*queryArgs = append(*queryArgs, req.CloseTime.To)
			*paramIndex++
		}
	}
}

// buildCaseOrderByClause constructs the ORDER BY clause based on the search request.
// This method creates appropriate sorting for search results, supporting multiple
// ordering strategies to meet different user needs. The ordering always includes
// a secondary sort by ID to ensure consistent pagination when primary sort values are identical.
//
// Supported ordering options:
// - SEARCH_ORDER_BY_RELEVANCE: Relevance-based ordering with full-text search ranking
// - SEARCH_ORDER_BY_CREATE_TIME: Chronological ordering by creation time
// - SEARCH_ORDER_BY_UPDATE_TIME: Chronological ordering by last modification time
// - SEARCH_ORDER_BY_PRIORITY: Ordering by case priority (high to low by default)
// - SEARCH_ORDER_BY_STATUS: Ordering by case status
// - SEARCH_ORDER_BY_DUE_DATE: Chronological ordering by due date
//
// All orderings support both ascending and descending directions.
// When text search is performed, relevance ordering uses PostgreSQL's ts_rank() function
// for intelligent ranking based on search term frequency and position.
//
// Performance: Uses indexes on timestamp columns, priority, and status for efficient sorting.
// Consistency: Always includes ID as secondary sort for deterministic pagination.
//
// SAMPLE INPUT/OUTPUT:
//
// INPUT (Relevance with text search):
//
//	orderBy: casespb.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE
//	ascending: false
//	hasTextSearch: true
//	query: "Green Jacket theft"
//
// OUTPUT:
//
//	"ORDER BY ts_rank(search_vector, phraseto_tsquery('english', 'Green Jacket theft')) DESC, update_time DESC, id DESC"
//
// INPUT (Priority ordering):
//
//	orderBy: casespb.SearchOrderBy_SEARCH_ORDER_BY_PRIORITY
//	ascending: true
//	hasTextSearch: false
//	query: ""
//
// OUTPUT:
//
//	"ORDER BY priority ASC, create_time ASC, id ASC"
//
// INPUT (Due date with NULL handling):
//
//	orderBy: casespb.SearchOrderBy_SEARCH_ORDER_BY_DUE_DATE
//	ascending: false
//	hasTextSearch: false
//	query: ""
//
// OUTPUT:
//
//	"ORDER BY due_date DESC NULLS LAST, create_time DESC, id DESC"
func (repo *PostgresCaseRepository) buildCaseOrderByClause(orderBy casespb.SearchOrderBy, ascending bool) string {
	// Step 1: Determine sort direction based on ascending flag.
	// Default is descending (newest/highest first) unless explicitly requested ascending.
	sortDirection := "DESC"
	if ascending {
		sortDirection = "ASC"
	}

	// Step 2: Build ORDER BY clause based on the requested ordering option.
	// Each case includes ID as secondary sort for consistent pagination.
	switch orderBy {
	case casespb.SearchOrderBy_SEARCH_ORDER_BY_CREATE_TIME:
		// Chronological ordering by creation time with ID as tiebreaker
		return fmt.Sprintf("ORDER BY create_time %s, id %s", sortDirection, sortDirection)

	case casespb.SearchOrderBy_SEARCH_ORDER_BY_UPDATE_TIME:
		// Chronological ordering by last modification time with ID as tiebreaker
		return fmt.Sprintf("ORDER BY update_time %s, id %s", sortDirection, sortDirection)

	case casespb.SearchOrderBy_SEARCH_ORDER_BY_PRIORITY:
		// Priority-based ordering with creation time as secondary sort
		// Higher priority values (more urgent) typically sort first
		return fmt.Sprintf("ORDER BY priority %s, create_time %s, id %s", sortDirection, sortDirection, sortDirection)

	case casespb.SearchOrderBy_SEARCH_ORDER_BY_STATUS:
		// Status-based ordering with update time as secondary sort
		// Groups cases by status, then by most recently updated
		return fmt.Sprintf("ORDER BY status %s, update_time %s, id %s", sortDirection, sortDirection, sortDirection)

	case casespb.SearchOrderBy_SEARCH_ORDER_BY_DUE_DATE:
		// Due date ordering with creation time as secondary sort
		// Handles NULL due dates by placing them last regardless of sort direction
		return fmt.Sprintf("ORDER BY due_date %s NULLS LAST, create_time %s, id %s", sortDirection, sortDirection, sortDirection)

	case casespb.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE:
		fallthrough
	default:
		// Step 3: Handle relevance-based ordering with optional full-text search ranking.
		// Use PostgreSQL's full-text search ranking for intelligent relevance scoring
		// ts_rank() considers term frequency, position, and document length
		// Note: Using update_time as primary sort since ts_rank requires complex parameterization
		// The full-text search WHERE conditions already provide relevance filtering
		return fmt.Sprintf("ORDER BY update_time %s, id %s", sortDirection, sortDirection)
	}
}

// getCaseSearchTotalCount retrieves the total count of cases matching the search criteria.
// This method executes a separate COUNT(*) query using the same WHERE conditions as the
// main search query, but without LIMIT/OFFSET to get the total number of matching results.
// This count is essential for pagination metadata, allowing clients to calculate total
// pages and display accurate pagination controls.
//
// The method builds a fresh COUNT query using the same filtering logic as the main search.
//
// SAMPLE INPUT/OUTPUT:
//
// INPUT:
//
//	ctx: context.Background()
//	tx: *sql.Tx (database transaction)
//	whereConditions: []string{
//		"(search_vector @@ phraseto_tsquery('english', $1)) OR (search_vector @@ to_tsquery('english', $2)) OR (title ILIKE $3 OR description ILIKE $3)",
//		"status = ANY($4)",
//		"create_time >= $5",
//	}
//	queryArgs: []interface{}{
//		"Green Jacket theft",          // phrase search query
//		"Green & Jacket & theft",      // all-words search query
//		"%Green Jacket theft%",        // ILIKE pattern
//		pq.Array([]int32{1, 2}),      // status array
//		"2024-01-01T00:00:00Z",       // create_time from
//	}
//
// OUTPUT:
//
//	totalCount: 42
//	error: nil
//
// GENERATED QUERY:
//
//	SELECT COUNT(*) FROM cases
//	  WHERE (search_vector @@ phraseto_tsquery('english', $1)) OR (search_vector @@ to_tsquery('english', $2)) OR (title ILIKE $3 OR description ILIKE $3))
//	  AND status = ANY($4)
//	  AND create_time >= $5
func (repo *PostgresCaseRepository) getCaseSearchTotalCount(ctx context.Context, tx *sql.Tx, whereConditions []string, queryArgs []interface{}) (int, error) {
	countQuery := "SELECT COUNT(*) FROM cases"
	if len(whereConditions) > 0 {
		countQuery += " WHERE " + strings.Join(whereConditions, " AND ")
	}

	var totalCount int
	err := tx.QueryRowContext(ctx, countQuery, queryArgs...).Scan(&totalCount)
	if err != nil {
		return 0, fmt.Errorf("getCaseSearchTotalCount: %w", err)
	}
	return totalCount, nil
}

// generateCaseSearchHighlights creates highlight information for search results.
// This method analyzes the search results and search terms to generate highlighted
// text fragments showing users exactly where their search terms were found.
// Highlights enhance the search experience by providing visual feedback about matches.
//
// The highlighting process:
// 1. Examines each returned case for search term matches
// 2. Creates text fragments with context around matched terms
// 3. Identifies which fields contained matches
// 4. Deduplicates fragments to avoid repetition
// 5. Returns a map of case ID to highlight information
//
// Highlighting supports all searchable fields: id, title, description.
// Context length is 30 characters before and after matches for readability.
//
// Performance: Only processes cases that were returned by the search query.
// UI Integration: Returns structured highlights ready for frontend display.
//
// SAMPLE INPUT/OUTPUT:
//
// INPUT:
//
//	cases: []*casespb.Case{
//		{
//			Id: "case-123",
//			Title: "Green Jacket Theft Investigation",
//			Description: "Investigation into theft of green jacket from downtown store",
//		},
//		{
//			Id: "case-456",
//			Title: "Missing Property Report",
//			Description: "Report of missing green items including jacket and wallet",
//		},
//	}
//	searchTermsMap: map[string][]string{
//		"query": []string{"Green Jacket theft"},
//	}
//
// OUTPUT:
//
//	map[string]*casespb.HighlightResult{
//		"case-123": {
//			Field: "title, description",
//			Fragments: []string{
//				"…Green Jacket Theft Investigation…",
//				"…theft of green jacket from downtown…",
//			},
//		},
//		"case-456": {
//			Field: "description",
//			Fragments: []string{
//				"…missing green items including jacket…",
//			},
//		},
//	}
//
// PROCESSING LOGIC:
//  1. Splits "Green Jacket theft" into ["green", "jacket", "theft"]
//  2. Searches each case field for these terms (case-insensitive)
//  3. Creates 30-character context fragments around matches
//  4. Deduplicates identical fragments
//  5. Groups fragments by case ID with field summary
func (repo *PostgresCaseRepository) generateCaseSearchHighlights(cases []*casespb.Case, searchTermsMap map[string][]string) map[string]*casespb.HighlightResult {
	// Step 1: Initialize the highlights map for results.
	// This map will contain highlights keyed by case ID for efficient lookup.
	highlights := make(map[string]*casespb.HighlightResult)

	// Step 2: Validate input parameters before processing.
	// No search terms means no highlighting to perform.
	if len(searchTermsMap) == 0 {
		return highlights
	}

	// Step 3: Extract search terms from the search terms map.
	// Focus on the general query terms for highlighting.
	var searchTerms []string
	if queryTerms, exists := searchTermsMap["query"]; exists && len(queryTerms) > 0 {
		// Split the query into individual words for highlighting
		for _, term := range queryTerms {
			words := strings.Fields(strings.ToLower(term))
			searchTerms = append(searchTerms, words...)
		}
	}

	// Step 4: Return empty highlights if no valid search terms.
	if len(searchTerms) == 0 {
		return highlights
	}

	// Step 5: Process each case for potential highlights.
	// Only cases with matching search terms will have highlight entries.
	for _, caseObj := range cases {
		var allFragments []string
		var highlightedFields []string

		// Step 6: Create field-to-value mapping for efficient field access.
		// This allows us to check field values without repetitive conditional logic.
		fieldValueMap := map[string]string{
			"id":          caseObj.Id,
			"title":       caseObj.Title,
			"description": caseObj.Description,
		}

		// Step 7: Check each searchable field against all search terms.
		// This creates highlights for every field/term combination that matches.
		for fieldName, fieldValue := range fieldValueMap {
			if fieldValue == "" {
				continue // Skip empty fields
			}

			// Step 8: Check each search term against the current field value.
			// Multiple terms can match the same field, creating multiple fragments.
			for _, searchTerm := range searchTerms {
				if repo.containsTermIgnoreCase(fieldValue, searchTerm) {
					// Step 9: Create a highlighted fragment for the match.
					// This includes context around the matched term for readability.
					fragment := repo.createHighlightFragment(fieldValue, searchTerm)
					if fragment != "" {
						allFragments = append(allFragments, fragment)
						// Track which fields had matches for summary information
						if !repo.containsString(highlightedFields, fieldName) {
							highlightedFields = append(highlightedFields, fieldName)
						}
					}
				}
			}
		}

		// Step 10: Create highlight result if any matches were found.
		// Only cases with actual matches get highlight entries.
		if len(allFragments) > 0 {
			highlights[caseObj.Id] = &casespb.HighlightResult{
				Field:     strings.Join(highlightedFields, ", "),   // Summary of matched fields
				Fragments: repo.deduplicateFragments(allFragments), // Unique text fragments
			}
		}
	}

	// Step 11: Return the complete highlights map.
	return highlights
}

// containsTermIgnoreCase checks if text contains the search term (case-insensitive).
// This method provides case-insensitive substring matching for search term detection.
// It's used in the highlighting system to determine if a field value contains a search term.
//
// The method converts both the text content and search term to lowercase before
// performing the substring check, ensuring consistent matching regardless of case.
//
// SAMPLE INPUT/OUTPUT:
//
//	containsTermIgnoreCase("Green Jacket Theft Investigation", "jacket") → true
//	containsTermIgnoreCase("downtown electronics store", "ELECTRONICS") → true
//	containsTermIgnoreCase("Missing Property Report", "vehicle") → false
//	containsTermIgnoreCase("", "test") → false
//
// PERFORMANCE: O(n) where n is the length of textContent
// USE CASES: Search highlighting, term validation, case-insensitive filtering
func (repo *PostgresCaseRepository) containsTermIgnoreCase(textContent, searchTerm string) bool {
	return strings.Contains(strings.ToLower(textContent), strings.ToLower(searchTerm))
}

// createHighlightFragment creates a highlighted fragment around the matched term.
// This method generates a text snippet with context around a search term match,
// providing users with readable context for where their search terms were found.
//
// The fragment creation process:
// 1. Locates the search term within the text (case-insensitive)
// 2. Extracts surrounding context (30 characters before and after)
// 3. Adds ellipsis ("…") if the fragment doesn't include the full text
// 4. Returns the complete fragment for display in search results
//
// This helps users understand the context of matches without displaying entire field values.
//
// SAMPLE INPUT/OUTPUT:
//
// INPUT (Match in middle of text):
//
//	textContent: "Investigation into theft of green jacket from downtown electronics store on Main Street"
//	searchTerm: "jacket"
//
// OUTPUT:
//
//	"…theft of green jacket from downtown electronics…"
//
// INPUT (Match at beginning):
//
//	textContent: "Green Jacket Theft Investigation Case"
//	searchTerm: "Green"
//
// OUTPUT:
//
//	"Green Jacket Theft Investigation…"
//
// INPUT (Match at end):
//
//	textContent: "Downtown store theft of green jacket"
//	searchTerm: "jacket"
//
// OUTPUT:
//
//	"…store theft of green jacket"
//
// INPUT (Short text, no truncation needed):
//
//	textContent: "Missing green jacket"
//	searchTerm: "jacket"
//
// OUTPUT:
//
//	"Missing green jacket"
//
// INPUT (Term not found):
//
//	textContent: "Vehicle theft investigation"
//	searchTerm: "jacket"
//
// OUTPUT:
//
//	""
//
// INPUT (Case-insensitive matching):
//
//	textContent: "GREEN JACKET theft case"
//	searchTerm: "jacket"
//
// OUTPUT:
//
//	"GREEN JACKET theft case"
//
// PERFORMANCE: O(n) where n is the length of textContent
// CONTEXT LENGTH: 30 characters before and after the match
// USE CASES: Search result highlighting, snippet generation, match preview
func (repo *PostgresCaseRepository) createHighlightFragment(textContent, searchTerm string) string {
	// Step 1: Perform case-insensitive search to locate the term.
	// Convert both text and term to lowercase for consistent matching.
	lowerCaseText := strings.ToLower(textContent)
	lowerCaseTerm := strings.ToLower(searchTerm)

	termIndex := strings.Index(lowerCaseText, lowerCaseTerm)
	if termIndex == -1 {
		return "" // Term not found, no fragment to create
	}

	// Step 2: Calculate fragment boundaries with context around the match.
	// Provide 30 characters of context before and after the match for readability.
	contextLength := 30
	fragmentStartIndex := repo.maxInt(0, termIndex-contextLength)
	fragmentEndIndex := repo.minInt(len(textContent), termIndex+len(searchTerm)+contextLength)

	// Step 3: Extract the fragment from the original text (preserving case).
	// Use original text rather than lowercase to maintain proper capitalization.
	highlightFragment := textContent[fragmentStartIndex:fragmentEndIndex]

	// Step 4: Add ellipsis indicators if we truncated content.
	// This shows users that there's more content beyond the displayed fragment.
	if fragmentStartIndex > 0 {
		highlightFragment = "…" + highlightFragment
	}
	if fragmentEndIndex < len(textContent) {
		highlightFragment += "…"
	}

	// Step 5: Return the complete fragment ready for display.
	return highlightFragment
}

// deduplicateFragments removes duplicate fragments from the list.
// This method ensures that identical text fragments aren't shown multiple times
// in search highlights, providing a cleaner user experience.
//
// The deduplication uses a map to track seen fragments efficiently,
// preserving the original order of unique fragments while removing duplicates.
//
// SAMPLE INPUT/OUTPUT:
//
// INPUT (With duplicates):
//
//	fragmentsList: []string{
//		"…Green Jacket Theft Investigation…",
//		"…theft of green jacket from downtown…",
//		"…Green Jacket Theft Investigation…",  // Duplicate
//		"…electronics store on Main Street…",
//		"…theft of green jacket from downtown…",  // Duplicate
//		"…missing property report case…",
//	}
//
// OUTPUT:
//
//	[]string{
//		"…Green Jacket Theft Investigation…",
//		"…theft of green jacket from downtown…",
//		"…electronics store on Main Street…",
//		"…missing property report case…",
//	}
//
// INPUT (No duplicates):
//
//	fragmentsList: []string{
//		"…unique fragment one…",
//		"…unique fragment two…",
//		"…unique fragment three…",
//	}
//
// OUTPUT:
//
//	[]string{
//		"…unique fragment one…",
//		"…unique fragment two…",
//		"…unique fragment three…",
//	}
//
// INPUT (Empty slice):
//
//	fragmentsList: []string{}
//
// OUTPUT:
//
//	[]string{}
//
// INPUT (All duplicates):
//
//	fragmentsList: []string{
//		"…same fragment…",
//		"…same fragment…",
//		"…same fragment…",
//	}
//
// OUTPUT:
//
//	[]string{
//		"…same fragment…",
//	}
//
// PERFORMANCE: O(n) where n is the number of fragments
// MEMORY: O(n) for the seen fragments map
// USE CASES: Search highlighting cleanup, result deduplication, UI optimization
func (repo *PostgresCaseRepository) deduplicateFragments(fragmentsList []string) []string {
	// Step 1: Initialize tracking structures for deduplication.
	// Use a map for fast duplicate detection and slice for ordered results.
	seenFragments := make(map[string]bool)
	var uniqueFragments []string

	// Step 2: Process each fragment, keeping only unique ones.
	// Maintain the original order while removing duplicates.
	for _, fragment := range fragmentsList {
		if !seenFragments[fragment] {
			seenFragments[fragment] = true
			uniqueFragments = append(uniqueFragments, fragment)
		}
	}

	// Step 3: Return the deduplicated list maintaining original order.
	return uniqueFragments
}

// containsString checks if a string slice contains a specific string.
// This utility method provides efficient string slice membership testing,
// used in the highlighting system to track which fields have been processed.
//
// The method uses linear search which is efficient for small slices
// (like the list of highlighted fields in search results).
//
// SAMPLE INPUT/OUTPUT:
//
//	containsString([]string{"title", "description", "id"}, "description") → true
//	containsString([]string{"title", "description", "id"}, "status") → false
//	containsString([]string{}, "title") → false
//	containsString([]string{"Title"}, "title") → false (case-sensitive)
//
// PERFORMANCE: O(n) where n is the length of stringSlice
// MEMORY: O(1) constant space
// USE CASES: Field validation, duplicate checking, slice membership testing
func (repo *PostgresCaseRepository) containsString(stringSlice []string, targetItem string) bool {
	// Step 1: Iterate through the slice looking for the target string.
	// Linear search is appropriate for small slices common in this use case.
	for _, stringItem := range stringSlice {
		if stringItem == targetItem {
			return true
		}
	}
	// Step 2: Return false if target not found in slice.
	return false
}

// minInt returns the minimum of two integers.
// This utility method provides safe integer minimum calculation for fragment boundaries.
// Used in highlight fragment creation to ensure array bounds are respected.
//
// SAMPLE INPUT/OUTPUT:
//
//	minInt(10, 25) → 10
//	minInt(50, 30) → 30
//	minInt(15, 15) → 15
//	minInt(-5, -10) → -10
//	minInt(0, 5) → 0
//
// PERFORMANCE: O(1) constant time
// MEMORY: O(1) constant space
// USE CASES: Array bounds checking, fragment boundary calculation, safe math operations
func (repo *PostgresCaseRepository) minInt(firstValue, secondValue int) int {
	if firstValue < secondValue {
		return firstValue
	}
	return secondValue
}

// maxInt returns the maximum of two integers.
// This utility method provides safe integer maximum calculation for fragment boundaries.
// Used in highlight fragment creation to ensure array bounds are respected.
//
// SAMPLE INPUT/OUTPUT:
//
//	maxInt(25, 10) → 25
//	maxInt(30, 50) → 50
//	maxInt(15, 15) → 15
//	maxInt(-10, -5) → -5
//	maxInt(5, 0) → 5
//
// PERFORMANCE: O(1) constant time
// MEMORY: O(1) constant space
// USE CASES: Array bounds checking, fragment boundary calculation, safe math operations
func (repo *PostgresCaseRepository) maxInt(firstValue, secondValue int) int {
	if firstValue > secondValue {
		return firstValue
	}
	return secondValue
}

// batchLoadCaseRelationships loads all relationships for multiple cases in batches to avoid N+1 queries
// This replaces individual relationship loading calls in SearchCases for better performance.
// Instead of N*5 queries (N cases * 5 relationships each), this executes only 5 total queries.
func (repo *PostgresCaseRepository) batchLoadCaseRelationships(ctx context.Context, tx *sql.Tx, caseIds []string) (map[string][]string, map[string][]string, map[string][]string, map[string][]string, map[string][]string, error) {
	if len(caseIds) == 0 {
		return make(map[string][]string), make(map[string][]string), make(map[string][]string), make(map[string][]string), make(map[string][]string), nil
	}

	// Initialize result maps
	tagsMap := make(map[string][]string)
	situationsMap := make(map[string][]string)
	reportsMap := make(map[string][]string)
	relatedCasesMap := make(map[string][]string)
	watchersMap := make(map[string][]string)

	// Initialize empty slices for all case IDs to ensure consistent results
	for _, caseId := range caseIds {
		tagsMap[caseId] = []string{}
		situationsMap[caseId] = []string{}
		reportsMap[caseId] = []string{}
		relatedCasesMap[caseId] = []string{}
		watchersMap[caseId] = []string{}
	}

	// Batch load tags - 1 query instead of N queries
	tagRows, err := tx.QueryContext(ctx, `SELECT case_id, tag FROM case_tags WHERE case_id = ANY($1)`, pq.Array(caseIds))
	if err != nil {
		return nil, nil, nil, nil, nil, fmt.Errorf("failed to batch load tags: %w", err)
	}
	defer tagRows.Close()

	for tagRows.Next() {
		var caseId, tag string
		if err := tagRows.Scan(&caseId, &tag); err != nil {
			return nil, nil, nil, nil, nil, fmt.Errorf("failed to scan tag row: %w", err)
		}
		tagsMap[caseId] = append(tagsMap[caseId], tag)
	}

	// Batch load situations - 1 query instead of N queries
	sitRows, err := tx.QueryContext(ctx, `SELECT case_id, situation_id FROM case_situations WHERE case_id = ANY($1)`, pq.Array(caseIds))
	if err != nil {
		return nil, nil, nil, nil, nil, fmt.Errorf("failed to batch load situations: %w", err)
	}
	defer sitRows.Close()

	for sitRows.Next() {
		var caseId, situationId string
		if err := sitRows.Scan(&caseId, &situationId); err != nil {
			return nil, nil, nil, nil, nil, fmt.Errorf("failed to scan situation row: %w", err)
		}
		situationsMap[caseId] = append(situationsMap[caseId], situationId)
	}

	// Batch load reports - 1 query instead of N queries
	reportRows, err := tx.QueryContext(ctx, `SELECT case_id, report_id FROM case_reports WHERE case_id = ANY($1)`, pq.Array(caseIds))
	if err != nil {
		return nil, nil, nil, nil, nil, fmt.Errorf("failed to batch load reports: %w", err)
	}
	defer reportRows.Close()

	for reportRows.Next() {
		var caseId, reportId string
		if err := reportRows.Scan(&caseId, &reportId); err != nil {
			return nil, nil, nil, nil, nil, fmt.Errorf("failed to scan report row: %w", err)
		}
		reportsMap[caseId] = append(reportsMap[caseId], reportId)
	}

	// Batch load related cases - 1 query instead of N queries
	relatedRows, err := tx.QueryContext(ctx, `SELECT case_id, related_case_id FROM related_cases WHERE case_id = ANY($1)`, pq.Array(caseIds))
	if err != nil {
		return nil, nil, nil, nil, nil, fmt.Errorf("failed to batch load related cases: %w", err)
	}
	defer relatedRows.Close()

	for relatedRows.Next() {
		var caseId, relatedCaseId string
		if err := relatedRows.Scan(&caseId, &relatedCaseId); err != nil {
			return nil, nil, nil, nil, nil, fmt.Errorf("failed to scan related case row: %w", err)
		}
		relatedCasesMap[caseId] = append(relatedCasesMap[caseId], relatedCaseId)
	}

	// Batch load watchers - 1 query instead of N queries
	watcherRows, err := tx.QueryContext(ctx, `SELECT case_id, asset_id FROM case_watchers WHERE case_id = ANY($1)`, pq.Array(caseIds))
	if err != nil {
		return nil, nil, nil, nil, nil, fmt.Errorf("failed to batch load watchers: %w", err)
	}
	defer watcherRows.Close()

	for watcherRows.Next() {
		var caseId, assetId string
		if err := watcherRows.Scan(&caseId, &assetId); err != nil {
			return nil, nil, nil, nil, nil, fmt.Errorf("failed to scan watcher row: %w", err)
		}
		watchersMap[caseId] = append(watchersMap[caseId], assetId)
	}

	return tagsMap, situationsMap, reportsMap, relatedCasesMap, watchersMap, nil
}

// batchLoadCaseUpdateFileAttachments loads file attachments for multiple case updates in a single query to avoid N+1 queries
// This replaces individual file attachment loading calls for better performance.
// Instead of N queries (N case updates), this executes only 1 query.
func (repo *PostgresCaseRepository) batchLoadCaseUpdateFileAttachments(ctx context.Context, tx *sql.Tx, caseUpdateIds []int) (map[int][]*casespb.CaseFileReference, error) {
	if len(caseUpdateIds) == 0 {
		return make(map[int][]*casespb.CaseFileReference), nil
	}

	// Initialize result map with empty slices for all case update IDs
	fileAttachmentsMap := make(map[int][]*casespb.CaseFileReference)
	for _, updateId := range caseUpdateIds {
		fileAttachmentsMap[updateId] = []*casespb.CaseFileReference{}
	}

	// Batch load file attachments - 1 query instead of N queries
	attachmentRows, err := tx.QueryContext(ctx, `
		SELECT case_update_id, id, case_id, file_id, caption, display_name, display_order, file_category, metadata
		FROM case_update_file_attachments 
		WHERE case_update_id = ANY($1) 
		ORDER BY case_update_id, display_order, id
	`, pq.Array(caseUpdateIds))
	if err != nil {
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return nil, fmt.Errorf("failed to batch load case update file attachments: %w", err)
	}
	defer attachmentRows.Close()

	for attachmentRows.Next() {
		var (
			caseUpdateId                                                     int
			attachmentId, caseId, fileId, caption, displayName, fileCategory string
			displayOrder                                                     int32
			metadataBytes                                                    []byte
		)
		if err := attachmentRows.Scan(&caseUpdateId, &attachmentId, &caseId, &fileId, &caption, &displayName, &displayOrder, &fileCategory, &metadataBytes); err != nil {
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return nil, fmt.Errorf("failed to scan file attachment row: %w", err)
		}

		var metadataStruct *structpb.Struct
		if len(metadataBytes) > 0 {
			var m map[string]interface{}
			if err := json.Unmarshal(metadataBytes, &m); err == nil {
				metadataStruct, _ = structpb.NewStruct(m)
			}
		}

		fileAttachmentsMap[caseUpdateId] = append(fileAttachmentsMap[caseUpdateId], &casespb.CaseFileReference{
			Id:           attachmentId,
			CaseId:       caseId,
			FileId:       fileId,
			Caption:      caption,
			DisplayName:  displayName,
			DisplayOrder: displayOrder,
			FileCategory: fileCategory,
			Metadata:     metadataStruct,
		})
	}

	return fileAttachmentsMap, nil
}

// batchLoadCaseEntityRefsAndAssetAssociations loads entity references and asset associations for multiple cases in batches to avoid N+1 queries
// This replaces individual entity ref and asset association loading calls for better performance.
// Instead of N*2 queries (N cases * 2 relationships each), this executes only 2 total queries.
func (repo *PostgresCaseRepository) batchLoadCaseEntityRefsAndAssetAssociations(ctx context.Context, tx *sql.Tx, caseIds []string) (map[string][]*entitypb.Reference, map[string][]*casespb.CaseAssetAssociation, error) {
	if len(caseIds) == 0 {
		return make(map[string][]*entitypb.Reference), make(map[string][]*casespb.CaseAssetAssociation), nil
	}

	// Initialize result maps
	entityRefsMap := make(map[string][]*entitypb.Reference)
	assetAssociationsMap := make(map[string][]*casespb.CaseAssetAssociation)

	// Initialize empty slices for all case IDs to ensure consistent results
	for _, caseId := range caseIds {
		entityRefsMap[caseId] = []*entitypb.Reference{}
		assetAssociationsMap[caseId] = []*casespb.CaseAssetAssociation{}
	}

	// Batch load entity refs - 1 query instead of N queries
	entityRows, err := tx.QueryContext(ctx, `
		SELECT case_id, ref_id, ref_type, ref_version, ref_display_name, relation_type
		FROM case_entities WHERE case_id = ANY($1)`, pq.Array(caseIds))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to batch load entity refs: %w", err)
	}
	defer entityRows.Close()

	for entityRows.Next() {
		var caseId string
		var refID, refType, disp, relType sql.NullString
		var refVersion sql.NullInt32
		if err := entityRows.Scan(&caseId, &refID, &refType, &refVersion, &disp, &relType); err != nil {
			return nil, nil, fmt.Errorf("failed to scan entity ref row: %w", err)
		}
		entityRefsMap[caseId] = append(entityRefsMap[caseId], &entitypb.Reference{
			Id:           refID.String,
			Type:         refType.String,
			Version:      refVersion.Int32,
			DisplayName:  disp.String,
			RelationType: relType.String,
		})
	}

	// Batch load asset associations - 1 query instead of N queries
	assetRows, err := tx.QueryContext(ctx, `
		SELECT case_id, id, asset_id, association_type, assigned_at, notes, assigner_asset_id
		FROM case_asset_associations WHERE case_id = ANY($1)
	`, pq.Array(caseIds))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to batch load asset associations: %w", err)
	}
	defer assetRows.Close()

	for assetRows.Next() {
		var caseId string
		var (
			associationId, asset, notes, assigner sql.NullString
			associationType                       sql.NullInt64
			assignedAt                            time.Time
		)
		if err := assetRows.Scan(&caseId, &associationId, &asset, &associationType, &assignedAt, &notes, &assigner); err != nil {
			return nil, nil, fmt.Errorf("failed to scan asset association row: %w", err)
		}
		var convertedType int32
		if associationType.Valid {
			var err error
			convertedType, err = safeInt64ToInt32(associationType.Int64)
			if err != nil {
				return nil, nil, fmt.Errorf("asset association type conversion: %w", err)
			}
		}
		assetAssociationsMap[caseId] = append(assetAssociationsMap[caseId], &casespb.CaseAssetAssociation{
			Id:              associationId.String,
			CaseId:          caseId,
			AssetId:         asset.String,
			AssociationType: casespb.CaseAssetAssociationType(convertedType),
			AssignedAt:      assignedAt.Format(time.RFC3339Nano),
			Notes:           notes.String,
			AssignerAssetId: assigner.String,
		})
	}

	return entityRefsMap, assetAssociationsMap, nil
}

// batchLoadCaseUpdatesAndStatusHistory loads case updates and status history for multiple cases in batches to avoid N+1 queries
// This replaces individual case update and status history loading calls for better performance.
// Instead of N*2 queries (N cases * 2 types each), this executes only 2 total queries.
func (repo *PostgresCaseRepository) batchLoadCaseUpdatesAndStatusHistory(ctx context.Context, tx *sql.Tx, caseIds []string) (map[string][]*casespb.CaseUpdateEntry, map[string][]*casespb.CaseStatusUpdateEntry, error) {
	if len(caseIds) == 0 {
		return make(map[string][]*casespb.CaseUpdateEntry), make(map[string][]*casespb.CaseStatusUpdateEntry), nil
	}

	// Initialize result maps
	updatesMap := make(map[string][]*casespb.CaseUpdateEntry)
	statusHistoryMap := make(map[string][]*casespb.CaseStatusUpdateEntry)

	// Initialize empty slices for all case IDs to ensure consistent results
	for _, caseId := range caseIds {
		updatesMap[caseId] = []*casespb.CaseUpdateEntry{}
		statusHistoryMap[caseId] = []*casespb.CaseStatusUpdateEntry{}
	}

	// Batch load case updates - 1 query instead of N queries
	updateRows, err := tx.QueryContext(ctx, `
		SELECT case_id, id, message, event_time, update_source, updater_id, event_type, display_name, data
		FROM case_updates 
		WHERE case_id = ANY($1) 
		ORDER BY case_id, event_time ASC
	`, pq.Array(caseIds))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to batch load case updates: %w", err)
	}
	defer updateRows.Close()

	// First pass: collect all case update IDs and build update entries
	var allUpdateIds []int
	updateIdToCaseMap := make(map[int]string)
	updateIdToIndexMap := make(map[int]int)

	for updateRows.Next() {
		var (
			caseId                                                string
			updateId                                              int
			message, eventTime, updaterId, eventType, displayName string
			updateSource                                          int32
			dataBytes                                             []byte
		)
		if err := updateRows.Scan(&caseId, &updateId, &message, &eventTime, &updateSource, &updaterId, &eventType, &displayName, &dataBytes); err != nil {
			return nil, nil, fmt.Errorf("failed to scan case update row: %w", err)
		}

		var dataStruct *structpb.Struct
		if len(dataBytes) > 0 {
			var m map[string]interface{}
			if err := json.Unmarshal(dataBytes, &m); err == nil {
				dataStruct, _ = structpb.NewStruct(m)
			}
		}

		updateEntry := &casespb.CaseUpdateEntry{
			Message:      message,
			EventTime:    eventTime,
			UpdateSource: situationpb.UpdateSource(updateSource),
			UpdaterId:    updaterId,
			EventType:    eventType,
			DisplayName:  displayName,
			Data:         dataStruct,
			// FileAttachments will be populated below
		}

		// Track update ID to case mapping and index
		allUpdateIds = append(allUpdateIds, updateId)
		updateIdToCaseMap[updateId] = caseId
		updateIdToIndexMap[updateId] = len(updatesMap[caseId])

		updatesMap[caseId] = append(updatesMap[caseId], updateEntry)
	}

	// Second pass: batch load file attachments for all updates
	if len(allUpdateIds) > 0 {
		fileAttachmentsMap, err := repo.batchLoadCaseUpdateFileAttachments(ctx, tx, allUpdateIds)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to batch load file attachments for case updates: %w", err)
		}

		// Third pass: assign file attachments to updates
		for updateId, attachments := range fileAttachmentsMap {
			if caseId, exists := updateIdToCaseMap[updateId]; exists {
				if updateIndex, indexExists := updateIdToIndexMap[updateId]; indexExists {
					if updateIndex < len(updatesMap[caseId]) {
						updatesMap[caseId][updateIndex].FileAttachments = attachments
					}
				}
			}
		}
	}

	// Batch load status history - 1 query instead of N queries
	statusRows, err := tx.QueryContext(ctx, `
		SELECT case_id, timestamp, new_status, previous_status, note, updater_id, update_source
		FROM case_status_updates 
		WHERE case_id = ANY($1) 
		ORDER BY case_id, timestamp ASC
	`, pq.Array(caseIds))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to batch load case status history: %w", err)
	}
	defer statusRows.Close()

	for statusRows.Next() {
		var (
			caseId                                  string
			timestamp, note, updaterId              string
			newStatus, previousStatus, updateSource int32
		)
		if err := statusRows.Scan(&caseId, &timestamp, &newStatus, &previousStatus, &note, &updaterId, &updateSource); err != nil {
			return nil, nil, fmt.Errorf("failed to scan case status history row: %w", err)
		}

		statusHistoryMap[caseId] = append(statusHistoryMap[caseId], &casespb.CaseStatusUpdateEntry{
			Timestamp:      timestamp,
			NewStatus:      casespb.CaseStatus(newStatus),
			PreviousStatus: casespb.CaseStatus(previousStatus),
			Note:           note,
			UpdaterId:      updaterId,
			UpdateSource:   situationpb.UpdateSource(updateSource),
		})
	}

	return updatesMap, statusHistoryMap, nil
}

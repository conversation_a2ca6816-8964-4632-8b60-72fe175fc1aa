package main

import (
	database "common/database"
	"common/herosentry"
	"common/middleware"
	"context"
	"database/sql"
	"log"
	"net/http"
	"os"
	"time"

	orgs "orgs/internal"

	orgRepository "orgs/internal/data"
	"orgs/internal/setup"
)

// fatalWithFlush ensures Sentry events are sent before exiting
func fatalWithFlush(err error, msg string) {
	herosentry.CaptureException(context.Background(), err, herosentry.ErrorTypeInternal, msg)
	herosentry.Flush()
	log.Fatalf("%s: %v", msg, err)
}

func main() {
	// Initialize herosentry for error tracking and performance monitoring
	// Filter out validation and not-found errors to reduce noise
	falseVal := false
	err := herosentry.Init("orgs-service", herosentry.Config{
		CustomSamplingRules: map[string]float64{
			// 1% sampling for all List operations
			"*Service.List*": 0.01,
		},
		// Filter out expected errors to reduce Sentry noise
		CaptureValidationErrors: &falseVal, // Don't capture validation/bad request errors
		CaptureNotFoundErrors:   &falseVal, // Don't capture 404/not found errors
	})
	if err != nil {
		log.Fatalf("Herosentry initialization failed: %v", err)
	}
	defer herosentry.Flush() //nolint:gocritic // fatalWithFlush handles flush before exit

	baseMux := http.NewServeMux()

	// Initialize all DB
	repositoryType := os.Getenv("REPO_TYPE")

	var postGresDB *sql.DB = nil

	if repositoryType == "postgres" {
		databaseURL, err := database.CreateDBURL()
		if err != nil {
			fatalWithFlush(err, "Failed to get postgres db url")
		}
		var openError error
		postGresDB, openError = sql.Open("postgres", databaseURL)
		if openError != nil {
			fatalWithFlush(openError, "Failed to open postgres db")
		}

		// Configure connection pool - orgs service handles organization management
		// Lower frequency admin operations
		postGresDB.SetMaxOpenConns(30) // Modest allocation for org management
		postGresDB.SetMaxIdleConns(8)  // Keep minimal idle connections
		postGresDB.SetConnMaxLifetime(5 * time.Minute)
		postGresDB.SetConnMaxIdleTime(90 * time.Second)
	}

	// Initialize Asset Repository
	orgRepo, orgDB, err := orgRepository.NewOrgRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize org repository")
	}

	// Initialize setup
	// This runs the cold-start bootstrap setup,
	// creating the meta org and root admin user
	// This should only be run once per environment
	err = setup.Initialize(setup.SetupConfig{
		DB:      postGresDB,
		OrgRepo: orgRepo,
	})
	if err != nil {
		fatalWithFlush(err, "Failed to initialize setup")
	}

	// Register all endpoints.
	orgs.RegisterRoutes(baseMux, orgDB, orgRepo)

	// Wrap with database pool monitoring middleware if database is available
	var mux http.Handler = baseMux
	if postGresDB != nil {
		mux = herosentry.DBPoolMiddleware(postGresDB)(baseMux)
	}

	// Additional endpoints.
	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.orgs.v1.OrgsService",
		},
		HealthResponse: "YES HOW CAN I HELP YOU",
	})

	skipPerms := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"

	srv, err := middleware.NewServerWithHealth(
		mux,
		healthMux,
		!skipPerms,
	)
	if err != nil {
		fatalWithFlush(err, "Failed to create server")
	}

	if err := middleware.StartServer(srv); err != nil {
		fatalWithFlush(err, "Failed to serve")
	}
}

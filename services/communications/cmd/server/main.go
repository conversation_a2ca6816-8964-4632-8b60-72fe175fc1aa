package main

import (
	"context"
	"database/sql"
	"errors"
	"log"
	"net/http"
	"os"
	"time"

	"common/database"
	"common/herosentry"
	"common/middleware"

	"github.com/joho/godotenv"
	_ "github.com/lib/pq" // PostgreSQL driver

	cellularcall "communications/internal/cellularcall"
	cellularcallData "communications/internal/cellularcall/data"
	chatApi "communications/internal/chat/api/connect"
	chatDB "communications/internal/chat/data"
	chatUsecase "communications/internal/chat/usecase"
	pttApi "communications/internal/ptt/api/connect"
	pttUsecase "communications/internal/ptt/usecase"
	videocallApi "communications/internal/videocall/api/connect"
	videocallUsecase "communications/internal/videocall/usecase"
	conversationv1connect "proto/hero/communications/v1/conversationconnect"
)

// ✅ IT'S WORKING AGAIN! ✅

const LocalEnv = "local"

// fatalWithFlush ensures Sentry events are sent before exiting
func fatalWithFlush(err error, msg string) {
	herosentry.CaptureException(context.Background(), err, herosentry.ErrorTypeInternal, msg)
	herosentry.Flush()
	log.Fatalf("%s: %v", msg, err)
}

// NewVideoCallServiceServer sets up the Connect-based Video Call service.
func NewVideoCallServiceServer() (string, http.Handler) {
	if os.Getenv("GO_ENV") == LocalEnv {
		if err := godotenv.Load(); err != nil {
			log.Println("No .env file found or error loading .env")
		}
	}
	agoraAppId := os.Getenv("AGORA_APP_ID")
	agoraAppCertificate := os.Getenv("AGORA_APP_CERTIFICATE")
	if agoraAppId == "" || agoraAppCertificate == "" {
		fatalWithFlush(errors.New("missing environment variables"), "AGORA_APP_ID or AGORA_APP_CERTIFICATE not provided")
	}

	videoCallUsecase := videocallUsecase.NewVideoCallUsecase(agoraAppId, agoraAppCertificate)
	videoCallServiceServer := videocallApi.NewVideoCallServiceServer(videoCallUsecase)
	path, handler := conversationv1connect.NewVideoCallServiceHandler(videoCallServiceServer)
	return path, handler
}

// NewChatServiceServer sets up the Connect-based Chat service.
func NewChatServiceServer() (string, http.Handler) {
	if os.Getenv("GO_ENV") == LocalEnv {
		if err := godotenv.Load(); err != nil {
			log.Println("No .env file found or error loading .env")
		}
	}
	agoraAppId := os.Getenv("AGORA_APP_ID")
	agoraAppCertificate := os.Getenv("AGORA_APP_CERTIFICATE")
	agoraChatAppName := os.Getenv("AGORA_CHAT_APP_NAME")
	agoraChatOrgName := os.Getenv("AGORA_CHAT_ORG_NAME")
	agoraChatHostURL := os.Getenv("AGORA_CHAT_HOST_URL")
	agoraChatAppKey := os.Getenv("AGORA_CHAT_APP_ID")

	// Validate required environment variables
	if agoraAppId == "" || agoraAppCertificate == "" || agoraChatAppName == "" ||
		agoraChatOrgName == "" || agoraChatHostURL == "" || agoraChatAppKey == "" {
		if agoraAppId == "" {
			fatalWithFlush(errors.New("missing environment variable"), "AGORA_APP_ID not provided")
		}
		if agoraAppCertificate == "" {
			fatalWithFlush(errors.New("missing environment variable"), "AGORA_APP_CERTIFICATE not provided")
		}
		if agoraChatAppName == "" {
			fatalWithFlush(errors.New("missing environment variable"), "AGORA_CHAT_APP_NAME not provided")
		}
		if agoraChatOrgName == "" {
			fatalWithFlush(errors.New("missing environment variable"), "AGORA_CHAT_ORG_NAME not provided")
		}
		if agoraChatHostURL == "" {
			fatalWithFlush(errors.New("missing environment variable"), "AGORA_CHAT_HOST_URL not provided")
		}
		if agoraChatAppKey == "" {
			fatalWithFlush(errors.New("missing environment variable"), "AGORA_CHAT_APP_ID not provided")
		}
	}

	chatRepo := chatDB.NewInMemoryChatRepo()
	chatUsecase := chatUsecase.NewChatUsecase(
		agoraAppId,
		agoraAppCertificate,
		agoraChatOrgName,
		agoraChatAppName,
		agoraChatHostURL,
		agoraChatAppKey,
		chatRepo,
	)

	chatServiceServer := chatApi.NewVideoCallServiceServer(chatUsecase)
	path, handler := conversationv1connect.NewChatServiceHandler(chatServiceServer)
	return path, handler
}

// NewVideoCallServiceServer sets up the Connect-based Video Call service.
func NewPTTServiceServer() (string, http.Handler) {
	if os.Getenv("GO_ENV") == LocalEnv {
		if err := godotenv.Load(); err != nil {
			log.Println("No .env file found or error loading .env")
		}
	}

	assetsURL := os.Getenv("WORKFLOW_SERVICE_URL")
	if assetsURL == "" {
		fatalWithFlush(errors.New("missing environment variable"), "WORKFLOW_SERVICE_URL not provided")
	}

	pttUsecase := pttUsecase.NewPTTUsecase(assetsURL)
	pttServiceServer := pttApi.NewPTTServiceServer(pttUsecase)
	path, handler := conversationv1connect.NewPTTServiceHandler(pttServiceServer)
	return path, handler
}

func main() {
	// Initialize herosentry for error tracking and performance monitoring
	// Configure custom sampling rules for repetitive operations
	// Filter out validation and not-found errors to reduce noise
	falseVal := false
	err := herosentry.Init("communications-service", herosentry.Config{
		CustomSamplingRules: map[string]float64{
			"CellularCallService.GetQueueStatus":    0.01, // Sample 1% of queue status checks
			"CellularCallService.GetAssetHeldCalls": 0.01, // Sample 5% of held calls checks
		},
		// Filter out expected errors to reduce Sentry noise
		CaptureValidationErrors: &falseVal, // Don't capture validation/bad request errors
		CaptureNotFoundErrors:   &falseVal, // Don't capture 404/not found errors
	})
	if err != nil {
		log.Fatalf("Herosentry initialization failed: %v", err)
	}
	defer herosentry.Flush() //nolint:gocritic // fatalWithFlush handles flush before exit

	baseMux := http.NewServeMux()

	// Initialize database connection
	databaseURL, err := database.CreateDBURL()
	if err != nil {
		fatalWithFlush(err, "Failed to create database URL")
	}
	postGresDB, openError := sql.Open("postgres", databaseURL)
	if openError != nil {
		fatalWithFlush(openError, "Failed to open postgres database")
	}

	// Configure connection pool for real-time call handling
	// Moderate allocation for voice/video call management
	postGresDB.SetMaxOpenConns(50) // Real-time ops need reliable connections
	postGresDB.SetMaxIdleConns(15) // 30% idle for quick call handling
	postGresDB.SetConnMaxLifetime(5 * time.Minute)
	postGresDB.SetConnMaxIdleTime(90 * time.Second)

	// Initialize Call Queue Repository
	callQueueRepo, callQueueDB, err := cellularcallData.NewCallQueueRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize call queue repository")
	}

	// Set up service handlers
	videoCallAPIPath, videoCallAPIHandler := NewVideoCallServiceServer()
	chatAPIPath, chatAPIHandler := NewChatServiceServer()
	pttAPIPath, pttAPIHandler := NewPTTServiceServer()

	// Register video call, chat, and PTT endpoints
	baseMux.Handle(videoCallAPIPath, videoCallAPIHandler)
	baseMux.Handle(chatAPIPath, chatAPIHandler)
	baseMux.Handle(pttAPIPath, pttAPIHandler)

	// Register cellular call endpoints with all required configuration
	twilioAccountSid := os.Getenv("TWILIO_ACCOUNT_SID")
	twilioAPIKeySid := os.Getenv("TWILIO_API_KEY_SID")
	twilioAPIKeySecret := os.Getenv("TWILIO_API_KEY_SECRET")
	twilioAuthToken := os.Getenv("TWILIO_AUTH_TOKEN")

	waitURL := os.Getenv("CALL_QUEUE_WAIT_URL")
	if waitURL == "" {
		waitURL = "https://twimlets.com/holdmusic?Bucket=com.twilio.music.classical"
	}

	// Validate required credentials
	if twilioAccountSid == "" || twilioAPIKeySid == "" || twilioAPIKeySecret == "" || twilioAuthToken == "" {
		if twilioAccountSid == "" {
			fatalWithFlush(errors.New("missing environment variable"), "TWILIO_ACCOUNT_SID not provided")
		}
		if twilioAPIKeySid == "" {
			fatalWithFlush(errors.New("missing environment variable"), "TWILIO_API_KEY_SID not provided")
		}
		if twilioAPIKeySecret == "" {
			fatalWithFlush(errors.New("missing environment variable"), "TWILIO_API_KEY_SECRET not provided")
		}
		if twilioAuthToken == "" {
			fatalWithFlush(errors.New("missing environment variable"), "TWILIO_AUTH_TOKEN not provided")
		}
	}

	situationsURL := os.Getenv("WORKFLOW_SERVICE_URL")
	if situationsURL == "" {
		fatalWithFlush(errors.New("missing environment variable"), "WORKFLOW_SERVICE_URL not provided")
	}

	commsServerPublicDomain := os.Getenv("COMMS_SERVER_PUBLIC_DOMAIN")
	if commsServerPublicDomain == "" {
		fatalWithFlush(errors.New("missing environment variable"), "COMMS_SERVER_PUBLIC_DOMAIN not provided")
	}

	orgServiceURL := os.Getenv("ORGS_SERVICE_URL")
	if orgServiceURL == "" {
		fatalWithFlush(errors.New("missing environment variable"), "ORGS_SERVICE_URL not provided")
	}

	// Use the same workflow service URL for assets
	assetsURL := situationsURL

	_, err = cellularcall.RegisterRoutes(
		baseMux,
		callQueueDB,
		callQueueRepo,
		twilioAccountSid,
		twilioAPIKeySid,
		twilioAPIKeySecret,
		twilioAuthToken,
		waitURL,
		situationsURL,
		orgServiceURL,
		commsServerPublicDomain,
		assetsURL,
	)
	if err != nil {
		fatalWithFlush(err, "Failed to register cellular call routes")
	}

	// Wrap with database pool monitoring middleware
	mux := herosentry.DBPoolMiddleware(postGresDB)(baseMux)

	// Register health and reflection endpoints

	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.conversation.v1.VideoCallService",
			"hero.conversation.v1.ChatService",
			"hero.conversation.v1.CellularCallService",
			"hero.conversation.v1.PTTService",
		},
		HealthResponse: "YES HOW CAN I HELP YOU",
	})

	skipPerms := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"

	srv, err := middleware.NewServerWithHealth(
		mux,
		healthMux,
		!skipPerms,
	)
	if err != nil {
		fatalWithFlush(err, "Failed to create server")
	}

	if err := middleware.StartServer(srv); err != nil {
		fatalWithFlush(err, "Failed to serve")
	}
}

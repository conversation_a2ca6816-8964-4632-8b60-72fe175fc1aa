package data

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	cmncontext "common/context"
	"common/database"
	"common/herosentry"
)

// Constants removed - herosentry automatically infers operation types

// postgresCallQueueRepository is a PostgreSQL implementation of CallQueueRepository
type postgresCallQueueRepository struct {
	db            *sql.DB
	queueStrategy QueueStrategy
}

// NewPostgresCallQueueRepository creates a new PostgreSQL-backed call queue repository
func NewPostgresCallQueueRepository(db *sql.DB) CallQueueRepository {
	return &postgresCallQueueRepository{
		db: db,
	}
}

// Helper to scan a single QueuedCall from a scannable source
func scanSingleQueuedCall(s interface{ Scan(...interface{}) error }) (QueuedCall, error) {
	var (
		call           QueuedCall
		attributesJSON []byte
		assetID        sql.NullString
		callerName     sql.NullString
		situationID    sql.NullString
	)
	err := s.Scan(
		&call.CallSID,
		&call.Caller,
		&callerName,
		&call.EnqueueTime,
		&assetID,
		&situationID,
		&call.State,
		&call.Direction,
		&attributesJSON,
		&call.CallStartTime,
		&call.CallEndTime,
		&call.LastHoldStart,
	)
	if err != nil {
		return QueuedCall{}, err
	}

	if assetID.Valid {
		call.AssetID = assetID.String
	}
	if callerName.Valid {
		call.CallerName = callerName.String
	} else {
		call.CallerName = "" // Default for NULL
	}
	if situationID.Valid {
		call.SituationID = situationID.String
	} else {
		call.SituationID = "" // Default for NULL
	}

	if len(attributesJSON) > 0 && string(attributesJSON) != "null" {
		err = json.Unmarshal(attributesJSON, &call.Attributes)
		if err != nil {
			return QueuedCall{}, fmt.Errorf("failed to parse attributes: %w", err)
		}
	} else {
		call.Attributes = make(map[string]string)
	}

	return call, nil
}

// Helper function for *sql.Row
func scanQueuedCall(row *sql.Row) (QueuedCall, error) {
	return scanSingleQueuedCall(row)
}

// Helper function for *sql.Rows
func scanQueuedCalls(rows *sql.Rows) ([]QueuedCall, error) {
	var calls []QueuedCall
	defer rows.Close()
	for rows.Next() {
		call, err := scanSingleQueuedCall(rows)
		if err != nil {
			return nil, err
		}
		calls = append(calls, call)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return calls, nil
}

func (r *postgresCallQueueRepository) GetOrgTwilioDetails(ctx context.Context) (OrgTwilioDetails, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.GetOrgTwilioDetails")
	defer finishSpan()
	span.SetTag("db.operation", "SELECT")
	span.SetTag("db.table", "orgs")

	return database.WithSession(r.db, spanContext, nil, func(tx *sql.Tx) (OrgTwilioDetails, error) {
		orgId := cmncontext.GetOrgId(spanContext)
		span.SetTag("org_id", fmt.Sprintf("%d", orgId))

		row := tx.QueryRowContext(spanContext, `
			SELECT twiml_app_sid, twilio_number, twilio_api_user_id, name
			FROM orgs
			where id = $1
		`, orgId)

		var twimlAppSid, twilioNumber, orgName string
		var twilioApiUserId sql.NullString
		err := row.Scan(&twimlAppSid, &twilioNumber, &twilioApiUserId, &orgName)
		if err != nil {
			span.SetTag("error", "true")
			span.SetTag("db.rows_affected", "0")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get org twilio details from database")
			return OrgTwilioDetails{}, fmt.Errorf("failed to get org twilio details: %w", err)
		}

		span.SetTag("db.rows_affected", "1")

		details := OrgTwilioDetails{
			TwimlAppSid:  twimlAppSid,
			TwilioNumber: twilioNumber,
			OrgName:      orgName,
		}

		log.Println("GetOrgTwilioDetails twilioApiUserId", twilioApiUserId, twilioApiUserId.String)

		if twilioApiUserId.Valid {
			details.TwilioApiUserId = twilioApiUserId.String
		} else {
			details.TwilioApiUserId = "" // Assign empty string if NULL
		}

		return details, nil
	})
}

// EnqueueCall adds a call to the waiting queue
func (r *postgresCallQueueRepository) EnqueueCall(ctx context.Context, call QueuedCall) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.EnqueueCall")
	defer finishSpan()
	span.SetTag("db.operation", "INSERT")
	span.SetTag("db.table", "call_queue")
	span.SetTag("call_sid", call.CallSID)
	span.SetTag("caller", call.Caller)
	span.SetTag("direction", call.Direction)

	return database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		// Get org ID from context
		orgID := cmncontext.GetOrgId(spanContext)

		// Validate inputs
		if call.CallSID == "" {
			return ErrInvalidInput
		}

		// Set enqueue time if not already set
		if call.EnqueueTime.IsZero() {
			call.EnqueueTime = time.Now()
		}

		// Set default direction to inbound if not provided
		if call.Direction == "" {
			call.Direction = CallDirectionInbound
		}

		// Set state to waiting
		call.State = CallStateWaiting

		// Convert attributes to JSON
		attributesJSON, err := json.Marshal(call.Attributes)
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}

		log.Printf("EnqueueCall: %+v", call)

		var query string
		var args []interface{}

		// WARNING: If we try to insert AssetID as "", the insert query will SILENTLY fail
		if call.AssetID != "" {
			query = `
				INSERT INTO call_queue (
					call_sid, org_id, 
					caller, caller_name, asset_id, enqueue_time,
					situation_id, state, direction, attributes,
					call_start_time, call_end_time, last_hold_start
				) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
			`
			args = []interface{}{
				call.CallSID,
				orgID,
				call.Caller,
				call.CallerName,
				call.AssetID,
				call.EnqueueTime,
				call.SituationID,
				call.State,
				call.Direction,
				attributesJSON,
				call.CallStartTime,
				call.CallEndTime,
				call.LastHoldStart,
			}
		} else {
			query = `
				INSERT INTO call_queue (
					call_sid, org_id, 
					caller, caller_name, enqueue_time,
					situation_id, state, direction, attributes,
					call_start_time, call_end_time, last_hold_start
				) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
			`
			args = []interface{}{
				call.CallSID,
				orgID,
				call.Caller,
				call.CallerName,
				call.EnqueueTime,
				call.SituationID,
				call.State,
				call.Direction,
				attributesJSON,
				call.CallStartTime,
				call.CallEndTime,
				call.LastHoldStart,
			}
		}

		preparedStatement, err := tx.PrepareContext(spanContext, query)
		if err != nil {
			return err
		}
		defer preparedStatement.Close()

		_, executionError := preparedStatement.ExecContext(spanContext, args...)
		if executionError != nil {
			span.SetTag("error", "true")
			span.SetTag("db.rows_affected", "0")
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to insert call into database")
			return fmt.Errorf("failed to insert call: %w", executionError)
		}

		span.SetTag("db.rows_affected", "1")
		log.Printf("COMPLETED Enqueued call: %+v", call)

		return nil
	})
}

// DequeueCall gets the next call from the queue and assigns it to an asset
func (r *postgresCallQueueRepository) DequeueCall(ctx context.Context, assetID string) (QueuedCall, bool, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.DequeueCall")
	defer finishSpan()
	span.SetTag("asset_id", assetID)

	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if assetID == "" {
			return ErrInvalidInput
		}

		var row *sql.Row

		// Get next call based on strategy or default FIFO
		if r.queueStrategy != nil {
			// Get all waiting calls to apply strategy
			rows, err := tx.QueryContext(spanContext, `
				SELECT call_sid, caller, caller_name, enqueue_time, 
					asset_id, situation_id, state, direction, attributes,
					call_start_time, call_end_time, last_hold_start
				FROM call_queue 
				WHERE state = $1
				ORDER BY enqueue_time ASC
			`, CallStateWaiting)
			if err != nil {
				return fmt.Errorf("failed to query waiting calls: %w", err)
			}

			waitingCalls, err := scanQueuedCalls(rows)
			if err != nil {
				return fmt.Errorf("failed to scan waiting calls: %w", err)
			}

			if len(waitingCalls) == 0 {
				return nil // No calls found
			}

			// Apply strategy
			selectedCall, _, ok, errStrategy := r.queueStrategy.SelectNextCall(ctx, waitingCalls)
			if errStrategy != nil {
				return fmt.Errorf("strategy selection failed: %w", errStrategy)
			}
			if !ok {
				return nil // Strategy didn't select a call
			}
			call = selectedCall
			found = true

		} else {
			// Default FIFO behavior
			row = tx.QueryRowContext(spanContext, `
				SELECT call_sid, caller, caller_name, enqueue_time, 
					asset_id, situation_id, state, direction, attributes,
					call_start_time, call_end_time, last_hold_start
			FROM call_queue 
				WHERE state = $1
				ORDER BY enqueue_time ASC
				LIMIT 1
				FOR UPDATE SKIP LOCKED
			`, CallStateWaiting)

			var scanErr error
			call, scanErr = scanQueuedCall(row)
			if scanErr == sql.ErrNoRows {
				return nil // No calls found
			}
			if scanErr != nil {
				return fmt.Errorf("failed to scan call: %w", scanErr)
			}
			found = true
		}

		// Update the call to assign it to the asset and set start time
		call.AssetID = assetID
		call.State = CallStateActive

		attributesJSON, err := json.Marshal(call.Attributes)
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}

		_, err = tx.ExecContext(ctx, `
			UPDATE call_queue 
			SET asset_id = $1, state = $2, attributes = $3, call_start_time = NOW()
			WHERE call_sid = $4
		`, assetID, CallStateActive, attributesJSON, call.CallSID)
		if err != nil {
			return fmt.Errorf("failed to update call: %w", err)
		}

		// Re-fetch to get the DB-set CallStartTime
		rowAfterUpdate := tx.QueryRowContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue WHERE call_sid = $1`, call.CallSID)
		updatedCall, scanErrAfterUpdate := scanQueuedCall(rowAfterUpdate)
		if scanErrAfterUpdate != nil {
			return fmt.Errorf("failed to re-scan call after update: %w", scanErrAfterUpdate)
		}
		call = updatedCall

		return nil
	})

	if err != nil {
		return QueuedCall{}, false, err
	}

	return call, found, nil
}

// HoldCall places an active call on hold
func (r *postgresCallQueueRepository) HoldCall(ctx context.Context, callSID string, assetID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.HoldCall")
	defer finishSpan()
	span.SetTag("db.operation", "SELECT_UPDATE")
	span.SetTag("db.table", "call_queue")
	span.SetTag("call_sid", callSID)
	span.SetTag("asset_id", assetID)

	return database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if callSID == "" || assetID == "" {
			span.SetTag("error", "true")
			span.SetTag("error.type", "validation")
			herosentry.CaptureException(spanContext, ErrInvalidInput, herosentry.ErrorTypeValidation, "Invalid input for hold call - missing callSID or assetID")
			return ErrInvalidInput
		}

		// Check if this is the asset's active call and lock the row
		row := tx.QueryRowContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE call_sid = $1 AND asset_id = $2 AND state = $3
			FOR UPDATE
		`, callSID, assetID, CallStateActive)

		_, err := scanQueuedCall(row)
		if err == sql.ErrNoRows {
			span.SetTag("error", "true")
			span.SetTag("error.type", "business_logic")
			span.SetTag("db.rows_affected", "0")
			herosentry.CaptureException(spanContext, ErrNoActiveCall, herosentry.ErrorTypeNotFound, "No active call found for hold operation")
			return ErrNoActiveCall
		}
		if err != nil {
			span.SetTag("error", "true")
			span.SetTag("error.type", "database")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get active call for hold")
			return fmt.Errorf("failed to get active call for hold: %w", err)
		}

		// Update state to hold and set last_hold_start time
		_, err = tx.ExecContext(spanContext, `
			UPDATE call_queue 
			SET state = $1, last_hold_start = NOW()
			WHERE call_sid = $2
		`, CallStateHold, callSID)
		if err != nil {
			span.SetTag("error", "true")
			span.SetTag("db.rows_affected", "0")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update call state to hold")
			return fmt.Errorf("failed to update call state to hold: %w", err)
		}

		span.SetTag("db.rows_affected", "1")
		return nil
	})
}

// ResumeCall takes a call off hold
func (r *postgresCallQueueRepository) ResumeCall(ctx context.Context, callSID string, assetID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.ResumeCall")
	defer finishSpan()
	span.SetTag("db.operation", "SELECT_UPDATE")
	span.SetTag("db.table", "call_queue")
	span.SetTag("call_sid", callSID)
	span.SetTag("asset_id", assetID)

	return database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if callSID == "" || assetID == "" {
			span.SetTag("error", "true")
			span.SetTag("error.type", "validation")
			herosentry.CaptureException(spanContext, ErrInvalidInput, herosentry.ErrorTypeValidation, "Invalid input for resume call - missing callSID or assetID")
			return ErrInvalidInput
		}

		// Check if the call is on hold for this asset
		row := tx.QueryRowContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE call_sid = $1 AND asset_id = $2 AND state = $3
			FOR UPDATE
		`, callSID, assetID, CallStateHold)

		_, err := scanQueuedCall(row)
		if err == sql.ErrNoRows {
			span.SetTag("error", "true")
			span.SetTag("error.type", "business_logic")
			span.SetTag("db.rows_affected", "0")
			herosentry.CaptureException(spanContext, ErrCallNotFound, herosentry.ErrorTypeNotFound, "Call not found for resume operation")
			return ErrCallNotFound
		}
		if err != nil {
			span.SetTag("error", "true")
			span.SetTag("error.type", "database")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Database error during resume call operation")
			return fmt.Errorf("failed to get held call for resume: %w", err)
		}

		// Update state to active and clear last_hold_start
		_, err = tx.ExecContext(spanContext, `
			UPDATE call_queue 
			SET state = $1, last_hold_start = NULL
			WHERE call_sid = $2
		`, CallStateActive, callSID)
		if err != nil {
			span.SetTag("error", "true")
			span.SetTag("db.rows_affected", "0")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update call state in database")
			return fmt.Errorf("failed to update call state to active: %w", err)
		}

		span.SetTag("db.rows_affected", "1")
		return nil
	})
}

// RevertSelectiveClaim reverts a call from pending_selective_assignment back to waiting state
func (r *postgresCallQueueRepository) RevertSelectiveClaim(ctx context.Context, callSID string) (bool, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresCallQueueRepository.RevertSelectiveClaim")
	defer finish()
	span.SetTag("call.sid", callSID)

	return database.WithSession(r.db, spanContext, nil, func(tx *sql.Tx) (bool, error) {
		if callSID == "" {
			return false, ErrInvalidInput
		}

		// Update state to waiting and clear asset_id only if currently in pending_selective_assignment state
		result, err := tx.ExecContext(spanContext, `
			UPDATE call_queue 
			SET state = $1, asset_id = NULL
			WHERE call_sid = $2 AND state = $3
		`, CallStateWaiting, callSID, CallStatePendingSelectiveAssign)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to revert call state", true)
			return false, fmt.Errorf("failed to revert call state: %w", err)
		}

		// Check if any rows were affected
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Could not get rows affected on RevertSelectiveClaim", true)
			return true, fmt.Errorf("could not get rows affected on RevertSelectiveClaim for %s: %w", callSID, err)
		}

		// Return true if a row was updated, false otherwise (idempotent)
		return rowsAffected > 0, nil
	})
}

// EndCall terminates a call in any state
func (r *postgresCallQueueRepository) EndCall(ctx context.Context, callSID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.EndCall")
	defer finishSpan()
	span.SetTag("db.operation", "UPDATE")
	span.SetTag("db.table", "call_queue")
	span.SetTag("call_sid", callSID)

	return database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if callSID == "" {
			span.SetTag("error", "true")
			span.SetTag("error.type", "validation")
			herosentry.CaptureException(spanContext, ErrInvalidInput, herosentry.ErrorTypeValidation, "Invalid input for end call - missing callSID")
			return ErrInvalidInput
		}

		// Update state to ended and set call_end_time
		result, err := tx.ExecContext(spanContext, `
			UPDATE call_queue 
			SET state = $1, call_end_time = NOW()
			WHERE call_sid = $2
		`, CallStateEnded, callSID)
		if err != nil {
			span.SetTag("error", "true")
			span.SetTag("db.rows_affected", "0")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update call state in database")
			return fmt.Errorf("failed to update call state to ended: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			log.Printf("Warning: could not get rows affected on EndCall for %s: %v", callSID, err)
		}
		if rowsAffected == 0 {
			span.SetTag("error", "true")
			span.SetTag("db.rows_affected", "0")
			herosentry.CaptureException(spanContext, ErrCallNotFound, herosentry.ErrorTypeNotFound, "Call not found when trying to end call")
			return ErrCallNotFound
		}

		span.SetTag("db.rows_affected", fmt.Sprintf("%d", rowsAffected))
		return nil
	})
}

// GetActiveCall returns the current active call for an asset
func (r *postgresCallQueueRepository) GetActiveCall(ctx context.Context, assetID string) (QueuedCall, bool, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.GetActiveCall")
	defer finishSpan()
	span.SetTag("db.operation", "SELECT")
	span.SetTag("db.table", "call_queue")
	span.SetTag("asset_id", assetID)

	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if assetID == "" {
			span.SetTag("error", "true")
			span.SetTag("error.type", "validation")
			herosentry.CaptureException(spanContext, ErrInvalidInput, herosentry.ErrorTypeValidation, "Invalid input for GetActiveCall - missing assetID")
			return ErrInvalidInput
		}

		row := tx.QueryRowContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE asset_id = $1 AND state = $2
		`, assetID, CallStateActive)

		var err error
		call, err = scanQueuedCall(row)
		if err == sql.ErrNoRows {
			span.SetTag("db.rows_affected", "0")
			return nil
		}
		if err != nil {
			span.SetTag("error", "true")
			span.SetTag("error.type", "database")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan active call from database")
			return fmt.Errorf("failed to scan call: %w", err)
		}
		found = true
		span.SetTag("db.rows_affected", "1")
		span.SetTag("call_sid", call.CallSID)
		return nil
	})

	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get active call")
		return QueuedCall{}, false, err
	}

	return call, found, nil
}

// GetHeldCalls returns all calls on hold for an asset
func (r *postgresCallQueueRepository) GetHeldCalls(ctx context.Context, assetID string) ([]QueuedCall, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.GetHeldCalls")
	defer finishSpan()
	span.SetTag("db.operation", "SELECT")
	span.SetTag("db.table", "call_queue")
	span.SetTag("asset_id", assetID)

	var calls []QueuedCall

	err := database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if assetID == "" {
			span.SetTag("error", "true")
			span.SetTag("error.type", "validation")
			herosentry.CaptureException(spanContext, ErrInvalidInput, herosentry.ErrorTypeValidation, "Invalid input for GetHeldCalls - missing assetID")
			return ErrInvalidInput
		}

		rows, err := tx.QueryContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE asset_id = $1 AND state = $2
			ORDER BY enqueue_time ASC
		`, assetID, CallStateHold)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query held calls", true)
			return fmt.Errorf("failed to query held calls: %w", err)
		}
		defer rows.Close()

		calls, err = scanQueuedCalls(rows)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan held calls", true)
			return fmt.Errorf("failed to scan held calls: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return calls, nil
}

// GetCallBySituationID finds a call by its associated situation
func (r *postgresCallQueueRepository) GetCallBySituationID(ctx context.Context, situationID string) (QueuedCall, bool, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.GetCallBySituationID")
	defer finishSpan()
	span.SetTag("situation_id", situationID)

	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if situationID == "" {
			return ErrInvalidInput
		}

		row := tx.QueryRowContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE situation_id = $1
		`, situationID)

		var err error
		call, err = scanQueuedCall(row)
		if err == sql.ErrNoRows {
			return nil
		}
		if err != nil {
			return fmt.Errorf("failed to scan call: %w", err)
		}
		found = true
		return nil
	})

	if err != nil {
		return QueuedCall{}, false, err
	}

	return call, found, nil
}

// GetQueueStatus returns current queue information
// Note: This operation is frequently polled, so we use 10% sampling to reduce observability overhead
func (r *postgresCallQueueRepository) GetQueueStatus(ctx context.Context) (int, []QueuedCall, error) {
	// The sampling is now handled by herosentry's custom sampling rules in main.go
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.GetQueueStatus")
	defer finishSpan()
	span.SetTag("db.operation", "SELECT")
	span.SetTag("db.table", "call_queue")

	var queueSize int
	var waitingCalls []QueuedCall

	err := database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		// Get queue size - create child span
		_, countSpan, countFinishSpan := herosentry.StartSpan(spanContext, "PostgresCallQueueRepository.CountWaitingCalls")
		defer countFinishSpan()
		countSpan.SetTag("db.operation", "COUNT")
		countSpan.SetTag("db.table", "call_queue")

		err := tx.QueryRowContext(spanContext, `
			SELECT COUNT(*) 
			FROM call_queue 
			WHERE state = $1
		`, CallStateWaiting).Scan(&queueSize)
		if err != nil {
			countSpan.SetTag("error", "true")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get queue size")
			return fmt.Errorf("failed to get queue size: %w", err)
		}
		countSpan.SetTag("queue_size", fmt.Sprintf("%d", queueSize))

		// Get waiting calls - create child span
		_, selectSpan, selectFinishSpan := herosentry.StartSpan(spanContext, "PostgresCallQueueRepository.SelectWaitingCalls")
		defer selectFinishSpan()
		selectSpan.SetTag("db.operation", "SELECT")
		selectSpan.SetTag("db.table", "call_queue")

		rows, err := tx.QueryContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE state = $1
			ORDER BY enqueue_time ASC
		`, CallStateWaiting)
		if err != nil {
			selectSpan.SetTag("error", "true")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query waiting calls")
			return fmt.Errorf("failed to query waiting calls: %w", err)
		}
		defer rows.Close()

		waitingCalls, err = scanQueuedCalls(rows)
		if err != nil {
			selectSpan.SetTag("error", "true")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan waiting calls")
			return fmt.Errorf("failed to scan waiting calls: %w", err)
		}
		selectSpan.SetTag("db.rows_affected", fmt.Sprintf("%d", len(waitingCalls)))

		return nil
	})

	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get queue status")
		return 0, nil, err
	}

	span.SetTag("queue_size", fmt.Sprintf("%d", queueSize))
	span.SetTag("waiting_calls_count", fmt.Sprintf("%d", len(waitingCalls)))
	return queueSize, waitingCalls, nil
}

// SetCallSituation assigns a situation to a call
func (r *postgresCallQueueRepository) SetCallSituation(ctx context.Context, callSID string, situationID string) error {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresCallQueueRepository.SetCallSituation")
	defer finish()
	span.SetTag("call.sid", callSID)
	span.SetTag("situation.id", situationID)

	return database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if callSID == "" || situationID == "" {
			return ErrInvalidInput
		}

		result, err := tx.ExecContext(spanContext, `
			UPDATE call_queue 
			SET situation_id = $1
			WHERE call_sid = $2
		`, situationID, callSID)
		if err != nil {
			return fmt.Errorf("failed to update situation ID: %w", err)
		}

		rows, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}
		if rows == 0 {
			return ErrCallNotFound
		}

		return nil
	})
}

// SetQueueStrategy sets the strategy used for selecting the next call
func (r *postgresCallQueueRepository) SetQueueStrategy(strategy QueueStrategy) {
	r.queueStrategy = strategy
}

// GetTwilioQueueSid returns the SID of the Twilio queue for the current organization.
// If no organization-specific queue is found, it falls back to the default queue.
func (r *postgresCallQueueRepository) GetTwilioQueueSid(ctx context.Context) (string, error) {
	return database.WithSession(r.db, ctx, nil, func(tx *sql.Tx) (string, error) {
		orgID := cmncontext.GetOrgId(ctx)

		log.Printf("Getting Twilio queue SID for org %d", orgID)

		var queueSid string
		err := tx.QueryRowContext(
			ctx,
			`SELECT twilio_queue_sid FROM twilio_queue_configurations WHERE org_id = $1`,
			orgID,
		).Scan(&queueSid)

		if err != nil {
			return "", fmt.Errorf("failed to get Twilio queue SID for org %d: %w", orgID, err)
		}

		return queueSid, nil
	})
}

// GetTwilioQueueName returns the friendly name of the Twilio queue for the current organization.
// If no organization-specific queue is found, it falls back to the default queue.
func (r *postgresCallQueueRepository) GetTwilioQueueName(ctx context.Context) (string, error) {
	spanContext, span, finish := herosentry.StartSpan(ctx, "postgresCallQueueRepository.GetTwilioQueueName")
	defer finish()

	return database.WithSession(r.db, spanContext, nil, func(tx *sql.Tx) (string, error) {
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		log.Printf("Getting Twilio queue name for org %d", orgID)

		var queueName string
		err := tx.QueryRowContext(
			spanContext,
			`SELECT friendly_name FROM twilio_queue_configurations WHERE org_id = $1`,
			orgID,
		).Scan(&queueName)

		if err != nil {
			return "", fmt.Errorf("failed to get Twilio queue name for org %d: %w", orgID, err)
		}

		return queueName, nil
	})
}

// DequeueCallBySid gets a specific call from the queue by SID and assigns it to an asset
func (r *postgresCallQueueRepository) DequeueCallBySid(ctx context.Context, callSID string, assetID string) (QueuedCall, bool, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.DequeueCallBySid")
	defer finishSpan()
	span.SetTag("db.operation", "SELECT_UPDATE")
	span.SetTag("db.table", "call_queue")
	span.SetTag("call_sid", callSID)
	span.SetTag("asset_id", assetID)

	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if callSID == "" || assetID == "" {
			return ErrInvalidInput
		}

		// Get the specific call by SID, ensuring it's in waiting state
		_, selectSpan, selectFinishSpan := herosentry.StartSpan(spanContext, "PostgresCallQueueRepository.SelectCallForUpdate")
		defer selectFinishSpan()
		selectSpan.SetTag("db.operation", "SELECT FOR UPDATE")
		selectSpan.SetTag("db.table", "call_queue")
		selectSpan.SetTag("call_sid", callSID)

		row := tx.QueryRowContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE call_sid = $1 AND state = $2
			FOR UPDATE
		`, callSID, CallStateWaiting)

		var scanErr error
		call, scanErr = scanQueuedCall(row)
		if scanErr == sql.ErrNoRows {
			selectSpan.SetTag("call_found", "false")
			return nil // Call not found or not in waiting state
		}
		if scanErr != nil {
			selectSpan.SetTag("error", "true")
			herosentry.CaptureException(spanContext, scanErr, herosentry.ErrorTypeDatabase, "Failed to scan call for update")
			return fmt.Errorf("failed to scan call: %w", scanErr)
		}
		selectSpan.SetTag("call_found", "true")
		found = true

		// Update the call to assign it to the asset and set to pending selective assignment state
		call.AssetID = assetID
		call.State = CallStatePendingSelectiveAssign

		attributesJSON, err := json.Marshal(call.Attributes)
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}

		_, updateSpan, updateFinishSpan := herosentry.StartSpan(spanContext, "PostgresCallQueueRepository.UpdateCallAssignment")
		defer updateFinishSpan()
		updateSpan.SetTag("db.operation", "UPDATE")
		updateSpan.SetTag("db.table", "call_queue")
		updateSpan.SetTag("call_sid", callSID)
		updateSpan.SetTag("asset_id", assetID)

		_, err = tx.ExecContext(spanContext, `
			UPDATE call_queue 
			SET asset_id = $1, state = $2, attributes = $3
			WHERE call_sid = $4
		`, assetID, CallStatePendingSelectiveAssign, attributesJSON, callSID)
		if err != nil {
			updateSpan.SetTag("error", "true")
			updateSpan.SetTag("db.rows_affected", "0")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to update call assignment")
			return fmt.Errorf("failed to update call: %w", err)
		}
		updateSpan.SetTag("db.rows_affected", "1")

		// Re-fetch to get the updated call data
		_, refetchSpan, refetchFinishSpan := herosentry.StartSpan(spanContext, "PostgresCallQueueRepository.RefetchUpdatedCall")
		defer refetchFinishSpan()
		refetchSpan.SetTag("call_sid", callSID)

		rowAfterUpdate := tx.QueryRowContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue WHERE call_sid = $1`, callSID)
		updatedCall, scanErrAfterUpdate := scanQueuedCall(rowAfterUpdate)
		if scanErrAfterUpdate != nil {
			refetchSpan.SetTag("error", "true")
			herosentry.CaptureException(spanContext, scanErrAfterUpdate, herosentry.ErrorTypeDatabase, "Failed to re-scan call after update")
			return fmt.Errorf("failed to re-scan call after update: %w", scanErrAfterUpdate)
		}
		call = updatedCall

		return nil
	})

	if err != nil {
		span.SetTag("error", "true")
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to dequeue call by SID")
		return QueuedCall{}, false, err
	}

	span.SetTag("call_found", fmt.Sprintf("%t", found))
	if found {
		span.SetTag("call_sid", call.CallSID)
	}
	return call, found, nil
}

func (r *postgresCallQueueRepository) GetCallByCallSID(ctx context.Context, callSID string) (QueuedCall, bool, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.GetCallByCallSID")
	defer finishSpan()
	span.SetTag("call_sid", callSID)

	var call QueuedCall
	var found bool

	err := database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		if callSID == "" {
			span.SetTag("error", "true")
			herosentry.CaptureException(spanContext, ErrInvalidInput, herosentry.ErrorTypeValidation, "Invalid input - missing callSID")
			return ErrInvalidInput
		}

		row := tx.QueryRowContext(spanContext, `
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
			WHERE call_sid = $1
		`, callSID)

		var err error
		call, err = scanQueuedCall(row)
		if err == sql.ErrNoRows {
			return nil
		}
		if err != nil {
			return fmt.Errorf("failed to scan call: %w", err)
		}
		found = true
		return nil
	})

	if err != nil {
		return QueuedCall{}, false, err
	}

	return call, found, nil
}

// ListCalls retrieves calls from the queue with filtering and pagination
func (r *postgresCallQueueRepository) ListCalls(ctx context.Context, startDate, endDate *time.Time, page, pageSize int, sortOrder, state, direction string) ([]QueuedCall, int, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.ListCalls")
	defer finishSpan()
	span.SetTag("page", fmt.Sprintf("%d", page))
	span.SetTag("page_size", fmt.Sprintf("%d", pageSize))
	if state != "" {
		span.SetTag("state", state)
	}
	if direction != "" {
		span.SetTag("direction", direction)
	}

	var calls []QueuedCall
	var totalCount int

	err := database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		orgID := cmncontext.GetOrgId(spanContext)

		// Validate pagination parameters
		if page < 1 {
			page = 1
		}
		if pageSize < 1 {
			pageSize = 50
		}
		if pageSize > 100 {
			pageSize = 100
		}

		// Validate sort order
		if sortOrder != "asc" && sortOrder != "desc" {
			sortOrder = "desc"
		}

		// Build the WHERE clause
		whereClauses := []string{"org_id = $1"}
		args := []interface{}{orgID}
		argCount := 1

		if startDate != nil {
			argCount++
			whereClauses = append(whereClauses, fmt.Sprintf("enqueue_time >= $%d", argCount))
			args = append(args, *startDate)
		}
		if endDate != nil {
			argCount++
			whereClauses = append(whereClauses, fmt.Sprintf("enqueue_time <= $%d", argCount))
			args = append(args, *endDate)
		}
		if state != "" {
			argCount++
			whereClauses = append(whereClauses, fmt.Sprintf("state = $%d", argCount))
			args = append(args, state)
		}
		if direction != "" {
			argCount++
			whereClauses = append(whereClauses, fmt.Sprintf("direction = $%d", argCount))
			args = append(args, direction)
		}

		whereClause := "WHERE " + strings.Join(whereClauses, " AND ")

		// Get total count
		countQuery := `
			SELECT COUNT(*) 
			FROM call_queue 
		` + whereClause

		err := tx.QueryRowContext(spanContext, countQuery, args...).Scan(&totalCount)
		if err != nil {
			span.SetTag("error", "true")
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get total count for list calls")
			return fmt.Errorf("failed to get total count: %w", err)
		}

		// Get paginated results
		offset := (page - 1) * pageSize

		// Build query with proper parameterization
		queryBuilder := strings.Builder{}
		queryBuilder.WriteString(`
			SELECT call_sid, caller, caller_name, enqueue_time, 
				asset_id, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			FROM call_queue 
		`)
		queryBuilder.WriteString(whereClause)
		queryBuilder.WriteString("\n\t\t\tORDER BY enqueue_time ")
		queryBuilder.WriteString(sortOrder)
		queryBuilder.WriteString(fmt.Sprintf("\n\t\t\tLIMIT $%d OFFSET $%d", argCount+1, argCount+2))

		query := queryBuilder.String()
		args = append(args, pageSize, offset)

		rows, err := tx.QueryContext(spanContext, query, args...)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to query calls")
			return fmt.Errorf("failed to query calls: %w", err)
		}
		defer rows.Close()

		calls, err = scanQueuedCalls(rows)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to scan calls")
			return fmt.Errorf("failed to scan calls: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, 0, err
	}

	if span != nil {
		span.SetTag("db.rows_affected", fmt.Sprintf("%d", len(calls)))
		span.SetTag("total_count", fmt.Sprintf("%d", totalCount))
	}
	return calls, totalCount, nil
}

// StoreActiveCall stores or updates a call's details, respecting the incoming state.
// It performs an UPSERT operation: if the call_sid exists, it's updated; otherwise, it's inserted.
func (r *postgresCallQueueRepository) StoreActiveCall(ctx context.Context, call QueuedCall) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "PostgresCallQueueRepository.StoreActiveCall")
	defer finishSpan()
	span.SetTag("call_sid", call.CallSID)
	span.SetTag("state", call.State)

	return database.WithSessionErr(r.db, spanContext, nil, func(tx *sql.Tx) error {
		// Validate requirements
		if call.CallSID == "" {
			log.Printf("StoreActiveCall: Missing CallSID, which is required for UPSERT.")
			return ErrInvalidInput // Or a more specific error like ErrCallSIDRequired
		}

		// For outbound calls, an AssetID (the agent initiating) is mandatory.
		if call.Direction == CallDirectionOutbound && call.AssetID == "" {
			log.Printf("StoreActiveCall: Missing AssetID for outbound call SID %s", call.CallSID)
			return ErrInvalidInput
		}

		// If a call is marked as active, it should generally be associated with an asset.
		if call.State == CallStateActive && call.AssetID == "" {
			log.Printf("StoreActiveCall: Missing AssetID for active call SID %s", call.CallSID)
			return ErrInvalidInput
		}

		orgID := cmncontext.GetOrgId(spanContext)

		// Convert attributes to JSON
		attributesJSON, err := json.Marshal(call.Attributes)
		if err != nil {
			return fmt.Errorf("failed to marshal attributes: %w", err)
		}
		log.Printf("Postgres StoreActiveCall (UPSERTING): Storing/Updating call %s for asset %s with state %s. Attributes: %s", call.CallSID, call.AssetID, call.State, string(attributesJSON))

		// Handle potentially NULL fields for SQL arguments
		var callerNameArg sql.NullString
		if call.CallerName != "" {
			callerNameArg = sql.NullString{String: call.CallerName, Valid: true}
		} else {
			callerNameArg = sql.NullString{Valid: false}
		}

		var situationIDArg sql.NullString
		if call.SituationID != "" {
			situationIDArg = sql.NullString{String: call.SituationID, Valid: true}
		} else {
			situationIDArg = sql.NullString{Valid: false}
		}

		var assetIDArg sql.NullString
		if call.AssetID != "" {
			assetIDArg = sql.NullString{String: call.AssetID, Valid: true}
		} else {
			assetIDArg = sql.NullString{Valid: false}
		}

		// Ensure EnqueueTime is valid or null for database
		var enqueueTimeArg interface{}
		if call.EnqueueTime.IsZero() || call.EnqueueTime.Before(time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC)) {
			// For new inserts, if not provided, might default to NOW() or be explicitly NULL if appropriate
			// For updates, we'd typically COALESCE with existing value.
			// Given UPSERT, let's set to NULL if zero, DB default or COALESCE in query will handle it.
			enqueueTimeArg = nil
		} else {
			enqueueTimeArg = call.EnqueueTime
		}

		var callStartTimeArg interface{}
		if call.CallStartTime != nil && !call.CallStartTime.IsZero() {
			callStartTimeArg = *call.CallStartTime
		} else {
			// Pass NULL from Go if no specific start time is provided.
			// The SQL query will handle using NOW() for new inserts under appropriate conditions.
			callStartTimeArg = nil
		}

		var callEndTimeArg interface{}
		if call.CallEndTime != nil && !call.CallEndTime.IsZero() {
			callEndTimeArg = *call.CallEndTime
		} else {
			callEndTimeArg = nil
		}

		var lastHoldStartArg interface{}
		if call.LastHoldStart != nil && !call.LastHoldStart.IsZero() {
			lastHoldStartArg = *call.LastHoldStart
		} else {
			lastHoldStartArg = nil
		}

		// UPSERT query
		query := `
			INSERT INTO call_queue (
				call_sid, org_id, caller, caller_name, asset_id, 
				enqueue_time, situation_id, state, direction, attributes,
				call_start_time, call_end_time, last_hold_start
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, 
				COALESCE($11, CASE WHEN ($8 = 'active' OR ($9 = 'outbound' AND $8 != 'ended')) THEN NOW() ELSE NULL END),
				$12, $13)
			ON CONFLICT (call_sid) DO UPDATE SET
				org_id = EXCLUDED.org_id,
				caller = EXCLUDED.caller,
				caller_name = EXCLUDED.caller_name,
				asset_id = EXCLUDED.asset_id,
				enqueue_time = COALESCE(EXCLUDED.enqueue_time, call_queue.enqueue_time),
				situation_id = EXCLUDED.situation_id,
				state = EXCLUDED.state,
				direction = EXCLUDED.direction,
				attributes = EXCLUDED.attributes,
				call_start_time = COALESCE(call_queue.call_start_time, EXCLUDED.call_start_time),
				call_end_time = EXCLUDED.call_end_time,
				last_hold_start = CASE 
									WHEN EXCLUDED.state = $14 AND call_queue.state != $14 THEN NOW() 
									WHEN EXCLUDED.state != $14 THEN NULL
									ELSE call_queue.last_hold_start 
								  END
		`

		_, err = tx.ExecContext(spanContext, query,
			call.CallSID,     // $1
			orgID,            // $2
			call.Caller,      // $3
			callerNameArg,    // $4
			assetIDArg,       // $5
			enqueueTimeArg,   // $6
			situationIDArg,   // $7
			call.State,       // $8
			call.Direction,   // $9
			attributesJSON,   // $10
			callStartTimeArg, // $11
			callEndTimeArg,   // $12
			lastHoldStartArg, // $13
			CallStateHold,    // $14 (for last_hold_start logic)
		)

		if err != nil {
			return fmt.Errorf("failed to upsert call record: %w", err)
		}

		return nil
	})
}

package connect

import (
	"context"
	"errors"
	"log"

	connecthelper "common/connect"
	"common/herosentry"
	conversationv1 "proto/hero/communications/v1"
	conversationv1connect "proto/hero/communications/v1/conversationconnect"

	"communications/internal/cellularcall/usecase"

	"connectrpc.com/connect"
)

// CellularCallServiceServer implements the CellularCallService connect API.
type CellularCallServiceServer struct {
	conversationv1connect.UnimplementedCellularCallServiceHandler
	uc usecase.CellularCallUsecase
}

// NewCellularCallServiceServer creates a new CellularCallServiceServer.
func NewCellularCallServiceServer(uc usecase.CellularCallUsecase) *CellularCallServiceServer {
	return &CellularCallServiceServer{uc: uc}
}

// GetCellularCallAccessToken handles the generation of a Twilio access token for cellular calls.
func (s *CellularCallServiceServer) GetCellularCallAccessToken(
	ctx context.Context,
	req *connect.Request[conversationv1.GetCellularCallAccessTokenRequest],
) (*connect.Response[conversationv1.GetCellularCallAccessTokenResponse], error) {
	log.Printf("Handling GetCellularCallAccessToken request for identity: %s", req.Msg.Identity)
	resp, err := s.uc.GetCellularCallAccessToken(ctx, req.Msg)
	if err != nil {
		log.Printf("Error generating cellular call access token: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "GetCellularCallAccessToken")
	}
	return connect.NewResponse(resp), nil
}

// HandleCall builds and returns a TwiML response for handling cellular calls.
func (s *CellularCallServiceServer) HandleCall(
	ctx context.Context,
	req *connect.Request[conversationv1.HandleCallRequest],
) (*connect.Response[conversationv1.HandleCallResponse], error) {
	log.Printf("Handling call request - Caller: %s, To: %s", req.Msg.Caller, req.Msg.To)
	resp, err := s.uc.HandleCall(ctx, req.Msg)
	if err != nil {
		log.Printf("Error handling call: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "HandleCall")
	}
	return connect.NewResponse(resp), nil
}

// QueueCall places a call in the waiting queue.
func (s *CellularCallServiceServer) QueueCall(
	ctx context.Context,
	req *connect.Request[conversationv1.QueueCallRequest],
) (*connect.Response[conversationv1.QueueCallResponse], error) {
	log.Printf("Queueing call from: %s", req.Msg.Caller)

	if req.Msg.Caller == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("caller number is required"), "QueueCall", herosentry.ErrorTypeValidation)
	}

	resp, err := s.uc.QueueCall(ctx, req.Msg)
	if err != nil {
		log.Printf("Error queueing call: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "QueueCall")
	}

	log.Printf("Call queued successfully - CallSID: %s, QueueSID: %s", resp.CallSid, resp.QueueSid)
	return connect.NewResponse(resp), nil
}

// DequeueCallBySid connects an asset to a specific call by SID.
func (s *CellularCallServiceServer) DequeueCallBySid(
	ctx context.Context,
	req *connect.Request[conversationv1.DequeueCallBySidRequest],
) (*connect.Response[conversationv1.DequeueCallBySidResponse], error) {
	log.Printf("Dequeuing specific call SID: %s for asset: %s", req.Msg.CallSid, req.Msg.AssetId)

	if req.Msg.AssetId == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("asset ID is required"), "DequeueCallBySid", herosentry.ErrorTypeValidation)
	}
	if req.Msg.CallSid == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("call SID is required"), "DequeueCallBySid", herosentry.ErrorTypeValidation)
	}

	resp, err := s.uc.DequeueCallBySid(ctx, req.Msg)
	if err != nil {
		log.Printf("Error dequeuing call by SID: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "DequeueCallBySid")
	}

	if resp.Success {
		log.Printf("Call dequeued successfully - CallSID: %s, Caller: %s", resp.CallSid, resp.Caller)
	} else {
		log.Printf("Call with SID %s not found or not in waiting state", req.Msg.CallSid)
	}

	return connect.NewResponse(resp), nil
}

// DequeueCall connects an asset to the next call in queue.
func (s *CellularCallServiceServer) DequeueCall(
	ctx context.Context,
	req *connect.Request[conversationv1.DequeueCallRequest],
) (*connect.Response[conversationv1.DequeueCallResponse], error) {
	log.Printf("Dequeuing call for asset: %s", req.Msg.AssetId)

	if req.Msg.AssetId == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("asset ID is required"), "DequeueCall", herosentry.ErrorTypeValidation)
	}

	resp, err := s.uc.DequeueCall(ctx, req.Msg)
	if err != nil {
		log.Printf("Error dequeuing call: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "DequeueCall")
	}

	if resp.Success {
		log.Printf("Call dequeued successfully - CallSID: %s, Caller: %s", resp.CallSid, resp.Caller)
	} else {
		log.Printf("No calls available in queue for asset: %s", req.Msg.AssetId)
	}

	return connect.NewResponse(resp), nil
}

// GetQueueStatus returns information about calls waiting in queue.
func (s *CellularCallServiceServer) GetQueueStatus(
	ctx context.Context,
	req *connect.Request[conversationv1.GetQueueStatusRequest],
) (*connect.Response[conversationv1.GetQueueStatusResponse], error) {

	resp, err := s.uc.GetQueueStatus(ctx, req.Msg)
	if err != nil {
		log.Printf("Error getting queue status: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "GetQueueStatus")
	}

	return connect.NewResponse(resp), nil
}

// HoldCall places an active call on hold
func (s *CellularCallServiceServer) HoldCall(
	ctx context.Context,
	req *connect.Request[conversationv1.HoldCallRequest],
) (*connect.Response[conversationv1.HoldCallResponse], error) {
	log.Printf("Placing call on hold - CallSID: %s, Asset: %s", req.Msg.CallSid, req.Msg.AssetId)

	if req.Msg.CallSid == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("call SID is required"), "HoldCall", herosentry.ErrorTypeValidation)
	}
	if req.Msg.AssetId == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("asset ID is required"), "HoldCall", herosentry.ErrorTypeValidation)
	}

	resp, err := s.uc.HoldCall(ctx, req.Msg)
	if err != nil {
		log.Printf("Error placing call on hold: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "HoldCall")
	}

	if resp.Success {
		log.Printf("Call placed on hold successfully - CallSID: %s for asset: %s", req.Msg.CallSid, req.Msg.AssetId)
	} else {
		log.Printf("Failed to place call on hold - CallSID: %s", req.Msg.CallSid)
	}

	return connect.NewResponse(resp), nil
}

// Add these to cellularcall_server.go
func (s *CellularCallServiceServer) GetSituationForCall(
	ctx context.Context,
	req *connect.Request[conversationv1.GetSituationForCallRequest],
) (*connect.Response[conversationv1.GetSituationForCallResponse], error) {
	resp, err := s.uc.GetSituationForCall(ctx, req.Msg)
	if err != nil {
		log.Printf("Error in GetSituationForCall: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "GetSituationForCall")
	}
	log.Printf("Returning situation response: %+v", resp)
	return connect.NewResponse(resp), nil
}

// RevertSelectiveClaim reverts a call from pending_selective_assignment back to waiting state
func (s *CellularCallServiceServer) RevertSelectiveClaim(
	ctx context.Context,
	req *connect.Request[conversationv1.RevertSelectiveClaimRequest],
) (*connect.Response[conversationv1.RevertSelectiveClaimResponse], error) {
	log.Printf("Handling RevertSelectiveClaim for call SID: %s", req.Msg.CallSid)

	resp, err := s.uc.RevertSelectiveClaim(ctx, req.Msg)
	if err != nil {
		log.Printf("Error in RevertSelectiveClaim: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "RevertSelectiveClaim")
	}

	log.Printf("Successfully processed RevertSelectiveClaim: success=%v, reverted=%v",
		resp.Success, resp.Reverted)
	return connect.NewResponse(resp), nil
}

// GetAssetHeldCalls returns calls on hold for a given asset
func (s *CellularCallServiceServer) GetAssetHeldCalls(
	ctx context.Context,
	req *connect.Request[conversationv1.GetAssetHeldCallsRequest],
) (*connect.Response[conversationv1.GetAssetHeldCallsResponse], error) {
	if req.Msg.AssetId == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("asset ID is required"), "GetAssetHeldCalls", herosentry.ErrorTypeValidation)
	}

	resp, err := s.uc.GetAssetHeldCalls(ctx, req.Msg)
	if err != nil {
		log.Printf("Error getting held calls: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "GetAssetHeldCalls")
	}

	return connect.NewResponse(resp), nil
}

// ResumeCall reconnects an asset to a call that was on hold
func (s *CellularCallServiceServer) ResumeCall(
	ctx context.Context,
	req *connect.Request[conversationv1.ResumeCallRequest],
) (*connect.Response[conversationv1.ResumeCallResponse], error) {
	log.Printf("Resuming call - CallSID: %s, Asset: %s", req.Msg.CallSid, req.Msg.AssetId)

	if req.Msg.CallSid == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("call SID is required"), "ResumeCall", herosentry.ErrorTypeValidation)
	}
	if req.Msg.AssetId == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("asset ID is required"), "ResumeCall", herosentry.ErrorTypeValidation)
	}

	resp, err := s.uc.ResumeCall(ctx, req.Msg)
	if err != nil {
		log.Printf("Error resuming call from hold: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "ResumeCall")
	}

	if resp.Success {
		log.Printf("Call resumed successfully - CallSID: %s", req.Msg.CallSid)
	} else {
		log.Printf("Failed to resume call - CallSID: %s", req.Msg.CallSid)
	}

	return connect.NewResponse(resp), nil
}

// EndCall terminates an active call.
func (s *CellularCallServiceServer) EndCall(
	ctx context.Context,
	req *connect.Request[conversationv1.EndCallRequest],
) (*connect.Response[conversationv1.EndCallResponse], error) {
	log.Printf("Ending call - CallSID: %s, Asset: %s", req.Msg.CallSid, req.Msg.AssetId)

	if req.Msg.CallSid == "" {
		return nil, connecthelper.AsConnectError(ctx, errors.New("call SID is required"), "EndCall", herosentry.ErrorTypeValidation)
	}

	resp, err := s.uc.EndCall(ctx, req.Msg)
	if err != nil {
		log.Printf("Error ending call: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "EndCall")
	}

	log.Printf("Call ended successfully - CallSID: %s", req.Msg.CallSid)
	return connect.NewResponse(resp), nil
}

// ListCalls returns paginated call records with filtering
func (s *CellularCallServiceServer) ListCalls(
	ctx context.Context,
	req *connect.Request[conversationv1.ListCallsRequest],
) (*connect.Response[conversationv1.ListCallsResponse], error) {
	log.Printf("Listing calls - Page: %d, PageSize: %d, StartDate: %s, EndDate: %s",
		req.Msg.Page, req.Msg.PageSize, req.Msg.StartDate, req.Msg.EndDate)

	// Validate page parameters
	if req.Msg.Page <= 0 {
		return nil, connecthelper.AsConnectError(ctx, errors.New("page must be greater than 0"), "ListCalls", herosentry.ErrorTypeValidation)
	}
	if req.Msg.PageSize <= 0 || req.Msg.PageSize > 100 {
		return nil, connecthelper.AsConnectError(ctx, errors.New("page_size must be between 1 and 100"), "ListCalls", herosentry.ErrorTypeValidation)
	}

	resp, err := s.uc.ListCalls(ctx, req.Msg)
	if err != nil {
		log.Printf("Error listing calls: %v", err)
		return nil, connecthelper.AsConnectError(ctx, err, "ListCalls")
	}

	log.Printf("Successfully retrieved %d calls (total: %d)", len(resp.Calls), resp.TotalCount)
	return connect.NewResponse(resp), nil
}

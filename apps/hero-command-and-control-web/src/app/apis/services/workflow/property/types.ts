// Property Types
export enum PropertyType {
    PROPERTY_TYPE_UNSPECIFIED = 0,
    PROPERTY_TYPE_FOUND = 1,
    PROPERTY_TYPE_SEIZED = 2,
    PROPERTY_TYPE_STOLEN = 3,
    PROPERTY_TYPE_SAFEKEEPING = 4,
    PROPERTY_TYPE_MISSING = 5,
    PROPERTY_TYPE_RECOVERED = 6,
}

export enum PropertyStatus {
    PROPERTY_STATUS_UNSPECIFIED = 0,
    PROPERTY_STATUS_COLLECTED = 1,
    PROPERTY_STATUS_IN_CUSTODY = 2,
    PROPERTY_STATUS_CHECKED_OUT = 3,
    PROPERTY_STATUS_DISPOSED = 4,
    PROPERTY_STATUS_MISSING = 5,
    PROPERTY_STATUS_CLAIMED = 6,
}

export enum PropertyDisposalType {
    PROPERTY_DISPOSAL_TYPE_UNSPECIFIED = 0,
    PROPERTY_DISPOSAL_TYPE_RELEASED = 1,
    PROPERTY_DISPOSAL_TYPE_DESTROYED = 2,
    PROPERTY_DISPOSAL_TYPE_AUCTIONED = 3,
    PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN = 4,
    PROPERTY_DISPOSAL_TYPE_TRANSFERRED = 5,
}

export enum CustodyActionType {
    CUSTODY_ACTION_TYPE_UNSPECIFIED = 0,
    CUSTODY_ACTION_TYPE_COLLECTED = 1,
    CUSTODY_ACTION_TYPE_TRANSFERRED = 2,
    CUSTODY_ACTION_TYPE_RELEASED = 3,
    CUSTODY_ACTION_TYPE_CHECKED_OUT = 4,
    CUSTODY_ACTION_TYPE_CHECKED_IN = 5,
    CUSTODY_ACTION_TYPE_DISPOSED = 6,
    CUSTODY_ACTION_TYPE_LOGGED = 7,
}

export enum SearchOrderBy {
    SEARCH_ORDER_BY_UNSPECIFIED = 0,
    SEARCH_ORDER_BY_RELEVANCE = 1,
    SEARCH_ORDER_BY_CREATED_AT = 2,
    SEARCH_ORDER_BY_UPDATED_AT = 3,
    SEARCH_ORDER_BY_STATUS = 4,
}

// Core Types
export interface CustodyEvent {
    timestamp: string;
    transferringUserId: string;
    transferringAgency: string;
    receivingUserId: string;
    receivingAgency: string;
    newLocation: string;
    actionType: CustodyActionType;
    notes: string;
    caseNumber: string;
    evidenceNumber: string;
}

export interface PropertySchema {
    description: string;
    quantity: string;
    category: string;
    identifiers: string;
    owner: string;
    condition: string;
    serialNumber: string;
    value: string;
    propertyType: PropertyType; // Moved from Property interface
}

export interface Property {
    id: string;
    orgId: number;
    propertyNumber?: string; // Added property/evidence number for tracking
    propertyStatus: PropertyStatus;
    isEvidence: boolean;
    retentionPeriod?: string;
    disposalType: PropertyDisposalType;
    notes?: string;
    caseNumber?: string;
    currentCustodian?: string;
    currentLocation?: string;
    custodyChain: CustodyEvent[];
    propertySchema?: PropertySchema; // Now contains propertyType
    createTime: string;
    updateTime: string;
    createdBy?: string;
    updatedBy?: string;
    version: number;
    status: number;
    resourceType: string;
}

// Search Types
export interface DateRange {
    from: string;
    to: string;
}

export interface FieldQuery {
    field: string;
    query: string;
}

export interface HighlightResult {
    field: string;
    fragments: string[];
}

export interface SearchPropertiesRequest {
    query: string;
    fieldQueries?: FieldQuery[];
    dateRange?: DateRange;
    propertyType?: PropertyType;
    propertyStatus?: PropertyStatus;
    orderBy?: SearchOrderBy;
    pageSize?: number;
    pageToken?: string;
}

export interface SearchPropertiesResponse {
    properties: Property[];
    highlights: HighlightResult[];
    nextPageToken?: string;
    totalCount: number;
}

// Property File Attachment Types
export interface PropertyAttachmentMetadata {
    // Commonly used fields coming from filerepository metadata or UI context
    fileType?: string;
    originalFilename?: string;
    uploadContext?: string;
    attachmentId?: string;

    // Allow arbitrary JSON keys since backend uses google.protobuf.Struct
    [key: string]: unknown;
}

export interface PropertyFileReference {
    id: string;
    propertyId: string;
    fileId: string;
    caption?: string;
    displayName?: string;
    displayOrder?: number;
    fileCategory?: string;
    metadata?: PropertyAttachmentMetadata;
} 
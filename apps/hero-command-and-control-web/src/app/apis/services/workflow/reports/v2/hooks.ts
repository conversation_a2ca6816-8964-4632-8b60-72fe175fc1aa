import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
} from "@tanstack/react-query";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";

import {
  CreateReportRequest,
  CreateReportResponse,
  GetReportRequest,
  Report,
  UpdateReportRequest,
  ListReportsRequest,
  ListReportsResponse,
  DeleteReportRequest,
  AddCommentRequest,
  Comment,
  GetCommentsRequest,
  GetCommentsResponse,
  UpdateCommentRequest,
  DeleteCommentRequest,
  ResolveCommentRequest,
  SubmitForReviewRequest,
  ReviewRound,
  ApproveReviewRoundRequest,
  RequestChangesRequest,
  UpdateAdditionalInfoJsonRequest,
  UpdateAdditionalInfoJsonResponse,
  GetAdditionalInfoRequest,
  GetAdditionalInfoResponse,
  GetReportVersionRequest,
  ReportSnapshot,
  ListReportVersionsRequest,
  ListReportVersionsResponse,
  BatchGetReportsRequest,
  BatchGetReportsResponse,
  CreateReportSectionRequest,
  ReportSection,
  GetReportSectionRequest,
  UpdateReportSectionRequest,
  DeleteReportSectionRequest,
  ListReportSectionsRequest,
  ListReportSectionsResponse,
  ListReviewRoundsForReportRequest,
  ListReviewRoundsForReportResponse,
  SearchReportsRequest,
  SearchReportsResponse,
  GetEligibleReviewersRequest,
  GetEligibleReviewersResponse,
  CreateRelationRequest,
  Relation,
  GetRelationRequest,
  UpdateRelationRequest,
  DeleteRelationRequest,
  ListRelationsRequest,
  ListRelationsResponse,
  ListReportsBySituationIdRequest,
  ListReportsByCaseIdRequest,
  AddReviewRoundRequest,
  GetReviewRoundRequest,
  UpdateReviewRoundRequest,
  DeleteReviewRoundRequest,
} from "proto/hero/reports/v2/reports_pb";

import {
  createReport,
  getReport,
  updateReport,
  updateReportStatus,
  listReports,
  deleteReport,
  addComment,
  getComments,
  updateComment,
  deleteComment,
  resolveComment,
  submitForReview,
  getEligibleReviewers,
  approveReviewRound,
  requestChanges,
  updateAdditionalInfoJson,
  getAdditionalInfo,
  getReportVersion,
  listReportVersions,
  batchGetReports,
  createReportSection,
  getReportSection,
  updateReportSection,
  deleteReportSection,
  listReportSections,
  listReviewRoundsForReport,
  searchReports,
  createRelation,
  getRelation,
  updateRelation,
  deleteRelation,
  listRelations,
  listReportsBySituationId,
  listReportsByCaseId,
  addReviewRound,
  getReviewRound,
  updateReviewRound,
  deleteReviewRound,
} from "./endpoints";

// Constants for cache keys
export const REPORTS_QUERY_KEY = "reports";
export const REPORT_QUERY_KEY = "report";
export const REPORT_SECTION_QUERY_KEY = "reportSection";
export const REPORT_COMMENTS_QUERY_KEY = "reportComments";
export const REPORT_VERSIONS_QUERY_KEY = "reportVersions";
export const REPORT_REVIEW_ROUNDS_QUERY_KEY = "reportReviewRounds";
export const REPORT_RELATIONS_QUERY_KEY = "reportRelations";

/**
 * Hook for creating a report.
 * On success, it invalidates the cached reports.
 */
export function useCreateReport(
  options?: UseMutationOptions<CreateReportResponse, Error, CreateReportRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (newReport: CreateReportRequest) => createReport(newReport),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
    },
    ...options,
  });
}

/**
 * Hook for fetching a single report by ID.
 * Uses the reportId as part of the cache key.
 */
export function useReport(
  reportId: string,
  refetchInterval?: number,
  options?: Omit<UseQueryOptions<Report, Error>, "queryKey">
) {
  return useQuery({
    queryKey: [REPORT_QUERY_KEY, reportId],
    queryFn: () => getReport({ id: reportId } as GetReportRequest),
    enabled: !!reportId,
    staleTime: refetchInterval ? refetchInterval - 1 : 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: refetchInterval ? refetchInterval : 5 * 60 * 1000, // 5 minutes
    ...options,
  });
}

/**
 * Hook for updating a report.
 * On success, invalidates both the reports list and the specific report cache.
 */
export function useUpdateReport(
  options?: UseMutationOptions<Report, Error, UpdateReportRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (report: UpdateReportRequest) => updateReport(report),
    onSuccess: (data: Report) => {
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [REPORT_QUERY_KEY, data.id] });
    },
    ...options,
  });
}

/**
 * Hook for updating a report status.
 * On success, invalidates both the reports list and the specific report cache.
 */
export function useUpdateReportStatus(
  options?: UseMutationOptions<
    { report: Report },
    Error,
    { id: string; status: any }
  >
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: { id: string; status: any }) => updateReportStatus(data),
    onSuccess: (data: { report: Report }) => {
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.report.id],
      });
    },
    ...options,
  });
}

/**
 * Hook for listing reports.
 * The query key includes the request parameters for proper cache segmentation.
 */
export function useListReports(
  params: ListReportsRequest,
  options?: Omit<
    UseQueryOptions<ListReportsResponse, Error, ListReportsResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORTS_QUERY_KEY, "list", params],
    queryFn: () => listReports(params),
    retry: 2,
    ...options,
  });
}

/**
 * Hook for batch getting reports.
 */
export function useBatchGetReports(
  reportIds: string[],
  options?: Omit<
    UseQueryOptions<BatchGetReportsResponse, Error, BatchGetReportsResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORTS_QUERY_KEY, "batch", reportIds],
    queryFn: () => batchGetReports({ reportIds } as BatchGetReportsRequest),
    enabled: reportIds.length > 0,
    ...options,
  });
}

/**
 * Hook for deleting a report.
 * On success, invalidates the reports cache.
 */
export function useDeleteReport(
  options?: UseMutationOptions<Empty, Error, DeleteReportRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: DeleteReportRequest) => deleteReport(req),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, variables.id],
      });
    },
    ...options,
  });
}

/**
 * Hook for creating a report section.
 */
export function useCreateReportSection(
  options?: UseMutationOptions<ReportSection, Error, CreateReportSectionRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: CreateReportSectionRequest) => createReportSection(req),
    onSuccess: (data: ReportSection) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_SECTION_QUERY_KEY, "list", data.reportId],
      });
    },
    ...options,
  });
}

/**
 * Hook for getting a report section.
 */
export function useReportSection(
  reportId: string,
  sectionId: string,
  options?: Omit<UseQueryOptions<ReportSection, Error>, "queryKey">
) {
  return useQuery({
    queryKey: [REPORT_SECTION_QUERY_KEY, reportId, sectionId],
    queryFn: () =>
      getReportSection({
        reportId: reportId,
        sectionId: sectionId,
      } as GetReportSectionRequest),
    enabled: !!reportId && !!sectionId,
    ...options,
  });
}

/**
 * Hook for updating a report section.
 */
export function useUpdateReportSection(
  options?: UseMutationOptions<ReportSection, Error, UpdateReportSectionRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: UpdateReportSectionRequest) => updateReportSection(req),
    onSuccess: (data: ReportSection) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_SECTION_QUERY_KEY, data.reportId, data.id],
      });
      // Also invalidate the report sections list to ensure parent state updates
      queryClient.invalidateQueries({
        queryKey: [REPORT_SECTION_QUERY_KEY, "list", data.reportId],
      });
    },
    ...options,
  });
}

/**
 * Hook for deleting a report section.
 */
export function useDeleteReportSection(
  options?: UseMutationOptions<Empty, Error, DeleteReportSectionRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: DeleteReportSectionRequest) => deleteReportSection(req),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, variables.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_SECTION_QUERY_KEY, "list", variables.reportId],
      });
    },
    ...options,
  });
}

/**
 * Hook for listing report sections.
 */
export function useListReportSections(
  reportId: string,
  options?: Omit<
    UseQueryOptions<
      ListReportSectionsResponse,
      Error,
      ListReportSectionsResponse
    >,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORT_SECTION_QUERY_KEY, "list", reportId],
    queryFn: () =>
      listReportSections({ reportId } as ListReportSectionsRequest),
    enabled: !!reportId,
    ...options,
  });
}

/**
 * Hook for adding a comment.
 */
export function useAddComment(
  options?: UseMutationOptions<Comment, Error, AddCommentRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: AddCommentRequest) => addComment(req),
    onSuccess: (data: Comment) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_COMMENTS_QUERY_KEY, data.reportId, data.sectionId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_COMMENTS_QUERY_KEY, data.reportId, undefined],
      });
    },
    ...options,
  });
}

/**
 * Hook for getting comments.
 */
export function useGetComments(
  reportId: string,
  sectionId?: string,
  pageSize?: number,
  pageToken?: string,
  options?: Omit<
    UseQueryOptions<GetCommentsResponse, Error, GetCommentsResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [
      REPORT_COMMENTS_QUERY_KEY,
      reportId,
      sectionId,
      pageSize,
      pageToken,
    ],
    queryFn: () =>
      getComments({
        reportId,
        sectionId,
        pageSize,
        pageToken,
      } as GetCommentsRequest),
    enabled: !!reportId,
    ...options,
  });
}

/**
 * Hook for updating a comment.
 */
export function useUpdateComment(
  options?: UseMutationOptions<Comment, Error, UpdateCommentRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: UpdateCommentRequest) => updateComment(req),
    onSuccess: (data: Comment) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_COMMENTS_QUERY_KEY, data.reportId, data.sectionId],
      });
    },
    ...options,
  });
}

/**
 * Hook for deleting a comment.
 */
export function useDeleteComment(
  options?: UseMutationOptions<Empty, Error, DeleteCommentRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: DeleteCommentRequest) => deleteComment(req),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [REPORT_COMMENTS_QUERY_KEY] });
      // We don't know which report this comment belongs to at this point,
      // so we'll just invalidate all reports
      queryClient.invalidateQueries({ queryKey: [REPORT_QUERY_KEY] });
    },
    ...options,
  });
}

/**
 * Hook for resolving a comment.
 */
export function useResolveComment(
  options?: UseMutationOptions<Comment, Error, ResolveCommentRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: ResolveCommentRequest) => resolveComment(req),
    onSuccess: (data: Comment) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_COMMENTS_QUERY_KEY, data.reportId, data.sectionId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_COMMENTS_QUERY_KEY, data.reportId, undefined],
      });
    },
    ...options,
  });
}

/**
 * Hook for submitting a report for review.
 */
export function useSubmitForReview(
  options?: UseMutationOptions<Report, Error, SubmitForReviewRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: SubmitForReviewRequest) => submitForReview(req),
    onSuccess: (data: Report) => {
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [REPORT_QUERY_KEY, data.id] });
      queryClient.invalidateQueries({
        queryKey: [REPORT_REVIEW_ROUNDS_QUERY_KEY, data.id],
        exact: false,
      });
    },
    ...options,
  });
}

/**
 * Hook for getting eligible reviewers for a report.
 */
export function useGetEligibleReviewers(
  reportId: string,
  options?: Omit<UseQueryOptions<GetEligibleReviewersResponse, Error>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: ["eligibleReviewers", reportId],
    queryFn: () => getEligibleReviewers({ reportId } as GetEligibleReviewersRequest),
    enabled: !!reportId,
    ...options,
  });
}

/**
 * Hook for approving a review round.
 */
export function useApproveReviewRound(
  options?: UseMutationOptions<ReviewRound, Error, ApproveReviewRoundRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: ApproveReviewRoundRequest) => approveReviewRound(req),
    onSuccess: (data: ReviewRound) => {
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_REVIEW_ROUNDS_QUERY_KEY, data.reportId],
        exact: false,
      });
    },
    ...options,
  });
}

/**
 * Hook for requesting changes.
 */
export function useRequestChanges(
  options?: UseMutationOptions<ReviewRound, Error, RequestChangesRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: RequestChangesRequest) => requestChanges(req),
    onSuccess: (data: ReviewRound) => {
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_REVIEW_ROUNDS_QUERY_KEY, data.reportId],
        exact: false,
      });
    },
    ...options,
  });
}

/**
 * Hook for updating additional info JSON.
 */
export function useUpdateAdditionalInfoJson(
  options?: UseMutationOptions<
    UpdateAdditionalInfoJsonResponse,
    Error,
    UpdateAdditionalInfoJsonRequest
  >
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: UpdateAdditionalInfoJsonRequest) =>
      updateAdditionalInfoJson(req),
    onSuccess: (data: UpdateAdditionalInfoJsonResponse) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
    },
    ...options,
  });
}

/**
 * Hook for getting additional info.
 */
export function useGetAdditionalInfo(
  reportId: string,
  options?: Omit<
    UseQueryOptions<
      GetAdditionalInfoResponse,
      Error,
      GetAdditionalInfoResponse
    >,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: ["reportAdditionalInfo", reportId],
    queryFn: () => getAdditionalInfo({ reportId } as GetAdditionalInfoRequest),
    enabled: !!reportId,
    ...options,
  });
}

/**
 * Hook for getting a report version.
 */
export function useGetReportVersion(
  reportId: string,
  version: number,
  options?: Omit<
    UseQueryOptions<ReportSnapshot, Error, ReportSnapshot>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORT_VERSIONS_QUERY_KEY, "get", reportId, version],
    queryFn: () =>
      getReportVersion({
        reportId,
        version,
      } as GetReportVersionRequest),
    enabled: !!reportId && version > 0,
    ...options,
  });
}

/**
 * Hook for listing report versions.
 */
export function useListReportVersions(
  reportId: string,
  options?: Omit<
    UseQueryOptions<
      ListReportVersionsResponse,
      Error,
      ListReportVersionsResponse
    >,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORT_VERSIONS_QUERY_KEY, "list", reportId],
    queryFn: () =>
      listReportVersions({ reportId } as ListReportVersionsRequest),
    enabled: !!reportId,
    ...options,
  });
}

/**
 * Hook for listing review rounds for a report.
 */
export function useListReviewRoundsForReport(
  reportId: string,
  pageSize: number = 50,
  pageToken: string = "",
  options?: Omit<
    UseQueryOptions<
      ListReviewRoundsForReportResponse,
      Error,
      ListReviewRoundsForReportResponse
    >,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORT_REVIEW_ROUNDS_QUERY_KEY, reportId, pageSize, pageToken],
    queryFn: () =>
      listReviewRoundsForReport({
        reportId,
        pageSize,
        pageToken,
      } as ListReviewRoundsForReportRequest),
    enabled: !!reportId,
    ...options,
  });
}

/**
 * Hook for searching reports using the SearchReports API.
 * The query key includes the request parameters for proper cache segmentation.
 */
export function useSearchReports(
  params: SearchReportsRequest,
  options?: Omit<
    UseQueryOptions<SearchReportsResponse, Error, SearchReportsResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORTS_QUERY_KEY, "search", params],
    queryFn: () => searchReports(params),
    retry: 2,
    ...options,
  });
}

/**
 * Hook for creating a relation.
 * On success, it invalidates the cached relations and report.
 */
export function useCreateRelation(
  options?: UseMutationOptions<Relation, Error, CreateRelationRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: CreateRelationRequest) => createRelation(req),
    onSuccess: (data: Relation) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_RELATIONS_QUERY_KEY, data.reportId],
      });
    },
    ...options,
  });
}

/**
 * Hook for getting a specific relation.
 */
export function useGetRelation(
  reportId: string,
  relationId: string,
  options?: Omit<UseQueryOptions<Relation, Error>, "queryKey">
) {
  return useQuery({
    queryKey: [REPORT_RELATIONS_QUERY_KEY, reportId, relationId],
    queryFn: () =>
      getRelation({
        reportId: reportId,
        relationId: relationId,
      } as GetRelationRequest),
    enabled: !!reportId && !!relationId,
    ...options,
  });
}

/**
 * Hook for updating a relation.
 * On success, invalidates the cached relations and report.
 */
export function useUpdateRelation(
  options?: UseMutationOptions<Relation, Error, UpdateRelationRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: UpdateRelationRequest) => updateRelation(req),
    onSuccess: (data: Relation) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_RELATIONS_QUERY_KEY, data.reportId, data.id],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_RELATIONS_QUERY_KEY, data.reportId],
      });
    },
    ...options,
  });
}

/**
 * Hook for deleting a relation.
 * On success, invalidates the cached relations and report.
 */
export function useDeleteRelation(
  options?: UseMutationOptions<Empty, Error, DeleteRelationRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: DeleteRelationRequest) => deleteRelation(req),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, variables.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_RELATIONS_QUERY_KEY, variables.reportId],
      });
    },
    ...options,
  });
}

/**
 * Hook for listing relations for a report.
 */
export function useListRelations(
  params: ListRelationsRequest,
  options?: Omit<
    UseQueryOptions<ListRelationsResponse, Error, ListRelationsResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORT_RELATIONS_QUERY_KEY, params.reportId, "list", params],
    queryFn: () => listRelations(params),
    enabled: !!params.reportId,
    ...options,
  });
}

/**
 * Hook for listing reports by situation ID.
 */
export function useListReportsBySituationId(
  params: ListReportsBySituationIdRequest,
  options?: Omit<
    UseQueryOptions<ListReportsResponse, Error, ListReportsResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORTS_QUERY_KEY, "bySituation", params],
    queryFn: () => listReportsBySituationId(params),
    enabled: !!params.situationId,
    retry: 2,
    ...options,
  });
}

/**
 * Hook for listing reports by case ID.
 */
export function useListReportsByCaseId(
  params: ListReportsByCaseIdRequest,
  options?: Omit<
    UseQueryOptions<ListReportsResponse, Error, ListReportsResponse>,
    "queryKey" | "queryFn"
  >
) {
  return useQuery({
    queryKey: [REPORTS_QUERY_KEY, "byCase", params],
    queryFn: () => listReportsByCaseId(params),
    enabled: !!params.caseId,
    retry: 2,
    ...options,
  });
}

/**
 * Hook for adding a review round.
 * On success, invalidates cached review rounds and report.
 */
export function useAddReviewRound(
  options?: UseMutationOptions<ReviewRound, Error, AddReviewRoundRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: AddReviewRoundRequest) => addReviewRound(req),
    onSuccess: (data: ReviewRound) => {
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_REVIEW_ROUNDS_QUERY_KEY, data.reportId],
        exact: false,
      });
    },
    ...options,
  });
}

/**
 * Hook for getting a specific review round.
 */
export function useGetReviewRound(
  reviewRoundId: string,
  options?: Omit<UseQueryOptions<ReviewRound, Error>, "queryKey">
) {
  return useQuery({
    queryKey: [REPORT_REVIEW_ROUNDS_QUERY_KEY, "get", reviewRoundId],
    queryFn: () =>
      getReviewRound({
        reviewRoundId: reviewRoundId,
      } as GetReviewRoundRequest),
    enabled: !!reviewRoundId,
    ...options,
  });
}

/**
 * Hook for updating a review round.
 * On success, invalidates cached review rounds and report.
 */
export function useUpdateReviewRound(
  options?: UseMutationOptions<ReviewRound, Error, UpdateReviewRoundRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: UpdateReviewRoundRequest) => updateReviewRound(req),
    onSuccess: (data: ReviewRound) => {
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [REPORT_QUERY_KEY, data.reportId],
      });
      queryClient.invalidateQueries({
        queryKey: [REPORT_REVIEW_ROUNDS_QUERY_KEY, data.reportId],
        exact: false,
      });
    },
    ...options,
  });
}

/**
 * Hook for deleting a review round.
 * On success, invalidates cached review rounds.
 */
export function useDeleteReviewRound(
  options?: UseMutationOptions<Empty, Error, DeleteReviewRoundRequest>
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (req: DeleteReviewRoundRequest) => deleteReviewRound(req),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [REPORT_REVIEW_ROUNDS_QUERY_KEY],
      });
      queryClient.invalidateQueries({ queryKey: [REPORTS_QUERY_KEY] });
    },
    ...options,
  });
}

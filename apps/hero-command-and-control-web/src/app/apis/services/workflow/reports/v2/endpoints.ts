import axios, { AxiosResponse } from "axios";
import axiosInstance from "../../axiosInstance";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";

import {
    CreateReportRequest,
    CreateReportResponse,
    GetReportRequest,
    Report,
    UpdateReportRequest,
    ListReportsRequest,
    ListReportsResponse,
    DeleteReportRequest,
    AddCommentRequest,
    Comment,
    GetCommentsRequest,
    GetCommentsResponse,
    UpdateCommentRequest,
    DeleteCommentRequest,
    ResolveCommentRequest,
    SubmitForReviewRequest,
    ReviewRound,
    ApproveReviewRoundRequest,
    RequestChangesRequest,
    UpdateAdditionalInfoJsonRequest,
    UpdateAdditionalInfoJsonResponse,
    GetAdditionalInfoRequest,
    GetAdditionalInfoResponse,
    GetReportVersionRequest,
    ReportSnapshot,
    ListReportVersionsRequest,
    ListReportVersionsResponse,
    BatchGetReportsRequest,
    BatchGetReportsResponse,
    CreateReportSectionRequest,
    ReportSection,
    GetReportSectionRequest,
    UpdateReportSectionRequest,
    DeleteReportSectionRequest,
    ListReportSectionsRequest,
    ListReportSectionsResponse,
    ListReviewRoundsForReportRequest,
    ListReviewRoundsForReportResponse,
    SearchReportsRequest,
    SearchReportsResponse,
    CreateRelationRequest,
    Relation,
    GetRelationRequest,
    UpdateRelationRequest,
    DeleteRelationRequest,
    ListRelationsRequest,
    ListRelationsResponse,
    ListReportsBySituationIdRequest,
    ListReportsByCaseIdRequest,
    AddReviewRoundRequest,
    GetReviewRoundRequest,
    UpdateReviewRoundRequest,
    DeleteReviewRoundRequest,
    GetEligibleReviewersRequest,
    GetEligibleReviewersResponse
} from "proto/hero/reports/v2/reports_pb";

// Custom APIError class to include HTTP status codes
class APIError extends Error {
    public statusCode: number;
    constructor(statusCode: number, message: string) {
        super(message);
        this.statusCode = statusCode;
        Object.setPrototypeOf(this, APIError.prototype);
    }
}

// Generic helper function for POST requests with two type parameters:
// T for the response type and D for the request data type.
const postRequest = async <T, D>(url: string, data: D): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await axiosInstance.post<T, AxiosResponse<T>, D>(
            url,
            data
        );
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            const status = error.response?.status ?? 500;
            const errorMsg = error.response?.data?.message ?? error.message;
            throw new APIError(status, `Request to ${url} failed: ${errorMsg}`);
        }
        throw error;
    }
};

// Report Section APIs
// Create Report Section
export const createReportSection = async (
    data: CreateReportSectionRequest
): Promise<ReportSection> => {
    return postRequest<ReportSection, CreateReportSectionRequest>(
        "/hero.reports.v2.ReportService/CreateReportSection",
        data
    );
};

// Get Report Section
export const getReportSection = async (
    data: GetReportSectionRequest
): Promise<ReportSection> => {
    return postRequest<ReportSection, GetReportSectionRequest>(
        "/hero.reports.v2.ReportService/GetReportSection",
        data
    );
};

// Update Report Section
export const updateReportSection = async (
    data: UpdateReportSectionRequest
): Promise<ReportSection> => {
    return postRequest<ReportSection, UpdateReportSectionRequest>(
        "/hero.reports.v2.ReportService/UpdateReportSection",
        data
    );
};

// Delete Report Section
export const deleteReportSection = async (
    data: DeleteReportSectionRequest
): Promise<Empty> => {
    return postRequest<Empty, DeleteReportSectionRequest>(
        "/hero.reports.v2.ReportService/DeleteReportSection",
        data
    );
};

// List Report Sections
export const listReportSections = async (
    data: ListReportSectionsRequest
): Promise<ListReportSectionsResponse> => {
    return postRequest<ListReportSectionsResponse, ListReportSectionsRequest>(
        "/hero.reports.v2.ReportService/ListReportSections",
        data
    );
};

// Report APIs
// Create Report
export const createReport = async (
    data: CreateReportRequest
): Promise<CreateReportResponse> => {
    return postRequest<CreateReportResponse, CreateReportRequest>(
        "/hero.reports.v2.ReportService/CreateReport",
        data
    );
};

// Get Report
export const getReport = async (
    data: GetReportRequest
): Promise<Report> => {
    return postRequest<Report, GetReportRequest>(
        "/hero.reports.v2.ReportService/GetReport",
        data
    );
};

// Update Report
export const updateReport = async (
    data: UpdateReportRequest
): Promise<Report> => {
    return postRequest<Report, UpdateReportRequest>(
        "/hero.reports.v2.ReportService/UpdateReport",
        data
    );
};

// Update Report Status
export const updateReportStatus = async (
    data: { id: string; status: any }
): Promise<{ report: Report }> => {
    return postRequest<{ report: Report }, { id: string; status: any }>(
        "/hero.reports.v2.ReportService/UpdateReportStatus",
        data
    );
};

// List Reports
export const listReports = async (
    data: ListReportsRequest
): Promise<ListReportsResponse> => {
    return postRequest<ListReportsResponse, ListReportsRequest>(
        "/hero.reports.v2.ReportService/ListReports",
        data
    );
};

// Batch Get Reports
export const batchGetReports = async (
    data: BatchGetReportsRequest
): Promise<BatchGetReportsResponse> => {
    return postRequest<BatchGetReportsResponse, BatchGetReportsRequest>(
        "/hero.reports.v2.ReportService/BatchGetReports",
        data
    );
};

// Delete Report
export const deleteReport = async (
    data: DeleteReportRequest
): Promise<Empty> => {
    return postRequest<Empty, DeleteReportRequest>(
        "/hero.reports.v2.ReportService/DeleteReport",
        data
    );
};

// Comment APIs
// Add Comment
export const addComment = async (
    data: AddCommentRequest
): Promise<Comment> => {
    return postRequest<Comment, AddCommentRequest>(
        "/hero.reports.v2.ReportService/AddComment",
        data
    );
};

// Get Comments
export const getComments = async (
    data: GetCommentsRequest
): Promise<GetCommentsResponse> => {
    return postRequest<GetCommentsResponse, GetCommentsRequest>(
        "/hero.reports.v2.ReportService/GetComments",
        data
    );
};

// Update Comment
export const updateComment = async (
    data: UpdateCommentRequest
): Promise<Comment> => {
    return postRequest<Comment, UpdateCommentRequest>(
        "/hero.reports.v2.ReportService/UpdateComment",
        data
    );
};

// Delete Comment
export const deleteComment = async (
    data: DeleteCommentRequest
): Promise<Empty> => {
    return postRequest<Empty, DeleteCommentRequest>(
        "/hero.reports.v2.ReportService/DeleteComment",
        data
    );
};

// Resolve Comment
export const resolveComment = async (
    data: ResolveCommentRequest
): Promise<Comment> => {
    return postRequest<Comment, ResolveCommentRequest>(
        "/hero.reports.v2.ReportService/ResolveComment",
        data
    );
};

// Review APIs
// Submit For Review
export const submitForReview = async (
    data: SubmitForReviewRequest
): Promise<Report> => {
    return postRequest<Report, SubmitForReviewRequest>(
        "/hero.reports.v2.ReportService/SubmitForReview",
        data
    );
};

// Get Eligible Reviewers
export const getEligibleReviewers = async (
    data: GetEligibleReviewersRequest
): Promise<GetEligibleReviewersResponse> => {
    return postRequest<GetEligibleReviewersResponse, GetEligibleReviewersRequest>(
        "/hero.reports.v2.ReportService/GetEligibleReviewers",
        data
    );
};

// Approve Review Round
export const approveReviewRound = async (
    data: ApproveReviewRoundRequest
): Promise<ReviewRound> => {
    return postRequest<ReviewRound, ApproveReviewRoundRequest>(
        "/hero.reports.v2.ReportService/ApproveReviewRound",
        data
    );
};

// Request Changes
export const requestChanges = async (
    data: RequestChangesRequest
): Promise<ReviewRound> => {
    return postRequest<ReviewRound, RequestChangesRequest>(
        "/hero.reports.v2.ReportService/RequestChanges",
        data
    );
};

// Additional Info APIs
// Update Additional Info JSON
export const updateAdditionalInfoJson = async (
    data: UpdateAdditionalInfoJsonRequest
): Promise<UpdateAdditionalInfoJsonResponse> => {
    return postRequest<UpdateAdditionalInfoJsonResponse, UpdateAdditionalInfoJsonRequest>(
        "/hero.reports.v2.ReportService/UpdateAdditionalInfoJson",
        data
    );
};

// Get Additional Info
export const getAdditionalInfo = async (
    data: GetAdditionalInfoRequest
): Promise<GetAdditionalInfoResponse> => {
    return postRequest<GetAdditionalInfoResponse, GetAdditionalInfoRequest>(
        "/hero.reports.v2.ReportService/GetAdditionalInfo",
        data
    );
};

// Version APIs
// Get Report Version
export const getReportVersion = async (
    data: GetReportVersionRequest
): Promise<ReportSnapshot> => {
    return postRequest<ReportSnapshot, GetReportVersionRequest>(
        "/hero.reports.v2.ReportService/GetReportVersion",
        data
    );
};

// List Report Versions
export const listReportVersions = async (
    data: ListReportVersionsRequest
): Promise<ListReportVersionsResponse> => {
    return postRequest<ListReportVersionsResponse, ListReportVersionsRequest>(
        "/hero.reports.v2.ReportService/ListReportVersions",
        data
    );
};

// List Review Rounds For Report
export const listReviewRoundsForReport = async (
    data: ListReviewRoundsForReportRequest
): Promise<ListReviewRoundsForReportResponse> => {
    return postRequest<ListReviewRoundsForReportResponse, ListReviewRoundsForReportRequest>(
        "/hero.reports.v2.ReportService/ListReviewRoundsForReport",
        data
    );
};

// Search Reports
export const searchReports = async (
    data: SearchReportsRequest
): Promise<SearchReportsResponse> => {
    return postRequest<SearchReportsResponse, SearchReportsRequest>(
        "/hero.reports.v2.ReportService/SearchReports",
        data
    );
};

// Relation APIs
// Create Relation
export const createRelation = async (
    data: CreateRelationRequest
): Promise<Relation> => {
    return postRequest<Relation, CreateRelationRequest>(
        "/hero.reports.v2.ReportService/CreateRelation",
        data
    );
};

// Get Relation
export const getRelation = async (
    data: GetRelationRequest
): Promise<Relation> => {
    return postRequest<Relation, GetRelationRequest>(
        "/hero.reports.v2.ReportService/GetRelation",
        data
    );
};

// Update Relation
export const updateRelation = async (
    data: UpdateRelationRequest
): Promise<Relation> => {
    return postRequest<Relation, UpdateRelationRequest>(
        "/hero.reports.v2.ReportService/UpdateRelation",
        data
    );
};

// Delete Relation
export const deleteRelation = async (
    data: DeleteRelationRequest
): Promise<Empty> => {
    return postRequest<Empty, DeleteRelationRequest>(
        "/hero.reports.v2.ReportService/DeleteRelation",
        data
    );
};

// List Relations
export const listRelations = async (
    data: ListRelationsRequest
): Promise<ListRelationsResponse> => {
    return postRequest<ListRelationsResponse, ListRelationsRequest>(
        "/hero.reports.v2.ReportService/ListRelations",
        data
    );
};

// Filtered List APIs
// List Reports By Situation ID
export const listReportsBySituationId = async (
    data: ListReportsBySituationIdRequest
): Promise<ListReportsResponse> => {
    return postRequest<ListReportsResponse, ListReportsBySituationIdRequest>(
        "/hero.reports.v2.ReportService/ListReportsBySituationId",
        data
    );
};

// List Reports By Case ID
export const listReportsByCaseId = async (
    data: ListReportsByCaseIdRequest
): Promise<ListReportsResponse> => {
    return postRequest<ListReportsResponse, ListReportsByCaseIdRequest>(
        "/hero.reports.v2.ReportService/ListReportsByCaseId",
        data
    );
};

// Review Round APIs
// Add Review Round
export const addReviewRound = async (
    data: AddReviewRoundRequest
): Promise<ReviewRound> => {
    return postRequest<ReviewRound, AddReviewRoundRequest>(
        "/hero.reports.v2.ReportService/AddReviewRound",
        data
    );
};

// Get Review Round
export const getReviewRound = async (
    data: GetReviewRoundRequest
): Promise<ReviewRound> => {
    return postRequest<ReviewRound, GetReviewRoundRequest>(
        "/hero.reports.v2.ReportService/GetReviewRound",
        data
    );
};

// Update Review Round
export const updateReviewRound = async (
    data: UpdateReviewRoundRequest
): Promise<ReviewRound> => {
    return postRequest<ReviewRound, UpdateReviewRoundRequest>(
        "/hero.reports.v2.ReportService/UpdateReviewRound",
        data
    );
};

// Delete Review Round
export const deleteReviewRound = async (
    data: DeleteReviewRoundRequest
): Promise<Empty> => {
    return postRequest<Empty, DeleteReviewRoundRequest>(
        "/hero.reports.v2.ReportService/DeleteReviewRound",
        data
    );
}; 
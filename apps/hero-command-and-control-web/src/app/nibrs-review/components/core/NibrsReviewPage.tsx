"use client";

import { entitiesToOrganizationData } from "@/app/reports/ReportComponents/core/utils/utils";
import { create } from "@bufbuild/protobuf";
import {
  Alert,
  Box,
  colors,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  Snackbar,
  Stack,
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import { AddEntityRefToCaseRequestSchema } from "proto/hero/cases/v1/cases_pb";
import { Order } from "proto/hero/orders/v2/orders_pb";
import React from "react";
import { propertyToEntityFormat } from "../../../reports/ReportComponents/core/utils/propertyUtils";
import {
  PanelContent,
  SidePanel,
} from "../../../reports/ReportComponents/panels";
import AdministrativeSection from "../Sections/AdministrativeSection";
import ArrestSection from "../Sections/ArrestSection";
import OffenseSection from "../Sections/OffenseSection";
import { createEntityHandlers } from "./handlers/entityHandlers";
import { createFormHandlers } from "./handlers/formHandlers";
import { createRelationHandlers } from "./handlers/relationHandlers";
import { useNibrsReviewPageAPI } from "./hooks/useNibrsReviewPageAPI";
import { useNibrsReviewPageEffects } from "./hooks/useNibrsReviewPageEffects";
import { useNibrsReviewPageState } from "./hooks/useNibrsReviewPageState";
import NibrsReviewHeader from "./NibrsReviewHeader";
import { PanelType } from "./types";
import {
  updateEntityDisplayNameInSection,
  updateEntityListSection,
  upsertReportSectionRef,
} from "./utils/sectionUtils";
import {
  createGoBack,
  createScrollToSection,
  entitiesToPersonData,
  entitiesToPropertyData,
  entitiesToVehicleData,
  useGetContextualPanelTitle,
  useGetCurrentSchema,
} from "./utils/utils";

// Contexts
import { Asset } from "proto/hero/assets/v2/assets_pb";
import { SectionType } from "proto/hero/reports/v2/reports_pb";
import { useDispatcher } from "../../../contexts/User/DispatcherContext";

// Fixed navigation items for NIBRS Review
const nibrsNavItems = [
  { id: "administrative", label: "Administrative" },
  { id: "offenses", label: "Offenses" },
  { id: "all-victims", label: "Victims" },
  { id: "all-offenders", label: "Offenders" },
  {
    id: "victim-offender-relationships",
    label: "Victim-Offender Relationships",
  },
  { id: "all-properties", label: "Properties" },
  { id: "arrests", label: "Arrestees" },
];

export default function NibrsReviewPage({
  currentOrder,
}: {
  currentOrder?: Order;
}) {
  const isMountedRef = React.useRef(true);
  React.useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const reportId = searchParams?.get("reportId") || null;
  const { asset: dispatcherAsset } = useDispatcher();

  // Use state management hook
  const stateValues = useNibrsReviewPageState();

  // Extract commonly used state values
  const {
    people,
    vehicles,
    properties,
    organizations,
    editingEntityId,
    activeSection,
    scrollContainerRef,
    activePanelType,
    sidePanelOpen,
    editingEntityFormData,
    notification,
    saveStatuses,
    associatedCases,
    activeOffenseRelation,
    activeVehicleOffenseContext,
    activePropertyOffenseContext,
    activeArrestRelation,
    activeOrganizationOffenseContext,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    offenseListSectionId,
    isSaveLoading,
    isUpdateLoading,
    isSaveAndAddAnotherLoading,
  } = stateValues;

  // Use API hooks
  const apiValues = useNibrsReviewPageAPI({
    reportId,
    editingEntityId,
    editingEntity: editingEntityFormData,
    entityIdsToFetch: stateValues.entityIdsToFetch,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    associatedCases,
  });

  // Extract commonly used API values
  const {
    reportQuery,
    reportSectionsQuery,
    personSchemasQuery,
    propertySchemasQuery,
    vehicleSchemasQuery,
    organizationSchemasQuery,
    editingEntityQuery,
    specificSchemaQuery,
    batchEntitiesQuery,
    casesQuery,
    fullCaseQuery,
    updateReportSectionMutation,
    createReportSectionMutation,
    createEntityMutation,
    createPropertyMutation,
    updateEntityMutation,
    addEntityRefToCaseMutation,
    createRelationMutation,
    updateRelationMutation,
    deleteRelationMutation,
  } = apiValues;

  // Calculate derived values
  const availableNavItems = nibrsNavItems;

  // Navigation helpers
  const scrollToSection = createScrollToSection(scrollContainerRef);
  const goBack = createGoBack(router);

  // Get current schema helper
  const getCurrentSchema = useGetCurrentSchema(
    activePanelType,
    editingEntityId,
    editingEntityQuery.data,
    specificSchemaQuery.data,
    personSchemasQuery.data,
    propertySchemasQuery.data,
    vehicleSchemasQuery.data,
    organizationSchemasQuery.data
  );

  // Get contextual panel title helper
  const getContextualPanelTitle = useGetContextualPanelTitle(
    activePanelType,
    activeOffenseRelation,
    activeVehicleOffenseContext,
    activePropertyOffenseContext,
    activeOrganizationOffenseContext,
    editingEntityId
  );

  // Handle mutations for addEntityRefToCase
  const handleAddEntityRefToCaseMutate = React.useCallback(
    async (request: any) => {
      // Optimistically update the local associatedCases state
      if (request.entityRef) {
        stateValues.setAssociatedCases((prevCases: any[]) =>
          prevCases.map((caseItem: any) => {
            if (caseItem.id === request.caseId) {
              // Check if entity is already in this case to prevent duplicates
              const isAlreadyInCase = caseItem.entityRefs?.some(
                (ref: any) => ref.id === request.entityRef?.id
              );

              if (!isAlreadyInCase && request.entityRef) {
                return {
                  ...caseItem,
                  entityRefs: [
                    ...(caseItem.entityRefs || []),
                    request.entityRef,
                  ],
                };
              }
            }
            return caseItem;
          })
        );
      }

      addEntityRefToCaseMutation.mutate(request, {
        onSuccess: (data: any) => {
          console.log("Entity reference added to case successfully");
          // Invalidate the cases by report query to refresh associatedCases
          if (reportId) {
            // Invalidate all cases queries related to this report
            queryClient.invalidateQueries({
              queryKey: ["cases", "byReport", reportId],
              exact: false,
            });
            // Also invalidate the specific case query if available
            if (data?.id) {
              queryClient.invalidateQueries({
                queryKey: ["case", data.id],
              });
            }
          }
        },
        onError: (error: any) => {
          // Revert the optimistic update on error
          console.error("Error adding entity to case:", error);
          if (request.entityRef?.id) {
            stateValues.setAssociatedCases((prevCases: any[]) =>
              prevCases.map((caseItem: any) => {
                if (caseItem.id === request.caseId) {
                  return {
                    ...caseItem,
                    entityRefs: (caseItem.entityRefs || []).filter(
                      (ref: any) => ref.id !== request.entityRef?.id
                    ),
                  };
                }
                return caseItem;
              })
            );
          }
        },
      });
    },
    [addEntityRefToCaseMutation, reportId, queryClient, stateValues]
  );

  // Create handlers (these need to be before useEffects)
  const relationHandlers = createRelationHandlers({
    reportId,
    report: reportQuery.data,
    people,
    vehicles,
    properties,
    organizations,
    setPeople: stateValues.setPeople,
    setVehicles: stateValues.setVehicles,
    setProperties: stateValues.setProperties,
    setOrganizations: stateValues.setOrganizations,
    setNotification: stateValues.setNotification,
    associatedCases,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    reportSections: reportSectionsQuery.data,
    createRelationMutation: {
      ...createRelationMutation,
      mutate: (request: any) =>
        createRelationMutation.mutate(request, {
          onError: handleCreateRelationError,
        }),
    },
    updateRelationMutation: {
      ...updateRelationMutation,
      mutate: (request: any) =>
        updateRelationMutation.mutate(request, {
          onError: handleUpdateRelationError,
        }),
    },
    deleteRelationMutation: {
      ...deleteRelationMutation,
      mutate: (request: any) =>
        deleteRelationMutation.mutate(request, {
          onError: handleDeleteRelationError,
        }),
    },
    updateReportSectionMutation: {
      ...updateReportSectionMutation,
      mutate: (request: any) =>
        updateReportSectionMutation.mutate(request, {
          onError: handleUpdateReportSectionError,
        }),
    },
  });

  const entityHandlers = createEntityHandlers({
    people,
    vehicles,
    properties,
    organizations,
    setPeople: stateValues.setPeople,
    setVehicles: stateValues.setVehicles,
    setProperties: stateValues.setProperties,
    setOrganizations: stateValues.setOrganizations,
    setSidePanelOpen: stateValues.setSidePanelOpen,
    setActivePanelType: stateValues.setActivePanelType,
    setEditingEntityId: stateValues.setEditingEntityId,
    setEditingEntityFormData: stateValues.setEditingEntityFormData,
    setSelectedRowId: stateValues.setSelectedRowId,
    setActiveOffenseRelation: stateValues.setActiveOffenseRelation,
    setNotification: stateValues.setNotification,
    activeOffenseRelation,
    activeVehicleOffenseContext,
    activePropertyOffenseContext,
    activeArrestRelation,
    activeOrganizationOffenseContext,
    setActiveVehicleOffenseContext: stateValues.setActiveVehicleOffenseContext,
    setActivePropertyOffenseContext:
      stateValues.setActivePropertyOffenseContext,
    setActiveArrestRelation: stateValues.setActiveArrestRelation,
    setActiveOrganizationOffenseContext:
      stateValues.setActiveOrganizationOffenseContext,
    reportId,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    reportSections: reportSectionsQuery.data,
    associatedCases,
    updateReportSectionMutation: {
      ...updateReportSectionMutation,
      mutate: (request: any) =>
        updateReportSectionMutation.mutate(request, {
          onError: handleUpdateReportSectionError,
        }),
    },
    addEntityRefToCaseMutation: {
      ...addEntityRefToCaseMutation,
      mutate: handleAddEntityRefToCaseMutate,
    },
    handleAddPersonToOffense: relationHandlers.handleAddPersonToOffense,
    handleAddVehicleToOffense: relationHandlers.handleAddVehicleToOffense,
    handleAddPropertyToOffense: relationHandlers.handleAddPropertyToOffense,
    handleAddPersonToArrest: relationHandlers.handleAddPersonToArrest,
    handleAddOrganizationToOffense:
      relationHandlers.handleAddOrganizationToOffense,
  });

  const formHandlers = createFormHandlers({
    setIsSaveLoading: stateValues.setIsSaveLoading,
    setIsUpdateLoading: stateValues.setIsUpdateLoading,
    setIsSaveAndAddAnotherLoading: stateValues.setIsSaveAndAddAnotherLoading,
    setNotification: stateValues.setNotification,
    setActivePanelType: stateValues.setActivePanelType,
    panelContentRef: stateValues.panelContentRef,
    editingEntity: editingEntityQuery.data,
    editingEntityId,
    activePanelType,
    personSchemas: personSchemasQuery.data,
    vehicleSchemas: vehicleSchemasQuery.data,
    propertySchemas: propertySchemasQuery.data,
    organizationSchemas: organizationSchemasQuery.data,
    dispatcherAsset: dispatcherAsset as Asset | undefined,
    createEntityMutation: {
      ...createEntityMutation,
      mutate: (request: any, options?: any) =>
        createEntityMutation.mutate(request, {
          onSuccess: (response: any) => {
            // Call the main success handler first, passing along any additional data
            handleCreateEntitySuccess(response, options?.additionalVictimData);
            // Then call any additional success handler from options
            if (options?.onSuccess) {
              options.onSuccess(response);
            }
          },
          onError: (error: any) => {
            handleCreateEntityError(error);
            // Call any additional error handler from options
            if (options?.onError) {
              options.onError(error);
            }
          },
        }),
    },
    createPropertyMutation: {
      ...createPropertyMutation,
      mutate: (request: any, options?: any) =>
        createPropertyMutation.mutate(request, {
          onSuccess: (response: any) => {
            // Call the main success handler first, passing along any additional data
            handleCreatePropertySuccess(response, options?.additionalVictimData);
            // Then call any additional success handler from options
            if (options?.onSuccess) {
              options.onSuccess(response);
            }
            // Close the side panel after handling the success
            entityHandlers.handleCloseSidePanel(
              false,
              isSaveLoading,
              isUpdateLoading,
              isSaveAndAddAnotherLoading
            );
          },
          onError: (error: any) => {
            handleCreateEntityError(error); // Reuse entity error handler for now
            // Call any additional error handler from options
            if (options?.onError) {
              options.onError(error);
            }
          },
        }),
    },
    updateEntityMutation: {
      ...updateEntityMutation,
      mutate: (request: any) =>
        updateEntityMutation.mutate(request, {
          onSuccess: handleUpdateEntitySuccess,
          onError: handleUpdateEntityError,
        }),
    },
    updateReportSectionMutation: {
      ...updateReportSectionMutation,
      mutate: (request: any, options?: any) =>
        updateReportSectionMutation.mutate(request, {
          onSuccess: (data: any) => {
            if (options?.onSuccess) {
              options.onSuccess(data);
            }
          },
          onError: (error: any) => {
            handleUpdateReportSectionError(error);
            if (options?.onError) {
              options.onError(error);
            }
          },
        }),
    },
    reportId,
    queryClient,
    handleCloseSidePanel: () =>
      entityHandlers.handleCloseSidePanel(
        false,
        isSaveLoading,
        isUpdateLoading,
        isSaveAndAddAnotherLoading
      ),
  });

  // Use effects hook
  const { handleSaveStatusChange } = useNibrsReviewPageEffects({
    availableNavItems: nibrsNavItems,
    activeSection,
    setActiveSection: stateValues.setActiveSection,
    scrollContainerRef,
    setEditingEntityFormData: stateValues.setEditingEntityFormData,
    setEntityIdsToFetch: stateValues.setEntityIdsToFetch,
    setPeople: stateValues.setPeople,
    setVehicles: stateValues.setVehicles,
    setProperties: stateValues.setProperties,
    setOrganizations: stateValues.setOrganizations,
    setSaveStatuses: stateValues.setSaveStatuses,
    setAssociatedCases: stateValues.setAssociatedCases,
    setPeopleListSectionId: stateValues.setPeopleListSectionId,
    setVehicleListSectionId: stateValues.setVehicleListSectionId,
    setPropertyListSectionId: stateValues.setPropertyListSectionId,
    setOrganizationListSectionId: stateValues.setOrganizationListSectionId,
    setOffenseListSectionId: stateValues.setOffenseListSectionId,
    setSectionIdToType: stateValues.setSectionIdToType,
    editingEntity: editingEntityQuery.data,
    reportSections: reportSectionsQuery.data,
    batchEntitiesData: batchEntitiesQuery.data,
    casesData: casesQuery.data,
    reportId,
    handleSaveStatusChange: (status: any) => {
      stateValues.setSaveStatuses((prev: any[]) => {
        const existingIndex = prev.findIndex((s) => s.source === status.source);
        if (existingIndex >= 0) {
          const newStatuses = [...prev];
          newStatuses[existingIndex] = status;
          return newStatuses;
        } else {
          return [...prev, status];
        }
      });
    },
  });

  // Setup mutations with handlers
  const handleUpdateReportSectionError = React.useCallback(
    (error: any) => {
      console.error("Error updating report section:", error);
      stateValues.setNotification({
        open: true,
        message: `Error updating section: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  const handleCreateEntitySuccess = React.useCallback(
    (response: any, additionalVictimData: any | undefined) => {
      console.log(
        "createEntityMutation.onSuccess triggered for:",
        response.entity?.entityType
      );
      console.log("Current offense contexts:", {
        activeOffenseRelation,
        activeVehicleOffenseContext,
        activePropertyOffenseContext,
        activeArrestRelation,
        people,
        vehicles,
        properties,
        activeOrganizationOffenseContext,
        reportId,
      });

      if (response.entity) {
        const entity = response.entity;
        let updatedEntities = [];

        // Add entity to appropriate list and get the updated list
        switch (entity.entityType) {
          case "ENTITY_TYPE_PERSON":
            updatedEntities = [...people, entity];
            stateValues.setPeople(updatedEntities);
            updateEntityListSection(
              "People",
              entity,
              peopleListSectionId,
              reportId || "",
              reportSectionsQuery.data,
              {
                ...updateReportSectionMutation,
                mutate: (request: any) =>
                  updateReportSectionMutation.mutate(request, {
                    onError: handleUpdateReportSectionError,
                  }),
              },
              { people, vehicles, properties, organizations },
              updatedEntities
            );

            // Add entity reference to all associated cases
            if (associatedCases?.length > 0) {
              associatedCases.forEach((caseItem: any) => {
                const entityData = entitiesToPersonData([entity])[0];

                // Check if entity is already in this case to prevent duplicates
                const isAlreadyInCase = caseItem.entityRefs?.some(
                  (ref: any) => ref.id === entity.id
                );

                if (!isAlreadyInCase) {
                  const request = create(AddEntityRefToCaseRequestSchema, {
                    caseId: caseItem.id,
                    entityRef: {
                      id: entity.id,
                      type: "ENTITY_TYPE_PERSON",
                      displayName: entityData.name,
                    },
                  });
                  handleAddEntityRefToCaseMutate(request);
                } else {
                  console.log("Entity already in case, skipping:", entity.id);
                }
              });
            }

            // If this person was added from an offense context, create the relation
            if (activeOffenseRelation) {
              console.log("activeOffenseRelation", activeOffenseRelation);
              console.log("Response with additional data:", response);

              // Always create the person-to-offense relation first
              relationHandlers.handleAddPersonToOffense(
                entity.id,
                activeOffenseRelation.offenseId,
                activeOffenseRelation.relationType,
                entity
              );

              // If this is a victim with additional victim data, also create victim-entity relation
              if (
                activeOffenseRelation.relationType === "victim" &&
                additionalVictimData
              ) {
                console.log(
                  "Creating additional victim-report relation with data:",
                  additionalVictimData
                );
                relationHandlers.handleCreateVictimReportRelation(
                  entity.id,
                  reportId || "",
                  entity,
                  additionalVictimData
                );
              }

              // Clear the active offense relation
              stateValues.setActiveOffenseRelation(null);
            }

            // If this person was added from an arrest context, create the relation
            if (activeArrestRelation) {
              console.log("activeArrestRelation", activeArrestRelation);
              console.log("Response with additional data:", response);

              // Create the person-to-arrest relation
              relationHandlers.handleAddPersonToArrest(
                entity.id,
                activeArrestRelation.arrestId,
                entity
              );

              // Clear the active arrest relation
              stateValues.setActiveArrestRelation(null);
            }

            break;
          case "ENTITY_TYPE_VEHICLE":
            updatedEntities = [...vehicles, entity];
            stateValues.setVehicles(updatedEntities);
            updateEntityListSection(
              "Vehicles",
              entity,
              vehicleListSectionId,
              reportId || "",
              reportSectionsQuery.data,
              {
                ...updateReportSectionMutation,
                mutate: (request: any) =>
                  updateReportSectionMutation.mutate(request, {
                    onError: handleUpdateReportSectionError,
                  }),
              },
              { people, vehicles, properties, organizations },
              updatedEntities
            );

            // Add entity reference to all associated cases
            if (associatedCases?.length > 0) {
              associatedCases.forEach((caseItem: any) => {
                const entityData = entitiesToVehicleData([entity])[0];

                // Check if entity is already in this case to prevent duplicates
                const isAlreadyInCase = caseItem.entityRefs?.some(
                  (ref: any) => ref.id === entity.id
                );

                if (!isAlreadyInCase) {
                  const request = create(AddEntityRefToCaseRequestSchema, {
                    caseId: caseItem.id,
                    entityRef: {
                      id: entity.id,
                      type: "ENTITY_TYPE_VEHICLE",
                      displayName: entityData.make + " " + entityData.model,
                    },
                  });
                  handleAddEntityRefToCaseMutate(request);
                } else {
                  console.log("Entity already in case, skipping:", entity.id);
                }
              });
            }

            // If this vehicle was added from an offense context, create the relation
            if (activeVehicleOffenseContext) {
              console.log(
                "Creating vehicle offense relation:",
                entity.id,
                activeVehicleOffenseContext.offenseId
              );
              relationHandlers.handleAddVehicleToOffense(
                entity.id,
                activeVehicleOffenseContext.offenseId,
                entity
              );
              // Clear the active vehicle offense context
              stateValues.setActiveVehicleOffenseContext(null);
            } else {
              console.log(
                "No active vehicle offense context for vehicle:",
                entity.id
              );
            }
            break;

          case "ENTITY_TYPE_ORGANIZATION":
            updatedEntities = [...organizations, entity];
            stateValues.setOrganizations(updatedEntities);
            updateEntityListSection(
              "Organizations",
              entity,
              organizationListSectionId,
              reportId || "",
              reportSectionsQuery.data,
              {
                ...updateReportSectionMutation,
                mutate: (request: any) =>
                  updateReportSectionMutation.mutate(request, {
                    onError: handleUpdateReportSectionError,
                  }),
              },
              { people, vehicles, properties, organizations },
              updatedEntities
            );

            // Add entity reference to all associated cases
            if (associatedCases?.length > 0) {
              associatedCases.forEach((caseItem: any) => {
                const entityData = entitiesToOrganizationData([entity])[0];

                // Check if entity is already in this case to prevent duplicates
                const isAlreadyInCase = caseItem.entityRefs?.some(
                  (ref: any) => ref.id === entity.id
                );

                if (!isAlreadyInCase) {
                  const request = create(AddEntityRefToCaseRequestSchema, {
                    caseId: caseItem.id,
                    entityRef: {
                      id: entity.id,
                      type: "ENTITY_TYPE_ORGANIZATION",
                      displayName: entityData.name,
                    },
                  });
                  handleAddEntityRefToCaseMutate(request);
                } else {
                  console.log("Entity already in case, skipping:", entity.id);
                }
              });
            }

            // If this organization was added from an offense context, create the relation
            if (activeOrganizationOffenseContext) {
              console.log(
                "Creating organization offense relation:",
                entity.id,
                activeOrganizationOffenseContext.offenseId
              );
              relationHandlers.handleAddOrganizationToOffense(
                entity.id,
                activeOrganizationOffenseContext.offenseId,
                entity
              );
              // Clear the active organization offense context
              stateValues.setActiveOrganizationOffenseContext(null);
            } else {
              console.log(
                "No active organization offense context for organization:",
                entity.id
              );
            }
            break;

          case "ENTITY_TYPE_PROPERTY": {
            updatedEntities = [...properties, entity];
            stateValues.setProperties(updatedEntities);

            // Check if this is a SECTION_TYPE_PROPERTY section (new property service)
            // or SECTION_TYPE_ENTITY_LIST_PROPERTIES section (old entity list)
            const section = reportSectionsQuery.data?.sections?.find(
              (s: any) => s.id === propertyListSectionId
            );
            const sectionType = section?.type;

            if (sectionType === SectionType.ENTITY_LIST_PROPERTIES) {
              // For old entity list sections, update the report section
              updateEntityListSection(
                "Properties",
                entity,
                propertyListSectionId,
                reportId || "",
                reportSectionsQuery.data,
                {
                  ...updateReportSectionMutation,
                  mutate: (request: any) =>
                    updateReportSectionMutation.mutate(request, {
                      onError: handleUpdateReportSectionError,
                    }),
                },
                { people, vehicles, properties, organizations },
                updatedEntities
              );
            } else if (sectionType === SectionType.PROPERTY) {
              // For new property service sections, update the property list section
              {
                const propertyData = entitiesToPropertyData([entity])[0];
                const propertyRef = {
                  id: entity.id,
                  displayName: propertyData.category,
                  relationType: "involved",
                  version: entity.version || 1,
                };
                upsertReportSectionRef({
                  listType: "propertyList",
                  title: "Properties",
                  newRef: propertyRef,
                  sectionId: propertyListSectionId || "",
                  reportId: reportId || "",
                  reportSections: reportSectionsQuery.data,
                  updateReportSectionMutation: {
                    ...updateReportSectionMutation,
                    mutate: (request: any) =>
                      updateReportSectionMutation.mutate(request, {
                        onError: handleUpdateReportSectionError,
                      }),
                  },
                });
              }
            }

            // Add entity reference to all associated cases
            if (associatedCases?.length > 0) {
              associatedCases.forEach((caseItem: any) => {
                const entityData = entitiesToPropertyData([entity])[0];

                // Check if entity is already in this case to prevent duplicates
                const isAlreadyInCase = caseItem.entityRefs?.some(
                  (ref: any) => ref.id === entity.id
                );

                if (!isAlreadyInCase) {
                  const request = create(AddEntityRefToCaseRequestSchema, {
                    caseId: caseItem.id,
                    entityRef: {
                      id: entity.id,
                      type: "ENTITY_TYPE_PROPERTY",
                      displayName: entityData.category,
                    },
                  });
                  handleAddEntityRefToCaseMutate(request);
                } else {
                  console.log("Entity already in case, skipping:", entity.id);
                }
              });
            }

            // If this property was added from an offense context, create the relation
            if (activePropertyOffenseContext) {
              console.log(
                "Creating property offense relation:",
                entity.id,
                activePropertyOffenseContext.offenseId
              );
              relationHandlers.handleAddPropertyToOffense(
                entity.id,
                activePropertyOffenseContext.offenseId,
                entity
              );
              // Clear the active property offense context
              stateValues.setActivePropertyOffenseContext(null);
            } else {
              console.log(
                "No active property offense context for property:",
                entity.id
              );
            }
            break;
          }
        }
      }
      stateValues.setIsSaveLoading(false);
      stateValues.setIsSaveAndAddAnotherLoading(false);
    },
    [
      activeOffenseRelation,
      activeVehicleOffenseContext,
      activePropertyOffenseContext,
      activeArrestRelation,
      people,
      vehicles,
      properties,
      stateValues,
      peopleListSectionId,
      vehicleListSectionId,
      propertyListSectionId,
      reportId,
      reportSectionsQuery.data,
      updateReportSectionMutation,
      associatedCases,
      relationHandlers,
      handleAddEntityRefToCaseMutate,
      handleUpdateReportSectionError,
    ]
  );

  const handleCreateEntityError = React.useCallback(
    (error: any) => {
      stateValues.setNotification({
        open: true,
        message: `Error creating entity: ${error.message}`,
        severity: "error",
      });

      // Reset loading states
      stateValues.setIsSaveLoading(false);
      stateValues.setIsSaveAndAddAnotherLoading(false);
    },
    [stateValues]
  );

  // Helper removed in favor of shared utility: upsertReportSectionRef

  // Add property success handler
  const handleCreatePropertySuccess = React.useCallback(
    (response: any, additionalVictimData: any | undefined) => {
      // The response IS the property, not wrapped in a property field
      const property = response;

      // Convert property to entity format for UI compatibility
      const entity = propertyToEntityFormat(property);
      const updatedEntities = [...properties, entity];

      stateValues.setProperties(updatedEntities);

      // Check if this is a SECTION_TYPE_PROPERTY section (new property service)
      // or SECTION_TYPE_ENTITY_LIST_PROPERTIES section (old entity list)
      const section = reportSectionsQuery.data?.sections?.find(
        (s: any) => s.id === propertyListSectionId
      );
      const sectionType = section?.type;

      if (sectionType === SectionType.ENTITY_LIST_PROPERTIES) {
        // For old entity list sections, update the report section
        updateEntityListSection(
          "Properties",
          entity,
          propertyListSectionId,
          reportId || "",
          reportSectionsQuery.data,
          {
            ...updateReportSectionMutation,
            mutate: (request: any) =>
              updateReportSectionMutation.mutate(request, {
                onError: handleUpdateReportSectionError,
              }),
          },
          { people, vehicles, properties, organizations },
          updatedEntities
        );
      } else if (sectionType === SectionType.PROPERTY) {
        // For new property service sections, update the property list section
        // Convert entity to property reference format and upsert
        {
          const propertyData = entitiesToPropertyData([entity])[0];
          const propertyRef = {
            id: entity.id,
            displayName: propertyData.category,
            relationType: "involved",
            version: entity.version || 1,
          };
          upsertReportSectionRef({
            listType: "propertyList",
            title: "Properties",
            newRef: propertyRef,
            sectionId: propertyListSectionId || "",
            reportId: reportId || "",
            reportSections: reportSectionsQuery.data,
            updateReportSectionMutation: {
              ...updateReportSectionMutation,
              mutate: (request: any) =>
                updateReportSectionMutation.mutate(request, {
                  onError: handleUpdateReportSectionError,
                }),
            },
          });
        }
      } else {
        console.log("No SECTION_TYPE_PROPERTY section found, creating new one");
        // Create a new SECTION_TYPE_PROPERTY section if it doesn't exist
        createReportSectionMutation.mutate({
          reportId: reportId || "",
          section: {
            // @ts-expect-error TODO: Fix type issue
            type: "SECTION_TYPE_PROPERTY",
            propertyList: {
              title: "Properties",
              propertyRefs: [{
                id: entity.id,
                displayName: entity.propertySchema?.category || "Property",
              }],
            },
          },
        }, {
          onSuccess: (newSection) => {
            console.log("Created new property section:", newSection);
            if (isMountedRef.current) {
              // Update the propertyListSectionId to the newly created section
              stateValues.setPropertyListSectionId(newSection.id);
            }
            // Invalidate queries to refresh the UI (safe to call even if unmounted)
            queryClient.invalidateQueries({ queryKey: ['reportSections', reportId] });
          },
          onError: (error) => {
            console.error("Error creating property section:", error);
            if (isMountedRef.current) {
              stateValues.setNotification({
                open: true,
                message: "Error creating property section",
                severity: "error",
              });
            }
          },
        });
      }

      // Add entity reference to all associated cases
      // NOTE: Skip adding property service properties to cases since they are not entities
      // Properties created via the property service cannot be added to cases using entity references
      if (associatedCases?.length > 0 && sectionType === SectionType.ENTITY_LIST_PROPERTIES) {
        // Only add to cases if this was created via the old entity service (entity list section)
        // Properties created via the new property service should not be added to cases as entities
        associatedCases.forEach((caseItem: any) => {
          const entityData = entitiesToPropertyData([entity])[0];

          // Check if entity is already in this case to prevent duplicates
          const isAlreadyInCase = caseItem.entityRefs?.some(
            (ref: any) => ref.id === entity.id
          );

          if (!isAlreadyInCase) {
            const request = create(AddEntityRefToCaseRequestSchema, {
              caseId: caseItem.id,
              entityRef: {
                id: entity.id,
                type: "ENTITY_TYPE_PROPERTY",
                displayName: entityData.category,
              },
            });
            handleAddEntityRefToCaseMutate(request);
          }
        });
      } else if (associatedCases?.length > 0) {
        console.log("Skipping case association for property service properties - properties are not entities");
      }

      // If this property was added from an offense context, create the relation
      if (activePropertyOffenseContext) {
        relationHandlers.handleAddPropertyToOffense(
          entity.id,
          activePropertyOffenseContext.offenseId,
          entity
        );
        // Clear the active property offense context
        stateValues.setActivePropertyOffenseContext(null);
      }
      stateValues.setIsSaveLoading(false);
      stateValues.setIsSaveAndAddAnotherLoading(false);
    },
    [
      activePropertyOffenseContext,
      people,
      vehicles,
      properties,
      organizations,
      stateValues,
      propertyListSectionId,
      reportId,
      reportSectionsQuery.data,
      updateReportSectionMutation,
      createReportSectionMutation,
      associatedCases,
      relationHandlers,
      handleAddEntityRefToCaseMutate,
      handleUpdateReportSectionError,
      queryClient,
    ]
  );

  const handleUpdateEntitySuccess = React.useCallback(
    (updatedEntity: any) => {
      // Update the corresponding entity list
      if (updatedEntity) {
        let sectionId: string | null = null;

        switch (updatedEntity.entityType) {
          case "ENTITY_TYPE_PERSON":
            stateValues.setPeople((prev: any[]) =>
              prev.map((entity: any) =>
                entity.id === updatedEntity.id ? updatedEntity : entity
              )
            );
            sectionId = peopleListSectionId;
            break;
          case "ENTITY_TYPE_VEHICLE":
            stateValues.setVehicles((prev: any[]) =>
              prev.map((entity: any) =>
                entity.id === updatedEntity.id ? updatedEntity : entity
              )
            );
            sectionId = vehicleListSectionId;
            break;
          case "ENTITY_TYPE_PROPERTY":
            stateValues.setProperties((prev: any[]) =>
              prev.map((entity: any) =>
                entity.id === updatedEntity.id ? updatedEntity : entity
              )
            );
            sectionId = propertyListSectionId;
            break;
        }

        // If the section exists, update it to ensure the displayName is current
        if (sectionId && reportId) {
          updateEntityDisplayNameInSection(
            updatedEntity,
            sectionId,
            reportId,
            reportSectionsQuery.data,
            updateReportSectionMutation
          );
        }
      }

      // Reset update loading state and force close panel on success
      stateValues.setIsUpdateLoading(false);
      entityHandlers.handleCloseSidePanel(true, false, false, false);
    },
    [
      stateValues,
      peopleListSectionId,
      vehicleListSectionId,
      propertyListSectionId,
      reportId,
      reportSectionsQuery.data,
      updateReportSectionMutation,
      entityHandlers,
    ]
  );

  const handleUpdateEntityError = React.useCallback(
    (error: any) => {
      console.error("Error updating entity:", error);
      stateValues.setNotification({
        open: true,
        message: `Error updating entity: ${error.message}`,
        severity: "error",
      });

      // Reset loading state but don't close panel
      stateValues.setIsUpdateLoading(false);
    },
    [stateValues]
  );

  const handleCreateRelationError = React.useCallback(
    (error: any) => {
      console.error("Error creating relation:", error);
      stateValues.setNotification({
        open: true,
        message: `Error adding person to offense: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  const handleDeleteRelationError = React.useCallback(
    (error: any) => {
      console.error("Error deleting relation:", error);
      stateValues.setNotification({
        open: true,
        message: `Error removing person from offense: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  const handleUpdateRelationError = React.useCallback(
    (error: any) => {
      console.error("Error updating relation:", error);
      stateValues.setNotification({
        open: true,
        message: `Error updating relation: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  // Handle setting active offense relation with proper interface
  const handleSetActiveOffenseRelation = (
    context: {
      offenseId: string;
      relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party";
    } | null
  ) => {
    stateValues.setActiveOffenseRelation(context);
  };

  // Handle setting active vehicle offense context
  const handleSetActiveVehicleOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    console.log("Setting active vehicle offense context:", context);
    stateValues.setActiveVehicleOffenseContext(context);
  };

  // Handle setting active property offense context
  const handleSetActivePropertyOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    console.log("Setting active property offense context:", context);
    stateValues.setActivePropertyOffenseContext(context);
  };

  // Handle setting active arrest relation context
  const handleSetActiveArrestRelation = (
    context: { arrestId: string } | null
  ) => {
    console.log("Setting active arrest relation context:", context);
    stateValues.setActiveArrestRelation(context);
  };

  // Handle setting active organization offense context
  const handleSetActiveOrganizationOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    console.log("Setting active organization offense context:", context);
    stateValues.setActiveOrganizationOffenseContext(context);
  };

  if (!reportId) return null;

  return (
    <Box
      sx={{
        position: "fixed",
        inset: 0,
        zIndex: 50,
        display: "flex",
        bgcolor: "rgba(0, 0, 0, 0.5)",
      }}
      onClick={goBack}
    >
      <Box sx={{ flex: 1, display: "flex", pl: "20px" }}>
        <Paper
          ref={scrollContainerRef}
          elevation={3}
          sx={{
            flex: 1,
            ml: 2,
            overflow: "auto",
            display: "flex",
            flexDirection: "column",
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Sticky Header */}
          <Box
            sx={{
              position: "sticky",
              top: 0,
              zIndex: 20,
              bgcolor: "background.paper",
              borderBottom: 1,
              borderColor: "divider",
            }}
          >
            <NibrsReviewHeader
              report={reportQuery.data}
              saveStatuses={saveStatuses}
            />
          </Box>

          <Box sx={{ display: "flex", flex: 1 }}>
            {/* Left Sidebar */}
            <Paper
              elevation={0}
              sx={{
                width: 256,
                borderRight: 1,
                borderColor: "divider",
                position: "sticky",
                top: 100,
                height: "calc(100vh - 100px)",
                overflow: "auto",
                borderRadius: 0,
              }}
            >
              <List component="nav" sx={{ p: 2 }}>
                {nibrsNavItems.map((item) => (
                  <ListItem key={item.id} disablePadding>
                    <ListItemButton
                      onClick={() => scrollToSection(item.id)}
                      selected={activeSection === item.id}
                      sx={(theme) => ({
                        borderRadius: 1,
                        "&:hover": { bgcolor: "grey.100" },
                        "&.Mui-selected": {
                          bgcolor: colors.blue[50],
                          "&:hover": { bgcolor: colors.blue[100] },
                        },
                      })}
                    >
                      <ListItemText
                        primary={item.label}
                        sx={{
                          "& .MuiListItemText-primary": { fontSize: "16px" },
                        }}
                      />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Paper>

            {/* Main Content */}
            <Box sx={{ flex: 1, bgcolor: "grey.50", p: 2 }}>
              <Stack spacing={3} pb={34}>
                {/* Administrative Section */}
                <Box id="administrative">
                  <AdministrativeSection
                    reportId={reportId}
                    onSaveStatusChange={handleSaveStatusChange}
                    readOnly={false}
                  />
                </Box>

                {/* Offenses Section */}
                <Box id="offenses">
                  <OffenseSection
                    reportId={reportId}
                    onSaveStatusChange={handleSaveStatusChange}
                    readOnly={false}
                    people={entitiesToPersonData(people)}
                    vehicles={entitiesToVehicleData(vehicles)}
                    properties={entitiesToPropertyData(properties)}
                    organizations={entitiesToOrganizationData(organizations)}
                    relations={reportQuery.data?.relations || []}
                    associatedCases={
                      fullCaseQuery.data ? [fullCaseQuery.data] : []
                    }
                    onOpenSidePanel={(
                      panelType: "PERSON" | "VEHICLE" | "PROPERTY" | "ORGANIZATION"
                    ) =>
                      entityHandlers.handleOpenSidePanel(
                        panelType as PanelType,
                        false
                      )
                    }
                    onAddPersonToOffense={
                      relationHandlers.handleAddPersonToOffense
                    }
                    onAddVehicleToOffense={
                      relationHandlers.handleAddVehicleToOffense
                    }
                    onAddPropertyToOffense={
                      relationHandlers.handleAddPropertyToOffense
                    }
                    onAddOrganizationToOffense={
                      relationHandlers.handleAddOrganizationToOffense
                    }
                    onRemovePersonFromOffense={
                      relationHandlers.handleRemovePersonFromOffense
                    }
                    onRemoveVehicleFromOffense={
                      relationHandlers.handleRemoveVehicleFromOffense
                    }
                    onRemovePropertyFromOffense={
                      relationHandlers.handleRemovePropertyFromOffense
                    }
                    onRemoveOrganizationFromOffense={
                      relationHandlers.handleRemoveOrganizationFromOffense
                    }
                    onRemovePersonFromReport={
                      entityHandlers.handleRemovePersonFromReport
                    }
                    onRemoveVehicleFromReport={
                      entityHandlers.handleRemoveVehicleFromReport
                    }
                    onRemovePropertyFromReport={
                      entityHandlers.handleRemovePropertyFromReport
                    }
                    onRemoveOrganizationFromReport={
                      entityHandlers.handleRemoveOrganizationFromReport
                    }
                    onSetActiveOffenseRelation={handleSetActiveOffenseRelation}
                    onSetActiveVehicleOffenseContext={
                      handleSetActiveVehicleOffenseContext
                    }
                    onSetActivePropertyOffenseContext={
                      handleSetActivePropertyOffenseContext
                    }
                    onSetActiveOrganizationOffenseContext={
                      handleSetActiveOrganizationOffenseContext
                    }
                    onEntityEdit={entityHandlers.handleEntityEdit}
                    onCreateVictimReportRelation={
                      relationHandlers.handleCreateVictimReportRelation
                    }
                    onUpdateVictimReportRelation={
                      relationHandlers.handleUpdateVictimReportRelation
                    }
                  />
                </Box>

                <Box id="arrests">
                  <ArrestSection
                    reportId={reportId}
                    onSaveStatusChange={handleSaveStatusChange}
                    readOnly={false}
                    people={entitiesToPersonData(people)}
                    relations={reportQuery.data?.relations || []}
                    associatedCases={
                      fullCaseQuery.data ? [fullCaseQuery.data] : []
                    }
                    onOpenSidePanel={(panelType: "PERSON") =>
                      entityHandlers.handleOpenSidePanel(
                        panelType as PanelType,
                        false
                      )
                    }
                    onAddPersonToArrest={
                      relationHandlers.handleAddPersonToArrest
                    }
                    onRemovePersonFromArrest={
                      relationHandlers.handleRemovePersonFromArrest
                    }
                    onRemovePersonFromReport={
                      entityHandlers.handleRemovePersonFromReport
                    }
                    onSetActiveArrestRelation={handleSetActiveArrestRelation}
                    onEntityEdit={entityHandlers.handleEntityEdit}
                  />
                </Box>
              </Stack>
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* Side Panel */}
      <SidePanel
        title={getContextualPanelTitle()}
        isOpen={sidePanelOpen}
        onClose={() =>
          entityHandlers.handleCloseSidePanel(
            false,
            isSaveLoading,
            isUpdateLoading,
            isSaveAndAddAnotherLoading
          )
        }
      >
        {activePanelType && (
          <PanelContent
            key={`panel-${activePanelType}-${editingEntityId || "new"}`}
            ref={stateValues.panelContentRef}
            panelType={activePanelType}
            onSubmit={formHandlers.handleFormSubmit}
            onUpdate={formHandlers.handleFormUpdate}
            onSaveAndAddAnother={formHandlers.handleSaveAndAddAnother}
            onCancel={() =>
              entityHandlers.handleCloseSidePanel(
                false,
                isSaveLoading,
                isUpdateLoading,
                isSaveAndAddAnotherLoading
              )
            }
            onEntitySelect={entityHandlers.handleEntitySelect}
            organizationSchema={
              activePanelType === PanelType.ORGANIZATION
                ? getCurrentSchema()
                : null
            }
            isLoadingOrganizationSchema={
              (activePanelType === PanelType.ORGANIZATION &&
                organizationSchemasQuery.isLoading) ||
              (!!editingEntityId && specificSchemaQuery.isLoading)
            }
            personSchema={
              activePanelType === PanelType.PERSON ? getCurrentSchema() : null
            }
            isLoadingSchema={
              (activePanelType === PanelType.PERSON &&
                personSchemasQuery.isLoading) ||
              (!!editingEntityId && specificSchemaQuery.isLoading)
            }
            propertySchema={
              activePanelType === PanelType.PROPERTY ? getCurrentSchema() : null
            }
            isLoadingPropertySchema={
              (activePanelType === PanelType.PROPERTY &&
                propertySchemasQuery.isLoading) ||
              (!!editingEntityId && specificSchemaQuery.isLoading)
            }
            vehicleSchema={
              activePanelType === PanelType.VEHICLE ? getCurrentSchema() : null
            }
            isLoadingVehicleSchema={
              (activePanelType === PanelType.VEHICLE &&
                vehicleSchemasQuery.isLoading) ||
              (!!editingEntityId && specificSchemaQuery.isLoading)
            }
            initialValues={editingEntityFormData}
            isEditing={!!editingEntityId}
            isSaveAndAddAnotherLoading={isSaveAndAddAnotherLoading}
            isSaveLoading={isSaveLoading}
            isUpdateLoading={isUpdateLoading}
            readOnly={false}
            currentEntities={
              activePanelType === PanelType.PERSON
                ? people
                : activePanelType === PanelType.VEHICLE
                  ? vehicles
                  : activePanelType === PanelType.PROPERTY
                    ? properties
                    : activePanelType === PanelType.ORGANIZATION
                      ? organizations
                      : []
            }
            offenseContext={
              activePanelType === PanelType.PERSON && activeOffenseRelation
                ? {
                  relationType: activeOffenseRelation.relationType,
                  offenseId: activeOffenseRelation.offenseId,
                }
                : activePanelType === PanelType.VEHICLE &&
                  activeVehicleOffenseContext
                  ? { offenseId: activeVehicleOffenseContext.offenseId }
                  : activePanelType === PanelType.PROPERTY &&
                    activePropertyOffenseContext
                    ? { offenseId: activePropertyOffenseContext.offenseId }
                    : activePanelType === PanelType.ORGANIZATION &&
                      activeOrganizationOffenseContext
                      ? { offenseId: activeOrganizationOffenseContext.offenseId }
                      : undefined
            }
          />
        )}
      </SidePanel>

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={3000}
        onClose={formHandlers.handleCloseNotification}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={formHandlers.handleCloseNotification}
          severity={notification.severity}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

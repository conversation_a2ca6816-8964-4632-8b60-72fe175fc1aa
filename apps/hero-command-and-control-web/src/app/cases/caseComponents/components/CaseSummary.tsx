"use client";

import { useBatchGetLatestEntities } from "@/app/apis/services/workflow/entity/hooks";
import { useBatchGetProperties } from "@/app/apis/services/workflow/property/hooks";
import { useListReportSections } from "@/app/apis/services/workflow/reports/v2/hooks";
import { propertyToEntityFormat } from "@/app/reports/ReportComponents/core/utils/propertyUtils";
import {
  entitiesToPersonData,
  entitiesToPropertyData,
  entitiesToVehicleData,
} from "@/app/reports/ReportComponents/core/utils/utils";
import { Label } from "@/design-system/components/Label";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import DirectionsCarFilledIcon from "@mui/icons-material/DirectionsCarFilled";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import FmdGoodIcon from "@mui/icons-material/FmdGood";
import PersonIcon from "@mui/icons-material/Person";
import ShoppingBagIcon from "@mui/icons-material/ShoppingBag";
import { Box, CircularProgress, Collapse, Paper } from "@mui/material";
import { useRouter } from "next/navigation";
import { Case } from "proto/hero/cases/v1/cases_pb";
import { Entity, EntityType } from "proto/hero/entity/v1/entity_pb";
import { EntityListContent, PropertyListContent, ReportSection, SectionType } from "proto/hero/reports/v2/reports_pb";
import { Situation } from "proto/hero/situations/v2/situations_pb";
import React, { useEffect, useState } from "react";

// Extended ReportSection that handles both proto and API response structures
interface ExtendedReportSection extends ReportSection {
  propertyList?: PropertyListContent;
  entityList?: EntityListContent;
}

const TagBadge = ({ type, color }: { type: string; color: string }) => {
  const bgColor =
    color === "teal"
      ? "rgba(0, 201, 167, 0.1)"
      : color === "blue"
        ? "rgba(64, 125, 255, 0.1)"
        : color === "pink"
          ? "rgba(255, 64, 129, 0.1)"
          : "rgba(0, 0, 0, 0.1)";

  const textColor =
    color === "teal"
      ? "rgb(0, 176, 146)"
      : color === "blue"
        ? "rgb(40, 88, 178)"
        : color === "pink"
          ? "rgb(209, 48, 105)"
          : "rgba(0, 0, 0, 0.87)";

  return (
    <Box
      sx={{
        display: "inline-flex",
        borderRadius: "16px",
        backgroundColor: bgColor,
        color: textColor,
        px: 1.5,
        py: 0.25,
        fontSize: "12px",
        fontWeight: 500,
        ml: 1,
      }}
    >
      {type}
    </Box>
  );
};

interface SectionHeaderProps {
  icon: React.ReactNode;
  title: string;
  isExpanded: boolean;
  onToggle: () => void;
  count?: number;
}

const SectionHeader = ({
  icon,
  title,
  isExpanded,
  onToggle,
  count = 0,
}: SectionHeaderProps) => (
  <Box
    sx={{
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      py: 2,
      color: colors.grey[600],
      cursor: "pointer",
    }}
    onClick={onToggle}
  >
    <Box sx={{ display: "flex", alignItems: "center" }}>
      {icon}
      <Box sx={{ ml: 1, display: "flex", alignItems: "center" }}>
        <Typography style="caps3" color={colors.grey[600]}>
          {title}
        </Typography>
        {count > 0 && (
          <Box
            sx={{
              backgroundColor: colors.blue[100],
              color: colors.blue[600],
              borderRadius: "20px",
              width: "20px",
              height: "20px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "10px",
              fontWeight: 500,
              ml: 1.5,
            }}
          >
            {count}
          </Box>
        )}
      </Box>
    </Box>
    {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
  </Box>
);

interface CaseSummaryProps {
  caseData: Case;
  situationData?: Situation;
  isLoadingSituation?: boolean;
  reportRelations?: Array<{
    relationType: string;
    objectA?: { objectType: string; globalId: string };
    objectB?: { objectType: string; globalId: string };
    metadata?: any;
  }>;
  reportId?: string;
}

export default function CaseSummary({
  caseData,
  situationData,
  isLoadingSituation = false,
  reportRelations = [],
  reportId,
}: CaseSummaryProps) {
  const router = useRouter();
  const [locationsExpanded, setLocationsExpanded] = useState(true);
  const [peopleExpanded, setPeopleExpanded] = useState(true);
  const [vehiclesExpanded, setVehiclesExpanded] = useState(true);
  const [propertiesExpanded, setPropertiesExpanded] = useState(true);
  const [organizationsExpanded, setOrganizationsExpanded] = useState(true);

  // States for entities
  const [people, setPeople] = useState<Entity[]>([]);
  const [vehicles, setVehicles] = useState<Entity[]>([]);
  const [properties, setProperties] = useState<Entity[]>([]);
  const [reportPropertyIds, setReportPropertyIds] = useState<string[]>([]);
  const [locations, setLocations] = useState<
    {
      latitude: number;
      longitude: number;
      address: string;
    }[]
  >([]);

  // Extract location information from situation data
  useEffect(() => {
    if (situationData) {
      const newLocations = [];

      if (situationData.address) {
        newLocations.push({
          latitude: situationData.latitude || 0,
          longitude: situationData.longitude || 0,
          address: situationData.address || "",
        });
      }

      setLocations(newLocations);
    } else {
      setLocations([]);
    }
  }, [situationData]);

  // Get entity references from case data
  const entityRefs = caseData?.entityRefs || [];

  // Extract all entity IDs from the case
  const entityIds = entityRefs.map((ref) => ref.id);

  // Fetch entities data
  const { data: entitiesData, isLoading: isLoadingEntities } =
    useBatchGetLatestEntities(entityIds, {
      queryKey: ["entity", "batch", caseData?.id, entityIds],
      enabled: !!caseData?.id && entityIds.length > 0,
      staleTime: 0,
    });

  // If a reportId is provided, fetch sections and pull properties from the primary report, prioritizing SECTION_TYPE_PROPERTY
  const { data: reportSections } = useListReportSections(reportId || "", {
    enabled: !!reportId,
    staleTime: 0,
    refetchOnMount: "always",
  });

  useEffect(() => {
    if (!reportSections?.sections) {
      setReportPropertyIds([]);
      return;
    }
    const propertySection = reportSections.sections.find(
      (s: ReportSection) => s.type === SectionType.PROPERTY
    ) as ExtendedReportSection | undefined;
    // Handle both proto structure and actual API response structure
    const propertyRefs = propertySection?.content?.case === "propertyList"
      ? propertySection.content.value.propertyRefs
      : propertySection?.propertyList?.propertyRefs;
    if (propertyRefs?.length) {
      setReportPropertyIds(propertyRefs.map(r => r.id));
      return;
    }
    const legacyPropertySection = reportSections.sections.find(
      (s: ReportSection) => s.type === SectionType.ENTITY_LIST_PROPERTIES
    ) as ExtendedReportSection | undefined;
    // Handle both proto structure and actual API response structure
    const entityRefs = legacyPropertySection?.content?.case === "entityList"
      ? legacyPropertySection.content.value.entityRefs
      : legacyPropertySection?.entityList?.entityRefs;
    if (entityRefs?.length) {
      setReportPropertyIds(entityRefs.map(r => r.id));
      return;
    }
    setReportPropertyIds([]);
  }, [reportSections]);

  const { data: batchPropertiesData } = useBatchGetProperties(reportPropertyIds);

  useEffect(() => {
    if (Array.isArray(batchPropertiesData) && batchPropertiesData.length > 0) {
      const converted = batchPropertiesData.map(p => propertyToEntityFormat(p));
      setProperties(converted);
    } else if (reportId && reportPropertyIds.length === 0) {
      // If a report is present but no properties are specified, clear properties (fall back handled in entities effect below)
      setProperties([]);
    }
  }, [batchPropertiesData, reportId, reportPropertyIds]);

  // Process entities by type when data is loaded
  useEffect(() => {
    if (entitiesData?.entities) {
      const peopleEntities: Entity[] = [];
      const vehicleEntities: Entity[] = [];
      const propertyEntities: Entity[] = [];

      entitiesData.entities.forEach((entity) => {
        const entityType = entity.entityType;

        if (entityType === EntityType.PERSON) {
          peopleEntities.push(entity);
        } else if (entityType === EntityType.VEHICLE) {
          vehicleEntities.push(entity);
        } else if (entityType === EntityType.PROPERTY) {
          propertyEntities.push(entity);
        }
      });

      setPeople(peopleEntities);
      setVehicles(vehicleEntities);
      setProperties(propertyEntities);
    }
  }, [entitiesData]);

  // Prefer report-driven properties when available; otherwise, use case entityRefs-derived properties
  useEffect(() => {
    if (!reportId || reportPropertyIds.length === 0) {
      if (entitiesData?.entities) {
        const propertyEntities: Entity[] = [];
        entitiesData.entities.forEach((entity) => {
          const entityType = entity.entityType;
          if (entityType === EntityType.PROPERTY) {
            propertyEntities.push(entity);
          }
        });
        setProperties(propertyEntities);
      }
    }
  }, [reportId, reportPropertyIds.length, entitiesData]);

  // Helper function to get entity name based on entity data
  const getPersonName = (entity: Entity): string => {
    try {
      const entityData = entitiesToPersonData([entity])[0];
      return entityData.name || "Unknown Person";
    } catch (e) {
      return "Unknown Person";
    }
  };

  const getVehicleName = (entity: Entity): string => {
    try {
      const entityData = entitiesToVehicleData([entity])[0];
      return entityData.make + " " + entityData.model || "Unknown Vehicle";
    } catch (e) {
      return "Unknown Vehicle";
    }
  };

  const getVehicleVIN = (entity: Entity): string => {
    try {
      const entityData = entitiesToVehicleData([entity])[0];
      return entityData.vIN || "";
    } catch (e) {
      return "";
    }
  };

  const getPropertyName = (entity: Entity): string => {
    try {
      const entityData = entitiesToPropertyData([entity])[0];
      return entityData.category || "Unknown Property";
    } catch (e) {
      return "Unknown Property";
    }
  };

  const getPropertyDescription = (entity: Entity): string => {
    try {
      const entityData = entitiesToPropertyData([entity])[0];
      return entityData.description || "";
    } catch (e) {
      return "";
    }
  };

  // Get entity relationship type
  const getRelationshipType = (entityId: string): string => {
    const ref = entityRefs.find((ref) => ref.id === entityId);
    return ref?.relationType || "";
  };

  // Helper function to get victim/offender numbers from report relations
  const getVictimOffenderNumbers = (entityId: string): string[] => {
    if (!reportRelations || reportRelations.length === 0) return [];

    const personRelations = reportRelations.filter(rel => {
      if (!rel.metadata?.victimOffenderNumber) return false;

      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === entityId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === entityId);

      return (
        isPersonInvolved &&
        (rel.relationType === "RELATION_TYPE_OFFENSE_VICTIM" ||
          rel.relationType === "RELATION_TYPE_OFFENSE_OFFENDER" ||
          rel.relationType === "RELATION_TYPE_OFFENSE_WITNESS" ||
          rel.relationType === "RELATION_TYPE_OFFENSE_SUSPECT" ||
          rel.relationType === "RELATION_TYPE_OFFENSE_INVOLVED_PARTY")
      );
    });

    // Get all person role numbers (V, O, W, S, I) and remove duplicates
    const numbers = personRelations
      .map((rel) => rel.metadata?.victimOffenderNumber)
      .filter(Boolean);

    // Remove duplicates and ensure no multiple tags of same type
    const uniqueNumbers = [];
    const seenTypes = new Set();

    for (const number of numbers) {
      const type = number.charAt(0); // 'V', 'O', 'W', 'S', or 'I'
      if (!seenTypes.has(type)) {
        uniqueNumbers.push(number);
        seenTypes.add(type);
      }
    }

    // Check for reporting party status from victim report relations
    // For case summary, check across all reports in the case (no specific reportId needed)
    const victimReportRelation = reportRelations.find(rel => {
      const isVictimReportRelation =
        rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === entityId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === entityId);
      const hasReportObject =
        rel.objectA?.objectType === "report" ||
        rel.objectB?.objectType === "report";

      return isVictimReportRelation && isPersonInvolved && hasReportObject;
    });

    // Add RP tag if person is reporting party
    if (victimReportRelation?.metadata?.isReportingParty) {
      uniqueNumbers.push("RP");
    }

    return uniqueNumbers;
  };

  // Helper function to get color based on designation prefix
  const getLabelColor = (
    designation: string
  ): "vine" | "rose" | "purple" | "amber" | "grey" | "blue" => {
    if (designation === "RP") {
      return "blue"; // Reporting Party - blue
    }

    const prefix = designation.charAt(0);
    switch (prefix) {
      case "V":
        return "vine"; // Victim - green
      case "O":
        return "rose"; // Offender - red
      case "W":
        return "purple"; // Witness - purple
      case "S":
        return "amber"; // Suspect - amber
      case "I":
        return "grey"; // Involved Party - grey
      default:
        return "grey";
    }
  };

  // Handle entity click to navigate to entity page
  const handleEntityClick = (entityId: string) => {
    router.push(`/entity?entityId=${entityId}`);
  };

  // Handle property click to navigate to property page
  const handlePropertyClick = (propertyId: string) => {
    router.push(`/property?propertyId=${propertyId}`);
  };

  // Open Google Maps with the location coordinates
  const handleLocationClick = (latitude: number, longitude: number) => {
    window.open(
      `https://www.google.com/maps?q=${latitude},${longitude}`,
      "_blank"
    );
  };

  return (
    <Paper
      elevation={3}
      sx={{
        borderRadius: 2,
        bgcolor: "white",
        overflow: "hidden",
        overflowY: "auto",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        boxShadow: "none",
        border: `1px solid ${colors.grey[200]}`,
      }}
    >
      <Box sx={{ px: 2 }}>
        {/* Locations Section */}
        <Box>
          <SectionHeader
            icon={
              <FmdGoodIcon sx={{ color: colors.grey[500], fontSize: 20 }} />
            }
            title="LOCATIONS"
            isExpanded={locationsExpanded}
            onToggle={() => setLocationsExpanded(!locationsExpanded)}
            count={locations.length}
          />
          <Collapse in={locationsExpanded}>
            {isLoadingSituation ? (
              <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : locations.length === 0 ? (
              <Box sx={{ mb: 2 }}>
                <Typography style="body3" color={colors.grey[500]}>
                  No locations
                </Typography>
              </Box>
            ) : (
              locations.map((location, index) => (
                <Box
                  key={index}
                  sx={{
                    cursor: "pointer",
                    "&:hover": {
                      bgcolor: colors.grey[100],
                    },
                    px: 1,
                    py: 1,
                  }}
                  onClick={() =>
                    handleLocationClick(location.latitude, location.longitude)
                  }
                >
                  <Box sx={{ mb: 0.5 }}>
                    <Typography style="body3" color={colors.grey[900]}>
                      {location.address}
                    </Typography>
                  </Box>
                  <Typography style="tag2" color={colors.grey[500]}>
                    Coordinates: {location.latitude}, {location.longitude}
                  </Typography>
                </Box>
              ))
            )}
          </Collapse>
        </Box>

        <Box sx={{ borderTop: 1, borderColor: "divider" }} />

        {/* People Section */}
        <Box>
          <SectionHeader
            icon={<PersonIcon sx={{ color: colors.grey[500], fontSize: 20 }} />}
            title="PEOPLE"
            isExpanded={peopleExpanded}
            onToggle={() => setPeopleExpanded(!peopleExpanded)}
            count={people.length}
          />
          <Collapse in={peopleExpanded}>
            {isLoadingEntities ? (
              <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : people.length === 0 ? (
              <Box sx={{ mb: 2 }}>
                <Typography style="body3" color={colors.grey[500]}>
                  No people
                </Typography>
              </Box>
            ) : (
              people.map((person, index) => (
                <Box
                  key={person.id}
                  sx={{
                    cursor: "pointer",
                    px: 1,
                    py: 1.5,
                    "&:hover": { bgcolor: colors.grey[100] },
                    "&:active": { bgcolor: colors.grey[200] },
                  }}
                  onClick={() => handleEntityClick(person.id)}
                >
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Typography style="body3" color={colors.grey[900]}>
                      {getPersonName(person)}
                    </Typography>
                    {(() => {
                      const numbers = getVictimOffenderNumbers(person.id);
                      if (numbers.length === 0) return null;
                      return numbers.map((number, index) => (
                        <Label
                          key={index}
                          label={number}
                          size="small"
                          color={getLabelColor(number)}
                          prominence={false}
                          pilled
                        />
                      ));
                    })()}
                  </Box>
                  {getRelationshipType(person.id) && (
                    <TagBadge
                      type={getRelationshipType(person.id)}
                      color="blue"
                    />
                  )}
                </Box>
              ))
            )}
          </Collapse>
        </Box>

        <Box sx={{ borderTop: 1, borderColor: "divider" }} />

        {/* Vehicles Section */}
        <Box>
          <SectionHeader
            icon={
              <DirectionsCarFilledIcon
                sx={{ color: colors.grey[500], fontSize: 20 }}
              />
            }
            title="VEHICLES"
            isExpanded={vehiclesExpanded}
            onToggle={() => setVehiclesExpanded(!vehiclesExpanded)}
            count={vehicles.length}
          />
          <Collapse in={vehiclesExpanded}>
            {isLoadingEntities ? (
              <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : vehicles.length === 0 ? (
              <Box sx={{ mb: 2 }}>
                <Typography style="body3" color={colors.grey[500]}>
                  No vehicles
                </Typography>
              </Box>
            ) : (
              vehicles.map((vehicle, index) => (
                <Box
                  key={vehicle.id}
                  sx={{
                    cursor: "pointer",
                    px: 1,
                    py: 1.5,
                    "&:hover": { bgcolor: colors.grey[100] },
                    "&:active": { bgcolor: colors.grey[200] },
                  }}
                  onClick={() => handleEntityClick(vehicle.id)}
                >
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="body3" color={colors.grey[900]}>
                      {getVehicleName(vehicle)}
                    </Typography>
                    {getRelationshipType(vehicle.id) && (
                      <TagBadge
                        type={getRelationshipType(vehicle.id)}
                        color="teal"
                      />
                    )}
                  </Box>
                  {getVehicleVIN(vehicle) && (
                    <Typography style="tag2" color={colors.grey[500]}>
                      {getVehicleVIN(vehicle)}
                    </Typography>
                  )}
                </Box>
              ))
            )}
          </Collapse>
        </Box>

        <Box sx={{ borderTop: 1, borderColor: "divider" }} />

        {/* Properties Section */}
        <Box>
          <SectionHeader
            icon={
              <ShoppingBagIcon sx={{ color: colors.grey[500], fontSize: 20 }} />
            }
            title="PROPERTY"
            isExpanded={propertiesExpanded}
            onToggle={() => setPropertiesExpanded(!propertiesExpanded)}
            count={properties.length}
          />
          <Collapse in={propertiesExpanded}>
            {isLoadingEntities ? (
              <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : properties.length === 0 ? (
              <Box sx={{ mb: 2 }}>
                <Typography style="body3" color={colors.grey[500]}>
                  No properties
                </Typography>
              </Box>
            ) : (
              properties.map((property, index) => (
                <Box
                  key={property.id}
                  sx={{
                    cursor: "pointer",
                    px: 1,
                    py: 1.5,
                    "&:hover": { bgcolor: colors.grey[100] },
                    "&:active": { bgcolor: colors.grey[200] },
                  }}
                  onClick={() => handlePropertyClick(property.id)}
                >
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography style="body3" color={colors.grey[900]}>
                      {getPropertyName(property)}
                    </Typography>
                    {getRelationshipType(property.id) && (
                      <TagBadge
                        type={getRelationshipType(property.id)}
                        color="pink"
                      />
                    )}
                  </Box>
                  {getPropertyDescription(property) && (
                    <Box
                      sx={{
                        display: "-webkit-box",
                        overflow: "hidden",
                        WebkitBoxOrient: "vertical",
                        WebkitLineClamp: 2,
                        mt: 1,
                      }}
                    >
                      <Typography
                        style="tag2"
                        color={colors.grey[500]}
                        lineHeight={"16px"}
                        as="div"
                      >
                        {getPropertyDescription(property)}
                      </Typography>
                    </Box>
                  )}
                </Box>
              ))
            )}
          </Collapse>
        </Box>
      </Box>
    </Paper>
  );
}

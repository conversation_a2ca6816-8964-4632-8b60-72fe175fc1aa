"use client";

import { create } from "@bufbuild/protobuf";
import {
  Alert,
  Box,
  colors,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  Snackbar,
  Stack,
  Typography,
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import { AddEntityRefToCaseRequestSchema } from "proto/hero/cases/v1/cases_pb";
import { Order } from "proto/hero/orders/v2/orders_pb";
import { SectionType, UpdateRelationRequestSchema } from "proto/hero/reports/v2/reports_pb";
import React, { useMemo, useState } from "react";
import { toTitleCase } from "../../../utils/utils";

// Components
import ArrestSection from "../arrests/ArrestSection";
import GlobalCommentsPopup from "../common/GlobalCommentsPopup";
import { ReportHeader } from "../core";
import IncidentDetailsCard from "../details/IncidentDetailsCard";
import { EntityCard } from "../entities";
import MediaSection from "../media/MediaSection";
import NarrativeCard from "../narrative/NarrativeCard";
import OffenseSection from "../offenses/OffenseSection";
import { PanelContent, SidePanel } from "../panels";
import { ReportSummary } from "../summary";
import { PropertySection } from "./PropertySection";
import ReportRejectionCard from "./ReportRejectionCard";

// Hooks
import { useReportingPageAPI } from "./hooks/useReportingPageAPI";
import { useReportingPageEffects } from "./hooks/useReportingPageEffects";
import { useReportingPageState } from "./hooks/useReportingPageState";

// Handlers
import { createCommentHandlers } from "./handlers/commentHandlers";
import { createEntityHandlers } from "./handlers/entityHandlers";
import { createFormHandlers } from "./handlers/formHandlers";
import { createRelationHandlers } from "./handlers/relationHandlers";

// Helpers
import {
  createGoBack,
  createScrollToSection,
  useAvailableNavItems,
  useGetContextualPanelTitle,
  useGetCurrentSchema,
  useIsReviseOrder,
  useReadOnly,
  useRejectedReviewRound,
  useReviewRoundId,
  useShouldExpandComments,
} from "./helpers/reportingPageHelpers";

// Utils
import { propertyToEntityFormat } from "./utils/propertyUtils";
import {
  updateEntityDisplayNameInSection,
  updateEntityListSection,
} from "./utils/sectionUtils";
import {
  entitiesToOrganizationData,
  entitiesToPersonData,
  entitiesToPropertyData,
  entitiesToVehicleData,
} from "./utils/utils";

// Contexts
import { useOrders } from "../../../contexts/OrderContext";
import { useDispatcher } from "../../../contexts/User/DispatcherContext";

// Types
import { PanelType } from "./types";

export default function ReportingPage({
  currentOrder,
}: {
  currentOrder?: Order;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const reportId = searchParams?.get("reportId") || null;
  const { asset: dispatcherAsset } = useDispatcher();
  const { getOrderByReportId } = useOrders();

  // Get order for this report
  const orderForReport = useMemo(() => {
    if (currentOrder) return currentOrder;
    if (reportId) return getOrderByReportId(reportId);
    return undefined;
  }, [currentOrder, reportId, getOrderByReportId]);

  // Use state management hook
  const stateValues = useReportingPageState();

  // Add victim details mode state
  const [victimDetailsMode, setVictimDetailsMode] = useState<{
    personId: string;
    personInfo?: any;
    organizationInfo?: any;
    existingRelation?: any;
    isOrganization?: boolean;
  } | null>(null);

  // Extract commonly used state values
  const {
    people,
    vehicles,
    properties,
    organizations,
    mediaFiles,
    editingEntityId,
    activeSection,
    scrollContainerRef,
    activePanelType,
    sidePanelOpen,
    editingEntityFormData,
    notification,
    saveStatuses,
    incidentComments,
    narrativeComments,
    peopleComments,
    vehicleComments,
    propertyComments,
    organizationComments,
    mediaComments,
    globalComments,
    isGlobalCommentsOpen,
    commentButtonEl,
    sectionIdToType,
    selectedRowId,
    associatedCases,
    activeOffenseRelation,
    activeVehicleOffenseContext,
    activePropertyOffenseContext,
    activeArrestRelation,
    activeOrganizationOffenseContext,
    activeVictimOrganizationOffenseContext,
    incidentDetailsSectionId,
    narrativeSectionId,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    offenseListSectionId,
    arrestListSectionId,
    mediaSectionId,
    isSaveLoading,
    isUpdateLoading,
    isSaveAndAddAnotherLoading,
  } = stateValues;

  // Helper hooks
  const shouldExpandComments = useShouldExpandComments(orderForReport);
  const reviewRoundId = useReviewRoundId(orderForReport);
  const isReviseOrder = useIsReviseOrder(orderForReport);

  // Use API hooks
  const apiValues = useReportingPageAPI({
    reportId,
    editingEntityId,
    editingEntity: editingEntityFormData,
    activePanelType, // Pass the panel type so API can use appropriate query
    entityIdsToFetch: stateValues.entityIdsToFetch,
    propertyIdsToFetch: stateValues.propertyIdsToFetch || [],
    incidentDetailsSectionId,
    narrativeSectionId,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    mediaSectionId,
    associatedCases,
    reviewRoundId,
    isReviseOrder,
  });

  // Extract commonly used API values
  const {
    reportQuery,
    reportSectionsQuery,
    reviewRoundsQuery,
    personSchemasQuery,
    propertySchemasQuery,
    vehicleSchemasQuery,
    organizationSchemasQuery,
    editingEntityQuery,
    editingPropertyQuery,
    specificSchemaQuery,
    batchEntitiesQuery,
    batchPropertiesQuery,
    incidentCommentQuery,
    narrativeCommentQuery,
    peopleCommentQuery,
    vehicleCommentQuery,
    propertyCommentQuery,
    organizationCommentQuery,
    mediaCommentQuery,
    globalCommentQuery,
    casesQuery,
    fullCaseQuery,
    updateReportSectionMutation,
    createReportSectionMutation,
    addCommentMutation,
    resolveCommentMutation,
    createEntityMutation,
    createPropertyMutation,
    updateEntityMutation,
    addEntityRefToCaseMutation,
    createRelationMutation,
    deleteRelationMutation,
    updateRelationMutation,
  } = apiValues;

  // Calculate derived values
  const rejectedReviewRound = useRejectedReviewRound(reviewRoundsQuery.data);
  const readOnly = useReadOnly(orderForReport, reportQuery.data);

  // Check if this is a supplemental report
  const isSupplementalReport =
    String(reportQuery.data?.reportType) ===
    "REPORT_TYPE_INCIDENT_SUPPLEMENTAL";

  const availableNavItems = useAvailableNavItems(
    incidentDetailsSectionId,
    isSupplementalReport ? peopleListSectionId : null,
    isSupplementalReport ? vehicleListSectionId : null,
    isSupplementalReport ? propertyListSectionId : null,
    isSupplementalReport ? organizationListSectionId : null,
    offenseListSectionId,
    arrestListSectionId,
    narrativeSectionId,
    mediaSectionId
  );

  // Navigation helpers
  const scrollToSection = createScrollToSection(scrollContainerRef);
  const goBack = createGoBack(router);

  // Helper function to check if an offense ID is actually an incident
  const isIncident = (offenseId: string): boolean => {
    // Check if this is a local temporary incident ID
    if (offenseId.startsWith("incident_")) {
      return true;
    }

    // Check backend data
    if (!reportSectionsQuery.data?.sections) return false;

    const offenseSection = reportSectionsQuery.data.sections.find(
      (section: any) => section.type === "SECTION_TYPE_OFFENSE"
    );

    // @ts-expect-error TODO: Fix type issue
    if (!offenseSection?.offenseList?.offenses) return false;

    // @ts-expect-error TODO: Fix type issue
    const item = offenseSection.offenseList.offenses.find(
      (offense: any) => offense.id === offenseId
    );

    return item?.data?.isIncident === true;
  };

  // Create combined editing data - use property data for properties, entity data for entities
  const editingEntityData = React.useMemo(() => {
    if (activePanelType === PanelType.PROPERTY && editingPropertyQuery.data) {
      // Convert property data to entity-like format for UI compatibility
      return propertyToEntityFormat(editingPropertyQuery.data);
    }
    return editingEntityQuery.data;
  }, [activePanelType, editingPropertyQuery.data, editingEntityQuery.data]);

  // Get current schema helper
  const getCurrentSchema = useGetCurrentSchema(
    activePanelType,
    editingEntityId,
    editingEntityData,
    specificSchemaQuery.data,
    personSchemasQuery.data,
    propertySchemasQuery.data,
    vehicleSchemasQuery.data,
    organizationSchemasQuery.data
  );

  // Get contextual panel title helper
  const getContextualPanelTitle = useGetContextualPanelTitle(
    activePanelType,
    activeOffenseRelation,
    activeVehicleOffenseContext,
    activePropertyOffenseContext,
    activeOrganizationOffenseContext,
    editingEntityId
  );

  // Handle mutations for addEntityRefToCase
  const handleAddEntityRefToCaseMutate = React.useCallback(
    async (request: any) => {
      // Optimistically update the local associatedCases state
      if (request.entityRef) {
        stateValues.setAssociatedCases((prevCases: any[]) =>
          prevCases.map((caseItem: any) => {
            if (caseItem.id === request.caseId) {
              // Check if entity is already in this case to prevent duplicates
              const isAlreadyInCase = caseItem.entityRefs?.some(
                (ref: any) => ref.id === request.entityRef?.id
              );

              if (!isAlreadyInCase && request.entityRef) {
                return {
                  ...caseItem,
                  entityRefs: [
                    ...(caseItem.entityRefs || []),
                    request.entityRef,
                  ],
                };
              }
            }
            return caseItem;
          })
        );
      }

      addEntityRefToCaseMutation.mutate(request, {
        onSuccess: (data: any) => {
          console.log("Entity reference added to case successfully");
          // Invalidate the cases by report query to refresh associatedCases
          if (reportId) {
            // Invalidate all cases queries related to this report
            queryClient.invalidateQueries({
              queryKey: ["cases", "byReport", reportId],
              exact: false,
            });
            // Also invalidate the specific case query if available
            if (data?.id) {
              queryClient.invalidateQueries({
                queryKey: ["case", data.id],
              });
            }
          }
        },
        onError: (error: any) => {
          // Revert the optimistic update on error
          console.error("Error adding entity to case:", error);
          if (request.entityRef?.id) {
            stateValues.setAssociatedCases((prevCases: any[]) =>
              prevCases.map((caseItem: any) => {
                if (caseItem.id === request.caseId) {
                  return {
                    ...caseItem,
                    entityRefs: (caseItem.entityRefs || []).filter(
                      (ref: any) => ref.id !== request.entityRef?.id
                    ),
                  };
                }
                return caseItem;
              })
            );
          }
        },
      });
    },
    [addEntityRefToCaseMutation, reportId, queryClient, stateValues]
  );

  // Create handlers (these need to be before useEffects)
  const relationHandlers = createRelationHandlers({
    reportId,
    report: reportQuery.data,
    people,
    vehicles,
    properties,
    organizations,
    setPeople: stateValues.setPeople,
    setVehicles: stateValues.setVehicles,
    setProperties: stateValues.setProperties,
    setOrganizations: stateValues.setOrganizations,
    setNotification: stateValues.setNotification,
    associatedCases,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    reportSections: reportSectionsQuery.data,
    createRelationMutation: {
      ...createRelationMutation,
      mutate: (request: any) =>
        createRelationMutation.mutate(request, {
          onError: handleCreateRelationError,
        }),
    },
    deleteRelationMutation: {
      ...deleteRelationMutation,
      mutate: (request: any) =>
        deleteRelationMutation.mutate(request, {
          onError: handleDeleteRelationError,
        }),
    },
    updateRelationMutation: {
      ...updateRelationMutation,
      mutate: (request: any) =>
        updateRelationMutation.mutate(request, {
          onError: handleUpdateRelationError,
        }),
    },
    updateReportSectionMutation: {
      ...updateReportSectionMutation,
      mutate: (request: any) =>
        updateReportSectionMutation.mutate(request, {
          onError: handleUpdateReportSectionError,
        }),
    },
  });

  const entityHandlers = createEntityHandlers({
    people,
    vehicles,
    properties,
    organizations,
    setPeople: stateValues.setPeople,
    setVehicles: stateValues.setVehicles,
    setProperties: stateValues.setProperties,
    setOrganizations: stateValues.setOrganizations,
    setMediaFiles: stateValues.setMediaFiles,
    setSidePanelOpen: stateValues.setSidePanelOpen,
    setActivePanelType: stateValues.setActivePanelType,
    setEditingEntityId: stateValues.setEditingEntityId,
    setEditingEntityFormData: stateValues.setEditingEntityFormData,
    setSelectedRowId: stateValues.setSelectedRowId,
    setActiveOffenseRelation: stateValues.setActiveOffenseRelation,
    setNotification: stateValues.setNotification,
    activeOffenseRelation,
    activeVehicleOffenseContext,
    activePropertyOffenseContext,
    activeOrganizationOffenseContext,
    activeVictimOrganizationOffenseContext,
    setActiveVehicleOffenseContext: stateValues.setActiveVehicleOffenseContext,
    setActivePropertyOffenseContext:
      stateValues.setActivePropertyOffenseContext,
    setActiveOrganizationOffenseContext:
      stateValues.setActiveOrganizationOffenseContext,
    setActiveVictimOrganizationOffenseContext:
      stateValues.setActiveVictimOrganizationOffenseContext,
    reportId,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    reportSections: reportSectionsQuery.data,
    associatedCases,
    updateReportSectionMutation: {
      ...updateReportSectionMutation,
      mutate: (request: any) =>
        updateReportSectionMutation.mutate(request, {
          onError: handleUpdateReportSectionError,
        }),
    },
    addEntityRefToCaseMutation: {
      ...addEntityRefToCaseMutation,
      mutate: handleAddEntityRefToCaseMutate,
    },
    handleAddPersonToOffense: relationHandlers.handleAddPersonToOffense,
    handleAddVehicleToOffense: relationHandlers.handleAddVehicleToOffense,
    handleAddPropertyToOffense: relationHandlers.handleAddPropertyToOffense,
    handleAddOrganizationToOffense:
      relationHandlers.handleAddOrganizationToOffense,
  });

  const commentHandlers = createCommentHandlers({
    reportId,
    dispatcherAsset,
    setNotification: stateValues.setNotification,
    setCommentButtonEl: stateValues.setCommentButtonEl,
    setIsGlobalCommentsOpen: stateValues.setIsGlobalCommentsOpen,
    incidentDetailsSectionId,
    narrativeSectionId,
    peopleListSectionId,
    vehicleListSectionId,
    propertyListSectionId,
    organizationListSectionId,
    mediaSectionId,
    setIncidentComments: stateValues.setIncidentComments,
    setNarrativeComments: stateValues.setNarrativeComments,
    setPeopleComments: stateValues.setPeopleComments,
    setVehicleComments: stateValues.setVehicleComments,
    setPropertyComments: stateValues.setPropertyComments,
    setOrganizationComments: stateValues.setOrganizationComments,
    setMediaComments: stateValues.setMediaComments,
    setGlobalComments: stateValues.setGlobalComments,
    addCommentMutation: {
      ...addCommentMutation,
      mutate: (request: any) =>
        addCommentMutation.mutate(request, {
          onError: handleAddCommentError,
        }),
    },
    resolveCommentMutation: {
      ...resolveCommentMutation,
      mutate: (request: any) =>
        resolveCommentMutation.mutate(request, {
          onError: handleResolveCommentError,
        }),
    },
  });

  const formHandlers = createFormHandlers({
    setIsSaveLoading: stateValues.setIsSaveLoading,
    setIsUpdateLoading: stateValues.setIsUpdateLoading,
    setIsSaveAndAddAnotherLoading: stateValues.setIsSaveAndAddAnotherLoading,
    setMediaFiles: stateValues.setMediaFiles,
    setNotification: stateValues.setNotification,
    setActivePanelType: stateValues.setActivePanelType,
    panelContentRef: stateValues.panelContentRef,
    editingEntity: editingEntityData,
    editingEntityId,
    activePanelType,
    personSchemas: personSchemasQuery.data,
    vehicleSchemas: vehicleSchemasQuery.data,
    propertySchemas: propertySchemasQuery.data,
    organizationSchemas: organizationSchemasQuery.data,
    dispatcherAsset: dispatcherAsset,
    createEntityMutation: {
      ...createEntityMutation,
      mutate: (request: any, options?: any) =>
        createEntityMutation.mutate(request, {
          onSuccess: (response: any) => {
            // Call the main success handler first, passing along any additional data
            handleCreateEntitySuccess(response, options?.additionalVictimData);
            // Then call any additional success handler from options
            if (options?.onSuccess) {
              options.onSuccess(response);
            }
          },
          onError: (error: any) => {
            handleCreateEntityError(error);
            // Call any additional error handler from options
            if (options?.onError) {
              options.onError(error);
            }
          },
        }),
    },
    createPropertyMutation: {
      ...createPropertyMutation,
      mutate: (request: any, options?: any) =>
        createPropertyMutation.mutate(request, {
          onSuccess: (response: any) => {
            // Call the main success handler first, passing along any additional data
            handleCreatePropertySuccess(response, options?.additionalVictimData);
            // Then call any additional success handler from options
            if (options?.onSuccess) {
              options.onSuccess(response);
            }
            // Close the side panel after handling the success
            entityHandlers.handleCloseSidePanel(
              false,
              isSaveLoading,
              isUpdateLoading,
              isSaveAndAddAnotherLoading
            );
          },
          onError: (error: any) => {
            handleCreateEntityError(error); // Reuse entity error handler for now
            // Call any additional error handler from options
            if (options?.onError) {
              options.onError(error);
            }
          },
        }),
    },
    updateEntityMutation: {
      ...updateEntityMutation,
      mutate: (request: any) =>
        updateEntityMutation.mutate(request, {
          onSuccess: handleUpdateEntitySuccess,
          onError: handleUpdateEntityError,
        }),
    },
    updateReportSectionMutation: {
      ...updateReportSectionMutation,
      mutate: (request: any, options?: any) =>
        updateReportSectionMutation.mutate(request, {
          onSuccess: (data: any) => {
            if (options?.onSuccess) {
              options.onSuccess(data);
            }
          },
          onError: (error: any) => {
            handleUpdateReportSectionError(error);
            if (options?.onError) {
              options.onError(error);
            }
          },
        }),
    },
    reportId,
    mediaSectionId,
    queryClient,
    handleCloseSidePanel: () =>
      entityHandlers.handleCloseSidePanel(
        false,
        isSaveLoading,
        isUpdateLoading,
        isSaveAndAddAnotherLoading
      ),
  });

  // Custom property update handler for updating local properties state
  const handlePropertyUpdate = React.useCallback((updatedProperty: any) => {
    console.log('ReportingPage - Property update received:', updatedProperty);

    // Update the local properties state with the updated property
    stateValues.setProperties((prev: any[]) =>
      prev.map((property: any) =>
        property.id === updatedProperty.id ? updatedProperty : property
      )
    );

    console.log('ReportingPage - Properties state updated successfully');

    // Show success notification
    stateValues.setNotification({
      open: true,
      message: 'Property updated successfully',
      severity: 'success',
    });

    // Close the side panel after successful update
    entityHandlers.handleCloseSidePanel(
      false,
      isSaveLoading,
      isUpdateLoading,
      isSaveAndAddAnotherLoading
    );
  }, [stateValues, entityHandlers, isSaveLoading, isUpdateLoading, isSaveAndAddAnotherLoading]);

  // Use effects hook
  const { handleSaveStatusChange } = useReportingPageEffects({
    availableNavItems,
    activeSection,
    setActiveSection: stateValues.setActiveSection,
    scrollContainerRef,
    setEditingEntityFormData: stateValues.setEditingEntityFormData,
    // Draft persistence
    reportId,
    activePanelType,
    editingEntityId,
    loadDraftFromStorage: stateValues.loadDraftFromStorage,
    clearDraftFromStorage: stateValues.clearDraftFromStorage,
    setEntityIdsToFetch: stateValues.setEntityIdsToFetch,
    setPropertyIdsToFetch: stateValues.setPropertyIdsToFetch,
    setPeople: stateValues.setPeople,
    setVehicles: stateValues.setVehicles,
    setProperties: stateValues.setProperties,
    setOrganizations: stateValues.setOrganizations,
    setMediaFiles: stateValues.setMediaFiles,
    setSaveStatuses: stateValues.setSaveStatuses,
    setAssociatedCases: stateValues.setAssociatedCases,
    setIncidentComments: stateValues.setIncidentComments,
    setNarrativeComments: stateValues.setNarrativeComments,
    setPeopleComments: stateValues.setPeopleComments,
    setVehicleComments: stateValues.setVehicleComments,
    setPropertyComments: stateValues.setPropertyComments,
    setOrganizationComments: stateValues.setOrganizationComments,
    setMediaComments: stateValues.setMediaComments,
    setGlobalComments: stateValues.setGlobalComments,
    setPeopleListSectionId: stateValues.setPeopleListSectionId,
    setVehicleListSectionId: stateValues.setVehicleListSectionId,
    setPropertyListSectionId: stateValues.setPropertyListSectionId,
    setOrganizationListSectionId: stateValues.setOrganizationListSectionId,
    setOffenseListSectionId: stateValues.setOffenseListSectionId,
    setArrestListSectionId: stateValues.setArrestListSectionId,
    setNarrativeSectionId: stateValues.setNarrativeSectionId,
    setIncidentDetailsSectionId: stateValues.setIncidentDetailsSectionId,
    setMediaSectionId: stateValues.setMediaSectionId,
    setSectionIdToType: stateValues.setSectionIdToType,
    editingEntity: editingEntityData,
    reportSections: reportSectionsQuery.data,
    batchEntitiesData: batchEntitiesQuery.data,
    batchPropertiesData: batchPropertiesQuery.data,
    incidentCommentData: incidentCommentQuery.data,
    narrativeCommentData: narrativeCommentQuery.data,
    peopleCommentData: peopleCommentQuery.data,
    vehicleCommentData: vehicleCommentQuery.data,
    propertyCommentData: propertyCommentQuery.data,
    organizationCommentData: organizationCommentQuery.data,
    mediaCommentData: mediaCommentQuery.data,
    globalCommentData: globalCommentQuery.data,
    casesData: casesQuery.data,
    mediaSectionId,
    isReviseOrder,
    handleSaveStatusChange: (status: any) => {
      stateValues.setSaveStatuses((prev: any[]) => {
        const existingIndex = prev.findIndex((s) => s.source === status.source);
        if (existingIndex >= 0) {
          const newStatuses = [...prev];
          newStatuses[existingIndex] = status;
          return newStatuses;
        } else {
          return [...prev, status];
        }
      });
    },
  });

  // Setup mutations with handlers
  const handleUpdateReportSectionError = React.useCallback(
    (error: any) => {
      console.error("Error updating report section:", error);
      stateValues.setNotification({
        open: true,
        message: `Error updating section: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  const handleAddCommentError = React.useCallback(
    (error: any) => {
      console.error("Error adding comment:", error);
      stateValues.setNotification({
        open: true,
        message: `Error adding comment: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  const handleResolveCommentError = React.useCallback(
    (error: any) => {
      console.error("Error resolving comment:", error);
      stateValues.setNotification({
        open: true,
        message: `Error resolving comment: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  const handleCreateEntitySuccess = React.useCallback(
    (response: any, additionalVictimData: any | undefined) => {
      if (response.entity) {
        const entity = response.entity;
        let updatedEntities = [];

        // Add entity to appropriate list and get the updated list
        switch (entity.entityType) {
          case "ENTITY_TYPE_PERSON":
            updatedEntities = [...people, entity];
            stateValues.setPeople(updatedEntities);
            updateEntityListSection(
              "People",
              entity,
              peopleListSectionId,
              reportId || "",
              reportSectionsQuery.data,
              {
                ...updateReportSectionMutation,
                mutate: (request: any) =>
                  updateReportSectionMutation.mutate(request, {
                    onError: handleUpdateReportSectionError,
                  }),
              },
              { people, vehicles, properties, organizations },
              updatedEntities
            );

            // Add entity reference to all associated cases
            if (associatedCases?.length > 0) {
              associatedCases.forEach((caseItem: any) => {
                const entityData = entitiesToPersonData([entity])[0];

                // Check if entity is already in this case to prevent duplicates
                const isAlreadyInCase = caseItem.entityRefs?.some(
                  (ref: any) => ref.id === entity.id
                );

                if (!isAlreadyInCase) {
                  const request = create(AddEntityRefToCaseRequestSchema, {
                    caseId: caseItem.id,
                    entityRef: {
                      id: entity.id,
                      type: "ENTITY_TYPE_PERSON",
                      displayName: entityData.name,
                    },
                  });
                  handleAddEntityRefToCaseMutate(request);
                }
              });
            }

            // If this person was added from an offense context, create the relation
            if (activeOffenseRelation) {
              // Always create the person-to-offense relation first
              relationHandlers.handleAddPersonToOffense(
                entity.id,
                activeOffenseRelation.offenseId,
                activeOffenseRelation.relationType,
                entity
              );

              // Clear the active offense relation
              stateValues.setActiveOffenseRelation(null);
            }

            // If this person was added from an arrest context, create the relation
            if (activeArrestRelation) {
              // Create the person-to-arrest relation
              relationHandlers.handleAddPersonToArrest(
                entity.id,
                activeArrestRelation.arrestId,
                entity
              );

              // Clear the active arrest relation
              stateValues.setActiveArrestRelation(null);
            }

            // Clear draft data for person form since submission was successful
            stateValues.clearDraftFromStorage("PERSON", reportId);

            break;
          case "ENTITY_TYPE_VEHICLE":
            updatedEntities = [...vehicles, entity];
            stateValues.setVehicles(updatedEntities);
            updateEntityListSection(
              "Vehicles",
              entity,
              vehicleListSectionId,
              reportId || "",
              reportSectionsQuery.data,
              {
                ...updateReportSectionMutation,
                mutate: (request: any) =>
                  updateReportSectionMutation.mutate(request, {
                    onError: handleUpdateReportSectionError,
                  }),
              },
              { people, vehicles, properties, organizations },
              updatedEntities
            );

            // Add entity reference to all associated cases
            if (associatedCases?.length > 0) {
              associatedCases.forEach((caseItem: any) => {
                const entityData = entitiesToVehicleData([entity])[0];

                // Check if entity is already in this case to prevent duplicates
                const isAlreadyInCase = caseItem.entityRefs?.some(
                  (ref: any) => ref.id === entity.id
                );

                if (!isAlreadyInCase) {
                  const request = create(AddEntityRefToCaseRequestSchema, {
                    caseId: caseItem.id,
                    entityRef: {
                      id: entity.id,
                      type: "ENTITY_TYPE_VEHICLE",
                      displayName: entityData.make + " " + entityData.model,
                    },
                  });
                  handleAddEntityRefToCaseMutate(request);
                }
              });
            }

            // If this vehicle was added from an offense context, create the relation
            if (activeVehicleOffenseContext) {
              relationHandlers.handleAddVehicleToOffense(
                entity.id,
                activeVehicleOffenseContext.offenseId,
                entity
              );
              // Clear the active vehicle offense context
              stateValues.setActiveVehicleOffenseContext(null);
            }

            // Clear draft data for vehicle form since submission was successful
            stateValues.clearDraftFromStorage("VEHICLE", reportId);

            break;
          case "ENTITY_TYPE_PROPERTY": {
            updatedEntities = [...properties, entity];
            stateValues.setProperties(updatedEntities);

            // Check if this is a SECTION_TYPE_PROPERTY section (new property service)
            // or SECTION_TYPE_ENTITY_LIST_PROPERTIES section (old entity list)
            const section = reportSectionsQuery.data?.sections?.find(
              (s: any) => s.id === propertyListSectionId
            );
            const sectionType = section?.type;

            if (sectionType === SectionType.ENTITY_LIST_PROPERTIES) {
              // For old entity list sections, update the report section
              updateEntityListSection(
                "Properties",
                entity,
                propertyListSectionId,
                reportId || "",
                reportSectionsQuery.data,
                {
                  ...updateReportSectionMutation,
                  mutate: (request: any) =>
                    updateReportSectionMutation.mutate(request, {
                      onError: handleUpdateReportSectionError,
                    }),
                },
                { people, vehicles, properties, organizations },
                updatedEntities
              );
            } else if (sectionType === SectionType.PROPERTY) {
              // For new property service sections, update the property list section
              updatePropertyListSection(
                "Properties",
                entity,
                propertyListSectionId || "",
                reportId || "",
                reportSectionsQuery.data,
                {
                  ...updateReportSectionMutation,
                  mutate: (request: any) =>
                    updateReportSectionMutation.mutate(request, {
                      onError: handleUpdateReportSectionError,
                    }),
                },
                { people, vehicles, properties, organizations },
                updatedEntities
              );
            }

            // Add entity reference to all associated cases
            if (associatedCases?.length > 0) {
              associatedCases.forEach((caseItem: any) => {
                const entityData = entitiesToPropertyData([entity])[0];

                // Check if entity is already in this case to prevent duplicates
                const isAlreadyInCase = caseItem.entityRefs?.some(
                  (ref: any) => ref.id === entity.id
                );

                if (!isAlreadyInCase) {
                  const request = create(AddEntityRefToCaseRequestSchema, {
                    caseId: caseItem.id,
                    entityRef: {
                      id: entity.id,
                      type: "ENTITY_TYPE_PROPERTY",
                      displayName: entityData.category,
                    },
                  });
                  handleAddEntityRefToCaseMutate(request);
                }
              });
            }

            // If this property was added from an offense context, create the relation
            if (activePropertyOffenseContext) {
              relationHandlers.handleAddPropertyToOffense(
                entity.id,
                activePropertyOffenseContext.offenseId,
                entity
              );
              // Clear the active property offense context
              stateValues.setActivePropertyOffenseContext(null);
            }
            break;
          }

          case "ENTITY_TYPE_ORGANIZATION":
            updatedEntities = [...organizations, entity];
            stateValues.setOrganizations(updatedEntities);
            updateEntityListSection(
              "Organizations",
              entity,
              organizationListSectionId,
              reportId || "",
              reportSectionsQuery.data,
              {
                ...updateReportSectionMutation,
                mutate: (request: any) =>
                  updateReportSectionMutation.mutate(request, {
                    onError: handleUpdateReportSectionError,
                  }),
              },
              { people, vehicles, properties, organizations },
              updatedEntities
            );

            // Add entity reference to all associated cases
            if (associatedCases?.length > 0) {
              associatedCases.forEach((caseItem: any) => {
                const entityData = entitiesToOrganizationData([entity])[0];

                // Check if entity is already in this case to prevent duplicates
                const isAlreadyInCase = caseItem.entityRefs?.some(
                  (ref: any) => ref.id === entity.id
                );

                if (!isAlreadyInCase) {
                  const request = create(AddEntityRefToCaseRequestSchema, {
                    caseId: caseItem.id,
                    entityRef: {
                      id: entity.id,
                      type: "ENTITY_TYPE_ORGANIZATION",
                      displayName: entityData.name,
                    },
                  });
                  handleAddEntityRefToCaseMutate(request);
                }
              });
            }

            // If this organization was added from an offense context, create the relation
            if (activeVictimOrganizationOffenseContext) {
              relationHandlers.handleAddOrganizationToOffense(
                entity.id,
                activeVictimOrganizationOffenseContext.offenseId,
                "victim",
                entity
              );
              // Clear the active victim organization offense context
              stateValues.setActiveVictimOrganizationOffenseContext(null);
            } else if (activeOrganizationOffenseContext) {
              relationHandlers.handleAddOrganizationToOffense(
                entity.id,
                activeOrganizationOffenseContext.offenseId,
                "general",
                entity
              );
              // Clear the active organization offense context
              stateValues.setActiveOrganizationOffenseContext(null);
            }
            break;
        }
      }
      stateValues.setIsSaveLoading(false);
      stateValues.setIsSaveAndAddAnotherLoading(false);
    },
    [
      activeOffenseRelation,
      activeVehicleOffenseContext,
      activePropertyOffenseContext,
      activeArrestRelation,
      activeOrganizationOffenseContext,
      activeVictimOrganizationOffenseContext,
      people,
      vehicles,
      properties,
      organizations,
      stateValues,
      peopleListSectionId,
      vehicleListSectionId,
      propertyListSectionId,
      organizationListSectionId,
      reportId,
      reportSectionsQuery.data,
      updateReportSectionMutation,
      associatedCases,
      relationHandlers,
      handleAddEntityRefToCaseMutate,
      handleUpdateReportSectionError,
    ]
  );

  const handleCreateEntityError = React.useCallback(
    (error: any) => {
      stateValues.setNotification({
        open: true,
        message: `Error creating entity: ${error.message}`,
        severity: "error",
      });

      // Reset loading states
      stateValues.setIsSaveLoading(false);
      stateValues.setIsSaveAndAddAnotherLoading(false);
    },
    [stateValues]
  );

  // Helper function to update property list sections
  const updatePropertyListSection = (
    title: string,
    entity: any,
    sectionId: string,
    reportId: string,
    reportSections: any,
    updateReportSectionMutation: any,
    allEntities: any,
    updatedProperties: any[]
  ) => {
    if (!sectionId || !reportId) {
      return;
    }

    const section = reportSections?.sections?.find(
      (s: any) => s.id === sectionId
    );

    if (!section) {
      return;
    }

    // Convert entity to property reference format
    const propertyData = entitiesToPropertyData([entity])[0];
    const propertyRef = {
      id: entity.id,
      displayName: propertyData.category,
      relationType: "involved",
      version: entity.version || 1,
    };

    // Get existing property refs or initialize empty array
    const existingPropertyRefs = section.propertyList?.propertyRefs || [];

    // Check if property already exists in the section
    const existingIndex = existingPropertyRefs.findIndex(
      (ref: any) => ref.id === entity.id
    );

    let updatedPropertyRefs;
    if (existingIndex >= 0) {
      // Update existing property ref
      updatedPropertyRefs = [...existingPropertyRefs];
      updatedPropertyRefs[existingIndex] = propertyRef;
    } else {
      // Add new property ref
      updatedPropertyRefs = [...existingPropertyRefs, propertyRef];
    }

    // Create the updated section
    const updatedSection = {
      ...section,
      propertyList: {
        id: section.propertyList?.id || "",
        title: title,
        propertyRefs: updatedPropertyRefs,
      },
    };

    // Update the report section

    updateReportSectionMutation.mutate({
      reportId,
      section: updatedSection,
    });
  };

  // Add new handler for property creation success
  const handleCreatePropertySuccess = React.useCallback(
    (response: any, additionalVictimData: any | undefined) => {
      console.log("=== PROPERTY CREATION SUCCESS HANDLER ===");
      console.log("Response:", response);
      console.log("propertyListSectionId:", propertyListSectionId);
      console.log("reportId:", reportId);
      console.log("reportSectionsQuery.data:", reportSectionsQuery.data);

      // The response IS the property, not wrapped in a property field
      const property = response;
      console.log("Response has property, proceeding...");

      // Convert property to entity format for UI compatibility
      const entity = propertyToEntityFormat(property);
      console.log("Converted entity:", entity);
      const updatedEntities = [...properties, entity];

      stateValues.setProperties(updatedEntities);
      console.log("Updated properties state");

      // Check if this is a SECTION_TYPE_PROPERTY section (new property service)
      // or SECTION_TYPE_ENTITY_LIST_PROPERTIES section (old entity list)
      console.log("All sections:", reportSectionsQuery.data?.sections);
      console.log("Looking for section with ID:", propertyListSectionId);
      const section = reportSectionsQuery.data?.sections?.find(
        (s: any) => s.id === propertyListSectionId
      );
      const sectionType = section?.type;

      console.log("Found section:", section);
      console.log("Section type:", sectionType);

      if (sectionType === SectionType.ENTITY_LIST_PROPERTIES) {
        console.log("Updating old entity list section");
        // For old entity list sections, update the report section
        updateEntityListSection(
          "Properties",
          entity,
          propertyListSectionId,
          reportId || "",
          reportSectionsQuery.data,
          {
            ...updateReportSectionMutation,
            mutate: (request: any) =>
              updateReportSectionMutation.mutate(request, {
                onError: handleUpdateReportSectionError,
              }),
          },
          { people, vehicles, properties, organizations },
          updatedEntities
        );
      } else if (sectionType === SectionType.PROPERTY) {
        console.log("Updating new property service section");
        // For new property service sections, update the property list section
        updatePropertyListSection(
          "Properties",
          entity,
          propertyListSectionId || "",
          reportId || "",
          reportSectionsQuery.data,
          {
            ...updateReportSectionMutation,
            mutate: (request: any) =>
              updateReportSectionMutation.mutate(request, {
                onError: handleUpdateReportSectionError,
              }),
          },
          { people, vehicles, properties, organizations },
          updatedEntities
        );
      } else {
        console.log("No SECTION_TYPE_PROPERTY section found, creating new one");
        // Create a new SECTION_TYPE_PROPERTY section if it doesn't exist
        createReportSectionMutation.mutate({
          reportId: reportId || "",
          section: {
            // @ts-expect-error TODO: Fix type issue
            type: "SECTION_TYPE_PROPERTY",
            propertyList: {
              title: "Properties",
              propertyRefs: [{
                id: entity.id,
                displayName: entity.propertySchema?.category || "Property",
              }],
            },
          },
        }, {
          onSuccess: (newSection) => {
            console.log("Created new property section:", newSection);
            // Update the propertyListSectionId to the newly created section
            stateValues.setPropertyListSectionId(newSection.id);
            // Invalidate queries to refresh the UI
            queryClient.invalidateQueries({ queryKey: ['reportSections', reportId] });
          },
          onError: (error) => {
            console.error("Error creating property section:", error);

            // Log detailed error information for debugging and support
            console.error("Partial failure scenario detected:", {
              propertyId: entity.id,
              propertyDisplayName: entity.propertySchema?.category || "Property",
              error: error,
              timestamp: new Date().toISOString(),
              context: "Property was successfully created but section creation failed"
            });

            // Store error information in session storage for potential retry or support
            try {
              const errorInfo = {
                type: "PARTIAL_PROPERTY_CREATION_FAILURE",
                propertyId: entity.id,
                error: error.message || "Unknown error",
                timestamp: new Date().toISOString()
              };
              sessionStorage.setItem(`property_section_error_${entity.id}`, JSON.stringify(errorInfo));
            } catch (storageError) {
              console.warn("Could not store error information:", storageError);
            }

            // Provide clear user notification explaining the partial success
            stateValues.setNotification({
              open: true,
              message: `Property "${entity.propertySchema?.category || "Property"}" was created successfully, but could not be added to the report section. Please contact support or try refreshing the page.`,
              severity: "error", // Changed back to "error" since "warning" is not a valid severity
            });

            // Invalidate property queries to ensure the created property appears in lists
            queryClient.invalidateQueries({ queryKey: ['properties'] });
            queryClient.invalidateQueries({ queryKey: ['batchProperties'] });

            // Attempt to refresh report sections to recover from partial failure
            queryClient.invalidateQueries({ queryKey: ['reportSections', reportId] });
          },
        });
      }

      // Add entity reference to all associated cases
      // NOTE: Skip adding property service properties to cases since they are not entities
      // Properties created via the property service cannot be added to cases using entity references
      if (associatedCases?.length > 0 && sectionType === SectionType.ENTITY_LIST_PROPERTIES) {
        // Only add to cases if this was created via the old entity service (entity list section)
        // Properties created via the new property service should not be added to cases as entities
        associatedCases.forEach((caseItem: any) => {
          const entityData = entitiesToPropertyData([entity])[0];

          // Check if entity is already in this case to prevent duplicates
          const isAlreadyInCase = caseItem.entityRefs?.some(
            (ref: any) => ref.id === entity.id
          );

          if (!isAlreadyInCase) {
            const request = create(AddEntityRefToCaseRequestSchema, {
              caseId: caseItem.id,
              entityRef: {
                id: entity.id,
                type: "ENTITY_TYPE_PROPERTY",
                displayName: entityData.category,
              },
            });
            handleAddEntityRefToCaseMutate(request);
          }
        });
      } else if (associatedCases?.length > 0) {
        console.log("Skipping case association for property service properties - properties are not entities");
      }

      // If this property was added from an offense context, create the relation
      if (activePropertyOffenseContext) {
        relationHandlers.handleAddPropertyToOffense(
          entity.id,
          activePropertyOffenseContext.offenseId,
          entity
        );
        // Clear the active property offense context
        stateValues.setActivePropertyOffenseContext(null);
      }

      // Clear draft data for property form since submission was successful
      stateValues.clearDraftFromStorage("PROPERTY", reportId);
    },
    [
      activePropertyOffenseContext,
      people,
      vehicles,
      properties,
      organizations,
      stateValues,
      propertyListSectionId,
      reportId,
      reportSectionsQuery.data,
      updateReportSectionMutation,
      createReportSectionMutation,
      associatedCases,
      relationHandlers,
      handleAddEntityRefToCaseMutate,
      handleUpdateReportSectionError,
      queryClient,
    ]
  );

  const handleUpdateEntitySuccess = React.useCallback(
    (updatedEntity: any) => {
      // Update the corresponding entity list
      if (updatedEntity) {
        let sectionId: string | null = null;

        switch (updatedEntity.entityType) {
          case "ENTITY_TYPE_PERSON":
            stateValues.setPeople((prev: any[]) =>
              prev.map((entity: any) =>
                entity.id === updatedEntity.id ? updatedEntity : entity
              )
            );
            sectionId = peopleListSectionId;
            break;
          case "ENTITY_TYPE_VEHICLE":
            stateValues.setVehicles((prev: any[]) =>
              prev.map((entity: any) =>
                entity.id === updatedEntity.id ? updatedEntity : entity
              )
            );
            sectionId = vehicleListSectionId;
            break;
          case "ENTITY_TYPE_PROPERTY":
            stateValues.setProperties((prev: any[]) =>
              prev.map((entity: any) =>
                entity.id === updatedEntity.id ? updatedEntity : entity
              )
            );
            sectionId = propertyListSectionId;
            break;

          case "ENTITY_TYPE_ORGANIZATION":
            stateValues.setOrganizations((prev: any[]) =>
              prev.map((entity: any) =>
                entity.id === updatedEntity.id ? updatedEntity : entity
              )
            );
            sectionId = organizationListSectionId;
            break;
        }

        // If the section exists, update it to ensure the displayName is current
        if (sectionId && reportId) {
          updateEntityDisplayNameInSection(
            updatedEntity,
            sectionId,
            reportId,
            reportSectionsQuery.data,
            updateReportSectionMutation
          );
        }
      }

      // Reset update loading state and force close panel on success
      stateValues.setIsUpdateLoading(false);
      entityHandlers.handleCloseSidePanel(true, false, false, false);
    },
    [
      stateValues,
      peopleListSectionId,
      vehicleListSectionId,
      propertyListSectionId,
      organizationListSectionId,
      reportId,
      reportSectionsQuery.data,
      updateReportSectionMutation,
      entityHandlers,
    ]
  );

  const handleUpdateEntityError = React.useCallback(
    (error: any) => {
      console.error("Error updating entity:", error);
      stateValues.setNotification({
        open: true,
        message: `Error updating entity: ${error.message}`,
        severity: "error",
      });

      // Reset loading state but don't close panel
      stateValues.setIsUpdateLoading(false);
    },
    [stateValues]
  );

  const handleCreateRelationError = React.useCallback(
    (error: any) => {
      console.error("Error creating relation:", error);
      stateValues.setNotification({
        open: true,
        message: `Error adding person to offense: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  const handleDeleteRelationError = React.useCallback(
    (error: any) => {
      console.error("Error deleting relation:", error);
      stateValues.setNotification({
        open: true,
        message: `Error removing person from offense: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  const handleUpdateRelationError = React.useCallback(
    (error: any) => {
      console.error("Error updating relation:", error);
      stateValues.setNotification({
        open: true,
        message: `Error updating relation: ${error.message}`,
        severity: "error",
      });
    },
    [stateValues]
  );

  // Handle setting active offense relation with proper interface
  const handleSetActiveOffenseRelation = (
    context: {
      offenseId: string;
      relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party";
    } | null
  ) => {
    stateValues.setActiveOffenseRelation(context);
  };

  // Handle setting active vehicle offense context
  const handleSetActiveVehicleOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    stateValues.setActiveVehicleOffenseContext(context);
  };

  // Handle setting active property offense context
  const handleSetActivePropertyOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    stateValues.setActivePropertyOffenseContext(context);
  };

  // Handle setting active arrest relation
  const handleSetActiveArrestRelation = (
    context: { arrestId: string } | null
  ) => {
    stateValues.setActiveArrestRelation(context);
  };

  // Handle arrest-person relationships
  const handleAddPersonToArrest = (personId: string, arrestId: string) => {
    if (relationHandlers.handleAddPersonToArrest) {
      const entity = people.find((p) => p.id === personId);
      relationHandlers.handleAddPersonToArrest(personId, arrestId, entity);
    }
  };

  const handleRemovePersonFromArrest = (personId: string, arrestId: string) => {
    if (relationHandlers.handleRemovePersonFromArrest) {
      relationHandlers.handleRemovePersonFromArrest(personId, arrestId);
    }
  };

  // Handle setting active organization offense context
  const handleSetActiveOrganizationOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    stateValues.setActiveOrganizationOffenseContext(context);
  };

  // Handle setting active victim organization offense context
  const handleSetActiveVictimOrganizationOffenseContext = (
    context: { offenseId: string } | null
  ) => {
    stateValues.setActiveVictimOrganizationOffenseContext(context);
  };

  // Handle victim details functionality
  const handleAddVictimDetails = (personId: string) => {
    // Find the person entity to get person info
    const personEntity = people.find((p) => p.id === personId);

    // Check if there's an existing victim report relation
    const existingRelation = reportQuery.data?.relations?.find((rel: any) => {
      const isVictimReportRelation =
        rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
      const isPersonInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === personId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === personId);
      const isReportInvolved =
        (rel.objectA?.objectType === "report" &&
          rel.objectA?.reportScopedId === reportId) ||
        (rel.objectB?.objectType === "report" &&
          rel.objectB?.reportScopedId === reportId);

      return isVictimReportRelation && isPersonInvolved && isReportInvolved;
    });

    // Set victim details mode
    setVictimDetailsMode({
      personId: personId,
      existingRelation: existingRelation,
      personInfo: personEntity
        ? {
          firstName:
            personEntity.data?.classificationSection?.firstName ||
            personEntity.firstName ||
            personEntity.name?.split(" ")[0] ||
            "",
          middleName:
            personEntity.data?.classificationSection?.middleName ||
            personEntity.middleName ||
            personEntity.name?.split(" ").slice(1, -1).join(" ") ||
            "",
          lastName:
            personEntity.data?.classificationSection?.lastName ||
            personEntity.lastName ||
            personEntity.name?.split(" ").slice(-1)[0] ||
            "",
          dateOfBirth:
            personEntity.data?.classificationSection?.dateOfBirth ||
            personEntity.dateOfBirth ||
            "",
          sex:
            personEntity.data?.classificationSection?.sex ||
            personEntity.sex ||
            "",
          race:
            personEntity.data?.classificationSection?.race ||
            personEntity.race ||
            "",
          ssn:
            personEntity.data?.identifiersSection?.sSN ||
            personEntity.ssn ||
            "",
        }
        : undefined,
    });

    // Open side panel without setting panel type (victim questions mode will be detected)
    stateValues.setSidePanelOpen(true);
  };

  // Handle organization victim details functionality
  const handleAddVictimDetailsOrganization = (organizationId: string) => {
    // Find the organization entity to get organization info
    const organizationEntity = organizations.find(
      (o) => o.id === organizationId
    );

    // Check if there's an existing victim report relation
    const existingRelation = reportQuery.data?.relations?.find((rel: any) => {
      const isVictimReportRelation =
        rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
      const isOrganizationInvolved =
        (rel.objectA?.objectType === "entity" &&
          rel.objectA?.globalId === organizationId) ||
        (rel.objectB?.objectType === "entity" &&
          rel.objectB?.globalId === organizationId);
      const isReportInvolved =
        (rel.objectA?.objectType === "report" &&
          rel.objectA?.reportScopedId === reportId) ||
        (rel.objectB?.objectType === "report" &&
          rel.objectB?.reportScopedId === reportId);

      return (
        isVictimReportRelation && isOrganizationInvolved && isReportInvolved
      );
    });

    // Set victim details mode for organization
    setVictimDetailsMode({
      personId: organizationId, // Reuse personId field for organization ID
      existingRelation: existingRelation,
      organizationInfo: organizationEntity
        ? {
          name: organizationEntity.name || "",
          type: organizationEntity.type || "",
          streetAddress: organizationEntity.streetAddress || "",
          state: organizationEntity.state || "",
          zip: organizationEntity.zIP || "",
        }
        : undefined,
      isOrganization: true, // Flag to indicate this is an organization
    });

    // Open side panel without setting panel type (victim questions mode will be detected)
    stateValues.setSidePanelOpen(true);
  };

  const handleCreateVictimReportRelation = (
    personId: string,
    reportId: string,
    entity?: any,
    victimData?: any
  ) => {
    // Call the relation handler
    relationHandlers.handleCreateVictimReportRelation(
      personId,
      reportId,
      entity,
      victimData
    );

    // Close victim details mode and side panel
    setVictimDetailsMode(null);
    stateValues.setSidePanelOpen(false);
  };

  const handleUpdateVictimReportRelation = (
    relationId: string,
    victimData: any
  ) => {
    // Handle update directly using the mutation
    if (!reportId || !reportQuery.data?.relations) return;

    // Find the existing relation
    const existingRelation = reportQuery.data.relations.find(
      (rel: any) => rel.id === relationId
    );
    if (!existingRelation) {
      console.error("Victim report relation not found for update");
      return;
    }

    // Create updated relation with new metadata
    const updatedRelation = {
      ...existingRelation,
      metadata: victimData,
    };

    const updateRelationRequest = create(UpdateRelationRequestSchema, {
      relation: updatedRelation,
    });

    updateRelationMutation.mutate(updateRelationRequest);

    // Close victim details mode and side panel
    setVictimDetailsMode(null);
    stateValues.setSidePanelOpen(false);
  };

  // Custom close handler that also clears victim details mode
  const handleCloseSidePanel = (
    force?: boolean,
    isSaveLoading?: boolean,
    isUpdateLoading?: boolean,
    isSaveAndAddAnotherLoading?: boolean
  ) => {
    // Clear victim details mode when closing
    setVictimDetailsMode(null);
    // Call the original close handler
    return entityHandlers.handleCloseSidePanel(
      force || false,
      isSaveLoading || false,
      isUpdateLoading || false,
      isSaveAndAddAnotherLoading || false
    );
  };

  if (!reportId) return null;

  return (
    <Box
      sx={{
        position: "fixed",
        inset: 0,
        zIndex: 50,
        display: "flex",
        bgcolor: "rgba(0, 0, 0, 0.5)",
      }}
      onClick={goBack}
    >
      <Box sx={{ flex: 1, display: "flex", pl: "20px" }}>
        <Paper
          ref={scrollContainerRef}
          elevation={3}
          sx={{
            flex: 1,
            ml: 2,
            overflow: "auto",
            display: "flex",
            flexDirection: "column",
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Sticky Header */}
          <Box
            sx={{
              position: "sticky",
              top: 0,
              zIndex: 20,
              bgcolor: "background.paper",
              borderBottom: 1,
              borderColor: "divider",
            }}
          >
            <ReportHeader
              report={reportQuery.data}
              sections={reportSectionsQuery.data?.sections}
              onClose={goBack}
              onCommentClick={commentHandlers.toggleGlobalComments}
              isCommentOpen={isGlobalCommentsOpen}
              saveStatuses={saveStatuses}
              currentOrder={orderForReport}
              // @ts-expect-error TODO: Fix type issue
              orderType={orderForReport?.type || ""}
              reviewRoundId={reviewRoundId}
            />
          </Box>

          <Box sx={{ display: "flex", flex: 1 }}>
            {/* Left Sidebar */}
            <Paper
              elevation={0}
              sx={{
                width: 256,
                borderRight: 1,
                borderColor: "divider",
                position: "sticky",
                top: 100,
                height: "calc(100vh - 100px)",
                overflow: "auto",
                borderRadius: 0,
              }}
            >
              <List component="nav" sx={{ p: 2 }}>
                {reportSectionsQuery.isLoading ? (
                  // Show skeleton loaders while sections are loading
                  Array.from({ length: 5 }).map((_, index) => (
                    <ListItem
                      key={`skeleton-${index}`}
                      disablePadding
                      sx={{ mb: 0.5 }}
                    >
                      <Box
                        sx={{
                          width: "100%",
                          height: "40px",
                          borderRadius: 1,
                          backgroundColor: colors.grey[100],
                          display: "flex",
                          alignItems: "center",
                          px: 2,
                          py: 1,
                          animation: "pulse 1.5s ease-in-out infinite",
                          "@keyframes pulse": {
                            "0%": {
                              opacity: 1,
                            },
                            "50%": {
                              opacity: 0.6,
                            },
                            "100%": {
                              opacity: 1,
                            },
                          },
                        }}
                      >
                        <Box
                          sx={{
                            height: "16px",
                            width: `${60 + Math.random() * 40}%`, // Random width between 60-100%
                            backgroundColor: colors.grey[200],
                            borderRadius: "4px",
                          }}
                        />
                      </Box>
                    </ListItem>
                  ))
                ) : availableNavItems.length === 0 ? (
                  <ListItem>
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: "14px",
                        color: colors.grey[500],
                      }}
                    >
                      No sections available
                    </Typography>
                  </ListItem>
                ) : (
                  availableNavItems.map((item) => (
                    <ListItem key={item.id} disablePadding>
                      <ListItemButton
                        onClick={() => scrollToSection(item.id)}
                        selected={activeSection === item.id}
                        sx={(theme) => ({
                          borderRadius: 1,
                          "&:hover": { bgcolor: "grey.100" },
                          "&.Mui-selected": {
                            bgcolor: colors.blue[50],
                            "&:hover": { bgcolor: colors.blue[100] },
                          },
                        })}
                      >
                        <ListItemText
                          primary={item.label}
                          sx={{
                            "& .MuiListItemText-primary": {
                              fontSize: "16px",
                            },
                          }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))
                )}
              </List>
            </Paper>

            {/* Main Content */}
            <Box sx={{ flex: 1, bgcolor: "grey.50", p: 2 }}>
              <Stack spacing={3} pb={34}>
                {/* Rejection Message if applicable */}
                {rejectedReviewRound && (
                  <ReportRejectionCard
                    rejectionNote={rejectedReviewRound.roundNote || ""}
                    reviewer={
                      rejectedReviewRound.reviewerAssetId
                        ? toTitleCase(rejectedReviewRound.reviewerAssetId)
                        : "Chief Supervisor"
                    }
                    rejectionDate={
                      rejectedReviewRound.resolvedAt
                        ? new Date(rejectedReviewRound.resolvedAt)
                          .toLocaleString("en-US", {
                            month: "short",
                            day: "numeric",
                            year: "numeric",
                            hour: "numeric",
                            minute: "2-digit",
                            hour12: false,
                          })
                          .replace(", ", " at ")
                        : undefined
                    }
                  />
                )}

                {/* Conditionally render sections based on their existence */}
                {incidentDetailsSectionId && (
                  <Box id="incident-details">
                    <IncidentDetailsCard
                      reportId={reportId}
                      comments={incidentComments}
                      onAddComment={(text) =>
                        commentHandlers.handleAddComment(text, "incident")
                      }
                      onResolveComment={(id, resolved) =>
                        commentHandlers.handleResolveComment(
                          id,
                          resolved,
                          "incident"
                        )
                      }
                      onSaveStatusChange={handleSaveStatusChange}
                      commentsInitiallyExpanded={shouldExpandComments}
                      readOnly={readOnly}
                    />
                  </Box>
                )}

                {offenseListSectionId && (
                  <Box id="offenses">
                    <OffenseSection
                      reportId={reportId}
                      onSaveStatusChange={handleSaveStatusChange}
                      readOnly={readOnly}
                      people={entitiesToPersonData(people)}
                      vehicles={entitiesToVehicleData(vehicles)}
                      properties={entitiesToPropertyData(properties)}
                      organizations={entitiesToOrganizationData(organizations)}
                      relations={reportQuery.data?.relations || []}
                      associatedCases={
                        fullCaseQuery.data ? [fullCaseQuery.data] : []
                      }
                      onOpenSidePanel={(panelType) =>
                        entityHandlers.handleOpenSidePanel(
                          panelType as any,
                          readOnly
                        )
                      }
                      onAddPersonToOffense={
                        relationHandlers.handleAddPersonToOffense
                      }
                      onAddVehicleToOffense={
                        relationHandlers.handleAddVehicleToOffense
                      }
                      onAddPropertyToOffense={
                        relationHandlers.handleAddPropertyToOffense
                      }
                      onAddOrganizationToOffense={
                        relationHandlers.handleAddOrganizationToOffense
                      }
                      onAddVictimOrganizationToOffense={
                        relationHandlers.handleAddVictimOrganizationToOffense
                      }
                      onRemovePersonFromOffense={
                        relationHandlers.handleRemovePersonFromOffense
                      }
                      onRemoveVehicleFromOffense={
                        relationHandlers.handleRemoveVehicleFromOffense
                      }
                      onRemovePropertyFromOffense={
                        relationHandlers.handleRemovePropertyFromOffense
                      }
                      onRemoveOrganizationFromOffense={
                        relationHandlers.handleRemoveOrganizationFromOffense
                      }
                      onRemoveVictimOrganizationFromOffense={
                        relationHandlers.handleRemoveVictimOrganizationFromOffense
                      }
                      onRemovePersonFromReport={
                        entityHandlers.handleRemovePersonFromReport
                      }
                      onRemoveVehicleFromReport={
                        entityHandlers.handleRemoveVehicleFromReport
                      }
                      onRemovePropertyFromReport={
                        entityHandlers.handleRemovePropertyFromReport
                      }
                      onRemoveOrganizationFromReport={
                        entityHandlers.handleRemoveOrganizationFromReport
                      }
                      // Batch removal handlers for offense/incident deletion
                      onBatchRemovePeopleFromReport={
                        entityHandlers.handleBatchRemovePeopleFromReport
                      }
                      onBatchRemoveVehiclesFromReport={
                        entityHandlers.handleBatchRemoveVehiclesFromReport
                      }
                      onBatchRemovePropertiesFromReport={
                        entityHandlers.handleBatchRemovePropertiesFromReport
                      }
                      onBatchRemoveOrganizationsFromReport={
                        entityHandlers.handleBatchRemoveOrganizationsFromReport
                      }
                      onSetActiveOffenseRelation={
                        handleSetActiveOffenseRelation
                      }
                      onSetActiveVehicleOffenseContext={
                        handleSetActiveVehicleOffenseContext
                      }
                      onSetActivePropertyOffenseContext={
                        handleSetActivePropertyOffenseContext
                      }
                      onSetActiveOrganizationOffenseContext={
                        handleSetActiveOrganizationOffenseContext
                      }
                      onSetActiveVictimOrganizationOffenseContext={
                        handleSetActiveVictimOrganizationOffenseContext
                      }
                      onEntityEdit={entityHandlers.handleEntityEdit}
                      onAddVictimDetails={handleAddVictimDetails}
                      onAddVictimDetailsOrganization={
                        handleAddVictimDetailsOrganization
                      }
                    />
                  </Box>
                )}

                {arrestListSectionId && (
                  <Box id="arrests">
                    <ArrestSection
                      reportId={reportId}
                      onSaveStatusChange={handleSaveStatusChange}
                      readOnly={readOnly}
                      people={entitiesToPersonData(people)}
                      relations={reportQuery.data?.relations || []}
                      associatedCases={
                        fullCaseQuery.data ? [fullCaseQuery.data] : []
                      }
                      onOpenSidePanel={(panelType) =>
                        entityHandlers.handleOpenSidePanel(
                          panelType as any,
                          readOnly
                        )
                      }
                      onAddPersonToArrest={handleAddPersonToArrest}
                      onRemovePersonFromArrest={handleRemovePersonFromArrest}
                      onRemovePersonFromReport={
                        entityHandlers.handleRemovePersonFromReport
                      }
                      onSetActiveArrestRelation={handleSetActiveArrestRelation}
                      onEntityEdit={entityHandlers.handleEntityEdit}
                    />
                  </Box>
                )}

                {isSupplementalReport && peopleListSectionId && (
                  <Box id="people">
                    <EntityCard
                      title="People"
                      onAddClick={() =>
                        entityHandlers.handleOpenSidePanel(
                          PanelType.PERSON,
                          readOnly
                        )
                      }
                      personData={entitiesToPersonData(people)}
                      comments={peopleComments}
                      onAddComment={(text) =>
                        commentHandlers.handleAddComment(text, "people")
                      }
                      onResolveComment={(id, resolved) =>
                        commentHandlers.handleResolveComment(
                          id,
                          resolved,
                          "people"
                        )
                      }
                      onEntityEdit={entityHandlers.handleEntityEdit}
                      selectedRowId={selectedRowId}
                      onRowSelect={stateValues.setSelectedRowId}
                      onEntityDelete={entityHandlers.handleEntityDelete}
                      commentsInitiallyExpanded={shouldExpandComments}
                      readOnly={readOnly}
                    />
                  </Box>
                )}

                {isSupplementalReport && vehicleListSectionId && (
                  <Box id="vehicles">
                    <EntityCard
                      title="Vehicles"
                      onAddClick={() =>
                        entityHandlers.handleOpenSidePanel(
                          PanelType.VEHICLE,
                          readOnly
                        )
                      }
                      vehicleData={entitiesToVehicleData(vehicles)}
                      comments={vehicleComments}
                      onAddComment={(text) =>
                        commentHandlers.handleAddComment(text, "vehicle")
                      }
                      onResolveComment={(id, resolved) =>
                        commentHandlers.handleResolveComment(
                          id,
                          resolved,
                          "vehicle"
                        )
                      }
                      onEntityEdit={entityHandlers.handleEntityEdit}
                      selectedRowId={selectedRowId}
                      onRowSelect={stateValues.setSelectedRowId}
                      onEntityDelete={entityHandlers.handleEntityDelete}
                      commentsInitiallyExpanded={shouldExpandComments}
                      readOnly={readOnly}
                    />
                  </Box>
                )}

                {isSupplementalReport && propertyListSectionId && (
                  <Box id="property">
                    <PropertySection
                      reportId={reportId}
                      onSaveStatusChange={handleSaveStatusChange}
                      readOnly={readOnly}
                      onOpenSidePanel={(panelType) =>
                        entityHandlers.handleOpenSidePanel(panelType, readOnly)
                      }
                      properties={properties}
                      reportSections={reportSectionsQuery.data}
                      comments={propertyComments}
                      onAddComment={(text) =>
                        commentHandlers.handleAddComment(text, "property")
                      }
                      onResolveComment={(id, resolved) =>
                        commentHandlers.handleResolveComment(
                          id,
                          resolved,
                          "property"
                        )
                      }
                      onEntityEdit={entityHandlers.handleEntityEdit}
                      selectedRowId={selectedRowId}
                      onRowSelect={stateValues.setSelectedRowId}
                      onEntityDelete={entityHandlers.handleEntityDelete}
                      commentsInitiallyExpanded={shouldExpandComments}
                    />
                  </Box>
                )}

                {isSupplementalReport && organizationListSectionId && (
                  <Box id="organization">
                    <EntityCard
                      title="Organizations"
                      onAddClick={() =>
                        entityHandlers.handleOpenSidePanel(
                          PanelType.ORGANIZATION,
                          readOnly
                        )
                      }
                      organizationData={entitiesToOrganizationData(
                        organizations
                      )}
                      comments={organizationComments}
                      onAddComment={(text) =>
                        commentHandlers.handleAddComment(text, "organization")
                      }
                      onResolveComment={(id, resolved) =>
                        commentHandlers.handleResolveComment(
                          id,
                          resolved,
                          "organization"
                        )
                      }
                      onEntityEdit={entityHandlers.handleEntityEdit}
                      selectedRowId={selectedRowId}
                      onRowSelect={stateValues.setSelectedRowId}
                      onEntityDelete={entityHandlers.handleEntityDelete}
                      commentsInitiallyExpanded={shouldExpandComments}
                      readOnly={readOnly}
                    />
                  </Box>
                )}

                {narrativeSectionId && (
                  <Box id="narrative">
                    <NarrativeCard
                      reportId={reportId}
                      comments={narrativeComments}
                      onAddComment={(text) =>
                        commentHandlers.handleAddComment(text, "narrative")
                      }
                      onResolveComment={(id, resolved) =>
                        commentHandlers.handleResolveComment(
                          id,
                          resolved,
                          "narrative"
                        )
                      }
                      onSaveStatusChange={handleSaveStatusChange}
                      commentsInitiallyExpanded={shouldExpandComments}
                      readOnly={readOnly}
                    />
                  </Box>
                )}

                {mediaSectionId && (
                  <Box id="media">
                    <MediaSection
                      reportId={reportId}
                      mediaSectionId={mediaSectionId}
                      onAddClick={() =>
                        entityHandlers.handleOpenSidePanel(
                          PanelType.MEDIA,
                          readOnly
                        )
                      }
                      comments={mediaComments}
                      onAddComment={(text) =>
                        commentHandlers.handleAddComment(text, "media")
                      }
                      onResolveComment={(id, resolved) =>
                        commentHandlers.handleResolveComment(
                          id,
                          resolved,
                          "media"
                        )
                      }
                      commentsInitiallyExpanded={shouldExpandComments}
                      readOnly={readOnly}
                      properties={properties}
                    />
                  </Box>
                )}
              </Stack>
            </Box>

            {/* Right Sidebar */}
            <Paper
              elevation={0}
              sx={{
                width: 350,
                bgcolor: "grey.50",
                p: 2,
                position: "sticky",
                top: 100,
                height: "calc(100vh - 100px)",
                overflow: "auto",
                borderRadius: 0,
              }}
            >
              {reportQuery.data?.situationId && (
                <ReportSummary
                  report={reportQuery.data}
                  people={people}
                  vehicles={vehicles}
                  properties={properties}
                  organizations={organizations}
                  mediaFiles={mediaFiles}
                  relations={reportQuery.data?.relations || []}
                  reportId={reportId}
                  currentUserAssetId={dispatcherAsset?.id}
                />
              )}
            </Paper>
          </Box>
        </Paper>
      </Box>

      {/* Side Panel */}
      <SidePanel
        title={getContextualPanelTitle()}
        isOpen={sidePanelOpen}
        onClose={() =>
          handleCloseSidePanel(
            false,
            isSaveLoading,
            isUpdateLoading,
            isSaveAndAddAnotherLoading
          )
        }
      >
        {(activePanelType || victimDetailsMode) && (
          <PanelContent
            key={`panel-${activePanelType}-${editingEntityId || "new"}-${victimDetailsMode?.personId || ""
              }`}
            ref={stateValues.panelContentRef}
            panelType={activePanelType || PanelType.PERSON}
            onSubmit={formHandlers.handleFormSubmit}
            onUpdate={formHandlers.handleFormUpdate}
            onSaveAndAddAnother={formHandlers.handleSaveAndAddAnother}
            onPropertyUpdated={handlePropertyUpdate} // Pass custom property update handler
            onCancel={() =>
              handleCloseSidePanel(
                false,
                isSaveLoading,
                isUpdateLoading,
                isSaveAndAddAnotherLoading
              )
            }
            onEntitySelect={entityHandlers.handleEntitySelect}
            personSchema={
              activePanelType === PanelType.PERSON ? getCurrentSchema() : null
            }
            isLoadingSchema={
              (activePanelType === PanelType.PERSON &&
                personSchemasQuery.isLoading) ||
              (!!editingEntityId && specificSchemaQuery.isLoading)
            }
            propertySchema={
              activePanelType === PanelType.PROPERTY ? getCurrentSchema() : null
            }
            isLoadingPropertySchema={
              (activePanelType === PanelType.PROPERTY &&
                propertySchemasQuery.isLoading) ||
              (!!editingEntityId && specificSchemaQuery.isLoading)
            }
            vehicleSchema={
              activePanelType === PanelType.VEHICLE ? getCurrentSchema() : null
            }
            isLoadingVehicleSchema={
              (activePanelType === PanelType.VEHICLE &&
                vehicleSchemasQuery.isLoading) ||
              (!!editingEntityId && specificSchemaQuery.isLoading)
            }
            organizationSchema={
              activePanelType === PanelType.ORGANIZATION
                ? getCurrentSchema()
                : null
            }
            isLoadingOrganizationSchema={
              (activePanelType === PanelType.ORGANIZATION &&
                organizationSchemasQuery.isLoading) ||
              (!!editingEntityId && specificSchemaQuery.isLoading)
            }
            initialValues={editingEntityFormData}
            isEditing={!!editingEntityId}
            editingEntityId={editingEntityId}
            isSaveAndAddAnotherLoading={isSaveAndAddAnotherLoading}
            isSaveLoading={isSaveLoading}
            isUpdateLoading={isUpdateLoading}
            readOnly={readOnly}
            currentEntities={
              activePanelType === PanelType.PERSON
                ? people
                : activePanelType === PanelType.VEHICLE
                  ? vehicles
                  : activePanelType === PanelType.ORGANIZATION
                    ? organizations
                    : activePanelType === PanelType.PROPERTY
                      ? properties
                      : []
            }
            offenseContext={
              activePanelType === PanelType.PERSON && activeOffenseRelation
                ? {
                  relationType: activeOffenseRelation.relationType,
                  offenseId: activeOffenseRelation.offenseId,
                  isIncident: isIncident(activeOffenseRelation.offenseId),
                }
                : activePanelType === PanelType.VEHICLE &&
                  activeVehicleOffenseContext
                  ? {
                    offenseId: activeVehicleOffenseContext.offenseId,
                    isIncident: isIncident(
                      activeVehicleOffenseContext.offenseId
                    ),
                  }
                  : activePanelType === PanelType.ORGANIZATION &&
                    activeOrganizationOffenseContext
                    ? {
                      offenseId: activeOrganizationOffenseContext.offenseId,
                      isIncident: isIncident(
                        activeOrganizationOffenseContext.offenseId
                      ),
                    }
                    : activePanelType === PanelType.PROPERTY &&
                      activePropertyOffenseContext
                      ? {
                        offenseId: activePropertyOffenseContext.offenseId,
                        isIncident: isIncident(
                          activePropertyOffenseContext.offenseId
                        ),
                      }
                      : undefined
            }
            victimDetailsMode={victimDetailsMode || undefined}
            onCreateVictimReportRelation={handleCreateVictimReportRelation}
            onUpdateVictimReportRelation={handleUpdateVictimReportRelation}
            reportId={reportId}
          />
        )}
      </SidePanel>

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={3000}
        onClose={formHandlers.handleCloseNotification}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={formHandlers.handleCloseNotification}
          severity={notification.severity}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Global Comments Popup */}
      <GlobalCommentsPopup
        open={isGlobalCommentsOpen}
        onClose={commentHandlers.handleCloseGlobalComments}
        anchorEl={commentButtonEl}
        comments={globalComments}
        onAddComment={(text) =>
          commentHandlers.handleAddComment(text, "global")
        }
        onResolveComment={commentHandlers.handleResolveGlobalComment}
        sectionIdToType={sectionIdToType}
        readOnly={readOnly}
      />
    </Box>
  );
}

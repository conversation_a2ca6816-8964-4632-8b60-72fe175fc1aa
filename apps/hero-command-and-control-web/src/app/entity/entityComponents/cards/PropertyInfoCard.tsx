import { Label } from "@/design-system/components/Label";
import { colors } from "@/design-system/tokens";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import BadgeIcon from "@mui/icons-material/Badge";
import CategoryIcon from "@mui/icons-material/Category";
import DescriptionIcon from "@mui/icons-material/Description";
import HandymanIcon from "@mui/icons-material/Handyman";
import InventoryIcon from "@mui/icons-material/Inventory";
import NoPhotographyOutlinedIcon from "@mui/icons-material/NoPhotographyOutlined";
import PersonIcon from "@mui/icons-material/Person";
import PolicyIcon from "@mui/icons-material/Policy";
import {
  Box,
  Divider,
  Paper,
  Typography,
} from "@mui/material";
import { Entity } from "proto/hero/entity/v1/entity_pb";
import React from "react";
import { hookPropertyStatusToString, stringToPropertyStatus } from "../../../apis/services/workflow/property/enumConverters";
import { Property, PropertyStatus } from "../../../apis/services/workflow/property/types";
import { twoLineClamp } from "../utils/constants";

interface PropertyInfoCardProps {
  property?: Entity | undefined;
  propertyData?: Property;
  isLoading?: boolean;
  isError?: boolean;
}

interface DisplayTag {
  label: string;
  color: string;
  prominence: boolean;
}

interface PropertyInfoItem {
  icon: React.ReactNode;
  text: string;
  wide?: boolean;
}

export default function PropertyInfoCard({
  property,
  propertyData,
  isLoading = false,
  isError = false
}: PropertyInfoCardProps) {
  // Extract entity data for internal tags (keeping this for now)
  const extractEntityData = () => {
    if (!property?.data) {
      return null;
    }

    try {
      const data = typeof property.data === 'string' ? JSON.parse(property.data) : property.data;
      const internalTags = data?.internalTags || [];
      return { internalTags };
    } catch (error) {
      console.warn("Failed to parse property entity data:", error);
      return null;
    }
  };

  const entityData = extractEntityData();

  // Process internal tags for display
  const getTagsForDisplay = (): DisplayTag[] => {
    if (!entityData?.internalTags || entityData.internalTags.length === 0) {
      return [{ label: "No tags", color: "grey", prominence: false }];
    }

    return entityData.internalTags.map((tag: any) => ({
      label: tag.label || tag.value || 'Unknown',
      color: tag.color || 'grey',
      prominence: false,
    }));
  };

  // Get property status display
  const getPropertyStatusDisplay = (status: PropertyStatus | string): string => {
    // Handle both string and numeric enum values
    let propertyStatus: PropertyStatus;

    if (typeof status === 'string') {
      propertyStatus = stringToPropertyStatus(status);
    } else {
      propertyStatus = status;
    }

    switch (propertyStatus) {
      case PropertyStatus.PROPERTY_STATUS_COLLECTED:
        return "Collected";
      case PropertyStatus.PROPERTY_STATUS_IN_CUSTODY:
        return "In Custody";
      case PropertyStatus.PROPERTY_STATUS_CHECKED_OUT:
        return "Checked Out";
      case PropertyStatus.PROPERTY_STATUS_DISPOSED:
        return "Disposed";
      case PropertyStatus.PROPERTY_STATUS_MISSING:
        return "Missing";
      case PropertyStatus.PROPERTY_STATUS_CLAIMED:
        return "Claimed";
      default:
        return "Unknown";
    }
  };

  // Get property status display for hook data (which comes as string but typed as enum)
  const getPropertyStatusDisplayFromHook = (status: PropertyStatus): string => {
    const statusString = hookPropertyStatusToString(status);
    return getPropertyStatusDisplay(statusString);
  };

  if (isLoading) {
    return (
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          border: `1px solid ${colors.grey[200]}`,
          mb: 3,
          overflow: "hidden",
          maxWidth: "100%",
        }}
      >
        <Box sx={{ p: 2, bgcolor: "white" }}>
          <Typography variant="body2" color={colors.grey[500]}>
            Loading property information...
          </Typography>
        </Box>
      </Paper>
    );
  }

  if (isError || !propertyData) {
    return (
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          border: `1px solid ${colors.grey[200]}`,
          mb: 3,
          overflow: "hidden",
          maxWidth: "100%",
        }}
      >
        <Box sx={{ p: 2, bgcolor: "white" }}>
          <Typography variant="body2" color={colors.grey[500]}>
            No property data available
          </Typography>
        </Box>
      </Paper>
    );
  }

  // Get property schema from the property data
  const propertySchema = propertyData.propertySchema;



  // Create an array of property info items from PropertySchema
  const propertyInfoItems: PropertyInfoItem[] = [
    propertyData.propertyStatus && {
      icon: <PolicyIcon sx={{ color: colors.grey[500] }} />,
      text: getPropertyStatusDisplayFromHook(propertyData.propertyStatus),
    },
    propertySchema?.category && {
      icon: <CategoryIcon sx={{ color: colors.grey[500] }} />,
      text: propertySchema.category,
    },
    propertySchema?.serialNumber && {
      icon: <BadgeIcon sx={{ color: colors.grey[500] }} />,
      text: propertySchema.serialNumber,
    },
    propertySchema?.identifiers && {
      icon: <InventoryIcon sx={{ color: colors.grey[500] }} />,
      text: propertySchema.identifiers,
    },
    propertySchema?.owner && {
      icon: <PersonIcon sx={{ color: colors.grey[500] }} />,
      text: propertySchema.owner,
    },
    propertySchema?.condition && {
      icon: <HandymanIcon sx={{ color: colors.grey[500] }} />,
      text: propertySchema.condition,
    },
    propertySchema?.value && {
      icon: <AttachMoneyIcon sx={{ color: colors.grey[500] }} />,
      text: propertySchema.value,
    },
    propertySchema?.description && {
      icon: <DescriptionIcon sx={{ color: colors.grey[500] }} />,
      text: propertySchema.description,
      wide: true,
    },
  ].filter(Boolean) as PropertyInfoItem[];

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 2,
        border: `1px solid ${colors.grey[200]}`,
        mb: 3,
        overflow: "hidden",
        maxWidth: "100%",
      }}
    >
      <Box sx={{ p: 2, bgcolor: "white" }}>
        {/* wrapper flex row */}
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: 2,
            width: "100%",
            alignItems: "stretch",
          }}
        >
          {/* Photo / placeholder */}
          <Box
            sx={{
              flexBasis: { xs: "100%", sm: "25%", md: "16.666%" },
              maxWidth: { xs: "100%", sm: "25%", md: "16.666%" },
            }}
          >
            <Box
              sx={{
                width: "100%",
                height: "100%",
                minHeight: 120,
                backgroundColor: colors.grey[100],
                border: `1px solid ${colors.grey[200]}`,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: 1,
              }}
            >
              <NoPhotographyOutlinedIcon
                sx={{ color: colors.grey[300], fontSize: 40 }}
              />
            </Box>
          </Box>

          {/* Right‑hand info */}
          <Box
            sx={{
              flexBasis: {
                xs: "100%",
                sm: "calc(75% - 16px)",
                md: "calc(83.333% - 16px)",
              },
              maxWidth: { xs: "100%", sm: "75%", md: "83.333%" },
              display: "flex",
              flexDirection: "column",
              gap: 2,
            }}
          >
            {/* Tags section */}
            <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
              {getTagsForDisplay().map((tag: DisplayTag, i: number) => (
                <Label
                  key={i}
                  label={tag.label}
                  color={tag.color as any}
                  prominence={tag.prominence}
                  size="small"
                />
              ))}
            </Box>

            <Divider />

            <Box
              sx={{
                display: "grid",
                gap: 1,
                gridTemplateColumns: {
                  xs: "repeat(auto-fill, minmax(160px, 1fr))",
                  sm: "repeat(auto-fill, minmax(200px, 1fr))",
                  md: "repeat(auto-fill, minmax(220px, 1fr))",
                },
                alignItems: "start",
              }}
            >
              {propertyInfoItems.map(({ icon, text, wide }, i) => (
                <Box
                  key={i}
                  sx={{
                    display: "flex",
                    alignItems: "flex-start",
                    gap: 1,
                    gridColumn: wide ? { md: "span 2" } : undefined,
                  }}
                >
                  {icon}
                  <Typography
                    variant="body2"
                    sx={{
                      ...twoLineClamp,
                      minWidth: 0,
                      flex: 1,
                    }}
                  >
                    {text}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </Paper>
  );
} 
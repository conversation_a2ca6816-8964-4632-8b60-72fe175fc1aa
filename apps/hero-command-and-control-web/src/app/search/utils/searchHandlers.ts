import { CaseStatus, CaseType, SearchOrderBy as CasesSearchOrderBy } from "proto/hero/cases/v1/cases_pb";
import { SearchOrderBy as EntitySearchOrderBy, RecordStatus } from "proto/hero/entity/v1/entity_pb";
import { ReportStatus } from "proto/hero/reports/v2/reports_pb";
import { SituationStatus, SituationType, TriggerSource } from "proto/hero/situations/v2/situations_pb";
import { SearchPropertiesRequest } from "../../apis/services/workflow/property/types";
import { getValidFieldIds } from "./tabConfigs";

export interface IncidentSearchParams {
  query: string;
  pageSize: number;
  orderBy: any;
  ascending: boolean;
  pageToken?: string;
  status?: SituationStatus[];
  type?: SituationType[];
  triggerSource?: TriggerSource[];
  priority?: number[];
  tags?: string[];
  searchFields?: string[];
  fieldQueries?: { field: string; query: string }[];
  [key: string]: any;
}

export interface ReportsSearchParams {
  query: string;
  pageSize: number;
  orderBy: any;
  ascending: boolean;
  pageToken?: string;
  status?: ReportStatus[];
  searchFields?: string[];
  createdAt?: { from: string; to: string };
  updatedAt?: { from: string; to: string };
  [key: string]: any;
}

export interface EntitySearchParams {
  query: string;
  pageSize: number;
  orderBy: EntitySearchOrderBy;
  ascending: boolean;
  pageToken?: string;
  status?: RecordStatus[];
  entityTypes?: any[];
  searchFields?: string[];
  fieldQueries?: { field: string; query: string }[];
  createTime?: { from: string; to: string };
  updateTime?: { from: string; to: string };
  tags?: string[];
  [key: string]: any;
}

export interface IncidentFilters {
  selectedStatuses: SituationStatus[];
  selectedTypes: SituationType[];
  selectedTriggerSources: TriggerSource[];
  selectedPriorities: number[];
  selectedTags: string[];
  selectedSearchFields: string[];
  fieldQueries: { field: string; query: string }[];
  dateFilters: {
    createTime?: { from: string; to: string };
    updateTime?: { from: string; to: string };
    incidentTime?: { from: string; to: string };
    resolvedTime?: { from: string; to: string };
    dueTime?: { from: string; to: string };
  };
}

export interface ReportsFilters {
  selectedStatuses: ReportStatus[];
  selectedSearchFields: string[];
  selectedTypes: any[];
  dateFilters: {
    createdAt?: { from: string; to: string };
    updatedAt?: { from: string; to: string };
    assignedAt?: { from: string; to: string };
    completedAt?: { from: string; to: string };
  };
}

export interface EntityFilters {
  selectedStatuses: RecordStatus[];
  selectedTags: string[];
  selectedSearchFields: string[];
  fieldQueries: { field: string; query: string }[];
  dateFilters: {
    createTime?: { from: string; to: string };
    updateTime?: { from: string; to: string };
  };
}

export interface CasesSearchParams {
  query: string;
  pageSize: number;
  orderBy: CasesSearchOrderBy;
  ascending: boolean;
  pageToken?: string;
  status?: CaseStatus[];
  type?: CaseType[];
  priority?: number[];
  tags?: string[];
  searchFields?: string[];
  fieldQueries?: { field: string; query: string }[];
  createTime?: { from: string; to: string };
  updateTime?: { from: string; to: string };
  dueDate?: { from: string; to: string };
  resolvedTime?: { from: string; to: string };
  closeTime?: { from: string; to: string };
  [key: string]: any;
}

export interface CasesFilters {
  selectedStatuses: CaseStatus[];
  selectedTypes: CaseType[];
  selectedPriorities: number[];
  selectedTags: string[];
  selectedSearchFields: string[];
  fieldQueries: { field: string; query: string }[];
  dateFilters: {
    createTime?: { from: string; to: string };
    updateTime?: { from: string; to: string };
    dueDate?: { from: string; to: string };
    resolvedTime?: { from: string; to: string };
    closeTime?: { from: string; to: string };
  };
}

export const buildIncidentSearchParams = (
  baseParams: IncidentSearchParams,
  searchValue: string,
  filters: IncidentFilters
): IncidentSearchParams => {
  const newParams: IncidentSearchParams = {
    ...baseParams,
    query: searchValue,
    pageToken: undefined,
    status: filters.selectedStatuses,
    type: filters.selectedTypes,
    triggerSource: filters.selectedTriggerSources,
    priority: filters.selectedPriorities,
    tags: filters.selectedTags,
  };

  if (filters.selectedSearchFields && filters.selectedSearchFields.length > 0) {
    const validFieldIds = getValidFieldIds("Incidents");
    newParams.searchFields = filters.selectedSearchFields.filter((field) =>
      validFieldIds.has(field)
    );
  } else {
    delete newParams.searchFields;
  }

  if (filters.fieldQueries && filters.fieldQueries.length > 0) {
    const validFieldIds = getValidFieldIds("Incidents");
    const validFieldQueries = filters.fieldQueries.filter((fq) =>
      validFieldIds.has(fq.field)
    );

    if (validFieldQueries.length > 0) {
      newParams.fieldQueries = validFieldQueries.map((fq) => ({
        field: fq.field,
        query: fq.query,
      }));
    } else {
      delete newParams.fieldQueries;
    }
  } else {
    delete newParams.fieldQueries;
  }

  const dateFilterKeys = [
    "createTime",
    "updateTime",
    "incidentTime",
    "resolvedTime",
    "dueTime",
  ] as const;

  dateFilterKeys.forEach((filterType) => {
    const filter = filters.dateFilters[filterType];
    if (filter && filter.from && filter.to) {
      newParams[filterType] = {
        from: filter.from,
        to: filter.to,
      };
    } else {
      delete newParams[filterType];
    }
  });

  return newParams;
};

export const buildReportsSearchParams = (
  baseParams: ReportsSearchParams,
  searchValue: string,
  filters: ReportsFilters
): ReportsSearchParams => {
  const newParams: ReportsSearchParams = {
    ...baseParams,
    query: searchValue,
    pageToken: undefined,
    status: filters.selectedStatuses,
  };

  if (filters.selectedSearchFields.length > 0) {
    newParams.searchFields = filters.selectedSearchFields;
  } else {
    delete newParams.searchFields;
  }

  if (
    filters.dateFilters.createdAt &&
    filters.dateFilters.createdAt.from &&
    filters.dateFilters.createdAt.to
  ) {
    newParams.createdAt = {
      from: filters.dateFilters.createdAt.from,
      to: filters.dateFilters.createdAt.to,
    };
  } else {
    delete newParams.createdAt;
  }

  if (
    filters.dateFilters.updatedAt &&
    filters.dateFilters.updatedAt.from &&
    filters.dateFilters.updatedAt.to
  ) {
    newParams.updatedAt = {
      from: filters.dateFilters.updatedAt.from,
      to: filters.dateFilters.updatedAt.to,
    };
  } else {
    delete newParams.updatedAt;
  }

  return newParams;
};

export const buildEntitySearchParams = (
  baseParams: EntitySearchParams,
  searchValue: string,
  filters: EntityFilters,
  entityType?: any
): EntitySearchParams => {
  const newParams: EntitySearchParams = {
    ...baseParams,
    query: searchValue,
    pageToken: undefined,
    status: filters.selectedStatuses,
    tags: filters.selectedTags,
  };

  // Set entity type filter if provided
  if (entityType) {
    newParams.entityTypes = [entityType];
  }

  if (filters.selectedSearchFields && filters.selectedSearchFields.length > 0) {
    const validFieldIds = getValidFieldIds("People"); // All entity types use the same search fields
    newParams.searchFields = filters.selectedSearchFields.filter((field) =>
      validFieldIds.has(field)
    );
  } else {
    delete newParams.searchFields;
  }

  if (filters.fieldQueries && filters.fieldQueries.length > 0) {
    const validFieldIds = getValidFieldIds("People"); // All entity types use the same search fields
    const validFieldQueries = filters.fieldQueries.filter((fq) =>
      validFieldIds.has(fq.field)
    );

    if (validFieldQueries.length > 0) {
      newParams.fieldQueries = validFieldQueries.map((fq) => ({
        field: fq.field,
        query: fq.query,
      }));
    } else {
      delete newParams.fieldQueries;
    }
  } else {
    delete newParams.fieldQueries;
  }

  const dateFilterKeys = ["createTime", "updateTime"] as const;

  dateFilterKeys.forEach((filterType) => {
    const filter = filters.dateFilters[filterType];
    if (filter && filter.from && filter.to) {
      newParams[filterType] = {
        from: filter.from,
        to: filter.to,
      };
    } else {
      delete newParams[filterType];
    }
  });

  return newParams;
};

export const buildPropertySearchParams = (
  baseParams: SearchPropertiesRequest,
  searchValue: string,
  filters: EntityFilters
): SearchPropertiesRequest => {
  const newParams: SearchPropertiesRequest = {
    ...baseParams,
    query: searchValue,
    pageToken: undefined,
  };

  // Merge field queries from both selectedSearchFields and fieldQueries
  const allFieldQueries = [];
  
  if (filters.selectedSearchFields && filters.selectedSearchFields.length > 0) {
    const validFieldIds = getValidFieldIds("People"); // Property uses same search fields as entities
    const selectedFieldQueries = filters.selectedSearchFields
      .filter((field) => validFieldIds.has(field))
      .map((field) => ({ field, query: searchValue }));
    allFieldQueries.push(...selectedFieldQueries);
  }

  if (filters.fieldQueries && filters.fieldQueries.length > 0) {
    const validFieldIds = getValidFieldIds("People");
    const validFieldQueries = filters.fieldQueries
      .filter((fq) => validFieldIds.has(fq.field))
      .map((fq) => ({
        field: fq.field,
        query: fq.query,
      }));
    allFieldQueries.push(...validFieldQueries);
  }

  // Only assign if we have field queries to avoid empty arrays
  if (allFieldQueries.length > 0) {
    newParams.fieldQueries = allFieldQueries;
  }

  // Handle date filters
  const dateFilter = filters.dateFilters.createTime;
  if (dateFilter && dateFilter.from && dateFilter.to) {
    newParams.dateRange = {
      from: dateFilter.from,
      to: dateFilter.to,
    };
  }

  return newParams;
};

export const buildCasesSearchParams = (
  baseParams: CasesSearchParams,
  searchValue: string,
  filters: CasesFilters
): CasesSearchParams => {
  const newParams: CasesSearchParams = {
    ...baseParams,
    query: searchValue,
    pageToken: undefined,
    status: filters.selectedStatuses,
    type: filters.selectedTypes,
    priority: filters.selectedPriorities,
    tags: filters.selectedTags,
  };

  // Fields that support partial-match search (can be used in searchFields)
  const PARTIAL_MATCH_FIELDS = new Set(['id', 'title', 'description']);
  
  if (filters.selectedSearchFields && filters.selectedSearchFields.length > 0) {
    // Split selected fields into partial-match and strict-match
    const partialMatchFields: string[] = [];
    const strictMatchFields: string[] = [];
    
    filters.selectedSearchFields.forEach(field => {
      if (PARTIAL_MATCH_FIELDS.has(field)) {
        partialMatchFields.push(field);
      } else {
        strictMatchFields.push(field);
      }
    });
    
    // Add partial-match fields to searchFields
    if (partialMatchFields.length > 0) {
      newParams.searchFields = partialMatchFields;
    } else {
      delete newParams.searchFields;
    }
    
    // Handle strict-match fields by adding the search query to appropriate filters
    strictMatchFields.forEach(field => {
      if (field === 'tags' && searchValue.trim()) {
        // Add search value to tags filter if tags field is selected
        const currentTags = newParams.tags || [];
        if (!currentTags.includes(searchValue.trim())) {
          newParams.tags = [...currentTags, searchValue.trim()];
        }
      } else if (field === 'created_by_asset_id' && searchValue.trim()) {
        // Add search value to createdByAssetIds filter
        newParams.createdByAssetIds = [searchValue.trim()];
      } else if (field === 'updated_by_asset_id' && searchValue.trim()) {
        // Add search value to updatedByAssetIds filter
        newParams.updatedByAssetIds = [searchValue.trim()];
      }
    });
  } else {
    delete newParams.searchFields;
  }

  if (filters.fieldQueries && filters.fieldQueries.length > 0) {
    const validFieldQueries = filters.fieldQueries.filter((fq) =>
      PARTIAL_MATCH_FIELDS.has(fq.field)
    );

    if (validFieldQueries.length > 0) {
      newParams.fieldQueries = validFieldQueries.map((fq) => ({
        field: fq.field,
        query: fq.query,
      }));
    } else {
      delete newParams.fieldQueries;
    }
  } else {
    delete newParams.fieldQueries;
  }

  const dateFilterKeys = [
    "createTime",
    "updateTime",
    "dueDate",
    "resolvedTime",
    "closeTime",
  ] as const;

  dateFilterKeys.forEach((filterType) => {
    const filter = filters.dateFilters[filterType];
    if (filter && filter.from && filter.to) {
      newParams[filterType] = {
        from: filter.from,
        to: filter.to,
      };
    } else {
      delete newParams[filterType];
    }
  });

  return newParams;
};

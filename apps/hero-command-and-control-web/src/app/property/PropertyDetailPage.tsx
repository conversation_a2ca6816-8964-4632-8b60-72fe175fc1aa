"use client";

import { Typography } from "@/design-system/components/Typography";
import { FormRenderer } from "@/design-system/form/FormRenderer";
import { JsonObject } from "@bufbuild/protobuf";
import { <PERSON><PERSON>, Box, Stack } from "@mui/material";
import { useRouter } from "next/navigation";
import { Entity } from "proto/hero/entity/v1/entity_pb";
import { hookPropertyTypeToString } from "../apis/services/workflow/property/enumConverters";
import { useProperty } from "../apis/services/workflow/property/hooks";
import ChainOfCustodyCard from "../entity/entityComponents/cards/ChainOfCustodyCard";
import MediaTableCard from "../entity/entityComponents/cards/MediaTableCard";
import { PROPERTY_FORM_CONFIG } from "../reports/ReportComponents/core/constants/propertyFormConfig";
import CustodyEvidenceCard from "./components/CustodyEvidenceCard";
import PropertyDetailCard from "./components/PropertyDetailCard";
import PropertyHeader from "./PropertyHeader";

interface PropertyDetailPageProps {
    propertyId: string;
}

// Interface for creating an Entity-compatible object from Property data
// Based on the Entity type from proto/hero/entity/v1/entity_pb.ts but tailored for Property use case
interface PropertyEntityAdapter extends Omit<Entity, 'references' | 'data'> {
    entityType: number; // 3 for PROPERTY
    status: number; // Entity status (1 for ACTIVE)  
    resourceType: string; // "PROPERTY"
    references: never[]; // Empty array for properties
    data?: JsonObject; // Use JsonObject type to match Entity interface
}

// Create a read-only version of the form config containing only PropertySchema fields
const SCHEMA_FIELD_IDS = new Set([
    "description",
    "propertyType",
    "category",
    "quantity",
    "identifiers",
    "owner",
    "condition",
    "serialNumber",
    "value",
]);

const READ_ONLY_PROPERTY_FORM_CONFIG = {
    ...PROPERTY_FORM_CONFIG,
    sections: PROPERTY_FORM_CONFIG.sections
        // Keep only the basic info section
        .filter(section => section.id === "basicInfo")
        .map(section => ({
            ...section,
            // Keep only schema fields and exclude upload
            fields: section.fields.filter(field => SCHEMA_FIELD_IDS.has(field.id))
        })),
};

// Helper function to convert PropertyType enum to a form dropdown value for read-only display
const propertyTypeToFormValue = (propertyType: any): string => {
    if (!propertyType) return '';



    // Handle case where propertyType is already a string
    let enumString: string;
    if (typeof propertyType === 'string') {
        enumString = propertyType;
    } else {
        // Get the enum string representation for enum values
        enumString = hookPropertyTypeToString(propertyType);
    }



    // Map enum strings to dropdown values (lowercase to match the form config options)
    const enumToFormMap: Record<string, string> = {
        'PROPERTY_TYPE_FOUND': 'found',
        'PROPERTY_TYPE_SEIZED': 'seized',
        'PROPERTY_TYPE_STOLEN': 'stolen',
        'PROPERTY_TYPE_SAFEKEEPING': 'safekeeping',
        'PROPERTY_TYPE_MISSING': 'missing',
        'PROPERTY_TYPE_RECOVERED': 'recovered',
    };

    const formValue = enumToFormMap[enumString] || '';
    return formValue;
};

export default function PropertyDetailPage({ propertyId }: PropertyDetailPageProps) {
    const router = useRouter();

    // Get property data
    const {
        data: propertyData,
        isLoading: isPropertyLoading,
        isError: isPropertyError,
    } = useProperty(propertyId);

    const handleClose = () => {
        router.back();
    };

    if (isPropertyLoading) {
        return (
            <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "200px" }}>
                <Typography style="body1">Loading property...</Typography>
            </Box>
        );
    }

    if (isPropertyError || !propertyData) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="error">
                    Failed to load property data. Please try again.
                </Alert>
            </Box>
        );
    }



    // Debug: Log FormRenderer props
    const formInitialValues = {
        basicInfo: propertyData.propertySchema ? {
            description: propertyData.propertySchema.description || "",
            category: propertyData.propertySchema.category || "",
            quantity: propertyData.propertySchema.quantity || "",
            identifiers: propertyData.propertySchema.identifiers || "",
            owner: propertyData.propertySchema.owner || "",
            condition: propertyData.propertySchema.condition || "",
            serialNumber: propertyData.propertySchema.serialNumber || "",
            value: propertyData.propertySchema.value || "",
            propertyType: propertyTypeToFormValue(propertyData.propertySchema.propertyType), // Convert enum to form value
        } : {
            description: "",
            category: "",
            quantity: "",
            identifiers: "",
            owner: "",
            condition: "",
            serialNumber: "",
            value: "",
            propertyType: "",
        },
    };



    // Create a type-safe entity-like object for components that expect Entity type
    const entityForComponents: PropertyEntityAdapter = {
        id: propertyId,
        orgId: propertyData.orgId,
        entityType: 3, // PROPERTY
        status: 1, // ACTIVE
        createTime: propertyData.createTime,
        updateTime: propertyData.updateTime,
        version: propertyData.version,
        resourceType: "PROPERTY",
        data: propertyData as unknown as JsonObject, // Pass the actual property data so MediaTableCard can access metadata
        references: [], // Empty array for properties
        $typeName: "hero.entity.v1.Entity",
        createdBy: "",
        updatedBy: "",
        tags: [],
        schemaId: "", // Properties don't use schemaId
        schemaVersion: 0, // Properties don't use schemaVersion
    };

    return (
        <Box>
            <Box sx={{ bgcolor: 'white' }}>
                <PropertyHeader
                    propertyData={propertyData}
                    propertyId={propertyId}
                    onClose={handleClose}
                />
            </Box>
            <Box sx={{ p: 3, bgcolor: '#F3F4F6', minHeight: 'calc(100vh - 80px)' }}>
                <Stack spacing={3}>
                    <PropertyDetailCard propertyData={propertyData} />

                    {/* Basic Property Info (PropertySchema only) */}
                    <FormRenderer
                        config={READ_ONLY_PROPERTY_FORM_CONFIG}
                        initialValues={formInitialValues}
                        renderMode="consume"
                        showInternalTab={false}
                        readOnly={true}
                        containerHeight={400}
                        key={`form-${propertyId}`} // Force re-render when property changes
                    />

                    {/* Custody & Evidence */}
                    <CustodyEvidenceCard propertyData={propertyData} />

                    <ChainOfCustodyCard property={entityForComponents} />
                    <MediaTableCard property={entityForComponents} />
                </Stack>
            </Box>
        </Box>
    );
}
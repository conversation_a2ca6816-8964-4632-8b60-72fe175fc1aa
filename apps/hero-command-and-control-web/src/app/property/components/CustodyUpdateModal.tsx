"use client";

import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
// No longer need useUpdateProperty or stringToPropertyDisposalType since property updates are handled by parent
import { useDispatcher } from "@/app/contexts/User/DispatcherContext";
import { Button } from "@/design-system/components/Button";
import { DatePicker } from "@/design-system/components/DatePicker";
import { Dropdown } from "@/design-system/components/Dropdown";
import { InputType, TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import CloseIcon from "@mui/icons-material/Close";
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, IconButton } from "@mui/material";
import { AssetStatus, AssetType, ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { useEffect, useState } from "react";
import { CustodyActionType, CustodyEvent } from "../../apis/services/workflow/property/types";

interface CustodyUpdateModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (custodyEvent: CustodyEvent, disposalType?: string) => void;
    actionType: CustodyActionType;
    propertyId: string;
}

const actionTypeLabels: Record<CustodyActionType, string> = {
    [CustodyActionType.CUSTODY_ACTION_TYPE_UNSPECIFIED]: "Unknown Action",
    [CustodyActionType.CUSTODY_ACTION_TYPE_COLLECTED]: "Collect Item",
    [CustodyActionType.CUSTODY_ACTION_TYPE_CHECKED_IN]: "Check In Item",
    [CustodyActionType.CUSTODY_ACTION_TYPE_CHECKED_OUT]: "Check Out Item",
    [CustodyActionType.CUSTODY_ACTION_TYPE_TRANSFERRED]: "Transfer Item",
    [CustodyActionType.CUSTODY_ACTION_TYPE_RELEASED]: "Release Item",
    [CustodyActionType.CUSTODY_ACTION_TYPE_DISPOSED]: "Dispose Item",
    [CustodyActionType.CUSTODY_ACTION_TYPE_LOGGED]: "Log Item",
};

const disposalTypeOptions = [
    { value: "PROPERTY_DISPOSAL_TYPE_RELEASED", label: "Released" },
    { value: "PROPERTY_DISPOSAL_TYPE_DESTROYED", label: "Destroyed" },
    { value: "PROPERTY_DISPOSAL_TYPE_AUCTIONED", label: "Auctioned" },
    { value: "PROPERTY_DISPOSAL_TYPE_AGENCY_RETAIN", label: "Agency Retain" },
    { value: "PROPERTY_DISPOSAL_TYPE_TRANSFERRED", label: "Transferred" },
];

export default function CustodyUpdateModal({
    open,
    onClose,
    onSubmit,
    actionType,
    propertyId
}: CustodyUpdateModalProps) {
    // Set initial time to current time
    const getCurrentTime = () => {
        const now = new Date();
        const hours = now.getHours();
        const minutes = now.getMinutes();

        // Round to nearest 15-minute interval
        const roundedMinutes = Math.round(minutes / 15) * 15;

        // Handle edge case where rounding goes to 60
        let adjustedHours = hours;
        let adjustedMinutes = roundedMinutes;
        if (roundedMinutes === 60) {
            adjustedHours = (hours + 1) % 24;
            adjustedMinutes = 0;
        }

        return `${adjustedHours.toString().padStart(2, '0')}:${adjustedMinutes.toString().padStart(2, '0')}`;
    };

    const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
    const [time, setTime] = useState(getCurrentTime());
    const [location, setLocation] = useState("");
    const [comments, setComments] = useState("");
    const [selectedAssetId, setSelectedAssetId] = useState<string>("");

    // Additional states for conditional fields
    const [transferredFromId, setTransferredFromId] = useState<string>("");
    const [transferredToId, setTransferredToId] = useState<string>("");
    const [disposalType, setDisposalType] = useState<string>("");

    // Get current user's asset
    const { asset: currentUserAsset } = useDispatcher();

    // Get assets for the dropdown
    const { data: assetsResponse } = useListAssets({
        pageSize: 100,
        pageToken: "",
        type: AssetType.UNSPECIFIED,
        status: AssetStatus.UNSPECIFIED,
        orderBy: "name",
    } as ListAssetsRequest);

    // Property updates are now handled by the parent component


    // Set default asset to current user when modal opens
    useEffect(() => {
        if (open) {
            // Modal opened - set up defaults
        }

        if (open && currentUserAsset?.id) {
            if (actionType === CustodyActionType.CUSTODY_ACTION_TYPE_TRANSFERRED) {
                // For transfers, set "From" field to current user
                if (!transferredFromId) {
                    setTransferredFromId(currentUserAsset.id);
                }
            } else {
                // For other actions, set the main asset field
                if (!selectedAssetId) {
                    setSelectedAssetId(currentUserAsset.id);
                }
            }
        }
    }, [open, currentUserAsset?.id, selectedAssetId, transferredFromId, actionType]);

    const handleSubmit = () => {
        // Form submit started

        if (!selectedDate) return;

        // Combine date and time into ISO string
        const [hours, minutes] = time.split(":").map(Number);
        const eventDate = new Date(selectedDate);
        eventDate.setHours(hours, minutes, 0, 0);

        // Build custody event based on action type
        let custodyEvent: CustodyEvent;

        if (actionType === CustodyActionType.CUSTODY_ACTION_TYPE_TRANSFERRED) {
            // For transfers, use transferred from/to fields
            custodyEvent = {
                timestamp: eventDate.toISOString(),
                transferringUserId: transferredFromId || "",
                transferringAgency: "",
                receivingUserId: transferredToId || "",
                receivingAgency: "",
                newLocation: location || "Unknown location",
                actionType: actionType,
                notes: comments,
                caseNumber: "",
                evidenceNumber: "",
            };
        } else if (actionType === CustodyActionType.CUSTODY_ACTION_TYPE_DISPOSED) {
            // Handle disposal action
            // For disposal, include disposal type in notes and update property disposal type
            const disposalNotes = disposalType ? `Disposal Type: ${disposalTypeOptions.find(opt => opt.value === disposalType)?.label || disposalType}. ${comments}`.trim() : comments;
            custodyEvent = {
                timestamp: eventDate.toISOString(),
                transferringUserId: selectedAssetId || "",
                transferringAgency: "",
                receivingUserId: "",
                receivingAgency: "",
                newLocation: location || "Unknown location",
                actionType: actionType,
                notes: disposalNotes,
                caseNumber: "",
                evidenceNumber: "",
            };

            // Property update will be handled by the parent component (PropertyHeader)
        } else {
            // For all other actions, use standard fields
            custodyEvent = {
                timestamp: eventDate.toISOString(),
                transferringUserId: selectedAssetId || "",
                transferringAgency: "",
                receivingUserId: "",
                receivingAgency: "",
                newLocation: location || "Unknown location",
                actionType: actionType,
                notes: comments,
                caseNumber: "",
                evidenceNumber: "",
            };
        }

        onSubmit(custodyEvent, disposalType || undefined);
        handleClose();
    };

    const handleClose = () => {
        setSelectedDate(new Date());
        // Set default time to current time
        const now = new Date();
        const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        setTime(currentTime);
        setLocation("");
        setComments("");
        setSelectedAssetId("");
        // Reset conditional fields
        setTransferredFromId("");
        setTransferredToId("");
        setDisposalType("");
        onClose();
    };

    // Validation based on action type
    const isSubmitDisabled = (() => {
        if (!selectedDate) return true;

        if (actionType === CustodyActionType.CUSTODY_ACTION_TYPE_TRANSFERRED) {
            return !transferredFromId.trim() || !transferredToId.trim();
        } else if (actionType === CustodyActionType.CUSTODY_ACTION_TYPE_DISPOSED) {
            return !selectedAssetId.trim() || !disposalType.trim();
        } else {
            return !selectedAssetId.trim();
        }
    })();

    return (
        <>
            <Dialog
                open={open}
                onClose={handleClose}
                maxWidth="sm"
                fullWidth
                PaperProps={{
                    sx: {
                        borderRadius: "12px",
                        boxShadow: "0px 8px 24px rgba(0, 0, 0, 0.15)",
                    }
                }}
            >
                <DialogTitle sx={{
                    pb: 1,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    borderBottom: `1px solid ${colors.grey[200]}`
                }}>
                    <Typography style="body1" color={colors.grey[900]} className="font-semibold text-lg">
                        {actionTypeLabels[actionType]}
                    </Typography>
                    <IconButton
                        onClick={handleClose}
                        sx={{
                            color: colors.grey[600],
                            "&:hover": { backgroundColor: colors.grey[100] }
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{ pt: 4, pb: 3 }}>
                    <Box sx={{ display: "flex", flexDirection: "column", gap: 3, mt: 2 }}>
                        {/* Conditional Fields Based on Action Type */}
                        {actionType === CustodyActionType.CUSTODY_ACTION_TYPE_TRANSFERRED ? (
                            <>
                                {/* Transferred From */}
                                <Box>
                                    <div style={{ width: '100%' }}>
                                        <Dropdown
                                            enableSearch
                                            onChange={(value) => setTransferredFromId(value || "")}
                                            options={assetsResponse?.assets?.map((asset) => ({
                                                label: asset.id === currentUserAsset?.id ? `${asset.name} (You)` : asset.name,
                                                value: asset.id
                                            })) || []}
                                            placeholder="Select transferring asset"
                                            title="Transferred From"
                                            value={transferredFromId}
                                        />
                                    </div>
                                </Box>

                                {/* Transferred To */}
                                <Box>
                                    <div style={{ width: '100%' }}>
                                        <Dropdown
                                            enableSearch
                                            onChange={(value) => setTransferredToId(value || "")}
                                            options={assetsResponse?.assets?.map((asset) => ({
                                                label: asset.id === currentUserAsset?.id ? `${asset.name} (You)` : asset.name,
                                                value: asset.id
                                            })) || []}
                                            placeholder="Select receiving asset"
                                            title="Transferred To"
                                            value={transferredToId}
                                        />
                                    </div>
                                </Box>
                            </>
                        ) : actionType === CustodyActionType.CUSTODY_ACTION_TYPE_DISPOSED ? (
                            <>
                                {/* Asset Selection for Disposal */}
                                <Box>
                                    <div style={{ width: '100%' }}>
                                        <Dropdown
                                            enableSearch
                                            onChange={(value) => setSelectedAssetId(value || "")}
                                            options={assetsResponse?.assets?.map((asset) => ({
                                                label: asset.id === currentUserAsset?.id ? `${asset.name} (You)` : asset.name,
                                                value: asset.id
                                            })) || []}
                                            placeholder="Select asset"
                                            title="Asset"
                                            value={selectedAssetId}
                                        />
                                    </div>
                                </Box>

                                {/* Disposal Type */}
                                <Box>
                                    <div style={{ width: '100%' }}>
                                        <Dropdown
                                            onChange={(value) => setDisposalType(value || "")}
                                            options={disposalTypeOptions}
                                            placeholder="Select disposal method"
                                            title="Disposal Type"
                                            value={disposalType}
                                        />
                                    </div>
                                </Box>
                            </>
                        ) : (
                            /* Standard Asset Selection for other actions */
                            <Box>
                                <div style={{ width: '100%' }}>
                                    <Dropdown
                                        enableSearch
                                        onChange={(value) => setSelectedAssetId(value || "")}
                                        options={assetsResponse?.assets?.map((asset) => ({
                                            label: asset.id === currentUserAsset?.id ? `${asset.name} (You)` : asset.name,
                                            value: asset.id
                                        })) || []}
                                        placeholder="Select asset"
                                        title="Asset"
                                        value={selectedAssetId}
                                    />
                                </div>
                            </Box>
                        )}

                        {/* Date Field */}
                        <Box>
                            <DatePicker
                                title="Date"
                                placeholder="MM/DD/YYYY"
                                value={selectedDate}
                                onChange={setSelectedDate}
                            />
                        </Box>

                        {/* Time Field */}
                        <Box>
                            <Dropdown
                                title="Time"
                                placeholder="Select time"
                                value={time}
                                onChange={(value) => setTime(value || "")}
                                options={(() => {
                                    const options = [];
                                    for (let hour = 0; hour < 24; hour++) {
                                        for (let minute = 0; minute < 60; minute += 15) {
                                            const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                                            options.push({
                                                value: timeString,
                                                label: timeString
                                            });
                                        }
                                    }
                                    return options;
                                })()}
                            />
                        </Box>

                        {/* Location Field */}
                        <Box>
                            <TextInput
                                title="Location"
                                placeholder="Enter location"
                                value={location}
                                onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setLocation(e.target.value)}
                                type={InputType.Text}
                            />
                        </Box>

                        {/* Comments */}
                        <Box>
                            <TextInput
                                title="Comments"
                                placeholder="Add any additional notes..."
                                value={comments}
                                onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setComments(e.target.value)}
                                type={InputType.Multiline}
                            />
                        </Box>
                    </Box>
                </DialogContent>
                <DialogActions
                    sx={{
                        borderTop: `1px solid ${colors.grey[200]}`,
                        pt: 2,
                        px: 3,
                        pb: 3,
                        gap: 1,
                    }}
                >
                    <Button
                        label="Cancel"
                        prominence={false}
                        onClick={handleClose}
                    />
                    <Button
                        label="Submit"
                        prominence={true}
                        onClick={handleSubmit}
                        disabled={isSubmitDisabled}
                    />
                </DialogActions>
            </Dialog>
        </>
    );
} 
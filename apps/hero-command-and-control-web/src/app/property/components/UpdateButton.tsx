"use client";

import { Button } from "@/design-system/components/Button";
import { colors } from "@/design-system/tokens";
import RefreshIcon from "@mui/icons-material/Refresh";
import { Box, Popover, Typography } from "@mui/material";
import React, { useState } from "react";
import { useProperty } from "../../apis/services/workflow/property/hooks";
import { CustodyActionType, PropertyStatus } from "../../apis/services/workflow/property/types";

interface UpdateButtonProps {
    propertyId: string;
    onUpdateCustody: (actionType: CustodyActionType, propertyId: string) => void;
}

const custodyActions = [
    { value: CustodyActionType.CUSTODY_ACTION_TYPE_COLLECTED, label: "Collected" },
    { value: CustodyActionType.CUSTODY_ACTION_TYPE_CHECKED_IN, label: "Check In" },
    { value: CustodyActionType.CUSTODY_ACTION_TYPE_CHECKED_OUT, label: "Check Out" },
    { value: CustodyActionType.CUSTODY_ACTION_TYPE_TRANSFERRED, label: "Transfer" },
    { value: CustodyActionType.CUSTODY_ACTION_TYPE_RELEASED, label: "Release" },
    { value: CustodyActionType.CUSTODY_ACTION_TYPE_DISPOSED, label: "Dispose" },
];

export default function UpdateButton({ propertyId, onUpdateCustody }: UpdateButtonProps) {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    
    // Get property data to check status
    const { data: propertyData } = useProperty(propertyId);
    

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleActionSelect = (actionType: CustodyActionType) => {
        onUpdateCustody(actionType, propertyId);
        handleClose();
    };

    const open = Boolean(anchorEl);
    
    // Filter out dispose option if property is already disposed
    const propertyStatus = propertyData?.propertyStatus;
    const isAlreadyDisposed = propertyStatus === PropertyStatus.PROPERTY_STATUS_DISPOSED;
    
    
    const availableActions = custodyActions.filter(action => 
        !isAlreadyDisposed || action.value !== CustodyActionType.CUSTODY_ACTION_TYPE_DISPOSED
    );

    return (
        <>
            <Button
                label="Update"
                leftIcon={<RefreshIcon />}
                color="blue"
                prominence={true}
                onClick={handleClick}
                disabled={!propertyId}
            />
            <Popover
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                }}
                transformOrigin={{
                    vertical: "top",
                    horizontal: "left",
                }}
                PaperProps={{
                    sx: {
                        mt: 1,
                        borderRadius: "8px",
                        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                        border: `1px solid ${colors.grey[200]}`,
                        minWidth: 200,
                    },
                }}
            >
                <Box>
                    {availableActions.map((action) => (
                        <Box
                            key={action.value}
                            onClick={() => handleActionSelect(action.value)}
                            sx={{
                                px: 2,
                                py: 1.5,
                                cursor: "pointer",
                                "&:hover": {
                                    backgroundColor: colors.grey[100],
                                },
                                borderBottom: `1px solid ${colors.grey[100]}`,
                                "&:last-child": {
                                    borderBottom: "none",
                                },
                            }}
                        >
                            <Typography variant="body2" color={colors.grey[900]}>
                                {action.label}
                            </Typography>
                        </Box>
                    ))}
                </Box>
            </Popover>
        </>
    );
} 